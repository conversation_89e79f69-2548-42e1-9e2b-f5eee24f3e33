import React, { useState, useMemo, useRef, useEffect } from 'react'
import {
  Button,
  Modal,
  Tabs,
  Table,
  Typography,
  Form,
  Switch,
  InputNumber,
  Select,
  TimePicker,
  message,
  App,
  Input,
  DatePicker,
  Space,
} from 'antd'
import type { FormInstance } from 'antd'
import axios from 'axios'
import { PageHeader } from '@ant-design/pro-layout'
import { useRequest, useAntdTable } from 'ahooks'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import MaterializeVirtualTableDrawer, {
  type MaterializeVirtualTableDrawerProps,
} from './components/MaterializeVirtualTableDrawer'
import request from 'src/shared/xengine-axios'
import { CatalogItem, DatabaseItem } from 'src/client/components/CatalogDatabaseTree'

interface RuleConfig {
  name: string
  description: string
  showType: string
  value: string
  showTypeChinese?: string
}
interface SQLRecommendation {
  /** 推荐日期 */
  dt: string
  /** 命中次数 */
  hit: string
  /** 用户名 */
  usr: string
  /** 查询耗时 */
  time_cost: string
  /** 推荐的SQL */
  recommendation: string
  /** 描述信息 */
  description: string
  /** 原始SQL */
  sql: string
  /** 是否已接受 '0' - 未接受, '1' - 已接受 */
  accept: '0' | '1'
  /** 唯一标识 */
  key: string
}

interface TableRecommendation {
  /** 推荐日期 */
  dt: string
  /** 虚拟表目录名称 */
  catalog_name: string
  /** 命中次数 */
  hit: string
  /** 数据库名称 */
  db_name: string
  /** 查询耗时 */
  time_cost: string
  /** 虚拟表名称 */
  table_name: string
  /** 是否已接受 */
  accept: number
  /** 唯一标识 */
  key: string
}

interface SqlQueryFormData {
  date: Dayjs
  user: string
}

interface TableQueryFormData {
  tableDate: Dayjs
}

export type OptionalDrawerProps = Partial<Omit<MaterializeVirtualTableDrawerProps, 'open'>> & {
  open: boolean
}

interface SqlFiltersProps {
  form: FormInstance<SqlQueryFormData>
  submit: () => void
}

const SqlFilters: React.FC<SqlFiltersProps> = ({ form, submit }) => (
  <Form form={form} layout="inline" className="mb-4">
    <Form.Item name="date" label="选择日期" initialValue={dayjs().subtract(1, 'day')}>
      <DatePicker
        disabledDate={(current) => {
          return current && (current > dayjs().endOf('day') || current < dayjs().subtract(30, 'days').startOf('day'))
        }}
      />
    </Form.Item>
    <Form.Item name="user" label="用户">
      <Input allowClear placeholder="请输入用户" />
    </Form.Item>
    <Form.Item>
      <Space>
        <Button type="primary" onClick={submit}>
          搜索
        </Button>
      </Space>
    </Form.Item>
  </Form>
)

export default function VirtualTableMaterializationRecommend() {
  const [form] = Form.useForm<RuleConfig>()
  const [acceptSqlForm] = Form.useForm<{ catalog: string; database: string; tableName: string }>()
  const [sqlForm] = Form.useForm<SqlQueryFormData>()
  const [tableForm] = Form.useForm<TableQueryFormData>()
  const { Paragraph } = Typography
  const [materializationRuleVisible, setMaterializationRuleVisible] = useState(false)
  const [sqlForModal, setSqlForModal] = useState('')
  const [saving, setSaving] = useState(false)
  const [materializeVirtualTableDrawerProps, setMaterializeVirtualTableDrawerProps] = useState<OptionalDrawerProps>({
    open: false,
  })
  const [acceptSqlLoading, setAcceptSqlLoading] = useState(false)
  const [originORrecommend, setOriginORrecommend] = useState<'origin' | 'recommend'>('origin')
  const [editingColumnIndex, setEditingColumnIndex] = useState<number | null>(null)
  const [editableColumns, setEditableColumns] = useState<string[]>([])
  const originRulesRef = useRef<RuleConfig[]>([])
  const [acceptSqlModalVisible, setAcceptSqlModalVisible] = useState(false)
  const [currentSqlRecord, setCurrentSqlRecord] = useState<SQLRecommendation | null>(null)
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  const sqlColumnColumns = useMemo(
    () => [
      {
        title: '列名',
        dataIndex: 'columnName',
        render: (_: unknown, _record: Record<string, unknown>, index: number) => {
          if (index === editingColumnIndex) {
            return (
              <Input
                defaultValue={editableColumns[index]}
                onChange={(e) => {
                  const newColumns = [...editableColumns]
                  newColumns[index] = e.target.value
                  setEditableColumns(newColumns)
                }}
                autoFocus
              />
            )
          }
          return editableColumns[index]
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        render: (_: unknown, _record: Record<string, unknown>, index: number) => {
          if (index === editingColumnIndex) {
            return (
              <Button type="link" onClick={() => saveColumnEdit()}>
                保存
              </Button>
            )
          }
          return (
            <Button type="link" onClick={() => startColumnEdit(index)}>
              编辑
            </Button>
          )
        },
      },
    ],
    [editingColumnIndex, editableColumns],
  )

  const recommendTableColumns = [
    {
      title: '虚拟表名称',
      dataIndex: 'table_name',
      render: (text: string, record: TableRecommendation) => (
        <Button
          type="link"
          onClick={() =>
            window.open(
              `${askBIPageUrls.manage.xengine.dataModel.businessVirtualTableDetail}?name=${text}&catalog=${record.catalog_name}&database=${record.db_name}`,
              '_blank',
            )
          }
        >
          {text}
        </Button>
      ),
    },
    { title: '数据库名称', dataIndex: 'db_name' },
    { title: '虚拟表目录', dataIndex: 'catalog_name' },
    { title: '命中次数', dataIndex: 'hit' },
    {
      title: '查询耗时',
      dataIndex: 'time_cost',
      render: (text: string) => {
        return <span>{parseFloat(text).toFixed(2)} 毫秒</span>
      },
    },
    { title: '推荐时间', dataIndex: 'dt' },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_: unknown, record: TableRecommendation) => (
        <Button
          type="link"
          onClick={() => handleAcceptTable(record)}
          disabled={record.accept === 1}
          className={record.accept === 1 ? 'cursor-not-allowed text-gray-400' : ''}
        >
          {record.accept === 0 ? '接受' : '已接受'}
        </Button>
      ),
    },
  ]
  const recommendSqlColumns = [
    {
      title: '原始SQL',
      dataIndex: 'sql',
      width: 360,
      render: (text: string) => (
        <div className="flex items-center justify-between">
          <span className="min-w-0 flex-1 truncate">{text}</span>
          <Button
            type="link"
            onClick={() => {
              setSqlForModal(text)
              setOriginORrecommend('origin')
            }}
            className="ml-2 whitespace-nowrap"
          >
            完整SQL
          </Button>
        </div>
      ),
    },
    {
      title: '推荐SQL',
      dataIndex: 'recommendation',
      width: 360,
      render: (text: string) => (
        <div className="flex items-center justify-between">
          <span className="min-w-0 flex-1 truncate">{text}</span>
          <Button
            type="link"
            onClick={() => {
              setSqlForModal(text)
              setOriginORrecommend('recommend')
            }}
            className="ml-2 whitespace-nowrap"
          >
            完整SQL
          </Button>
        </div>
      ),
    },
    { title: '用户', dataIndex: 'usr' },
    { title: '命中次数', dataIndex: 'hit' },
    {
      title: '查询耗时',
      dataIndex: 'time_cost',
      render: (text: string) => {
        return <span>{parseFloat(text).toFixed(2)} 毫秒</span>
      },
    },
    { title: '推荐时间', dataIndex: 'dt' },
    {
      title: '操作',
      width: 170,
      dataIndex: 'action',
      render: (_: unknown, record: SQLRecommendation) => (
        <div className="flex items-center">
          <Button
            type="link"
            onClick={() => handleAcceptSql(record)}
            disabled={record.accept === '1'}
            className={record.accept === '1' ? 'cursor-not-allowed text-gray-400' : ''}
          >
            {record.accept === '0' ? '接受' : '已接受'}
          </Button>
          {record.accept === '1' && (
            <Button
              type="link"
              className="px-0"
              onClick={() => {
                getRelatedVirtualTables(record.recommendation, record.key)
              }}
              loading={loadingStates[record.key]}
            >
              虚拟表详情
            </Button>
          )}
        </div>
      ),
    },
  ]

  const {
    run: getMaterializationRule,
    data: materializationRuleData,
    loading: ruleLoading,
  } = useRequest<RuleConfig[], any>(async () => {
    const res = await axios.get(askBIApiUrls.xengine.materializedRecommendation.materializationRule)
    return res.data.data
  })

  const getSqlList = async (
    { current, pageSize }: { current: number; pageSize: number },
    formData: SqlQueryFormData,
  ) => {
    const params: {
      date: string
      user?: string
      currentPage: number
      numInPage: number
    } = {
      date: formData.date ? formData.date.format('YYYY-MM-DD') : dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      currentPage: current,
      numInPage: pageSize,
    }

    if (formData.user) {
      params.user = formData.user
    }

    const res = await axios.get(askBIApiUrls.xengine.materializedRecommendation.recommendSql, {
      params,
    })

    return {
      total: res.data.data.total || 0,
      list: res.data.data.data.map((item: SQLRecommendation, index: number) => ({
        ...item,
        key: `sql-${index}`,
      })),
    }
  }

  const {
    tableProps: sqlTableProps,
    search,
    refresh: refreshSqlList,
  } = useAntdTable(getSqlList, {
    form: sqlForm,
    defaultPageSize: 10,
    defaultParams: [
      { current: 1, pageSize: 10 },
      { date: dayjs().subtract(1, 'day'), user: '' },
    ],
  })

  const { submit: submitSqlSearch, reset: resetSqlSearch } = search

  const getTableList = async (
    { current, pageSize }: { current: number; pageSize: number },
    formData: TableQueryFormData,
  ) => {
    const params: {
      date: string
      currentPage: number
      numInPage: number
    } = {
      date: formData.tableDate
        ? formData.tableDate.format('YYYY-MM-DD')
        : dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      currentPage: current,
      numInPage: pageSize,
    }

    const res = await axios.get(askBIApiUrls.xengine.materializedRecommendation.recommendTable, {
      params,
    })

    return {
      total: res.data.data.total || 0,
      list: res.data.data.data.map((item: TableRecommendation, index: number) => ({
        ...item,
        key: `table-${index}`,
      })),
    }
  }

  const {
    tableProps: recommendTableProps,
    search: tableSearch,
    refresh: refreshTableList,
  } = useAntdTable(getTableList, {
    form: tableForm,
    defaultPageSize: 10,
    defaultParams: [{ current: 1, pageSize: 10 }, { tableDate: dayjs().subtract(1, 'day') }],
  })

  const { submit: submitTableSearch } = tableSearch

  const { run: getSqlColumn, data: sqlColumnData = [] } = useRequest(
    async (sql: string) => {
      const res = await axios.post(askBIApiUrls.xengine.materializedRecommendation.getSqlColumn, {
        sql,
      })
      return res.data.data
    },
    {
      manual: true,
    },
  )

  const { run: getCatalogData, data: catalogData } = useRequest(
    () =>
      request
        .get<{}, CatalogItem[]>(askBIApiUrls.xengine.catalogList, { params: { current: 1, pageSize: -1 } })
        .then((res) => res.filter((item) => item.type === 'INTERNAL')),
    {
      manual: true,
    },
  )

  const { run: getDatabaseData, data: databaseData } = useRequest(
    async (catalog: string) => {
      const res = await request.get<{ catalog: string }, DatabaseItem[]>(askBIApiUrls.xengine.databaseList, {
        params: { catalog },
      })

      return res
    },
    {
      manual: true,
    },
  )

  const { run: getRelatedVirtualTables } = useRequest(
    async (sql: string, key: string) => {
      setLoadingStates((prev) => ({ ...prev, [key]: true }))
      try {
        const res = await axios.post(askBIApiUrls.xengine.materializedRecommendation.relativeVTables, {
          sql,
        })
        return { data: res.data.data }
      } finally {
        setLoadingStates((prev) => ({ ...prev, [key]: false }))
      }
    },
    {
      manual: true,
      onSuccess: ({ data }) => {
        if (data.length > 0) {
          const [catalog, database, name] = data[0].split(',')
          window.open(
            `${askBIPageUrls.manage.xengine.dataModel.businessVirtualTableDetail}?name=${name}&catalog=${catalog}&database=${database}`,
            '_blank',
          )
        } else {
          message.info('未找到相关虚拟表，请手动前往业务虚拟表菜单')
        }
      },
    },
  )

  useEffect(() => {
    if (sqlColumnData && sqlColumnData.length > 0) {
      setEditableColumns(sqlColumnData)
    }
  }, [sqlColumnData])

  // 虚拟表接受
  const handleAcceptTable = async (record: TableRecommendation) => {
    // columns，通过虚拟表详情接口拿到
    const res = await axios.get(askBIApiUrls.xengine.vTableDetail, {
      params: { catalog: record.catalog_name, database: record.db_name, name: record.table_name },
    })
    setMaterializeVirtualTableDrawerProps({
      open: true,
      columns: res.data.data.columns,
      catalog: record.catalog_name,
      database: record.db_name,
      table: record.table_name,
    })
  }

  // SQL接受
  const handleAcceptSql = async (data: SQLRecommendation) => {
    setCurrentSqlRecord(data)
    setAcceptSqlModalVisible(true)
    getSqlColumn(data.recommendation)
    getCatalogData()
  }

  const startColumnEdit = (index: number) => {
    setEditingColumnIndex(index)
  }

  const saveColumnEdit = () => {
    setEditingColumnIndex(null)
  }

  const handleAcceptSqlConfirm = async () => {
    setAcceptSqlLoading(true)
    try {
      const values = await acceptSqlForm.validateFields()

      const formattedColumns = editableColumns.map((col: string) => ({ name: col }))
      await axios.post(askBIApiUrls.xengine.materializedRecommendation.materializationSql, {
        sql: currentSqlRecord?.sql,
        table: {
          catalogName: values.catalog,
          databaseName: values.database,
          name: values.tableName,
          columns: formattedColumns,
          query: currentSqlRecord?.recommendation,
        },
      })

      message.success('SQL物化成功')
      setAcceptSqlModalVisible(false)
      acceptSqlForm.resetFields()
      setEditingColumnIndex(null)
      refreshSqlList()

      // 跳转到虚拟表详情
      window.open(
        `${askBIPageUrls.manage.xengine.dataModel.businessVirtualTableDetail}?name=${values.tableName}&catalog=${values.catalog}&database=${values.database}`,
        '_blank',
      )
    } catch (error: unknown) {
      if (axios.isAxiosError(error) && error.response) {
        message.error(error.response.data.msg)
      } else {
        message.error('操作失败')
      }
    } finally {
      setAcceptSqlLoading(false)
    }
  }

  const sqlRules = useMemo(
    () => (materializationRuleData ?? []).filter((r) => r.description.startsWith('[SQL推荐]')),
    [materializationRuleData],
  )
  const mvRules = useMemo(
    () => (materializationRuleData ?? []).filter((r) => r.description.startsWith('[虚拟表物化推荐]')),
    [materializationRuleData],
  )

  const renderField = (rule: RuleConfig) => {
    switch (rule.showType) {
      case 'boolean':
        return <Switch />
      case 'number':
        return <InputNumber min={0} style={{ width: '100%' }} />
      case 'time':
        return (
          <Form.Item name={rule.name} rules={[{ required: true, message: '请输入时间' }]} className="mb-0">
            <TimePicker format="HH:mm:ss" style={{ width: '100%' }} inputReadOnly={true} />
          </Form.Item>
        )
      case 'list':
        return <Select mode="tags" style={{ width: '100%' }} />
      default: {
        if (rule.showType.startsWith('enum(')) {
          const opts = rule.showType.match(/^enum\((.*?)\)$/)?.[1]?.split(',') ?? []
          // 如果 showTypeChinese 有值，将其解析为选项的中文标签
          if (rule.showTypeChinese) {
            const chineseLabels = rule.showTypeChinese.split(',')

            return (
              <Select
                options={opts
                  .filter((o) => o !== 'COST_RULE')
                  .map((o, index) => ({
                    value: o,
                    label: `${o}(${chineseLabels[index]})`,
                  }))}
                placeholder="请选择"
              />
            )
          }

          return <Select options={opts.map((o) => ({ value: o, label: o }))} placeholder="请选择" />
        }
        return null
      }
    }
  }

  const buildFormItems = (rules: RuleConfig[]) =>
    rules.map((rule) => (
      <Form.Item
        key={rule.name}
        label={rule.description.replace(/\[.*?]/, '')}
        name={rule.name}
        initialValue={
          rule.showType === 'boolean'
            ? rule.value === 'true'
            : rule.showType === 'number'
              ? Number(rule.value)
              : rule.showType === 'time'
                ? dayjs(rule.value, 'HH:mm:ss')
                : rule.value
        }
        valuePropName={rule.showType === 'boolean' ? 'checked' : 'value'}
      >
        {renderField(rule)}
      </Form.Item>
    ))

  const ruleTabs = [
    { key: 'sql', label: 'SQL推荐', children: buildFormItems(sqlRules) },
    { key: 'mv', label: '虚拟表物化推荐', children: buildFormItems(mvRules) },
  ]

  // 保存规则 (逐条调用)
  const handleSaveRule = async () => {
    try {
      const values = await form.validateFields()

      // 取出 origin & 现值，做 diff，过滤没改动的
      const changedEntries = Object.entries(values).filter(([name, v]) => {
        const origin = originRulesRef.current.find((r) => r.name === name)?.value
        // 将表单值统一转成后端期待的 string
        const toStr = (val: unknown, rule?: RuleConfig): string => {
          if (!rule) return String(val)
          const { showType } = rule
          if (showType === 'boolean') return val ? 'true' : 'false'
          if (showType === 'time') return dayjs(val as Dayjs).format('HH:mm:ss')
          return String(val)
        }
        const rule = originRulesRef.current.find((r) => r.name === name)
        return toStr(v, rule) !== origin
      })

      if (changedEntries.length === 0) {
        message.info('没有需要保存的改动')
        return
      }

      setSaving(true)

      // 构建批量更新的配置对象
      const configs = changedEntries.map(([name, rawVal]) => {
        const rule = originRulesRef.current.find((r) => r.name === name)!
        const value =
          rule.showType === 'boolean'
            ? rawVal
              ? 'true'
              : 'false'
            : rule.showType === 'time'
              ? dayjs(rawVal as unknown as Dayjs).format('HH:mm:ss')
              : String(rawVal)

        return { key: name, value }
      })

      // 使用批量更新接口
      try {
        await axios.post(askBIApiUrls.xengine.materializedRecommendation.materializationRulesToUpdate, { configs })

        message.success('规则已保存')
        setMaterializationRuleVisible(false)
        getMaterializationRule()
      } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
          message.error('规则保存失败，请重试或检查日志')
        } else {
          message.error('未知错误')
        }
        console.error(error)
      }
    } catch (error: unknown) {
      // 表单校验报错等
      console.error(error)
    } finally {
      setSaving(false)
      form.resetFields()
    }
  }

  const TableDateFilter = () => (
    <Form form={tableForm} layout="inline" className="mb-4">
      <Form.Item name="tableDate" label="选择日期" initialValue={dayjs().subtract(1, 'day')}>
        <DatePicker
          disabledDate={(current) => {
            return current && (current > dayjs().endOf('day') || current < dayjs().subtract(30, 'days').startOf('day'))
          }}
        />
      </Form.Item>
      <Form.Item>
        <Space>
          <Button type="primary" onClick={submitTableSearch}>
            搜索
          </Button>
        </Space>
      </Form.Item>
    </Form>
  )

  // 打开弹窗时存原值
  const openRuleModal = async () => {
    await getMaterializationRule()
    originRulesRef.current = materializationRuleData ?? []
    setMaterializationRuleVisible(true)
  }

  const handleCloseModal = () => {
    form.resetFields()
    setMaterializationRuleVisible(false)
  }
  return (
    <App>
      <PageHeader
        title="虚拟表物化推荐"
        extra={
          <Button type="primary" onClick={openRuleModal}>
            物化发现规则
          </Button>
        }
      />

      <Tabs defaultActiveKey="recommend-sql" destroyInactiveTabPane={false}>
        <Tabs.TabPane tab="推荐SQL" key="recommend-sql">
          <SqlFilters form={sqlForm} submit={submitSqlSearch} />
          <Table
            columns={recommendSqlColumns}
            {...sqlTableProps}
            tableLayout="fixed"
            pagination={{
              ...sqlTableProps.pagination,
              showTotal: (total) => `共 ${total} 条`,
            }}
          />
        </Tabs.TabPane>

        <Tabs.TabPane tab="推荐表" key="recommend-table">
          <TableDateFilter />
          <Table
            columns={recommendTableColumns}
            {...recommendTableProps}
            tableLayout="fixed"
            pagination={{
              ...recommendTableProps.pagination,
              showTotal: (total) => `共 ${total} 条`,
            }}
          />
        </Tabs.TabPane>
      </Tabs>

      <Modal
        title="物化发现规则"
        width={720}
        open={materializationRuleVisible}
        onCancel={() => handleCloseModal()}
        onOk={handleSaveRule}
        confirmLoading={saving}
        loading={ruleLoading}
      >
        <Form form={form} layout="vertical">
          <Tabs defaultActiveKey="sql" items={ruleTabs} />
        </Form>
      </Modal>

      <Modal
        title={originORrecommend === 'origin' ? '原始SQL' : '推荐SQL'}
        open={!!sqlForModal}
        footer={null}
        onCancel={() => setSqlForModal('')}
        width={800}
      >
        <Paragraph copyable={{ text: sqlForModal }} style={{ whiteSpace: 'pre-wrap' }}>
          {sqlForModal}
        </Paragraph>
      </Modal>

      <MaterializeVirtualTableDrawer
        open={materializeVirtualTableDrawerProps.open}
        columns={materializeVirtualTableDrawerProps.columns}
        catalog={materializeVirtualTableDrawerProps.catalog}
        database={materializeVirtualTableDrawerProps.database}
        table={materializeVirtualTableDrawerProps.table}
        closeController={() => setMaterializeVirtualTableDrawerProps({ open: false })}
        onSuccess={() => {
          setMaterializeVirtualTableDrawerProps({ open: false })
          refreshTableList()
        }}
      />

      <Modal
        title="SQL物化配置"
        open={acceptSqlModalVisible}
        onOk={handleAcceptSqlConfirm}
        onCancel={() => {
          setAcceptSqlModalVisible(false)
          acceptSqlForm.resetFields()
          setEditingColumnIndex(null)
        }}
        confirmLoading={acceptSqlLoading}
      >
        <Form form={acceptSqlForm} layout="vertical">
          <Form.Item label="虚拟表目录" name="catalog" rules={[{ required: true, message: '请输入虚拟表目录' }]}>
            <Select
              placeholder="请选择虚拟表目录"
              options={catalogData?.map((item) => ({ value: item.name, label: item.name }))}
              onChange={(value) => {
                getDatabaseData(value)
                acceptSqlForm.setFieldValue('database', undefined)
              }}
            />
          </Form.Item>
          <Form.Item label="数据库" name="database" rules={[{ required: true, message: '请输入数据库' }]}>
            <Select
              placeholder="请选择数据库"
              options={databaseData?.map((item) => ({ value: item.name, label: item.name }))}
            />
          </Form.Item>
          <Form.Item label="虚拟表名称" name="tableName" rules={[{ required: true, message: '请输入虚拟表名称' }]}>
            <Input placeholder="请输入虚拟表名称" />
          </Form.Item>
          <Form.Item label="列配置" name="columnSetting">
            <Table
              dataSource={editableColumns.map((col, index) => ({ key: index, columnName: col }))}
              columns={sqlColumnColumns}
              pagination={false}
              scroll={{ y: 300 }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </App>
  )
}
