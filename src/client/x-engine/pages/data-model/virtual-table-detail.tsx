import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { Table, message, Descriptions, Popover, Empty, Typography, Tag, Button, Switch } from 'antd'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { editor as IEditorType } from 'monaco-editor/esm/vs/editor/editor.api'
import type { AnyObject } from 'src/shared/xengine-types'

import LayoutCard from '@ui/layoutCard/LayoutCard'
import GLineage from '@model/GLineage'

import { useAntdTable, useRequest } from 'ahooks'
import { Api, type ApiEngineV1VtableDetailGet200ResponseData } from '@api'
import { getUnitId, formatERDataToGLineage, getDagFromTableData } from '@libs'
import { get } from 'lodash-es'

import dayjs from 'dayjs'
import { formatVTLineageData } from '@libs/lineageFormatFns'
import { routerMap } from '@XEngineRouter/routerMap'
import {
  VIRTUAL_TABLE_DETAIL_PANELS,
  detailTabItems,
  columnTypeColumns,
  taskListColumns,
} from './forms-conf/constant-conf'
import cs from './data-model.module.scss'
import { EditorInDrawer } from 'src/client/x-engine/widget/model/sql/EditorInDrawer'
import { OptionalDrawerProps } from './virtual-table-materialization-recommend'
import MaterializeVirtualTableDrawer from './components/MaterializeVirtualTableDrawer'
import LoadingText from 'src/client/components/LoadingText'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'

enum IVirtualTableType {
  LIKE = '贴源虚拟表',
  AS = '业务虚拟表',
}

const VIRTUAL_TABLE_DETAIL_PANELS_CHILDREN = {
  LIKE: [
    VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE,
    VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW,
    VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION,
  ],
  AS: [
    VIRTUAL_TABLE_DETAIL_PANELS.ER_RELATION,
    VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE,
    VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW,
    // VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND,
    VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION,
  ],
}
function getDetailPanelTabList(computeType: string) {
  const VTType = new RegExp(routerMap.dataModel.businessVirtualTableDetail.path).test(location.href) ? 'AS' : 'LIKE'
  const childrenGroupIndexes = VIRTUAL_TABLE_DETAIL_PANELS_CHILDREN[VTType].filter((i) => {
    return computeType !== 'BATCH' ? i !== VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW : true
  })
  const filterDetailTabItems = detailTabItems?.filter((item) => childrenGroupIndexes.includes(+item.key))
  return (filterDetailTabItems || []).map((i) => ({ key: i.key, tab: i.label }))
}

function MoreItemsPopover({ data }: { data: string[] }) {
  const limit = 3
  const joinChar = ','
  if (!Array.isArray(data)) {
    return <>{data ? data : '-'}</>
  }
  if (data.length <= limit) {
    return <>{data.join(joinChar)}</>
  }
  const ctx = data.slice(0, limit).join(joinChar) + '等'
  const completeCtx = data.join(joinChar)
  return <Popover content={completeCtx}>{ctx}</Popover>
}

const common = [
  {
    label: '虚拟表目录',
    key: 'catalogName',
    render: (record: any) => {
      return get(record, 'catalogName', '-') || '-'
    },
  },
  {
    label: '虚拟表数据库',
    key: 'databaseName',
    render: (record: any) => {
      return get(record, 'databaseName', '-') || '-'
    },
  },
  {
    label: '虚拟表描述',
    key: 'comment',
    render: (record: any) => {
      return get(record, 'comment', '-') || '-'
    },
  },
  {
    label: '表状态',
    key: 'available',
    render: (record: any) => {
      const status = get(record, 'available')
      console.log('record', record, status)
      return status ? <Tag color="green">正常</Tag> : <Tag color="red">异常</Tag>
    },
  },
  {
    label: '创建时间',
    key: 'createTime',
    render: (record: any) => {
      const time = get(record, 'createTime')
      const createTime = time ? dayjs(new Date(time)).format('YYYY-MM-DD HH:mm:ss') : '-'
      return createTime
    },
  },
  {
    label: '创建人',
    key: 'creator',
    render: (record: any) => {
      return get(record, 'creator', '-') || '-'
    },
  },
  {
    label: '虚拟表类型',
    key: 'virtualTableType',
    render: (record: any) => {
      const virtualTableType = get(record, 'virtualTableType') as 'LIKE' | 'AS'
      const VTType = IVirtualTableType[virtualTableType]
      return VTType ? VTType : '-'
    },
  },
]

const tableInfo = {
  AS: [
    ...common,
    {
      label: '关联事实表',
      key: 'factTable',
      render: (data: any) => {
        const factTableArr = get(data?.dataModelDesc, 'factTable', '-').split('.') || ['-']
        return factTableArr[factTableArr.length - 1]
      },
    },
    {
      label: '时间键',
      key: 'timeColumn',
      render: (data: any) => {
        const timeColumn = get(data, 'timeColumn', '-')
        return <MoreItemsPopover data={timeColumn} />
      },
    },
  ],
  LIKE: [
    ...common,
    {
      label: '分区键（PARTITION BY）',
      key: 'partitionKey',
      render: (record: any) => {
        const partitionKeys = get(record, 'partitionKey', '-')
        return <MoreItemsPopover data={partitionKeys} />
      },
    },
  ],
  STREAM: [
    {
      label: '数据来源',
      key: 'source',
      render: (record: any) => {
        return record?.source
      },
    },
    {
      label: '来源topic',
      key: 'topic',
      render: (record: any) => {
        return record?.topic
      },
    },
    {
      label: '消费组',
      key: 'group',
      render: (record: any) => {
        return record?.group
      },
    },
    {
      label: '消费体格式',
      key: 'format',
      render: (record: any) => {
        return record?.format
      },
    },
    {
      label: '分区数量',
      key: 'partition',
      render: (record: any) => {
        return record?.partition
      },
    },
    {
      label: '时间列',
      key: 'timeColumn',
      render: (record: any) => {
        return record?.timeColumn
      },
    },
    {
      label: '分片键',
      key: 'distributedKeys',
      render: (record: any) => {
        return record?.distributedKeys
      },
    },
  ],
}
const virtualTableCreateTypeTitleMap = {
  SQL_CREATE: '通过SQL创建虚拟表SQL',
  FILTER: '可视化FILTER清洗SQL',
  SQL_FILTER: '自定义SQL清洗SQL',
  COPY: '复制虚拟表SQL',
  JOIN: '虚拟表JOIN创建SQL',
  // todo: 兼容数据，2025.4.07过段时间删除
  CEP: '通过SQL创建虚拟表SQL',
  SQL: '自定义SQL清洗SQL',
}

function VirtualTableDetail() {
  const [searchParams] = useSearchParams()
  const tableName = searchParams.get('name') || ''
  const catalog = searchParams.get('catalog') || 'dipeak'
  const database = searchParams.get('database') || 'default'
  const navigate = useNavigate()
  const [SQLEditorInfo, setSQLEditorInfo] = useState({
    open: false,
    createSql: '',
    title: 'SQL展示',
  })
  const [editor, setEditor] = useState<IEditorType.IStandaloneCodeEditor | null>(null)

  const [vtableRawData, setVtableRawData] = useState<ApiEngineV1VtableDetailGet200ResponseData & AnyObject>()
  const [materializeVirtualTableDrawerProps, setMaterializeVirtualTableDrawerProps] = useState<OptionalDrawerProps>({
    open: false,
  })

  // column type panel
  const {
    run: getTableDetail,
    tableProps: columnTypeProps,
    loading: getTableDetailLoading,
  } = useAntdTable(
    useCallback(
      () =>
        Api.apiEngineV1VtableDetailGet({
          name: tableName,
          catalog,
          database,
        }).then((res) => {
          setVtableRawData(res)
          return {
            list: res.columns,
            name: res.name,
            total: res.columns.length ?? 0,
          }
        }),
      [tableName, catalog, database],
    ),
    {
      onError(e: any) {
        console.error(e?.msg || '获取虚拟表字段信息出错')
      },
      defaultPageSize: 10,
    },
  )
  // data preview
  const { data: previewData, tableProps: previewDataProps } = useAntdTable(
    useCallback(
      () =>
        Api.apiEngineV1VtablePreviewGet({
          vtableName: tableName,
          catalog,
          database,
          limit: 50,
        }).then((res) => ({
          list: res,
          total: res.length ?? 0,
        })),
      [tableName, catalog, database],
    ),
    {
      ready: Boolean(vtableRawData && vtableRawData.computeType === 'BATCH'),
      onError(e: any) {
        message.error(e?.msg || '获取虚拟表数据出错')
      },
      defaultPageSize: 10,
    },
  )
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW].children = (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW}
      className={cs.dataPreviewTableWidth}
      {...previewDataProps}
      dataSource={previewData?.list}
      columns={Object.keys(previewData?.list[0] ?? []).map((e) => ({
        title: e,
        dataIndex: e,
        width: 300,
      }))}
      scroll={{ x: 'max-content' }}
      rowKey={() => getUnitId()}
    />
  )
  const { data: lineageData } = useRequest(Api.apiEngineV1VtableGetTableLineageGet, {
    defaultParams: [
      {
        catalogName: catalog,
        databaseName: database,
        tableName: tableName,
      },
    ],
  })
  // 字段类型
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE].children = (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE}
      {...columnTypeProps}
      columns={columnTypeColumns.concat()}
      rowKey={(record) => record.id}
      scroll={{ x: 'max-content' }}
    />
  )
  // 关联模型
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.ER_RELATION].children = (
    <GLineage
      typeKey="type"
      data={formatERDataToGLineage(getDagFromTableData(vtableRawData))}
      emptyDescriptionText="当前虚拟表无关联模型"
    />
  )
  const { tableProps: MVTableProps } = useAntdTable(
    ({ current, pageSize }) => {
      return Api.apiEngineV1MetricsListPost({
        catalog: catalog,
        database: database,
        table: tableName,
        current,
        pageSize,
      } as any)
    },
    {
      defaultPageSize: 10,
      defaultCurrent: 1,
    },
  )

  // related mv
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.RELATED_MV].children = (
    <Table tableLayout="fixed" rowKey={'mvId'} columns={taskListColumns} {...MVTableProps} />
  )

  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION].children = (
    <GLineage typeKey="type" data={formatVTLineageData(lineageData)} />
  )

  // detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.AUTH_MANAGE].children = (
  //     <AuthMange virtualTableName={tableName} catalogName={catalog} databaseName={database} />
  // )

  // detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.QUALITY_MONTOR].children = (
  //     <VirtualTableQuality catalog={catalog} database={database} vtable={tableName} />
  // )

  const [activeTabKey, setActiveTabKey] = useState<string>(`${VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE}`)
  const descriptionItems = useMemo(() => {
    const virtualTableType = vtableRawData?.virtualTableType
    const createSql = vtableRawData?.createSql || ''
    const originDescItems = tableInfo[virtualTableType as keyof typeof tableInfo]
    if (virtualTableType === 'AS' && createSql) {
      return originDescItems.concat({
        label: '表来源',
        key: 'createSql',
        render: () => (
          <Typography.Link
            onClick={() =>
              setSQLEditorInfo({
                open: true,
                createSql,
                title:
                  virtualTableCreateTypeTitleMap[
                    vtableRawData?.virtualTableCreateType as keyof typeof virtualTableCreateTypeTitleMap
                  ] || 'SQL展示',
              })
            }
          >
            SQL查看
          </Typography.Link>
        ),
      })
    }
    return tableInfo[virtualTableType as keyof typeof tableInfo]
  }, [vtableRawData])

  useEffect(() => {
    if (SQLEditorInfo.open && editor) {
      editor.setValue(SQLEditorInfo.createSql)
    }
  }, [editor, SQLEditorInfo])

  return (
    <div className="flex h-full flex-col">
      <PageHeader
        className="flex-none"
        title={'虚拟表：' + tableName}
        onBack={() =>
          navigate(
            `${
              vtableRawData?.virtualTableType === 'AS'
                ? routerMap.dataModel.businessVirtualTable.path
                : routerMap.dataModel.virtualTable.path
            }?catalog=${catalog}&database=${database}`,
          )
        }
        extra={
          <div className="flex items-center gap-2">
            <span>物化</span>
            <LoadingText
              type="link"
              api={async () => {
                if (vtableRawData && 'materialized.view.name' in vtableRawData?.settings) {
                  // 已开启物化
                  const [, , mvName] = get(vtableRawData, ['settings', 'materialized.view.name'])?.split('.') || []
                  return request.delete(`${askBIApiUrls.xengine.mv.dropMv}?mvName=${mvName}&displayName=${mvName}`)
                } else {
                  // 未开启物化
                  setMaterializeVirtualTableDrawerProps({
                    open: true,
                    columns: vtableRawData?.columns?.map((col) => ({
                      ...col,
                      derived: false,
                      vertexId: col.id.toString(),
                    })),
                    catalog: catalog,
                    database: database,
                    table: tableName,
                  })
                  return Promise.resolve()
                }
              }}
              popconfirmProps={
                vtableRawData && 'materialized.view.name' in vtableRawData?.settings
                  ? { title: '确认关闭物化？' }
                  : undefined
              }
              onFail={(err) => message.error(err.message || '操作失败')}
              onSuccess={() => {
                getTableDetail({ current: 1, pageSize: 10 })
              }}
            >
              <Switch checked={vtableRawData && 'materialized.view.name' in vtableRawData?.settings} />
            </LoadingText>
          </div>
        }
      />
      {descriptionItems && (
        <Descriptions column={4} bordered className="flex-none">
          {descriptionItems.map((item, index: number) => {
            return (
              <Descriptions.Item label={item.label} key={index}>
                {item.render ? item.render(vtableRawData) : get(vtableRawData, item.key) || '-'}
              </Descriptions.Item>
            )
          })}
        </Descriptions>
      )}
      <LayoutCard
        className="flex flex-1 flex-col"
        classNames={{
          body: 'flex-1',
        }}
        loading={getTableDetailLoading}
        tabList={getDetailPanelTabList(vtableRawData?.computeType || '')}
        activeTabKey={activeTabKey}
        onTabChange={(i) => {
          setActiveTabKey(i)
        }}
      >
        {detailTabItems[parseInt(activeTabKey)]?.children || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
      </LayoutCard>
      <EditorInDrawer
        hideActions
        onClose={() => setSQLEditorInfo((pre) => ({ ...pre, open: false }))}
        customHandleConfirm={() => setSQLEditorInfo((pre) => ({ ...pre, open: false }))}
        editor={editor}
        setEditor={setEditor}
        title={SQLEditorInfo.title || '创建表SQL'}
        open={SQLEditorInfo.open}
        showSide={false}
      />
      <MaterializeVirtualTableDrawer
        open={materializeVirtualTableDrawerProps.open}
        columns={materializeVirtualTableDrawerProps.columns}
        catalog={materializeVirtualTableDrawerProps.catalog}
        database={materializeVirtualTableDrawerProps.database}
        table={materializeVirtualTableDrawerProps.table}
        closeController={() => setMaterializeVirtualTableDrawerProps({ open: false })}
        onSuccess={() => {
          getTableDetail({ current: 1, pageSize: 10 })
          setMaterializeVirtualTableDrawerProps({ open: false })
        }}
      />
    </div>
  )
}

export default VirtualTableDetail
