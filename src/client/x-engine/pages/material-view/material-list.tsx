// @ts-nocheck
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Col, Row, Typography, message, Statistic, Dropdown, Table, Form, Button, Space, Input, Select } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import {
  Api,
  type ApiEngineV1MvCountOverviewGet200ResponseData,
  type ApiEngineV1MvHitOverviewGet200ResponseData,
  type ApiEngineV1MvTaskOverviewGet200ResponseData,
  type ApiEngineV1MvStorageOverviewGet200ResponseData,
} from '@api'
import { getMVTableColumns, BroadcastIdManage } from './conf/dataConf'
import { routerMap } from '@XEngineRouter/routerMap'
import { useAntdTable, useRequest } from 'ahooks'
import request from 'src/shared/xengine-axios'
import LayoutCard, { GhostCard } from '@ui/layoutCard/LayoutCard'
import { formatByteSmart, getFiltersEntries } from '@libs/util'
import restURI from '@libs/restURI'
import cs from './material-view.module.scss'
import { useImmer } from 'use-immer'
import { pickBy } from 'lodash-es'
import { IUnit, IUnitObj } from '../../static.d'
import { DownOutlined, MoreOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import MVRecordsModal from '@model/material-view/MVRecordsModal'
import { envAtom } from '@atoms/xEngineAtoms'
import { useAtom } from 'jotai'
import axios from 'axios'
import { ENV_PATH } from '@constant/index'
import { askBIApiUrls } from 'src/shared/url-map'
import { customerFilterValue, customerIsSupportType } from 'src/shared/customer-resolver'
import CustomerHiddenWrap from 'src/client/components/CustomerHiddenWrap'

const { Title } = Typography

enum IOperateEnum {
  create = 'create',
  disable = 'disable',
}

// type IMvOverviewKey = keyof ApiEngineV1MvOverviewGet200ResponseData
type IMvOverviewKey =
  | keyof ApiEngineV1MvHitOverviewGet200ResponseData
  | keyof ApiEngineV1MvCountOverviewGet200ResponseData
  | keyof ApiEngineV1MvTaskOverviewGet200ResponseData
  | keyof ApiEngineV1MvStorageOverviewGet200ResponseData

interface IDescriptionInnerItem<T extends keyof IDataMapType> {
  label: string
  key: keyof IDataMapType[T]['data']
  render?: (key: IMvOverviewKey) => React.ReactNode
}

type IDataMapType = {
  mvCount: {
    data: ApiEngineV1MvCountOverviewGet200ResponseData
    item: IDescriptionInnerItem<'mvCount'>
  }
  hitCount: {
    data: ApiEngineV1MvHitOverviewGet200ResponseData
    item: IDescriptionInnerItem<'hitCount'>
  }
  storeCount: {
    data: ApiEngineV1MvStorageOverviewGet200ResponseData
    item: IDescriptionInnerItem<'storeCount'>
  }
  jobCount: {
    data: ApiEngineV1MvTaskOverviewGet200ResponseData
    item: IDescriptionInnerItem<'jobCount'>[]
  }
}
interface IDescriptionItem<T extends keyof IDataMapType> {
  span: number
  title: string
  items: IDataMapType[T]['item'][]
}

enum IDataType {
  mvCount = 'mvCount',
  hitCount = 'hitCount',
  slowNodesCount = 'slowNodesCount',
  analyze = 'analyze',
  storeCount = 'storeCount',
  jobCount = 'jobCount',
}
type SearchParamsType = {
  project?: string
  factTable?: string
}
type DescriptionListType = {
  [K in keyof IDataMapType]: IDescriptionItem<K>
}
const getDescriptionList: (days: number) => DescriptionListType = (days = 30) => ({
  mvCount: {
    title: '物化视图总数',
    span: customerIsSupportType('mvWidthExpand') ? 24 : 6,
    items: [
      // {
      //     label: '人工生成',
      //     key: 'manualCount',
      // },
      // {
      //     label: '贴源导入',
      //     key: 'mirrorCount',
      // },
      // {
      //     label: '智能物化',
      //     key: 'smartCount',
      // },
      {
        label: '',
        key: 'totalCount',
      },
    ],
  },
  hitCount: {
    title: '物化视图命中次数',
    span: 18,
    items: [
      {
        label: `近${days}天物化视图命中数`,
        key: 'mvQueries',
      },
      {
        label: `近${days}天查询总数-(不区分项目、模型)`,
        key: 'queries',
      },
      {
        label: `近${days}天物化命中率`,
        key: 'hitRate',
      },
    ],
  },

  ...(customerIsSupportType('mvDiskHide')
    ? {}
    : {
        storeCount: {
          span: 8,
          title: '物化视图存储占用',
          items: [
            [
              {
                label: `近${days}日新增存储`,
                key: 'recentMvOnDisk',
              },
              {
                label: '总占用存储',
                key: 'mvOnDisk',
              },
            ],
            [
              {
                label: '集群压缩存储',
                key: 'clusterCompressedDiskBytes',
              },

              {
                label: '集群未压缩存储',
                key: 'clusterUncompressedDiskBytes',
              },
            ],
          ],
        },
      }),
  jobCount: {
    span: 16,
    title: '物化视图任务信息',
    items: [
      [
        {
          label: '总任务数',
          key: 'totalTaskCount',
        },
        {
          label: '隶属视图',
          key: 'totalTaskRelateMvCount',
        },
      ],
      [
        {
          label: `近${days}天新增任务数`,
          key: 'latestDaysTaskCount',
        },
        {
          label: '隶属视图',
          key: 'latestDaysTaskRelateMvCount',
        },
      ],
      [
        {
          label: `近${days}天失败任务数`,
          key: 'latestDaysFailedTaskCount',
        },
        {
          label: '隶属视图',
          key: 'latestDaysFailedTaskRelateMvCount',
        },
      ],
      [
        {
          key: 'runningTaskCount',
          label: '当前运行中任务量',
        },
      ],
      [
        {
          key: 'waitingTaskCount',
          label: '当前排队中任务量',
        },
      ],
      [
        {
          key: 'pausedTaskCount',
          label: '暂停中的任务数量',
        },
      ],
    ],
  },
  // slowNodesCount: {
  //     span: 6,
  //     title: '物化视图耗时节点命中总数',
  //     items: [
  //         {
  //             label: '近七天',
  //             key: 'slowNodesCount',
  //         },
  //     ],
  // },
  // analyse: {
  //     span: 18,
  //     title: '物化视图耗时节点分析',
  //     items: [
  //         {
  //             label: '近7天SQL包含join节点数量',
  //             key: 'joinNodesCount',
  //         },
  //         {
  //             label: '近7天SQL包含agg节点数量',
  //             key: 'aggNodesCount',
  //         },
  //         {
  //             label: '近7天SQL包含distinct节点数量',
  //             key: 'distinctNodesCount',
  //         },
  //         {
  //             label: '物化命中join节点数量',
  //             key: 'joinHitsCount',
  //         },
  //         {
  //             label: '物化命中agg节点数量',
  //             key: 'aggregationHitsCount',
  //         },
  //         {
  //             label: '物化命中distinct节点数量',
  //             key: 'distinctHitsCount',
  //         },
  //     ],
  // },
})
function QueryProjectModel({
  onSearch,
  loading,
}: {
  onSearch: (query: { project?: string; factTable?: string }) => void
  loading?: boolean
}) {
  const [form] = Form.useForm()
  return (
    <Form form={form} className="flex flex-1 justify-end" onFinish={onSearch}>
      <Space size="large">
        {/* todo: 5月底版本暂时隐藏 */}
        {/* <Form.Item name="modelName" label="模型名称" className="m-0">
          <Input placeholder="请输入" allowClear />
        </Form.Item> */}
        <Form.Item name="mvName" label="物化视图名称" className="m-0">
          <Input placeholder="请输入" allowClear />
        </Form.Item>
        <CustomerHiddenWrap type="mvFilterFactTableHide">
          <Form.Item name="factTable" label="事实表" className="m-0">
            <Input placeholder="请输入" allowClear />
          </Form.Item>
        </CustomerHiddenWrap>

        <Button type="primary" htmlType="submit" loading={loading}>
          查询
        </Button>
      </Space>
    </Form>
  )
}

const MaterialList: React.FC = () => {
  const [unit, setUnit] = useImmer<IUnitObj>({
    uncompressedSize: IUnit.Byte,
    compressedSize: IUnit.Byte,
  })

  const [envValues, setEnvValues] = useAtom(envAtom)
  const project = JSON.parse(localStorage.getItem('lastTenantSelection') || '{}').project
  useEffect(() => {
    axios.get(ENV_PATH).then((res: any) => {
      if (res) {
        setEnvValues(res)
      }
    })
  }, [])
  const descriptionList = getDescriptionList(+(envValues?.MV_QUERY_DAYS || 30))

  const {
    tableProps,
    run: getMVList,
    refresh,
  } = useAntdTable(
    (arg) => {
      delete arg.extra
      const { filters, sorter, queryParams, ...restQueryParam } = arg
      const reqArg = Object.assign(
        getFiltersEntries(filters ?? {}),
        sorter?.order?.length
          ? {
              sortField: arg.sorter.field,
              isReverseOrder: sorter.order === 'descend',
            }
          : {},
        // TODO merge test
        restQueryParam,
        {
          project,
        },
        pickBy(queryParams, Boolean),
      )
      return request.get(askBIApiUrls.xengine.mv.list, { params: reqArg })
    },
    {
      defaultCurrent: 1,
      defaultPageSize: 10,
    },
  )

  const { run: changeBlackListStatus } = useRequest(Api.apiEngineV1MvBlacklistManagePost, {
    manual: true,
    onSuccess(data) {
      if (data) {
        message.success('已加入黑名单')
        refresh()
      }
    },
  })

  const { run: changeMVActive } = useRequest(Api.apiEngineV1MvActivePost, {
    manual: true,
    onSuccess(data, arg) {
      if (data) {
        refresh()
        message.success(arg[0].active ? '已启用' : '已禁用')
      }
    },
  })

  const {
    loading: mvCountOverviewLoading,
    data: mvCountOverviewData,
    run: getMvCountOverview,
  } = useRequest((searchParams: SearchParamsType) =>
    Api.apiEngineV1MvCountOverviewGet({
      ...searchParams,
      project,
    }),
  )

  const {
    loading: mvHitOverviewLoading,
    data: mvHitOverviewData,
    run: getMvHitOverview,
  } = useRequest(
    (searchParams: SearchParamsType) =>
      Api.apiEngineV1MvHitOverviewGet({
        latestDays: +envValues?.MV_QUERY_DAYS || 30,
        ...searchParams,
        project,
      }).catch(() => null),
    {
      refreshDeps: [envValues?.MV_QUERY_DAYS],
    },
  )

  const {
    loading: mvTaskOverviewLoading,
    data: mvTaskOverviewData,
    run: getMvTaskOverview,
  } = useRequest(
    (searchParams: SearchParamsType) =>
      Api.apiEngineV1MvTaskOverviewGet({
        latestDays: +envValues?.MV_QUERY_DAYS || 30,
        ...searchParams,
        project,
      }).catch(() => null),
    {
      refreshDeps: [envValues?.MV_QUERY_DAYS],
    },
  )

  const {
    loading: mvStorageOverviewLoading,
    data: mvStorageOverviewData,
    run: getMvStorageOverview,
  } = useRequest(
    (searchParams: SearchParamsType) =>
      Api.apiEngineV1MvStorageOverviewGet({
        latestDays: +envValues?.MV_QUERY_DAYS || 30,
        ...searchParams,
        project,
      }).catch(() => null),
    {
      refreshDeps: [envValues?.MV_QUERY_DAYS],
    },
  )

  const {
    run: getActiveStatus,
    data: activeStatus,
    loading: getActiveStatusLoading,
  } = useRequest(Api.apiEngineV1MvActiveStatusGet)
  const { run: changeGlobalMVActive, loading: changeGlobalMVActiveLoading } = useRequest(Api.apiEngineV1MvActivePost, {
    manual: true,
    onSuccess: () => {
      refresh()
      getActiveStatus()
    },
  })
  const PageHeaderExtra = () => {
    const navigate = useNavigate()

    const [selectedMenuKey, setSelectedMenuKey] = useState<keyof typeof IOperateEnum>(IOperateEnum.create)

    const menuItems = [
      {
        label: '创建物化视图',
        key: IOperateEnum.create,
        onClick: () => {
          navigate(routerMap.smartx.createMaterialView.path)
        },
      },
      {
        label: activeStatus ? '全局禁用物化视图' : '全局启用用物化视图',
        key: IOperateEnum.disable,
        onClick: () => {
          changeGlobalMVActive({ active: !activeStatus })
        },
      },
    ] as const

    return (
      <>
        <Dropdown.Button
          style={{ display: 'none' }}
          icon={<DownOutlined />}
          loading={changeGlobalMVActiveLoading || getActiveStatusLoading}
          type={'primary'}
          size={'middle'}
          menu={{
            selectable: true,
            items: menuItems,
            selectedKeys: [selectedMenuKey],
            defaultSelectedKeys: [IOperateEnum.create],
            onClick(e) {
              setSelectedMenuKey(e.key)
            },
          }}
          onClick={() => {
            menuItems
              .find((i) => {
                return i.key === selectedMenuKey
              })
              .onClick()
          }}
        >
          {
            menuItems.find((item) => {
              return item.key === selectedMenuKey
            })?.label
          }
        </Dropdown.Button>
      </>
    )
  }

  function handleDeleteMVSuccess() {
    message.success('已删除')
    refresh()
  }

  return (
    <>
      <MVRecordsModal broadcastIdManage={BroadcastIdManage} />
      <PageHeader
        title={
          <div className={cs.pageHeaderTitle}>
            物化视图
            {!activeStatus && <span className={cs.statusWarning}>已全局禁用物化视图,请全局启用后再使用</span>}
          </div>
        }
        extra={[
          customerIsSupportType('mvSearchHide') ? null : (
            <QueryProjectModel
              key="query-project-model"
              onSearch={(args) => {
                const searchParams = pickBy(args, Boolean)
                getMVList({
                  current: 1,
                  pageSize: 10,
                  queryParams: searchParams,
                })
                getMvCountOverview(searchParams)
                getMvHitOverview(searchParams)
                getMvTaskOverview(searchParams)
                getMvStorageOverview(searchParams)
              }}
              loading={tableProps.loading}
            />
          ),
          <PageHeaderExtra key="page-header-extra" />,
          !customerIsSupportType('downloadMvStatisticsHide') && (
            <Dropdown
              key="more"
              arrow
              menu={{
                items: [
                  {
                    key: 'download-mv-statistics',
                    label: (
                      <Button type="link" icon={<VerticalAlignBottomOutlined />}>
                        <a href={restURI.mv.statisticsDownload} download="全部模型存储信息.xlsx">
                          下载全部模型存储信息
                        </a>
                      </Button>
                    ),
                  },
                ],
              }}
            >
              <div className="cursor-pointer rounded-sm bg-white px-2 py-1">
                <MoreOutlined />
              </div>
            </Dropdown>
          ),
        ]}
      ></PageHeader>
      <Row gutter={[18, 18]} wrap={false} align="stretch">
        {/*<MaterialErrorList />*/}
        <Col flex={'auto'}>
          <Row gutter={[18, 18]} className={cs.MVDetailContainer}>
            {Object.keys(descriptionList).map((key) => {
              const item = descriptionList[key as keyof IDataMapType]
              return (
                <Col span={item.span} key={key}>
                  <LayoutCard
                    title={
                      <>
                        <span>{item.title}</span>&nbsp;&nbsp;
                        {/*{key === IDataType.hitCount && (*/}
                        {/*    <span className='text-sm text-gray-600 font-light'>*/}
                        {/*        {mvHitOverviewData?.updateTime &&*/}
                        {/*            `(数据更新于${dayjs(mvHitOverviewData?.updateTime).format(*/}
                        {/*                'YYYY-MM-DD HH:mm:ss',*/}
                        {/*            )})`}*/}
                        {/*    </span>*/}
                        {/*)}*/}
                        {key === IDataType.jobCount && (
                          <span className="text-sm font-light text-gray-600">
                            {mvTaskOverviewData?.updateTime &&
                              `(数据更新于${dayjs(mvTaskOverviewData?.updateTime).format('YYYY-MM-DD HH:mm:ss')})`}
                          </span>
                        )}
                      </>
                    }
                    style={{
                      // Due to internal constrains, this cannot be rewritten using the classname property
                      margin: 0,
                      padding: 0,
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    {key === IDataType.hitCount && (
                      <GhostCard loading={mvHitOverviewLoading}>
                        <Row gutter={30}>
                          {item.items.map((j, index) => {
                            return (
                              <Col key={index}>
                                <Statistic title={j.label} value={mvHitOverviewData?.[j.key]} />
                              </Col>
                            )
                          })}
                        </Row>
                      </GhostCard>
                    )}
                    {/* {key === IDataType.slowNodesCount && (
                                            <Row gutter={30}>
                                                    {item.items.map((j) => {
                                                        return (
                                                            <Col>
                                                                <Statistic
                                                                    title={j.label}
                                                                    value={MvOverviewData?.[j.key]}
                                                                />
                                                            </Col>
                                                        )
                                                    })}
                                            </Row>
                                        )} */}
                    {key === IDataType.mvCount && (
                      <GhostCard loading={mvCountOverviewLoading}>
                        <Title className={cs.MVCountTitle} level={3}>
                          {item.items
                            .map((i) => {
                              return mvCountOverviewData?.[i.key]?.toString() || '-'
                            })
                            .join('/')}
                        </Title>
                        <div className={cs.MVCountLabel}>
                          {item.items
                            .map((i) => {
                              return i.label
                            })
                            .join('/')}
                        </div>
                      </GhostCard>
                    )}
                    {key === IDataType.storeCount && (
                      <GhostCard loading={mvStorageOverviewLoading}>
                        {/* <Title className={cs.MVCountTitle} level={3}>
                          {item.items
                            .map((i) => {
                              return formatByteSmart(mvStorageOverviewData?.[i.key] || 0)
                            })
                            .join('/')}
                        </Title>
                        <div className={cs.MVCountLabel}>
                          {item.items
                            .map((i) => {
                              return i.label
                            })
                            .join('/')}
                        </div> */}
                        <Row gutter={30}>
                          {item.items.map((j, index) => {
                            return (
                              <Col key={index}>
                                <Statistic
                                  title={j.map((k) => k.label).join('/')}
                                  value={j.map((k) => formatByteSmart(mvStorageOverviewData?.[k.key] || 0)).join('/')}
                                />
                              </Col>
                            )
                          })}
                        </Row>
                      </GhostCard>
                    )}

                    {/* {key === IDataType.analyse && (
                                            <Descriptions>
                                                {descriptionList[IDataType.analyse].items.map((j) => {
                                                    return (
                                                        <Descriptions.Item label={j.label}>
                                                            {MvOverviewData?.[j.key]}
                                                        </Descriptions.Item>
                                                    )
                                                })}
                                            </Descriptions>
                                        )} */}
                    {key === IDataType.jobCount && (
                      <GhostCard loading={mvTaskOverviewLoading}>
                        <Row gutter={30}>
                          {item.items.map((j, index) => {
                            return (
                              <Col key={index}>
                                <Statistic
                                  title={j.map((k) => k.label).join('/')}
                                  value={j.map((k) => mvTaskOverviewData?.[k.key]).join('/')}
                                />
                              </Col>
                            )
                          })}
                        </Row>
                      </GhostCard>
                    )}
                  </LayoutCard>
                </Col>
              )
            })}
          </Row>
        </Col>
      </Row>
      <LayoutCard>
        <Table
          tableLayout="fixed"
          rowKey={'mvId'}
          columns={customerFilterValue(
            'mvListTableColumnsFilter',
            getMVTableColumns({
              unit,
              setUnit,
              changeBlackListStatus,
              changeMVActive,
              handleDeleteMVSuccess,
            }),
          )}
          {...tableProps}
          pagination={{
            ...tableProps.pagination,
            showSizeChanger: true,
          }}
        />
      </LayoutCard>
    </>
  )
}

export default MaterialList
