import React from 'react'
import LayoutCard, { GhostCard } from '@ui/layoutCard/LayoutCard'
import { Api } from '@api'
import { Button, Table, Typography, Result, Empty, Space, type TableProps } from 'antd'
import { routerMap } from '@XEngineRouter/routerMap'
import { Link } from 'react-router-dom'

interface GetCommandsInfoChildrenPropsType {
  data: Record<string, any>
  address: string
  loading: boolean
  onClickDetail?: (recordInfo?: { type: string } | Record<string, any>) => void
  onClickSetOperation?: (recordInfo?: { type: string } | Record<string, any>) => void
}

export const CommandsEnum = {
  listReplicas: '查看副本',
  listTasks: '查看正在运行的任务',
  listSettings: '查看配置',
  dumpStack: '打印堆栈日志',
  refreshThreadPool: '清空阻塞线程任务',
  // getSetting: '查看单个配置',
}
// 查询命令的配置信息
export const getCommandsInfo = [
  {
    commandText: 'listReplicas',
    api: Api.apiEngineV1ReplicaListGet,
    children: ({ data = {}, loading, onClickDetail }: GetCommandsInfoChildrenPropsType) => {
      const isReplicasData = Array.isArray(data.list)
      const dataSource = isReplicasData ? data.list : []
      return (
        <Table
          loading={loading}
          rowKey="name"
          dataSource={dataSource}
          columns={
            [
              {
                dataIndex: 'database',
                title: '数据库名称',
                render(_, record) {
                  const { name } = record || {}
                  const [database] = (name || '').replace('$$', '').split('.')
                  return <>{database || ''}</>
                },
              },
              {
                dataIndex: 'table',
                title: '数据表名称',
                render(_, record) {
                  const { name } = record || {}
                  const [, tableName] = (name || '').replaceAll('$$', '').split('.')
                  return <>{tableName || ''}</>
                },
              },
              {
                dataIndex: 'operation',
                title: '操作',
                render: (_, { name = '' }) => (
                  <Typography.Link
                    onClick={() => {
                      typeof onClickDetail === 'function' &&
                        onClickDetail({
                          type: CommandsEnum.listReplicas,
                          name: name,
                        })
                    }}
                  >
                    详情
                  </Typography.Link>
                ),
              },
            ] as TableProps['columns']
          }
        />
      )
    },
  },
  {
    commandText: 'listTasks',
    api: Api.apiEngineV1TaskListGet,
    children: ({ data = {}, loading, onClickDetail }: GetCommandsInfoChildrenPropsType) => {
      const isTasksData = Array.isArray(data.list)
      const dataSource = isTasksData ? data.list : []
      return (
        <Table
          loading={loading}
          rowKey="taskName"
          dataSource={dataSource}
          columns={
            [
              {
                dataIndex: 'taskName',
                title: 'taskName',
              },
              {
                dataIndex: 'operation',
                title: '操作',
                render: (_, { taskName = '' }) => (
                  <Typography.Link
                    onClick={() => {
                      typeof onClickDetail === 'function' &&
                        onClickDetail({
                          taskName: taskName,
                          type: CommandsEnum.listTasks,
                        })
                    }}
                  >
                    详情
                  </Typography.Link>
                ),
              },
            ] as TableProps['columns']
          }
        />
      )
    },
  },
  {
    commandText: 'dumpStack',
    api: Api.apiEngineV1StackGet,
    children: ({ data, loading, address }: GetCommandsInfoChildrenPropsType) => {
      const initData = data || {}
      const isError = initData.code ? true : false
      return (
        <React.Fragment>
          {data === undefined ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ) : (
            <GhostCard loading={loading}>
              {isError ? (
                <Result status="error" title="stack信息写入失败" subTitle={data.msg || ''} />
              ) : (
                <Result
                  status="success"
                  title="stack信息已成功写入"
                  subTitle="stack信息已写入log中，可进入log界面查看"
                  extra={[
                    <Link to={`${routerMap.advance.logFileList.path}?ip=${address}`} key="log">
                      <Button type="primary">查看log</Button>
                    </Link>,
                  ]}
                />
              )}
            </GhostCard>
          )}
        </React.Fragment>
      )
    },
  },
  {
    commandText: 'refreshThreadPool',
    api: Api.apiEngineV1ThreadGet,
    children: ({ data, loading }: GetCommandsInfoChildrenPropsType) => {
      const initData = data || {}
      const isError = initData.code ? true : false
      return (
        <React.Fragment>
          {data === undefined ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ) : (
            <GhostCard loading={loading}>
              {isError ? (
                <Result status="error" title="操作失败" subTitle={data.msg || ''} />
              ) : (
                <Result status="success" title="操作成功" subTitle="已成功触发新开线程" />
              )}
            </GhostCard>
          )}
        </React.Fragment>
      )
    },
  },
  {
    commandText: 'listSettings',
    api: Api.apiEngineV1SettingsListGet,
    children: ({ data = {}, loading, onClickSetOperation }: GetCommandsInfoChildrenPropsType) => {
      const isTasksData = Array.isArray(data.list)
      const dataSource = isTasksData ? data.list : []
      return (
        <Table
          loading={loading}
          rowKey="taskName"
          columns={
            [
              {
                dataIndex: 'name',
                title: 'name',
                width: '240px',
              },
              {
                dataIndex: 'value',
                title: '类型',
                width: '140px',
              },
              {
                dataIndex: 'type',
                title: 'value',
                width: '140px',
              },
              {
                dataIndex: 'desc',
                title: '描述',
              },
              {
                dataIndex: 'operation',
                title: '操作',
                width: '80px',
                render(_, record) {
                  return (
                    <Space>
                      <Typography.Link
                        onClick={() => {
                          typeof onClickSetOperation === 'function' &&
                            onClickSetOperation({
                              type: CommandsEnum.listSettings,
                              name: record.name,
                              value: record.type,
                            })
                        }}
                      >
                        修改
                      </Typography.Link>
                    </Space>
                  )
                },
              },
            ] as TableProps['columns']
          }
          dataSource={dataSource}
        />
      )
    },
  },
  {
    commandText: 'getSetting',
    api: Api.apiEngineV1SettingsGetGet,
    children: ({ data = {}, loading }: GetCommandsInfoChildrenPropsType) => {
      const ctx = data.msg ? data.msg : data
      return (
        <LayoutCard loading={loading}>
          {typeof ctx === 'string' && ctx ? (
            <Typography.Paragraph>{ctx}</Typography.Paragraph>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </LayoutCard>
      )
    },
  },
]
