import './instrument'
import React, { useEffect, useState } from 'react'
import ReactDOM from 'react-dom/client'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import '../shared/config'
import { GlobalWorkerOptions } from 'pdfjs-dist'
import axios from 'axios'
import { useLocation } from 'react-router-dom'
import { useAtomValue, useSetAtom, Provider } from 'jotai'
import { chatPath, TOKEN_BI, XEngineHomePath } from 'src/shared/constants'
import { getQueryState } from 'src/shared/common-utils'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import Router from './Router'
import './client.css'
import './markdown.css'
import { ErrorBoundary, FallbackComponent } from './ErrorBoundary'
import 'github-markdown-css/github-markdown.css'
import 'react-reflex/styles.css'
import 'katex/dist/katex.min.css'
import { isIOS, autoLogin, stateStore, formatPathWithBaseUrl, autoPufaLogin } from './utils'
import { queryStateAtom, tenantListAtom, lastTenantSelectionAtom, isOpenTenantAtom } from './pages/AskBI/askBIAtoms'
import WaterMark from './components/WaterMark/WaterMark'
import TenantModal from './components/TenantModal/TenantModal'
import '../shared/customer-resolver'
import ThemeConfigProvider from './ThemeConfigProvider'

// 设置语言包
dayjs.locale('zh-cn')

window.__APP_VERSION__ = __APP_VERSION__

/** 设置 PDF.js 工作线程的源文件路径 */
GlobalWorkerOptions.workerSrc = formatPathWithBaseUrl('/js/pdf-v3.8.worker.min.js')

const App = () => {
  // 用于处理输入框失去焦点事件，解决输入框被弹起下不来的问题
  const handleBlur = (event: any) => {
    if (
      document.documentElement.offsetHeight <= document.documentElement.clientHeight &&
      ['input', 'textarea'].includes(event.target.localName)
    ) {
      document.body.scrollIntoView() // 回到顶部
    }
  }

  useEffect(() => {
    if (isIOS) {
      document.addEventListener('blur', handleBlur, true)
    }

    return () => {
      if (isIOS) {
        document.removeEventListener('blur', handleBlur, true)
      }
    }
  }, [])

  return (
    <React.StrictMode>
      <Provider store={stateStore}>
        <ErrorBoundary fallbackComponent={FallbackComponent}>
          <ThemeConfigProvider>
            <Router />
            {/* 水印 */}
            <WaterMark />
          </ThemeConfigProvider>
        </ErrorBoundary>
      </Provider>
    </React.StrictMode>
  )
}

export const TenantWrapper = () => {
  const location = useLocation()
  const [showTenantModal, setShowTenantModal] = useState(false)
  const setTenantList = useSetAtom(tenantListAtom)
  const setIsOpenTenant = useSetAtom(isOpenTenantAtom)
  const setLastTenantSelection = useSetAtom(lastTenantSelectionAtom)
  const isOpenTenant = useAtomValue(isOpenTenantAtom)

  useEffect(() => {
    const checkTenantState = async () => {
      try {
        const tenantResponse = await axios.get(askBIApiUrls.xengine.tenant.getTenantState)
        setIsOpenTenant(tenantResponse.data.data)
      } catch (error) {
        console.error('Failed to get tenant state:', error)
      }
    }

    checkTenantState()
  }, [isOpenTenant, setIsOpenTenant])

  useEffect(() => {
    const initializeTenant = async () => {
      if (!isOpenTenant) return

      try {
        // 1. 获取租户列表
        const response = await axios.get(askBIApiUrls.xengine.tenant.list)
        let normalTenants: any[] = []

        if (response.data?.data) {
          if (Array.isArray(response.data.data)) {
            normalTenants = response.data.data.filter((tenant: { state: string }) => tenant.state === 'NORMAL')
          } else if (response.data.data.list && Array.isArray(response.data.data.list)) {
            normalTenants = response.data.data.list.filter((tenant: { state: string }) => tenant.state === 'NORMAL')
          }
          setTenantList(normalTenants)

          // 2. 检查本地存储的租户信息
          if (location.pathname.startsWith('/manage')) {
            const selectedTenant = localStorage.getItem('lastTenantSelection')

            if (selectedTenant) {
              try {
                const parsedTenant = JSON.parse(selectedTenant)
                const tenantExists = normalTenants.some((tenant) => tenant.tenantName === parsedTenant.tenant)

                if (!tenantExists) {
                  // 如果之前选择的租户不在当前列表中，清除选择并显示选择框
                  localStorage.removeItem('lastTenantSelection')
                  setShowTenantModal(true)
                  document.cookie = 'TENANT_SESSION_ID=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
                  document.cookie = 'PROJECT_SESSION_ID=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
                } else {
                  document.cookie = `TENANT_SESSION_ID=${parsedTenant.tenant}; path=/; SameSite=Lax`
                  document.cookie = `PROJECT_SESSION_ID=${parsedTenant.project}; path=/; SameSite=Lax`
                }
              } catch (error) {
                console.error('Invalid tenant selection format:', error)
                localStorage.removeItem('lastTenantSelection')
                setShowTenantModal(true)
              }
            } else {
              setShowTenantModal(true)
            }
          } else {
            setShowTenantModal(false)
          }
        }
      } catch (error) {
        console.error('Failed to fetch tenant list:', error)
      }
    }

    initializeTenant()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setTenantList, isOpenTenant])

  const handleTenantSelect = (tenantInfo: { tenant: string; catalogName: string; project: string }) => {
    setLastTenantSelection(tenantInfo)
    setShowTenantModal(false)
    window.location.reload()
  }

  return <TenantModal visible={showTenantModal} onSelect={handleTenantSelect} />
}

async function beforeRender() {
  // 检查当前是否在登录页
  const isLoginPage = window.location.pathname === askBIPageUrls.login
  if (isLoginPage) {
    try {
      const token = localStorage.getItem(TOKEN_BI)
      if (token) {
        // 如果有token，尝试获取用户信息来验证token是否有效
        const response = await axios.get(askBIApiUrls.auth.userInfo)
        if (response.data.code === 0 && response.data.data) {
          const url = new URL(window.location.href)
          const path = url.searchParams.get('path')
          const remainingParams = new URLSearchParams(url.search)
          remainingParams.delete('path')

          const envResponse = await axios.get(askBIApiUrls.env.list)
          const envData = envResponse.data.data
          const defaultHomePath = envData?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XEngineHomePath : chatPath

          window.location.href = path
            ? formatPathWithBaseUrl(`${path}?${remainingParams.toString()}`)
            : formatPathWithBaseUrl(defaultHomePath)

          return
        }
      }
    } catch (error) {
      localStorage.removeItem(TOKEN_BI)
      console.error('检查登录状态失败:', error)
    }
  }

  // for pufa 获取url上的usertoken和用户名角色等信息
  const searchParams = new URLSearchParams(location.search)
  const userToken = searchParams.get('userToken')
  if (userToken) {
    try {
      // 如果登录成功，直接跳过后续的逻辑
      await autoPufaLogin(userToken)
      return // 如果登录成功，直接跳过后续逻辑
    } catch (error) {
      console.error('Auto login failed:', error)
      // 如果自动登录失败，可以选择继续执行后续逻辑
    }
  }
  // 初始化的时候直接设置一次query，后期状态管理存储，不再次获取query
  let queryState = getQueryState()
  // 如果用户明确表示，不需要刷新保留原有状态，可以在query增加这条
  if (!queryState.enableReloadQueryState) {
    queryState = getQueryState(true)
  }

  if (queryState.enableAutoLogin) {
    await autoLogin()
    stateStore.set(queryStateAtom, () => queryState)
  }
}
beforeRender().finally(() => {
  ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(<App />)
})
