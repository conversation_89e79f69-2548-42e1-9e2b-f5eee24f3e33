/**
 * @description h5 消息输入框
 */
import React, { forwardRef, useImperativeHandle, useLayoutEffect, useRef, useState } from 'react'
import { EllipsisOutlined, SyncOutlined } from '@ant-design/icons'
import { App, But<PERSON>, Toolt<PERSON> } from 'antd'
import axios from 'axios'
import { useRequest } from 'ahooks'
import clsx from 'clsx'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { useLocation, useNavigate } from 'react-router-dom'
import qs from 'query-string'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import {
  agentMessageAtom,
  chatsAtom,
  conversationIdAtom,
  currentDatasetAtom,
  isLoadingDataset<PERSON>tom,
  isShowMatchedParamDrawerAtom,
  isShowSpeechRecognitionAtom,
  llmList<PERSON>tom,
  llmType<PERSON><PERSON>,
  metricConfig<PERSON>tom,
  showAskHistoryList<PERSON>tom,
} from '../../pages/AskBI/askBIAtoms'
import { APIResponse, LlmListByUsernameResponse } from '../../../shared/common-types'
import { DEFAULT_INPUT_MESSAGE_LENGTH } from '../../../shared/constants'
import { isAndroid } from '../../utils'
import { useSafeClick } from '../../hooks/useSafeClick'
import {
  SvgIcon,
  askHistoryIcon,
  backToHomeIcon,
  clearHistoryMessageIcon,
  emptyCloseIcon,
  h5SendMessageIcon,
  keyboardIcon,
  microphoneEmpty,
} from '../SvgIcon'
import './MessageInput.css'
import MessageInputEditable from '../MessageInputEditable/MessageInputEditable'
import baowuLogo from '/img/baowu/baowu-logo.png'
import CustomSpeechRecognition from '../CustomSpeechRecognition/CustomSpeechRecognition'
import LayoutHeader from '../../LayoutHeader'
import ConverBarWidget from '../ConverWidget'
import { useInitChats } from '../chats'
import NetWorkSearch from './NetWorkSearch'

const operationSafeAreaClass = 'operation-safe-area'

interface MessageInputProps {
  // message: string | JsonContentItem[]
  /** 是否为第一个消息，如果是的时候，切换模型不需要确认 */
  isFirstMessage: boolean
  isSubmitting: boolean
  /** 是否支持 LlmToggle */
  enableLlmToggle: boolean
  /** 目前首页 isMiniMode=true，聊天页 isMiniMode=false */
  isMiniMode: boolean
  defaultPlaceholder?: string
  pressEnterToSubmit: (e: React.KeyboardEvent<Element>) => void
  onSubmit: (message?: string) => void
}

const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
const supportSpeech = isAndroid ? false : SpeechRecognition != null

const MessageInputH5 = forwardRef<{ focus: () => void }, MessageInputProps>(function MessageInput(
  { isMiniMode, isSubmitting, pressEnterToSubmit, onSubmit },
  ref,
) {
  const navigate = useNavigate()
  const message = useAtomValue(agentMessageAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const [_conversationId, setConversationId] = useAtom(conversationIdAtom)
  const chats = useAtomValue(chatsAtom)
  const { initChats } = useInitChats()
  const [llmList, setLlmList] = useAtom(llmListAtom)
  const setLlmType = useSetAtom(llmTypeAtom)
  const setAskHistoryOpen = useSetAtom(showAskHistoryListAtom)
  const { message: antdMessage } = App.useApp()
  const location = useLocation()
  const [isShowMatchedParamDrawer, setIsShowMatchedParamDrawer] = useAtom(isShowMatchedParamDrawerAtom)
  const [isShowSpeechRecognition, setIsShowSpeechRecognition] = useAtom(isShowSpeechRecognitionAtom)
  const supportSpeechRecognition = isBaoWu(metricConfig?.metricTableName) ? true : supportSpeech

  const [showOperationButton, setShowOperationButton] = useState(false)
  const isLoadingDataset = useAtomValue(isLoadingDatasetAtom)
  const [focused, setFocused] = useState(false)
  const [speechShowed, setSpeechShowed] = useState(false)
  const [asked, setAsked] = useState(false)
  const llmType = useAtomValue(llmTypeAtom)

  // 安全点击区域
  useSafeClick(operationSafeAreaClass, () => setShowOperationButton(false))

  const inputRef = useRef<{ focus: () => void; blur: () => void }>(null)

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus()
    },
    blur: () => {
      inputRef.current?.blur()
    },
  }))

  // 请求模型列表
  // TODO: 移到 askBIAtoms 里面去
  useRequest(
    async () => {
      try {
        // 在首页和 chat 页面切换的时候，MessageInput 会被重新渲染，这里如果多次请求，会导致 llmType 被重置，所以判断 llmList 是否存在。如果存在就不再请求
        if (llmList?.length > 0) {
          return
        }
        const response = await axios.get<APIResponse<LlmListByUsernameResponse>>(askBIApiUrls.auth.llms)
        if (
          response.data.data?.llmList &&
          response.data.data?.llmList.length > 0 &&
          response.data.data?.defaultLlmType
        ) {
          setLlmList(response.data.data.llmList)
          // 如果默认模型不再模型列表中，就把默认模型设置为第一个
          setLlmType(response.data.data.defaultLlmType)
        } else {
          antdMessage.error('你没有模型使用权限，请联系管理员开通')
        }
      } catch (error: any) {
        antdMessage.error('获取模型列表失败，请联系管理员' + error.message || '未知原因')
      }
    },
    {
      throttleWait: 300,
    },
  )

  // 清空历史记录
  const clearHistory = () => {
    // 清空 conversation & chart ID , 初始化chats数组
    setConversationId(null)
    initChats()
  }
  // 提交按钮
  const renderSubmit = (
    <>
      {isSubmitting ? (
        <SyncOutlined spin className="flex flex-none rounded-full border text-xl text-primary dark:text-gray-600" />
      ) : (
        <Tooltip placement="top" title="发送">
          <div
            className={clsx(
              'flex size-9 shrink-0 cursor-pointer items-center justify-center rounded-full bg-primary text-white',
            )}
            onClick={(e) => {
              e && e.preventDefault()
              inputRef.current?.blur()
              onSubmit()
            }}
          >
            <SvgIcon icon={h5SendMessageIcon} className="h-5 w-5 dark:text-slate-400" />
          </div>
        </Tooltip>
      )}
    </>
  )

  const renderCloseBtn = (
    <Button
      size="small"
      type="text"
      className="ml-2"
      icon={<SvgIcon icon={emptyCloseIcon} className="size-6" />}
      onClick={() => {
        setIsShowMatchedParamDrawer(false)
      }}
    />
  )

  // 移动端 在输入框上面的块
  const renderH5Features = isShowMatchedParamDrawer ? (
    <div className={clsx('h5-features flex items-center justify-between gap-2')}>
      {isBaoWu(metricConfig?.metricTableName) ? (
        <div className="message-input-icon-list mb-3 flex w-full items-center justify-between py-2">
          <div className="flex flex-1">
            <div className="size-8">
              <img className="size-8" src={baowuLogo} alt="logo" />
            </div>
            <div className={clsx('mr-2 flex flex-1 justify-end gap-5')}>
              <ConverBarWidget />
              <div
                className="flex items-center"
                onClick={() => {
                  setAskHistoryOpen(true)
                }}
              >
                <SvgIcon icon={askHistoryIcon} className="size-6 cursor-pointer text-[#333038]" />
              </div>
            </div>
          </div>
          {/* <div className="divider ml-6 mr-3 h-[18px] border-r" /> */}
          {renderCloseBtn}
        </div>
      ) : (
        <div className="message-input-icon-list flex flex-1 items-center justify-between">
          <div className="flex-1">
            <LayoutHeader navPadding={false} />
          </div>

          {renderCloseBtn}
        </div>
      )}
    </div>
  ) : null

  // 清空历史会话 & 返回首页
  const renderLeftOperationButton = (
    <div
      className={
        'absolute bottom-[80px] right-[20px] flex h-12 min-w-[192px] justify-start rounded-sm border bg-white px-4 py-3 shadow-lg'
      }
    >
      {!metricConfig || isBaoWu(metricConfig?.metricTableName) ? (
        chats.length > 1 ? (
          <div
            className="flex flex-1 flex-shrink-0 items-center justify-start"
            onClick={() => {
              if (chats.length > 1) {
                clearHistory()
                setShowOperationButton(false)
              }
            }}
          >
            <SvgIcon icon={clearHistoryMessageIcon} className="mr-1 size-6 cursor-pointer text-black dark:text-white" />
            清空会话记录
          </div>
        ) : null
      ) : (
        <div
          className="flex flex-shrink-0 items-center justify-center"
          onClick={() => {
            if (chats.length > 1) {
              clearHistory()
            } else {
              navigate(askBIPageUrls.home)
            }
            setShowOperationButton(false)
          }}
        >
          <SvgIcon
            icon={chats.length > 1 ? clearHistoryMessageIcon : backToHomeIcon}
            className="mr-1 size-5 cursor-pointer text-black dark:text-white"
          />
          {chats.length > 1 ? '清空会话记录' : '返回首页'}
        </div>
      )}
    </div>
  )

  const handleChangeSpeechKeyboard = () => {
    const flag = !isShowSpeechRecognition
    setIsShowSpeechRecognition(flag)
    // 点击切换语音/键盘, 关闭弹窗
    setIsShowMatchedParamDrawer(false)
  }

  useLayoutEffect(() => {
    const { autofocus, ask, showSpeech }: { autofocus?: string; ask?: string; showSpeech?: string } = qs.parse(
      location.search,
    )

    if (inputRef.current) {
      if (ask) {
        if (currentDataset && llmType && !isLoadingDataset && !asked) {
          setAsked(true)
          setTimeout(() => {
            // 前置的参数太多了, 加个定时器等参数设置完再请求吧
            onSubmit(ask)
          }, 0)
        }
        return
      }

      if (showSpeech === '1' && !speechShowed) {
        // 如果showSpeech=1,则直接显示语音
        setIsShowSpeechRecognition(true)
        setSpeechShowed(true)
        // 显示语音模块时防止自动聚焦
        setFocused(true)
        return
      }

      if (autofocus === '1' && !focused) {
        setFocused(true)
        inputRef.current.focus()
        setIsShowMatchedParamDrawer(true)
      }
    }
  }, [
    inputRef,
    setIsShowMatchedParamDrawer,
    setIsShowSpeechRecognition,
    currentDataset,
    llmType,
    asked,
    focused,
    speechShowed,
    isLoadingDataset,
    onSubmit,
    location.search,
  ])

  return (
    <div
      className={clsx('message-input-comp relative w-full overscroll-contain px-5 py-3', {
        'bg-white': isShowMatchedParamDrawer,
        'bg-transparent': !isMiniMode && !isShowMatchedParamDrawer,
      })}
    >
      {/* H5输入框上方的功能 */}

      {renderH5Features}
      <div className="rounded-xl border bg-white px-4 py-3">
        {/* 输入框 */}
        {isShowSpeechRecognition ? (
          <CustomSpeechRecognition onSubmit={onSubmit} />
        ) : (
          <div
            id="message-input"
            className={clsx(
              'message-input flex w-full flex-col items-center justify-center rounded-lg bg-white focus-within:ring-primary hover:ring-1 hover:ring-primary',
              {
                'ring-red-300 focus-within:ring-red-400 hover:ring-red-400':
                  message.length >= DEFAULT_INPUT_MESSAGE_LENGTH,
                'ring-1 ring-primary': isShowMatchedParamDrawer,
              },
            )}
          >
            <div className="items-left flex w-full justify-between ring-primary">
              <div className="w-full flex-auto" data-replicated-value={message}>
                <MessageInputEditable
                  maxLength={DEFAULT_INPUT_MESSAGE_LENGTH}
                  isMiniMode={isMiniMode}
                  ref={inputRef}
                  onKeyDown={(e) => {
                    !e.nativeEvent.isComposing && pressEnterToSubmit(e)
                  }}
                  placeholder="请输入你的问题..."
                />
              </div>
            </div>
          </div>
        )}

        {/* 输入框底部功能 */}
        <div className="mt-5 flex items-center justify-between">
          {/* 联网搜索功能 */}
          <NetWorkSearch />

          {/* 底部右侧功能 */}
          <div className="flex flex-1 items-center justify-end">
            <div className="flex items-center">
              {/* 弹窗关闭时语音识别图标 */}

              {/* 清空历史记录 & 返回首页*/}
              {showOperationButton && renderLeftOperationButton}
              {/* 打开上面弹层的按钮 */}
              {location.pathname === askBIPageUrls.chatNew &&
              metricConfig &&
              !isShowMatchedParamDrawer &&
              (!isBaoWu(metricConfig?.metricTableName) || chats.length > 1) ? (
                <div
                  className={clsx(
                    operationSafeAreaClass,
                    'mr-5 flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full border border-[#D9D9D9] dark:border-white',
                  )}
                >
                  <EllipsisOutlined
                    className="rotate-90"
                    onClick={() => {
                      setShowOperationButton(!showOperationButton)
                    }}
                  />
                </div>
              ) : null}
            </div>

            {/* 语音输入按钮 */}
            {supportSpeechRecognition ? (
              <div
                onClick={handleChangeSpeechKeyboard}
                className="mr-5 flex size-9 flex-shrink-0 items-center justify-center rounded-full border"
              >
                <SvgIcon
                  icon={isShowSpeechRecognition ? keyboardIcon : microphoneEmpty}
                  className="size-6 text-[#575757]"
                />
              </div>
            ) : null}
            {/* 提交按钮 */}
            {renderSubmit}
          </div>
        </div>
        {/* 适配iphone底部导航栏高度 */}
      </div>
      <div className="safe-area-inset-bottom" />
    </div>
  )
})

export default MessageInputH5
