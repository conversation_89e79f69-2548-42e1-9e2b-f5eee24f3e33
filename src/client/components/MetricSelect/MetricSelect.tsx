import { Form, Col, Select, message } from 'antd'
import { useRequest } from 'ahooks'
import * as React from 'react'
import { LiteralUnion } from 'antd/es/_util/type'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'
import { MetricConfig, ProjectType, ResponseErrorType, Scenario } from 'src/shared/metric-types'

export interface BaseOptionType {
  disabled?: boolean
  [name: string]: any
}
export interface DefaultOptionType extends BaseOptionType {
  label: React.ReactNode
  value?: string | number | null
  children?: Omit<DefaultOptionType, 'children'>[]
}

/**
 * @param props setFieldValue : form.setFieldValue , in order to clear selection.
 * span: to configuration the col span
 * @returns
 */
const MetricSelect = (props: {
  params?: { projectId: string; scenesId: string; metricsId: string }
  setParams?: (data: any) => void
  required?: {
    projectId: boolean
    scenesId: boolean
  }
  setFieldValue: (name: string, value: any) => void
  span: number
  flex?: number | LiteralUnion<'none' | 'auto'>
  offset?: number
  getProjectName?: (projectList: { id: number; name: string }[], val: any) => string
  formatProjectOptions?: (val: any) => DefaultOptionType
  formatMetricOptions?: (val: any) => DefaultOptionType
  formatScenarioOptions?: (val: any) => DefaultOptionType
}) => {
  const {
    params,
    setParams,
    required,
    setFieldValue,
    span,
    flex,
    offset,
    formatProjectOptions,
    formatScenarioOptions,
    formatMetricOptions,
  } = props
  const { data: projectList } = useRequest(
    async () => {
      return request.get<{}, { projects: ProjectType[] }>(askBIApiUrls.auth.projects)
    },
    {
      onError: (error: ResponseErrorType) => message.error(error?.msg || '获取项目出错'),
    },
  )
  const {
    runAsync: getScenario,
    data: scenarioList,
    mutate: mutateScenario,
  } = useRequest(
    (params) => {
      if (params.projectId === undefined) {
        return Promise.resolve([])
      }
      return request
        .get<{ current: number; pageSize: number }, { list: Scenario[] }>(askBIApiUrls.auth.scene.list, { params })
        .then((res) => res.list)
    },
    {
      manual: true,
      onError: (error: ResponseErrorType) => message.error(error?.msg || '获取场景出错'),
      onFinally() {
        setFieldValue('sceneId', null)
        setFieldValue('metricsId', null)
      },
    },
  )

  const {
    run: getMetric,
    data: metricList,
    mutate: mutateMetric,
  } = useRequest(
    async (params: { sceneId: string; current: number; pageSize: number }) => {
      if (params.sceneId === undefined) {
        return Promise.resolve([])
      }
      return request
        .get<{ current: number; pageSize: number }, MetricConfig>(askBIApiUrls.metrics.listInScene(params.sceneId), {
          params: { current: 1, pageSize: -1 },
        })
        .then((res) => res.allMetrics)
    },
    {
      manual: true,
      onError(error: ResponseErrorType) {
        message.error(error?.msg || '获取指标出错')
      },
      onFinally() {
        setFieldValue('metricsId', null)
      },
    },
  )

  const onProjectChange = (e: string | number) => {
    getScenario({ projectId: e, current: 1, pageSize: -1 })
    if (e === undefined) {
      // 做了清除选中操作
      mutateScenario([])
      mutateMetric([])
    }
    setParams &&
      setParams({
        ...params,
        projectId: e,
        scenesId: '',
      })
  }
  const onScenarioChange = (e: string) => {
    if (e === undefined) {
      mutateMetric([])
    }
    getMetric({ sceneId: e, current: 1, pageSize: -1 })
  }
  return (
    <>
      <Col span={span} offset={offset} flex={flex}>
        <Form.Item
          name="projectId"
          label="项目"
          rules={[{ required: required ? required.projectId : true, message: '请选择项目' }]}
        >
          <Select
            popupMatchSelectWidth={false}
            showSearch
            allowClear
            placeholder="请选择项目"
            value={params && params.projectId}
            onChange={onProjectChange}
            options={
              projectList
                ? projectList.projects?.map(
                    formatProjectOptions ? formatProjectOptions : (e) => ({ label: e.name, value: e.id }),
                  )
                : []
            }
          />
        </Form.Item>
      </Col>
      <Col span={span} offset={offset} flex={flex}>
        <Form.Item
          name="sceneId"
          label="场景"
          rules={[{ required: required ? required.scenesId : true, message: '请选择场景' }]}
        >
          <Select
            showSearch
            popupMatchSelectWidth={false}
            placeholder="请选择场景"
            allowClear
            value={params && params.scenesId}
            onChange={onScenarioChange}
            options={
              scenarioList
                ? scenarioList.map(
                    formatScenarioOptions ? formatScenarioOptions : (e) => ({ label: e?.label, value: e?.id }),
                  )
                : []
            }
          />
        </Form.Item>
      </Col>
      <Col span={span} offset={offset} flex={flex}>
        <Form.Item
          name="metricsId"
          label="指标"
          rules={[{ required: required ? required.scenesId : true, message: '请选择指标' }]}
        >
          <Select
            popupMatchSelectWidth={false}
            showSearch
            allowClear
            placeholder="请选择指标"
            value={params && params.metricsId}
            options={
              metricList
                ? metricList.map(formatMetricOptions ? formatMetricOptions : (e) => ({ label: e.name, value: e.name }))
                : []
            }
          />
        </Form.Item>
      </Col>
    </>
  )
}
export default MetricSelect
