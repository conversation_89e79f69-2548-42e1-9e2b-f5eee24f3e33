import { InfoCircleOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import React from 'react'
import { AssistantChatError } from 'src/client/utils'
import { ChatResponseErrorTypes } from 'src/shared/common-types'
import { assertExhaustive } from 'src/shared/common-utils'
import { MetricConfig, NoListMetric, SceneType } from 'src/shared/metric-types'

const brand = '问数'

const getDesc = ({ content, metricConfig }: { content: AssistantChatError; metricConfig: MetricConfig | null }) => {
  switch (content.errType) {
    case ChatResponseErrorTypes.LLM_ERROR: {
      return '请您稍后再试'
    }
    case ChatResponseErrorTypes.METRICS_NOT_EXIST:
    case ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST:
    case ChatResponseErrorTypes.PROJECT_NOT_EXIST:
    case ChatResponseErrorTypes.MODEL_NOT_EXIST:
    case ChatResponseErrorTypes.CHIT_CHAT:
    case ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED:
    case ChatResponseErrorTypes.COST_SCENE_ERROR:
    case ChatResponseErrorTypes.E_UNKNOWN: {
      return '请您换个问题再试试'
    }
    case ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT: {
      return '没有对比数据，无法归因'
    }
    case ChatResponseErrorTypes.REFRESHING_CACHE: {
      return '稍等一会儿'
    }
    case ChatResponseErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL:
    case ChatResponseErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL:
    case ChatResponseErrorTypes.SENSITIVE_QUESTION:
    case ChatResponseErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL:
    case ChatResponseErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL: {
      return ''
    }
    case ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST:
    case ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST: {
      const hasCumulativeMetric = metricConfig?.allMetrics
        .filter((e) => content?.metricNames?.includes(e.name) && e.type !== 'list')
        .some((e) => !(e as NoListMetric).isCumulative)
      return (
        <span>
          {content.subUnreadyReason}
          {hasCumulativeMetric ? (
            <Tooltip title="该指标属于不可累加指标，取期末时间的数据。">
              <InfoCircleOutlined className="ml-1" />
            </Tooltip>
          ) : null}
        </span>
      )
    }
    case ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST:
    case ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST:
    case ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT:
    case ChatResponseErrorTypes.LOOKUP_FAILED:
    case ChatResponseErrorTypes.CALCULATE_FAILED:
    case ChatResponseErrorTypes.NEED_MANUAL_SELECT:
    case ChatResponseErrorTypes.MANUAL_SELECT_NOT_EXIST:
    case ChatResponseErrorTypes.MANUAL_SELECT_NOT_ENOUGH:
    case ChatResponseErrorTypes.NOT_NEED_BI_RESULT:
    case ChatResponseErrorTypes.E_MATCH:
    case ChatResponseErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED:
    case ChatResponseErrorTypes.NO_DATA_AUTHORITY: {
      return content.subUnreadyReason || null
    }
    default:
      return assertExhaustive(content.errType)
  }
}
const getImgUrl = ({
  content,
  metricErrorImg,
  metricTreeErrorImg,
  modelErrorImg,
  notAnalyzeErrorImg,
  otherErrorImg,
  noSceneErrorImg,
}: {
  content: AssistantChatError
  metricErrorImg: string
  metricTreeErrorImg: string
  modelErrorImg: string
  notAnalyzeErrorImg: string
  otherErrorImg: string
  noSceneErrorImg: string
}) => {
  switch (content.errType) {
    case ChatResponseErrorTypes.METRICS_NOT_EXIST:
    case ChatResponseErrorTypes.PROJECT_NOT_EXIST:
    case ChatResponseErrorTypes.MODEL_NOT_EXIST: {
      return metricErrorImg
    }

    case ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST:
    case ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT:
    case ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT:
    case ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST:
    case ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST: {
      return metricTreeErrorImg
    }
    case ChatResponseErrorTypes.REFRESHING_CACHE: {
      return noSceneErrorImg
    }
    case ChatResponseErrorTypes.LLM_ERROR: {
      return modelErrorImg
    }
    case ChatResponseErrorTypes.CHIT_CHAT: {
      return notAnalyzeErrorImg
    }
    case ChatResponseErrorTypes.COST_SCENE_ERROR:
    case ChatResponseErrorTypes.E_UNKNOWN:
    case ChatResponseErrorTypes.E_MATCH:
    case ChatResponseErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED:
    case ChatResponseErrorTypes.SENSITIVE_QUESTION:
    case ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED: {
      return otherErrorImg
    }
    case ChatResponseErrorTypes.NO_DATA_AUTHORITY:
    case ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST:
    case ChatResponseErrorTypes.LOOKUP_FAILED:
    case ChatResponseErrorTypes.CALCULATE_FAILED:
    case ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST:
    case ChatResponseErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL:
    case ChatResponseErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL:
    case ChatResponseErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL:
    case ChatResponseErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL:
    case ChatResponseErrorTypes.NEED_MANUAL_SELECT:
    case ChatResponseErrorTypes.MANUAL_SELECT_NOT_EXIST:
    case ChatResponseErrorTypes.MANUAL_SELECT_NOT_ENOUGH:
    case ChatResponseErrorTypes.NOT_NEED_BI_RESULT: {
      return null
    }
    default:
      return assertExhaustive(content.errType)
  }
}

// 目前除了getTitle不一样,其他都一样
export const universalConfig = {
  getTitle: ({ content }: { content: AssistantChatError }) => {
    switch (content.errType) {
      // -1
      case ChatResponseErrorTypes.E_UNKNOWN: {
        return '未知类型错误'
      }
      // -2
      case ChatResponseErrorTypes.LLM_ERROR: {
        return '网络暂时断开了'
      }
      // -3
      case ChatResponseErrorTypes.METRICS_NOT_EXIST: {
        return '未找到指标'
      }
      // -4
      case ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST: {
        return '未找到维度'
      }
      // -5
      case ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST: {
        return '归因无法找到指标树'
      }
      // -6
      case ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT: {
        return '不支持的归因类型'
      }
      // -7
      case ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST: {
        return '归因无法找到相关数据'
      }
      // -8
      case ChatResponseErrorTypes.PROJECT_NOT_EXIST: {
        return '项目不存在'
      }
      // -9
      case ChatResponseErrorTypes.MODEL_NOT_EXIST: {
        return '场景不存在'
      }
      // -10
      case ChatResponseErrorTypes.REFRESHING_CACHE: {
        return `场景正在创建中`
      }
      // -11
      case ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT: {
        return `大模型提参结果为空`
      }
      // -12
      case ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED:
      case ChatResponseErrorTypes.CHIT_CHAT: {
        return `超纲了，${brand}大模型正在努力学习中…`
      }

      case ChatResponseErrorTypes.NO_DATA_AUTHORITY: {
        return `抱歉，超出了您的数据访问权限😅`
      }
      case ChatResponseErrorTypes.LOOKUP_FAILED: {
        return '查询指标的值失败'
      }
      case ChatResponseErrorTypes.CALCULATE_FAILED: {
        return '计算指标的值失败'
      }
      case ChatResponseErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL:
      case ChatResponseErrorTypes.NEED_MANUAL_SELECT:
      case ChatResponseErrorTypes.MANUAL_SELECT_NOT_EXIST:
      case ChatResponseErrorTypes.MANUAL_SELECT_NOT_ENOUGH:
      case ChatResponseErrorTypes.NOT_NEED_BI_RESULT:
      case ChatResponseErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL:
      case ChatResponseErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL:
      case ChatResponseErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL: {
        return '工具调用异常，请尝试重新或尝试其他问题'
      }
      case ChatResponseErrorTypes.COST_SCENE_ERROR:
      case ChatResponseErrorTypes.SENSITIVE_QUESTION:
      case ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST:
      case ChatResponseErrorTypes.E_MATCH:
      case ChatResponseErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED:
      case ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST: {
        return content.unreadyReason || `超纲了，${brand}大模型正在努力学习中…`
      }
      default: {
        return assertExhaustive(content.errType)
      }
    }
  },
  getDesc,

  getImgUrl,
}

export const baowuConfig = {
  getTitle: ({ content, scene }: { content: AssistantChatError; scene?: SceneType }) => {
    switch (content.errType) {
      case ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT: {
        return '没有对比数据，无法归因'
      }
      case ChatResponseErrorTypes.REFRESHING_CACHE: {
        return `场景正在创建中`
      }
      case ChatResponseErrorTypes.LLM_ERROR: {
        return '网络暂时断开了'
      }

      case ChatResponseErrorTypes.LOOKUP_FAILED:
      case ChatResponseErrorTypes.CALCULATE_FAILED:
      case ChatResponseErrorTypes.CHIT_CHAT:
      case ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED:
      case ChatResponseErrorTypes.E_UNKNOWN: {
        return `超纲了，${brand}大模型正在努力学习中…`
      }
      // 其他后端错误↓
      case ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST:
      case ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST:
      case ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT: {
        return `该指标${brand}大模型正在学习中`
      }
      case ChatResponseErrorTypes.NO_DATA_AUTHORITY: {
        return `抱歉，超出了您的数据访问权限😅`
      }

      case ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST:
      case ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST: {
        // 追问关闭时依旧展示title
        if (!scene?.enableTryQueryUp) {
          return content.unreadyReason
        }
        // 宝武无数据时不会显示错误内容title
        return ''
      }
      case ChatResponseErrorTypes.COST_SCENE_ERROR: {
        // 宝武无数据时不会显示错误内容title
        return content.unreadyReason
      }
      case ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST:
      case ChatResponseErrorTypes.PROJECT_NOT_EXIST:
      case ChatResponseErrorTypes.METRICS_NOT_EXIST:
      case ChatResponseErrorTypes.E_MATCH:
      case ChatResponseErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED:
      case ChatResponseErrorTypes.MODEL_NOT_EXIST: {
        return content.unreadyReason || `超纲了，${brand}大模型正在努力学习中…`
      }
      case ChatResponseErrorTypes.NEED_MANUAL_SELECT:
      case ChatResponseErrorTypes.MANUAL_SELECT_NOT_EXIST:
      case ChatResponseErrorTypes.MANUAL_SELECT_NOT_ENOUGH:
      case ChatResponseErrorTypes.NOT_NEED_BI_RESULT:
      case ChatResponseErrorTypes.SENSITIVE_QUESTION:
      case ChatResponseErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL:
      case ChatResponseErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL:
      case ChatResponseErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL:
      case ChatResponseErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL: {
        return '工具调用异常，请尝试重新或尝试其他问题'
      }
      default:
        return assertExhaustive(content.errType)
    }
  },
  getDesc,

  getImgUrl,
}
