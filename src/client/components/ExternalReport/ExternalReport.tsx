import React from 'react'
import { MetricConfig, TimeQueryParams } from 'src/shared/metric-types'
import BaowuReport from './baowuReport'

const ExternalReport = ({
  externalReports,
  metricConfig,
  where,
  timeQueryParams,
}: {
  externalReports: string[]
  metricConfig: MetricConfig | null
  where: string
  timeQueryParams?: TimeQueryParams
}) => {
  if (!metricConfig) {
    return <div>暂无指标中心信息</div>
  }
  const externalReportsDetail = metricConfig.allExternalReports.filter((report) =>
    externalReports.includes(report.name),
  )
  if (externalReportsDetail.length === 0) {
    return <div>没有可用的外部报告</div>
  }

  return (
    <div className="external-report-container -mx-6">
      {externalReportsDetail.map((report) => {
        // 通过报表的type来控制渲染
        switch (report.type) {
          case 'baowu_report':
            return (
              <BaowuReport
                metricConfig={metricConfig}
                key={report.name}
                report={report}
                where={where}
                timeQueryParams={timeQueryParams}
              />
            )
          default:
            return (
              <div key={report.name}>
                {report.label}-{report.name}
              </div>
            )
        }
      })}
    </div>
  )
}

export default React.memo(ExternalReport)
