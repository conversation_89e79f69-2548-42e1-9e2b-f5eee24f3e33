import { useRequest } from 'ahooks'
import axios from 'axios'
import React from 'react'
import { useAtomValue } from 'jotai'
import { RenderTime } from 'src/client/charts/Card'
import { currentLoginUserAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { APIResponse } from 'src/shared/common-types'
import { ExternalReport, MetricConfig, TimeQueryParams } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { BaowuReportType, determineBaowuReportType } from 'src/shared/baowu-share-utils'
import { getCookieValue, scrollToBottom } from 'src/client/utils'

const heightConfigMap = {
  Simplified: '580px',
  ExpenseAndAssets: '240px',
  Overview: '580px',
}

const BaowuReport = ({
  report,
  where,
  timeQueryParams,
  metricConfig,
}: {
  report: ExternalReport
  where: string
  timeQueryParams?: TimeQueryParams
  metricConfig: MetricConfig | null
}) => {
  const reportType = determineBaowuReportType(report.name)

  const cookieUsername = getCookieValue('u_info')
  const currentUser = useAtomValue(currentLoginUserAtom)
  const {
    data: reportDetail,
    loading: isLoading,
    error,
  } = useRequest(
    async () => {
      const response = await axios.post<
        APIResponse<{
          url: string
          companyInnerCodes: Array<{ COMPANY_INNER_CODE: string; COMPANY_INNER_CODE_DES: string }>
        }>
      >(askBIApiUrls.externalReport.baowu, {
        reportId: report.id,
        where,
        timeQueryParams,
        username: currentUser?.username || cookieUsername,
      })
      return response.data.data
    },
    {
      onSuccess() {
        setTimeout(scrollToBottom, 100)
      },
      onError: (error: any) => {
        console.error('Get baowu report url failed', report, error)
      },
    },
  )

  if (isLoading) {
    return <div className="px-4">加载中...</div>
  }

  if (error) {
    return <div className="px-4">报告加载失败，请联系管理员处理。</div>
  }

  return (
    <div>
      <iframe src={reportDetail?.url} style={{ width: '100%', height: heightConfigMap[reportType] }} />
      {reportType === BaowuReportType.ExpenseAndAssets && (
        <div className="mt-2 flex flex-col gap-1 px-4">
          <div className="flex items-center justify-between">
            <div className="w-fit rounded-md border border-[#E4E4E4] bg-[#FCFCFC] px-2 py-1.5 text-xs">
              {reportDetail?.companyInnerCodes[0]['COMPANY_INNER_CODE_DES']}
            </div>
          </div>
          <RenderTime timeQueryParams={timeQueryParams} metricConfig={metricConfig} />
        </div>
      )}
    </div>
  )
}

export default React.memo(BaowuReport)
