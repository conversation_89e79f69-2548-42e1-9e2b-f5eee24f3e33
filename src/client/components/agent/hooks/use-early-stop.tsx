import axios from 'axios'
import { useAtomValue } from 'jotai'
import React, { useRef, useState, useEffect } from 'react'
import { parseWhereConditions } from '@shared/match-utils'
import { readyResponseToChatAns, unreadyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
import { ChatResponse, ChatResponseErrorMap, ChatResponseErrorTypes } from 'src/shared/common-types'
import { jsonTryParse } from 'src/shared/common-utils'
import { askBIApiUrls } from 'src/shared/url-map'
import { Nl2agentStatus } from 'src/client/hooks/useChatProgress'
import { AnsChatItem, AssistantChartChatItem } from 'src/client/utils'
import { getMultiAgentData } from '../utils'
import { PlanData, CurrentConfidenceSelection } from '../utils/agent'
import { ChatHistoryItemContext } from '../../ChatHistory/ChatHistoryItemContext'
import { chatRecordAtom } from '../../chats'
import BiResultContent from '../../ChatHistory/BiResultContent'
import { useBiResultContentProps } from './use-bi-result-content-props'

export async function fetchParamsExtract({
  data: earlyData,
  ac,
  requestData,
}: {
  data?: Nl2agentStatus | undefined
  ac?: AbortController
  requestData: Record<string, any>
}): Promise<AnsChatItem[] | undefined> {
  console.info('fetchParamsExtract', earlyData, requestData)
  if (!earlyData) return
  const paramsExtractResponse: any = jsonTryParse(earlyData?.data ?? '') ?? 'Unknown'
  if (paramsExtractResponse?.metric_query_resp) {
    console.info('提前拿到返回结果，不需要请求', paramsExtractResponse?.metric_query_resp)
    const resp = paramsExtractResponse?.metric_query_resp
    const sceneId = paramsExtractResponse.sceneId
    return [
      resp.ready ? readyResponseToChatAns(resp, sceneId, resp.traceId || '') : unreadyResponseToChatAns(resp, sceneId),
    ]
  }
  const res = await axios.post(
    askBIApiUrls.agent.metricQuery,
    {
      paramsExtractResponse: { code: Number(earlyData?.code ?? 0), data: paramsExtractResponse },
      ...requestData,
    },
    { signal: ac?.signal, headers: { traceId: requestData.traceId } },
  )

  const data = res.data.data as ChatResponse[]
  if (!data) return
  // 先和原逻辑对齐，暂时没用
  return data.map((resp) => {
    const sceneId = resp.sceneId
    if (resp.ready) {
      return readyResponseToChatAns(resp, sceneId, resp.traceId || '')
    } else {
      return unreadyResponseToChatAns(resp, sceneId)
    }
  })
}

export function useEarlyStop({ data: earlyData, planData }: { data: Nl2agentStatus | undefined; planData: PlanData }): {
  biResultContentProps?: Parameters<typeof BiResultContent>[0]
  loading: boolean
  earlyAnsItem: AnsChatItem[] | undefined
  setEarlyAnsItem: (ans: AnsChatItem[] | undefined) => void
  setCurrentConfidenceSelection: (selection?: CurrentConfidenceSelection) => void
} {
  const chatRecord = useAtomValue(chatRecordAtom)
  const { chat } = React.useContext(ChatHistoryItemContext)
  const acRef = useRef<AbortController>()
  const multiAgentData = getMultiAgentData(chat)!
  const [earlyAnsItem, setEarlyAnsItem] = planData.useState('earlyAnsItem')
  const [_currentConfidenceSelection, setCurrentConfidenceSelection] = planData.useState('currentConfidenceSelection')
  const requestData = chat.getAskChat(chatRecord)?.requestData ?? {}
  const [loading, setLoading] = useState(false)
  const biResultContentProps = useBiResultContentProps()
  useEffect(() => {
    if (
      earlyAnsItem ||
      ChatResponseErrorMap[earlyData?.code as keyof typeof ChatResponseErrorMap] ===
        ChatResponseErrorTypes.NOT_NEED_BI_RESULT ||
      ChatResponseErrorMap[earlyData?.code as keyof typeof ChatResponseErrorMap] ===
        ChatResponseErrorTypes.NEED_MANUAL_SELECT
    )
      return
    console.info('走老逻辑', earlyData)
    if (earlyData && earlyData.code !== undefined) {
      console.info('老逻辑有数据了')
      // chatProgress?.cancelRequest()
      if (!requestData) return
      acRef.current = new AbortController()
      setLoading(true)
      fetchParamsExtract({ data: earlyData, ac: acRef.current, requestData }).then(
        (ansItem) => {
          if (ansItem) {
            ansItem.forEach((item) => {
              const { content, sceneId, traceId } = item
              const chartItem = content.find(({ type }) => type === 'chart')
              if (chartItem) {
                content.push({
                  type: 'chart-insight',
                  status: 'pending',
                  chartItem: chartItem as AssistantChartChatItem,
                  sceneId,
                  chatId: chat.id,
                  traceId: traceId || '',
                  text: null,
                })
              }
            })
            setEarlyAnsItem(ansItem)
            const ansWithConfidenceData = ansItem.find((i) => i.confidenceOriginData)
            console.info('ansWithConfidenceData', ansItem, ansWithConfidenceData)
            if (ansWithConfidenceData) {
              const chartContent = ansWithConfidenceData.content[0]
              console.info('chartContent', chartContent)
              if (
                ansWithConfidenceData.confidenceOriginData &&
                'queryParamsVerified' in chartContent &&
                chartContent.queryParamsVerified
              ) {
                const where = ansWithConfidenceData.confidenceOriginData.originWhere
                const codeValues = parseWhereConditions(where).flatMap((i) => i.value.codeValues)
                if (codeValues.length > 1) {
                  setCurrentConfidenceSelection({
                    sceneId: ansWithConfidenceData.sceneId,
                    metricNames: chartContent.queryParamsVerified.queryParams.metricNames,
                    where: ansWithConfidenceData.confidenceOriginData.where,
                  })
                }
              }
            }
            multiAgentData.emit('onYieldResult')
          }
          setLoading(false)
        },
        (err) => {
          console.info('fetchParamsExtract Error', err)
          if (err?.code !== 'ERR_CANCELED') {
            setLoading(false)
          }
        },
      )
      return () => {
        console.info('老逻辑ABORT')
        acRef.current?.abort()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [earlyData?.data])

  // useEffect(() => {
  //   if (chatProgress) {
  //     const earlyListener: ChatProgressHooks['onAfterFetch'] = (chatProgressData) => {
  //       const { nl2agentStatus } = chatProgress.transformChatProgressData(chatProgressData)
  //       const data = findKeyFromDataList(id, nl2agentStatus ?? [])
  //       setData(data)
  //     }

  //     chatProgress?.data.hooks.on('onAfterFetch', earlyListener)
  //     return () => {
  //       chatProgress?.data.hooks.removeListener('onAfterFetch', earlyListener)
  //     }
  //   }
  // }, [chatProgress, id, setData])
  if (loading || !earlyAnsItem)
    return {
      loading,
      earlyAnsItem,
      setEarlyAnsItem,
      setCurrentConfidenceSelection,
    }

  const firstAnsItem = earlyAnsItem[0]
  const mainChart = firstAnsItem?.content.find((obj) => obj.type === 'chart')
  return {
    loading,
    biResultContentProps: {
      ...biResultContentProps,
      chatAnsItem: firstAnsItem,
      showActionBar: true,
      showFeedback: false,
      showSqlButton: firstAnsItem.content.some(({ type }) => type === 'sql'), // !isBaoWu(metricConfig?.metricTableName)
      showSwitchChartButton: Boolean(mainChart?.recommendChartTypes?.length),
      showCharInsightButton: firstAnsItem.content.some(({ type }) => type === 'chart-insight'),
      showMainChartButtons: Boolean(mainChart),
      currentSelectedSceneId: firstAnsItem.sceneId,
      earlyAnsItem,
      planData,
    },
    earlyAnsItem,
    setEarlyAnsItem,
    setCurrentConfidenceSelection,
  }
}
