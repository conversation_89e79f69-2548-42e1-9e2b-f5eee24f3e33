import { useAtomValue } from 'jotai'
import React, { useEffect } from 'react'
import { currentParamsExtractApiAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { EventSource, ResponseEntity } from 'src/shared'
import { abortControllerManager } from 'src/client/utils'
import { Nl2agentStatus } from 'src/client/hooks/useChatProgress'
import { askBIApiUrls } from 'src/shared/url-map'
import { ChatHistoryItemContext } from '../../ChatHistory/ChatHistoryItemContext'
import { getMultiAgentData } from '../utils'
import { PlanData } from '../utils/agent'

export function useCot({
  uuid,
  data,
  planData,
}: {
  uuid: string
  data: Nl2agentStatus | undefined
  planData: PlanData
  afterSetCot?: () => void
}) {
  const { chat } = React.useContext(ChatHistoryItemContext)
  const multiAgentData = getMultiAgentData(chat)!
  const currentParamsExtractApi = useAtomValue(currentParamsExtractApiAtom)
  const [cot, setCot] = planData.useState('cot')

  useEffect(() => {
    if (data?.cot) {
      setCot(data.cot)
      return
    }
    if (!data || multiAgentData.inferStatus === 'done') return
    if (!uuid) return
    const eventSource = new EventSource()
    const ac = {
      abort: () => eventSource?.abort(),
    }
    abortControllerManager.add(ac)
    // setCot('')
    eventSource.on('onMessage', (msg) => {
      // console.log('onMessage', msg)
      if (msg.data) {
        const data = ResponseEntity.from<string>(JSON.parse(msg.data))
        if (data.getCode() === 0) {
          const str = data.getData() ?? ''
          setCot((old) => old + str)
        }
      }
    })
    eventSource.on('onError', (err) => {
      console.info('Cot onError', err)
    })
    // eventSource.on('onStatusChange', (status) => {
    //   if (status === 'close') {
    //   }
    // })
    setTimeout(() => {
      eventSource
        .fetch(askBIApiUrls.agent.cotStream, {
          method: 'post',
          body: JSON.stringify({ uuid, currentParamsExtractApi }),
        })
        .catch((err) => {
          console.info('SSE SSE ERR', err)
        })
        .finally(() => {})
    }, 200)

    return () => {
      abortControllerManager.delete(ac)
      ac.abort()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuid])

  return {
    cot,
    setCot,
  }
}
