import React, { memo, PropsWithChildren, useEffect, useRef, useState } from 'react'
import { useAtomValue } from 'jotai'
import { ChatResponseErrorMap, ChatResponseErrorTypes } from 'src/shared/common-types'
import { DocReferenceTextNodeList } from 'src/client/pages/AskDoc/DocDetail/AnswerView'
import { Nl2agentStatusToolType } from 'src/client/hooks/useChatProgress'
import { AnsChatItem } from 'src/client/utils'
import { ChatHistoryItemContext } from '../ChatHistory/ChatHistoryItemContext'
import BiResultContent from '../ChatHistory/BiResultContent'
import { chatRecordAtom } from '../chats'
import { findKeyFromDataList, getMultiAgentData, getResolvedMarkdown, PlanDataHooks } from './utils'
import { fetchParamsExtract, useCot, useEarlyStop, usePlanData, useCalculator } from './hooks'
import { Markdown } from './markdown'

export const ResultMarkdownWrapper = memo(function ToolMarkdownWrapperMemo(props: PropsWithChildren<{}>) {
  return <div className="multi-agent-markdown multi-agent-markdown-end flex w-full flex-col">{props.children}</div>
})

export const MultiAgentResultEarlyStop = memo(function MultiAgentResultEarlyStopMemo(props: MultiAgentResultProps) {
  const { chat } = React.useContext(ChatHistoryItemContext)
  const multiAgentData = getMultiAgentData(chat)
  const { id, isViewMode } = props
  const { data, planData, setData } = usePlanData({ id })
  const { biResultContentProps, loading, earlyAnsItem, setEarlyAnsItem, setCurrentConfidenceSelection } = useEarlyStop({
    data,
    planData,
  })

  useEffect(() => {
    if (multiAgentData) {
      const multiAgentEarlyAnsItemListener: PlanDataHooks['onUpdateEarlyAnsItem'] = (ans) => {
        setEarlyAnsItem(ans)
      }
      const multiAgentStateMapListener: PlanDataHooks['onUpdateCurrentConfidenceSelection'] = (
        currentConfidenceSelection,
      ) => {
        setCurrentConfidenceSelection(currentConfidenceSelection)
      }

      const multiAgentAbortMapListener = () => {
        // data?.status = 'succeed'
        const originData = findKeyFromDataList(id, multiAgentData.dataList)
        if (originData) {
          originData.status = 'aborted'
          setData({ ...originData, status: 'aborted' })
        }
      }
      planData.on('onUpdateEarlyAnsItem', multiAgentEarlyAnsItemListener)
      planData.on('onUpdateCurrentConfidenceSelection', multiAgentStateMapListener)
      multiAgentData.on('onAbort', multiAgentAbortMapListener)
      return () => {
        planData.removeListener('onUpdateEarlyAnsItem', multiAgentEarlyAnsItemListener)
        planData.removeListener('onUpdateCurrentConfidenceSelection', multiAgentStateMapListener)
        multiAgentData.removeListener('onAbort', multiAgentAbortMapListener)
      }
    }
  }, [multiAgentData, chat, setEarlyAnsItem, planData, setCurrentConfidenceSelection, id, setData])
  if (
    ChatResponseErrorMap[data?.code as keyof typeof ChatResponseErrorMap] === ChatResponseErrorTypes.NOT_NEED_BI_RESULT
  )
    return null
  if (loading || data?.status === 'running') return <span className="loading-typist" />
  if (!earlyAnsItem) {
    return null
  }
  return <BiResultContent {...biResultContentProps!} isViewMode={isViewMode} />
})

export const MultiAgentResultBi = memo(function MultiAgentToolBiMemo(props: MultiAgentResultProps) {
  const chatRecord = useAtomValue(chatRecordAtom)
  const { chat } = React.useContext(ChatHistoryItemContext)
  const multiAgentData = getMultiAgentData(chat)
  const requestData = chat.getAskChat(chatRecord)?.requestData ?? {}
  // const biResultContentProps = useBiResultContentProps()
  const { id } = props
  const { planData, data, uuid } = usePlanData({ id })
  const { node: CalculatorNode } = useCalculator(planData)
  const acRef = useRef<AbortController>()

  const { cot } = useCot({
    uuid,
    data,
    planData,
  })

  // 这里不确定是展示什么 默认先set AnsItem[0]
  const [ansItem, setAnsItem] = useState<AnsChatItem | undefined>()
  useEffect(() => {
    if (data?.status === 'failed' && requestData) {
      acRef.current = new AbortController()
      fetchParamsExtract({ ac: acRef.current, data, requestData }).then((ansItem) => {
        setAnsItem(ansItem ? ansItem[0] : undefined)
      })
      return () => {
        acRef.current?.abort()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.status])

  useEffect(() => {
    if (ansItem || cot) {
      multiAgentData?.emit('onYieldResult')
    }
  }, [ansItem, cot, multiAgentData])

  // const dataNode = useMemo(() => {
  //   if (Number(planData.data?.code) !== 0) return null
  //   if (!planData.data?.data) return null
  //   const tryData = jsonTryParse(planData.data?.data)
  //   const dataList: string[] = Array.isArray(tryData) ? tryData : [planData.data?.data]
  //   return dataList.map((v, i) => <Markdown md={formatScientificNotationNumber(v ?? '')} key={i} />)
  // }, [planData.data?.code, planData.data?.data])

  return <div className="flex w-full flex-col gap-[16px]">{CalculatorNode}</div>
})

export const MultiAgentResultChat = memo(function MultiAgentToolBrainMemo(props: MultiAgentResultProps) {
  const { chat } = React.useContext(ChatHistoryItemContext)
  const multiAgentData = getMultiAgentData(chat)
  const { id } = props
  const { planData, data, uuid } = usePlanData({ id })
  const { cot } = useCot({
    uuid,
    data,
    planData,
  })
  const { result, extraData } = getResolvedMarkdown({
    cot,
    isRunning: planData.data?.status === 'running',
  })

  useEffect(() => {
    if (cot) {
      multiAgentData?.emit('onYieldResult')
    }
  }, [cot, multiAgentData])

  return (
    <ResultMarkdownWrapper>
      {result && <Markdown md={result} />}
      {extraData?.code === 0 && Array.isArray(extraData?.data?.sourceNodes?.textNodes) && (
        <DocReferenceTextNodeList nodes={extraData?.data?.sourceNodes?.textNodes} />
      )}
    </ResultMarkdownWrapper>
  )
})

export const MultiAgentResultRecord: Record<Nl2agentStatusToolType, React.FC<MultiAgentResultProps>> = {
  brain: MultiAgentResultEarlyStop,
  bi: MultiAgentResultBi,
  judge: MultiAgentResultEarlyStop,
  code: MultiAgentResultEarlyStop,
  early_stop: MultiAgentResultEarlyStop,
  python_code_tool: MultiAgentResultEarlyStop,
  metric_meta: MultiAgentResultEarlyStop,
  metric_attr: MultiAgentResultEarlyStop,
  chat: MultiAgentResultChat,
  fast_lookup: MultiAgentResultEarlyStop,
  match: MultiAgentResultEarlyStop,
  run_tools_after_manual_select: MultiAgentResultEarlyStop,
}

export interface MultiAgentResultProps {
  id: string
  isViewMode?: boolean
}

export const MultiAgentResult = memo(function MultiAgentResultMemo(props: { id: string; isViewMode: boolean }) {
  const { id } = props
  const { chat } = React.useContext(ChatHistoryItemContext)
  const data = findKeyFromDataList(id, getMultiAgentData(chat)?.dataList)
  const Comp = MultiAgentResultRecord[data?.tool_type as Nl2agentStatusToolType] ?? MultiAgentResultEarlyStop
  return <Comp {...props} />
})
