import React from 'react'
import { ChatResponseErrorMap, ChatResponseErrorTypes } from 'src/shared/common-types'
import { jsonTryParse } from 'src/shared/common-utils'
import { Nl2agentStatus, Nl2agentStatusToolType } from 'src/client/hooks/useChatProgress'
import { Chat } from 'src/client/utils'
import { BaseChat, MultiAgentChat } from '../../chats'
import { breadToKey, MultiAgentData } from './agent'

export function getMultiAgentData(chat?: BaseChat) {
  if (!chat) return null
  if (MultiAgentChat.assert(chat)) {
    return chat.data
  }
  return null
}

export function isMultiAgent(chat?: Chat) {
  if (!chat) return false
  const content = chat.ans?.[0]?.content?.[0]
  return content?.type === 'multi-agent'
}

export function hasResultData(data?: Nl2agentStatus, _?: number, _2?: Nl2agentStatus[]) {
  if (!data) return false
  if (data.tool_type === 'brain') return false
  if (data.tool_type === 'fast_lookup' || data.tool_type === 'metric_attr') {
    if (!data.data) return false
    if (
      ChatResponseErrorMap[data.code as keyof typeof ChatResponseErrorMap] ===
        ChatResponseErrorTypes.NOT_NEED_BI_RESULT ||
      ChatResponseErrorMap[data.code as keyof typeof ChatResponseErrorMap] === ChatResponseErrorTypes.NEED_MANUAL_SELECT
    )
      return false
    if (Number(data.code) === 0 && typeof data.data === 'string' && !jsonTryParse(data.data)) {
      return false
    }
    return true
  }
  if (data.tool_type === 'python_code_tool') return false
  if (data.tool_type === 'run_tools_after_manual_select') return false
  if (data.tool_type === 'chat' && data.cot_uuid) return true
  if (data.tool_type === 'judge') return false
  if (data.tool_type === 'bi' && data.nl2agent_build_msg) {
    if (
      ChatResponseErrorMap[data.code as keyof typeof ChatResponseErrorMap] === ChatResponseErrorTypes.NOT_NEED_BI_RESULT
    )
      return false
    return true
  }
  if (
    ChatResponseErrorMap[data.code as keyof typeof ChatResponseErrorMap] === ChatResponseErrorTypes.NOT_NEED_BI_RESULT
  )
    return false
  if (Number(data.code) === 0 && jsonTryParse(data.data ?? '')) return true
  return false
}

export function hasThinkData(data?: Nl2agentStatus, _?: number, _2?: Nl2agentStatus[]) {
  if (!data) return false
  if (data.cot_uuid) return true
  return false
}

export function isFinalInferStatus(inferStatus?: MultiAgentData['inferStatus']) {
  return inferStatus === 'done' || inferStatus === 'disabled-match'
}

export function findKeyFromDataList(key: string, dataList: Nl2agentStatus[] | undefined) {
  return dataList?.find((v) => breadToKey(v.breadcrumbs) === key)
}

export function isSubAgent(data?: Nl2agentStatus) {
  return data?.tool_type !== 'brain'
}

export const EarlyStopToolTypes: Nl2agentStatusToolType[] = ['fast_lookup', 'metric_attr', 'metric_meta', 'bi']

export function isEarlyStop(dataList: Nl2agentStatus[] = []) {
  return (
    dataList?.[0]?.plan?.find((v) => v.name === 'early_stop') &&
    dataList?.[0]?.plan?.find((v) => EarlyStopToolTypes.includes(v.name))
  )
}

export const MultiAgentContext = React.createContext({ isViewMode: false })
