/* eslint-disable @typescript-eslint/naming-convention */
import { markdownTable } from 'markdown-table'
import { formatOrderBy, sortByOrderBy, jsonTryParse } from 'src/shared/common-utils'
import { MetricConfig, Metric } from 'src/shared/metric-types'
import { Nl2agentStatusToolType } from 'src/client/hooks/useChatProgress'
import { AnsChatItem, Chat } from 'src/client/utils'
import { isMetric, Message, OlapRow } from 'src/shared/common-types'
import { getCalculatorContext, calculatorContextToMessage } from '../calculator'
import { getMultiAgentData, hasResultData } from './multi-agent'
import { MultiAgentData, PlanData, breadToKey } from './agent'

export function toMessageContentAnsItemList(
  ansItemList: AnsChatItem[],
  metricConfigRecord: Record<string, MetricConfig>,
) {
  let content = ''
  for (const ansItem of ansItemList) {
    const metricConfig = metricConfigRecord[ansItem.sceneId]
    if (!metricConfig) continue
    if (ansItem.role === 'assistant') {
      for (const contentItem of ansItem.content) {
        if (contentItem.type === 'chat-error' && contentItem.tryQueryToSqlData.ansChatItemList) {
          content = toMessageContentAnsItemList(contentItem.tryQueryToSqlData.ansChatItemList, metricConfigRecord)
        } else if (
          (contentItem.type === 'chart' && contentItem.queryParamsVerified) ||
          (contentItem.type === 'chat-error' && contentItem.queryParamsVerified)
        ) {
          const { queryParamsVerified } = contentItem
          const table: string[][] = []
          if (contentItem.type === 'chart') {
            const { rowsMetadata, rows } = contentItem
            const metrics = rowsMetadata
              .filter((item) => isMetric(item) && item.value.type !== 'periodOverPeriod')
              .map((item) => item.value as Metric)
            const orderByList = formatOrderBy({
              queryParamsVerified: queryParamsVerified,
              defaultValue: {
                metricName: metrics[0].name,
                orderBy: 'desc',
              },
            })
            const sortedData = sortByOrderBy({
              rows: rows as OlapRow[],
              orderByList,
            })
            if (sortedData.length <= 20) {
              table.push(contentItem.rowsMetadata.map((v) => v.value.label))
              table.push(
                ...sortedData.map((v) => contentItem.rowsMetadata.map((meta) => (v as any)[meta.value.name] ?? '')),
              )
              content += markdownTable(table)
            } else {
              table.push(contentItem.rowsMetadata.map((v) => v.value.label))
              table.push(
                ...sortedData
                  .slice(0, 10)
                  .map((v) => contentItem.rowsMetadata.map((meta) => (v as any)[meta.value.name] ?? '')),
              )
              content += markdownTable(table)
              content += '...省略' + (sortedData.length - 20) + '行'
              content += markdownTable(
                sortedData
                  .slice(-10)
                  .map((v) => contentItem.rowsMetadata.map((meta) => (v as any)[meta.value.name] ?? '')),
              )
            }
          }
        } else if (contentItem.type === 'deepseek' && contentItem.data.trigger === 'error') {
          content = contentItem.data.content
        } else if (contentItem.type === 'multi-agent') {
          content += Array.from(contentItem.data.agentMap.values())
            .filter((v) => v.earlyAnsItem)
            .map((v) => toMessageContentAnsItemList(v.earlyAnsItem ?? [], metricConfigRecord))
            .join('\n\n')
        }
      }
    }
  }
  return content
}

export function toMessageContent(chat: Chat, metricConfigRecord: Record<string, MetricConfig>): string {
  return toMessageContentAnsItemList(chat.ans, metricConfigRecord)
}

export function isDeepseekErrorTriggerChatItem(chat?: Chat): boolean {
  const content = chat?.ans.at(0)?.content.at(0)
  return !!(chat && !chat.ask && content && content.type === 'deepseek' && content.data.trigger === 'error')
}

function defaultToolToMessageRecord() {
  return ''
}

export const toolToMessageRecord: Record<
  Nl2agentStatusToolType,
  ({
    metricConfigRecord,
    multiAgentData,
    planData,
  }: {
    multiAgentData: MultiAgentData
    planData: PlanData
    metricConfigRecord: Record<string, MetricConfig>
  }) => string
> = {
  brain: defaultToolToMessageRecord,
  bi: ({ planData, metricConfigRecord }) => {
    const ctx = getCalculatorContext(planData)
    return calculatorContextToMessage(ctx, metricConfigRecord)
  },
  judge: defaultToolToMessageRecord,
  code: defaultToolToMessageRecord,
  early_stop: defaultToolToMessageRecord,
  python_code_tool: defaultToolToMessageRecord,
  metric_meta: defaultToolToMessageRecord,
  metric_attr: defaultToolToMessageRecord,
  chat: ({ planData }) => planData.cot,
  fast_lookup: ({ planData, metricConfigRecord }) => {
    if (typeof planData.data?.data === 'string' && !jsonTryParse(planData.data.data)) {
      return planData.data.data
    }
    if (planData.earlyAnsItem) {
      return toMessageContentAnsItemList(planData.earlyAnsItem, metricConfigRecord)
    }
    return ''
  },
  match: defaultToolToMessageRecord,
  run_tools_after_manual_select: defaultToolToMessageRecord,
}

export function multiAgentDataToMessage(
  multiAgentData: MultiAgentData,
  metricConfigRecord: Record<string, MetricConfig>,
): string {
  const resultList = multiAgentData.dataList.filter(hasResultData)
  return resultList
    .filter((data) => data.tool_type)
    .map((data) => {
      const planData = multiAgentData.agentMap.get(breadToKey(data.breadcrumbs))
      return [
        '----------',
        `工具: ${data.tool_type!}`,
        toolToMessageRecord[data.tool_type!]?.({ multiAgentData, planData, metricConfigRecord }),
      ].join('\n')
    })
    .join('\n\n\n\n')
}

export function toMessages({
  chats,
  metricConfigRecord,
  newMessage,
  parentId,
}: {
  newMessage?: string
  chats: Chat[]
  parentId?: string
  metricConfigRecord: Record<string, MetricConfig>
}): Message[] {
  const chatRecord = Object.fromEntries(chats.map((chat) => [chat.id, chat]))
  const messages: Message[] = []
  const enableFollowUp = !!parentId
  const followUpChatSet = new Set<string>()
  while (parentId) {
    followUpChatSet.add(parentId)
    parentId = chatRecord[parentId].ask.parentId!
  }
  for (let i = 0; i < chats.length; i++) {
    const chat = chats[i]
    const multiAgentData = getMultiAgentData(chat as any)!
    if (!multiAgentData) continue
    if (enableFollowUp && !followUpChatSet.has(chat.id)) continue
    if (chat.ask && chat.ask.role === 'user') {
      messages.push({
        content: chat.ask.content,
        role: 'user',
      })

      messages.push({
        role: 'assistant',
        content: multiAgentDataToMessage(multiAgentData, metricConfigRecord),
      })
      // const lastItem = data.dataList.at(-1)
      // if (isEarlyStop(data.dataList)) {
      //   if (lastItem) {
      //     const planData = data.agentMap.get(breadToKey(lastItem.breadcrumbs))
      //     if (planData.earlyAnsItem) {
      //       messages.push({
      //         role: 'assistant',
      //         content: toMessageContentAnsItemList(planData.earlyAnsItem, metricConfig),
      //       })
      //     }
      //   }
      // } else {
      //   if (lastItem && lastItem.tool_type === 'chat') {
      //     messages.push({ role: 'assistant', content: data.agentMap.get(breadToKey(lastItem.breadcrumbs))!.cot })
      //   }
      // }
    }
    // DeepSeek 特判
    // if (isDeepseekErrorTriggerChatItem(chat)) {
    //   messages.pop()
    //   messages.push({ role: 'assistant', content: toMessageContent(chat, metricConfig) })
    // }
  }
  if (newMessage) {
    messages.push({
      role: 'user',
      content: newMessage,
    })
  }
  return messages
}
