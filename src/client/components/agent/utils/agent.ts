/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/naming-convention */
import { AnsChatItem } from 'src/client/utils'
import { ReactBaseModel } from 'src/client/utils/react-base-model'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { MetricConfig, Metric, Dimension } from 'src/shared/metric-types'
import { Nl2agentStatus } from 'src/client/hooks/useChatProgress'
import { parseMatchedData, createMatchedDataHooks, MatchedDataGroups } from '../match'
import { ChatErrorProps } from '../../ChatError/ChatError'

export class DefaultMap<K, V> extends Map<K, V> {
  constructor(private getDefaultData: (key?: K) => V = () => undefined as any) {
    super()
  }
  setDefault(v: DefaultMap<K, V>['getDefaultData']) {
    this.getDefaultData = v
    return this
  }
  get(key: K) {
    if (!super.has(key) && this.getDefaultData) super.set(key, this.getDefaultData(key))
    return super.get(key)!
  }
}

export interface PlanDataHooks {
  onUpdateEarlyAnsItem: (v: PlanData['earlyAnsItem']) => void
  onUpdateCurrentConfidenceSelection: (v: PlanData['currentConfidenceSelection']) => void
}

export type CurrentConfidenceSelection = {
  metricNames: string[]
  where: string
  sceneId: string
}

export class PlanData extends ReactBaseModel<PlanDataHooks> {
  cot = ''
  data: Nl2agentStatus | undefined
  earlyAnsItem?: AnsChatItem[]
  ansItemRecord: Record<string, AnsChatItem[]> = {}
  currentConfidenceSelection?: CurrentConfidenceSelection
}

// py query-metric提参
export type QueryParam = {
  type: 'query-metric' | 'dimension-list' | 'data-overview' | 'dimension-detail' | 'metric-list' | 'metric-detail'
  param?: string
  query_metric: {
    metricNames: string[]
    isMetricNamesExactMatch: boolean
    where: string
    isWhereExactMatch: boolean
    where_json?: {
      dimension_sub_wheres?: {
        dimension_name: string
        // operator: string
        // illegal_dimension_values: string[]
        dimension_values: { dimension_value_name: string; score: number }[]
      }[]
    }
    orderBys: string[]
  }
  extra_info: {
    metric_ner: string[]
    where_ner: string[]
    metric_scores?: Record<string, number>
  }
}

//| 'project_query_metric_attr' | 'project_query_metric_meta'
export interface MatchedDataQueryMetricItem {
  type: 'project_query_metric' | 'query_metric'
  data: Record<string, QueryParam>
}

export interface MatchedDataMetaItem {
  query: string
  type: 'project_query_metric_meta'
  data: Record<string, QueryParam>
}

// 外层string是UUID 内层是sceneId
export type MatchedData = Record<string, MatchedDataQueryMetricItem | MatchedDataMetaItem>

export interface MultiAgentDataHooks {
  onYieldResult: () => void
  onNeedMatch: (data: MatchedData) => void
  onFinally: () => void
  onAbort: () => void
  onError: (data: {
    type: 'match' | 'sensitive-question' | 'canceled' | 'common'
    chatErrorProps?: ChatErrorProps
  }) => void
}

export class MultiAgentData extends ReactBaseModel<MultiAgentDataHooks> {
  static from(o: any, metricConfigRecord: Record<string, MetricConfig>): MultiAgentData {
    // 如果不存在,直接返回一个空的实例
    const multiAgentData = new MultiAgentData()
    if (!o) return multiAgentData
    try {
      const data = multiAgentData
        .set('inferStatus', o.inferStatus)
        .set('dataList', o.dataList)
        .set('matchedData', o.matchedData)
        .set('hasMatch', o.hasMatch)
      if (o.matchedData && o.hasMatch) {
        const newMatchedDataGroup = parseMatchedData({
          matchedData: o.matchedData,
          metricConfigRecord,
          hooks: createMatchedDataHooks({
            isBaowu: isBaoWu(Object.values(metricConfigRecord).map((v) => v.metricTableName)),
          }),
        })
        for (const group of Object.values(newMatchedDataGroup)) {
          group.display = true
          for (const opt of group.options) {
            opt.check = true
          }
        }
        data.set('matchedDataGroups', newMatchedDataGroup)
      }
      for (const key of Object.keys(o.agentMap)) {
        data.agentMap
          .get(key)
          .set('cot', o.agentMap[key].cot)
          .set('earlyAnsItem', o.agentMap[key].earlyAnsItem)
          .set(
            'data',
            data.dataList.find((v) => breadToKey(v.breadcrumbs)),
          )
      }
      return data
    } catch (e) {
      return multiAgentData
    }
  }
  inferStatus: 'inferring' | 'done' | 'waiting-match' | 'confirmed-match' | 'disabled-match' = 'inferring'
  dataList: Nl2agentStatus[] = []
  agentMap = new DefaultMap<string, PlanData>(() => new PlanData())
  matchedData?: MatchedData
  hasMatch = false
  matchedDataGroups?: MatchedDataGroups
  toPlain() {
    return {
      inferStatus: this.inferStatus,
      dataList: this.dataList,
      matchedData: this.matchedData,
      hasMatch: this.hasMatch,
      agentMap: Array.from(this.agentMap.entries()).reduce(
        (o, [key, data]) => {
          o[key] = {
            cot: data.cot,
            earlyAnsItem: data.earlyAnsItem,
          }
          return o
        },
        {} as Record<string, any>,
      ),
    }
  }
  toJSON() {
    return JSON.stringify(this.toPlain())
  }
}

export interface MatchedDataGroupOption {
  value: string
  label: string
  check: boolean
  order: number
  list: QueryParam[]
  display: boolean
  metric?: Metric
  dimension?: Dimension
}

export type MatchedDataGroup = {
  key: string
  label: string
  options: MatchedDataGroupOption[]
  display: boolean
  maxSelectedNum: number
  minSelectedNum: number
  currentSelectedNum: number
  type: 'query-metric:metric' | 'query-metric:dimension' | 'meta'
}

// export function stringifyMatchedDataGroups(groups: MatchedDataGroups) {}

export function breadToKey(bread: string[] = []) {
  return bread.join('#')
}
