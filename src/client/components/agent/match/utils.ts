/* eslint-disable @typescript-eslint/naming-convention */
import * as tapable from 'tapable'
import { Dimension, MetricConfig } from '@shared/metric-types'
import { conditionsToWherePart, isDocMetric, parseWhereConditions } from '@shared/match-utils'
import { ParsedCondition } from '@shared/types/match-types'
import { DefaultMap, MatchedData, MatchedDataGroup, MatchedDataQueryMetricItem, QueryParam } from '../utils/agent'

/**
 * 提取指标组
 */
function extractMetricGroup({
  data,
  metricConfig,
  queryParam,
  hooks,
}: {
  data: MatchedDataGroups
  metricConfig: MetricConfig | undefined
  queryParam: QueryParam
  hooks: MatchedDataHooks
}) {
  // 如果精确匹配了，跳过
  if (queryParam.query_metric.isMetricNamesExactMatch) return
  // 获取ner数组
  const ner = queryParam.extra_info?.metric_ner?.sort((a, b) => a.localeCompare(b)) ?? []
  const label = ner.join(',') || '指标'
  const metricNameSet = new Set(queryParam.query_metric.metricNames)
  const scores = queryParam.extra_info.metric_scores ?? {}
  const key = label
  const options =
    metricConfig?.allMetrics
      .filter((v) => metricNameSet.has(v.name))
      .map((v) => ({
        label: v.label,
        value: v.name,
        check: false,
        display: true,
        order: scores[v.name] ?? 0,
        metric: v,
        list: [],
      })) ?? []
  if (!data[key]) {
    data[key] = {
      key,
      label,
      options: [],
      display: true,
      type: 'query-metric:metric',
      maxSelectedNum: Math.max(ner.length, 1),
      currentSelectedNum: 0,
      minSelectedNum: 1,
    }
    hooks.afterCreateMatchedDataGroup.call(data, key, data[key])
  }

  // 遍历所有的options
  // 只有不存在相同的option时，插入一条新的option
  // 在list里插入当前queryParam
  const currentOptionRecord = Object.fromEntries(data[key].options.map((opt) => [opt.value, opt]))
  for (const opt of options) {
    let exist = currentOptionRecord[opt.value]
    if (!exist) {
      data[key].options.push((exist = currentOptionRecord[opt.label] = opt))
    }
    exist.list.push(queryParam)
  }
}

/**
 * 提取维度组
 */
function extractDimensionGroup({
  data,
  metricConfig,
  queryParam,
  hooks,
}: {
  data: MatchedDataGroups
  metricConfig: MetricConfig | undefined
  queryParam: QueryParam
  hooks: MatchedDataHooks
}) {
  if (queryParam.query_metric.isWhereExactMatch) return
  const ner = queryParam.extra_info?.where_ner?.sort((a, b) => a.localeCompare(b)) ?? []
  const label = ner.join(',') || '维度'
  const allConditions = hooks.afterParseWhereConditions.call(
    parseWhereConditions(
      hooks.beforeParseWhereConditions.call(queryParam.query_metric.where, queryParam),
      metricConfig,
    ),
    queryParam,
    metricConfig,
  )
  for (const condition of allConditions) {
    const key = label
    const scores =
      queryParam.query_metric.where_json?.dimension_sub_wheres
        ?.find((v) => v.dimension_name === condition.value.name)
        ?.dimension_values.reduce(
          (o, { dimension_value_name, score }) => {
            o[dimension_value_name] = score
            return o
          },
          {} as Record<string, number>,
        ) ?? {}
    const dimension = metricConfig?.allDimensions.find((v) => v.name === condition.value.name)
    const options = condition.value.codeValues.map((v) => ({
      label: v,
      value: v,
      check: false,
      display: true,
      order: scores[v] ?? 0,
      dimension,
      list: [],
    }))
    if (!data[key]) {
      data[key] = {
        key,
        label,
        options: [],
        display: true,
        type: 'query-metric:dimension',
        maxSelectedNum: Number.MAX_SAFE_INTEGER,
        currentSelectedNum: 0,
        minSelectedNum: 1,
      }
      hooks.afterCreateMatchedDataGroup.call(data, key, data[key])
    }

    const currentOptionRecord = Object.fromEntries(data[key].options.map((opt) => [opt.value, opt]))
    for (const opt of options) {
      let exist = currentOptionRecord[opt.value]
      if (!exist) {
        data[key].options.push((exist = currentOptionRecord[opt.label] = opt))
      }
      exist.list.push(queryParam)
    }
  }
}

/**
 * 从查数类型中，提取组信息
 */
function parseMatchedDataQueryMetric({
  item,
  data,
  metricConfigRecord,
  hooks,
}: {
  metricConfigRecord: Record<string, MetricConfig>
  item: MatchedDataQueryMetricItem
  data: MatchedDataGroups
  hooks: MatchedDataHooks
}) {
  for (const [sceneId, queryParam] of Object.entries(item.data)) {
    extractMetricGroup({
      data,
      metricConfig: metricConfigRecord[sceneId],
      queryParam,
      hooks,
    })
    extractDimensionGroup({
      data,
      metricConfig: metricConfigRecord[sceneId],
      queryParam,
      hooks,
    })
  }
}

/**
 * 把后端的数据MatchedData转换成MatchedDataGroups
 * 利用引用关系，选择完后直接修改MatchedData数据，反馈给后端
 */
export function parseMatchedData({
  matchedData,
  metricConfigRecord,
  hooks = createMatchedDataHooks(),
}: {
  matchedData?: MatchedData
  metricConfigRecord: Record<string, MetricConfig>
  hooks?: MatchedDataHooks
}) {
  const data: MatchedDataGroups = {}
  for (const [_, item] of Object.entries(matchedData ?? {})) {
    switch (item.type) {
      case 'project_query_metric':
      case 'query_metric':
        parseMatchedDataQueryMetric({ item, data, metricConfigRecord, hooks })
        break
      // 废弃
      case 'project_query_metric_meta':
        break
      default:
        break
    }
  }
  // 如果可选项只有一个，那么不展示
  for (const group of Object.values(data)) {
    group.options = group.options.sort((a, b) => b.order - a.order)
    // 如果可选项数量小于等于1 不展示组
    if (group.options.length <= 1) {
      group.display = false
    }
    // 如果可选项数量等于1，可选项默认选中
    if (group.options.length === 1) {
      group.options[0].check = true
      group.currentSelectedNum = 1
    }
    // 存在分数为1的option
    if (group.options.some((opt) => opt.order === 1)) {
      const orderList = group.options.filter((opt) => opt.order === 1)
      if (orderList.length === 1) {
        // 如果可选项数量大于1，但是存在1个分数为1的，不展示组
        group.display = false
        orderList[0].check = true
        group.currentSelectedNum = 1
      } else {
        // 如果存在多个分数为1的opt，则其他分数不为1的不展示
        const set = new Set(orderList)
        for (const opt of group.options) {
          if (!set.has(opt)) {
            opt.display = false
          }
        }
      }
    }
  }

  return data
}

/**
 * 从选择的指标中，提取要展示的维度
 */
export function extractDimensionGroups(matchedDataGroups: MatchedDataGroups, enableDisplay = true) {
  // 获取所有的指标组
  const metricAllGroups = Object.values(matchedDataGroups).filter((v) => {
    if (enableDisplay && !v.display) return false
    return v.type === 'query-metric:metric'
  })
  // 如果没有可以展示的指标组，则要展示所有维度组
  if (metricAllGroups.length === 0)
    return Object.values(matchedDataGroups).filter((v) => {
      if (enableDisplay && !v.display) return false
      return v.type === 'query-metric:dimension'
    })
  const metricGroups = metricAllGroups.filter((v) => v.options.some((v) => v.check))
  // 如果有可展示的指标，但是没有指标被选择
  // 那就不能选择维度组
  if (metricGroups.length === 0) return []
  const dimensionGroups = new Set<MatchedDataGroup>()
  // 从可以选择的组中获取所有queryParams
  const selectedQueryParamSet = new Set(
    metricGroups
      .flatMap((v) => v.options)
      .filter((v) => v.check)
      .flatMap((v) => v.list),
  )
  // 遍历所有的维度组，获取每个queryParam对应的维度组
  const dimensionMap = new Map<QueryParam, MatchedDataGroup>(
    Object.values(matchedDataGroups)
      .filter((v) => v.type === 'query-metric:dimension' && ((enableDisplay && v.display) || !enableDisplay))
      .flatMap((group) => group.options.flatMap((opt) => opt.list.map((q) => [q, group]))),
  )
  for (const group of metricGroups) {
    for (const opt of group.options) {
      for (const q of opt.list) {
        const g = dimensionMap.get(q)
        if (g) {
          dimensionGroups.add(g)
        }
      }
    }
  }
  // 遍历每个option，是否可展示
  // 如果原来展示了，现在不展示，则减少选中的数量
  for (const g of dimensionGroups) {
    for (const opt of g.options) {
      opt.display = opt.list.some((q) => selectedQueryParamSet.has(q))
      if (!opt.display && opt.check) {
        opt.check = false
        g.currentSelectedNum -= 1
      }
    }
  }
  return Array.from(dimensionGroups)
}

export type MatchedDataGroups = Record<string, MatchedDataGroup>

/**
 * 更新原有的MatchedData中的queryParam
 */
export function updateMatchedData({
  matchedDataGroups,
}: {
  matchedData?: MatchedData
  matchedDataGroups: MatchedDataGroups
  metricConfigRecord: Record<string, MetricConfig>
}) {
  // 记录每一个queryParam需要做哪些改造
  // 因为引用关系的保持，直接改对象里的值即可
  const map = new DefaultMap<
    QueryParam,
    { biMetric: string[] | null; docMetric: string[] | null; dimension: DefaultMap<Dimension, ParsedCondition> | null }
  >(() => ({
    biMetric: null,
    docMetric: null,
    dimension: null,
  }))
  for (const item of Object.values(matchedDataGroups).filter((v) => v.type === 'query-metric:metric')) {
    let selectedOptions = item.options.filter((v) => v.check)
    // 如果当前组中没有选中过的，就当作全部选中
    if (selectedOptions.length === 0) {
      selectedOptions = item.options
      item.options.forEach((v) => (v.check = true))
    }
    for (const opt of selectedOptions) {
      for (const q of opt.list) {
        const data = map.get(q)
        if (isDocMetric(opt.metric)) {
          data.biMetric ??= []
          data.biMetric!.push(opt.value)
        } else if (!isDocMetric(opt.metric)) {
          data.docMetric ??= []
          data.docMetric!.push(opt.value)
        }
      }
    }
  }
  // 提取选中的维度
  for (const item of extractDimensionGroups(matchedDataGroups, false)) {
    let selectedOptions = item.options.filter((v) => v.check)
    if (selectedOptions.length === 0) {
      selectedOptions = item.options
      item.options.forEach((v) => (v.check = true))
    }
    for (const opt of selectedOptions) {
      for (const q of opt.list) {
        const dim = opt.dimension
        if (!dim) continue
        const data = map.get(q)
        data.dimension ??= new DefaultMap(() => ({
          type: 'dimension',
          value: { name: dim.name, label: dim.label, codeValues: [] },
        }))
        const cond = data.dimension.get(dim)
        cond.value.codeValues.push(opt.value)
      }
    }
  }
  for (const [queryParam, { biMetric, docMetric, dimension }] of map.entries()) {
    // 如果doc和bi指标都存在，则当作只有bi指标
    if (docMetric?.length && !biMetric?.length) {
      queryParam.query_metric.metricNames = docMetric
    } else if (biMetric?.length) {
      queryParam.query_metric.metricNames = biMetric
    }
    if (dimension) queryParam.query_metric.where = conditionsToWherePart(Array.from(dimension.values()))
  }

  // if (matchedData) {
  //   for (const uuid of Object.keys(matchedData)) {
  //     for (const sceneId of Object.keys(matchedData[uuid].data)) {
  //       const q = matchedData[uuid].data[sceneId]
  //       if (q.query_metric.isMetricNamesExactMatch && q.query_metric.isWhereExactMatch) continue
  //       // 如果没有精确匹配，而且没有改造，则说明用户没有选中它
  //       // 直接删除，不需要后续查询了
  //       if (!map.has(q)) {
  //         delete matchedData[uuid].data[sceneId]
  //       }
  //     }
  //     if (Object.keys(matchedData[uuid].data).length === 0) {
  //       delete matchedData[uuid]
  //     }
  //   }
  // }
}

export function createMatchedDataHooks({ isBaowu = false }: { isBaowu?: boolean } = {}) {
  const hooks = Object.freeze({
    afterCreateMatchedDataGroup: new tapable.SyncHook<[MatchedDataGroups, string, MatchedDataGroup]>([
      'matchedDataGroups',
      'key',
      'matchedDataGroup',
    ]),
    beforeParseWhereConditions: new tapable.SyncWaterfallHook<[string, QueryParam]>(['where', 'queryParam']),
    afterParseWhereConditions: new tapable.SyncWaterfallHook<[ParsedCondition[], QueryParam, MetricConfig | undefined]>(
      ['conditions', 'queryParam', 'metricConfig'],
    ),
  })
  if (isBaowu) {
    hooks.beforeParseWhereConditions.tap('处理未精准匹配但where为空串的问题', (where, queryParam) => {
      if (!queryParam.query_metric.isWhereExactMatch && where === '') {
        return `COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')`
      }
      return where
    })
  }
  return hooks
}

export type MatchedDataHooks = ReturnType<typeof createMatchedDataHooks>

export function skipMatch(groups: MatchedDataGroups) {
  if (Object.values(groups).every((group) => !group.display)) {
    console.info('没有可以展示的组')
    return true
  }
  return false
}

export function getMatchedDataFromResponse(resp: any): MatchedData | null {
  return resp?.data?.data?.[0]?.['manual_selects'] ?? null
}
