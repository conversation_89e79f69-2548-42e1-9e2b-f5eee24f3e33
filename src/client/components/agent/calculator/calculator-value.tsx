/* eslint-disable react-hooks/exhaustive-deps */
import React, { ReactNode } from 'react'
import { useAtomValue } from 'jotai'
import { metricConfigRecordAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { PlanNode } from 'src/client/hooks/useChatProgress'
import { formatNumber } from 'src/shared/common-utils'
import { MetricConfig } from 'src/shared/metric-types'
import { CalculatorNode, isPercentage } from './utils'

export function getCalculatorValue({
  data,
  node,
  metricConfigRecord,
}: {
  data: PlanNode
  node: CalculatorNode
  metricConfigRecord: Record<string, MetricConfig>
}) {
  const sceneId = Array.from(Object.keys(data?.meta?.query_metric_result ?? {}))[0]
  const queryMetricResult = data?.meta?.query_metric_result?.[sceneId]?.query_metric

  function pickValueFromSchema() {
    const val = data.data?.table[0]?.[data.data?.schema?.at(-1)?.column_name ?? '']
    return val
  }

  function pickValueFromLookupData() {
    if (!queryMetricResult) return
    const allMetrics = metricConfigRecord?.[sceneId]?.allMetrics || []
    const val =
      data.data?.table[0]?.[allMetrics.find((v) => v.name === queryMetricResult?.metricNames?.[0])?.label ?? '']
    return val
  }

  const val = node.name === 'lookup_data' ? pickValueFromLookupData() : pickValueFromSchema()
  return formatNumber(val, '.2f' + (isPercentage(node.description) ? '%' : ''))
}

export function CalculatorValue({ data, node }: { data: PlanNode; node: CalculatorNode }) {
  const metricConfigRecord = useAtomValue(metricConfigRecordAtom)
  const val = getCalculatorValue({ node, data, metricConfigRecord })
  function wrapper(node: ReactNode) {
    return <div className="text-[28px] font-[700] leading-[28px] text-[#0F172A]">{node}</div>
  }
  if (val === undefined) return wrapper('计算错误')
  return wrapper(val)
}
