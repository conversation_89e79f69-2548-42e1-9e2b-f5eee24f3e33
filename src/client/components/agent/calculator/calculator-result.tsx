/* eslint-disable react/prop-types */
import React, { useContext } from 'react'
import { ConfigProvider } from 'antd'
import { PlanNode, PlanNodeFailed } from 'src/client/hooks/useChatProgress'
import { ChatResponseError } from 'src/shared/common-types'
import { unreadyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
import { useChatError } from '../../ChatError/ChatError'
import { ChatHistoryItemContext } from '../../ChatHistory/ChatHistoryItemContext'
import { CalculatorContext, CalculatorNode } from './utils'
import { CalculatorTable } from './calculator-table'
import { CalculatorSingularData } from './calculator-singular-data'
import { CalculatorMultipleData } from './calculator-multiple-data'
import { CalculatorValue } from './calculator-value'
import { CalculatorLookup } from './calculator-lookup'

function CalculatorResultMultipleStepSingularData(props: React.ComponentProps<typeof CalculatorResult>) {
  const leafNode = props.list.at(-1)!
  const data = leafNode.data as PlanNode
  return (
    <div className="flex flex-col gap-[8px] rounded-[7px] bg-[#F4EFF9] px-[20px] pb-[12px] pt-[16px]">
      <div className="text-[14px] font-[500] leading-[24px] text-[#0F172A]">{leafNode.description}</div>
      <CalculatorValue node={leafNode} data={data} />
      <CalculatorResultComputeMode node={leafNode} />
    </div>
  )
}

function CalculatorResultMultipleStepMultipleData(props: React.ComponentProps<typeof CalculatorResult>) {
  const leafNode = props.list.at(-1)!
  const data = leafNode.data as PlanNode

  return (
    <>
      <div className="flex flex-col gap-[8px] rounded-[7px] bg-[#F4EFF9] px-[20px] pb-[12px] pt-[16px]">
        <div className="text-[14px] font-[500] leading-[24px] text-[#0F172A]">{leafNode.description}</div>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                headerBg: '#ffffff',
              },
            },
          }}
        >
          <CalculatorTable data={data.data} node={leafNode} />
        </ConfigProvider>
        <CalculatorResultComputeMode node={leafNode} />
      </div>
    </>
  )
}

function CalculatorResultMultipleStepLoading(props: { node: CalculatorNode }) {
  const { node } = props
  const { chatProgress } = useContext(ChatHistoryItemContext)
  const chatErrorData = useChatError({
    content:
      node.type === 2 && (node.data as PlanNodeFailed).metric_query_resp
        ? unreadyResponseToChatAns(
            (node.data as PlanNodeFailed).metric_query_resp as ChatResponseError,
            (node.data as PlanNodeFailed).metric_query_resp?.sceneId ?? '',
          ).content[0]
        : undefined,
    multiAgentMode: true,
  })
  const expressionResult = chatErrorData?.title ?? (chatProgress?.getLatestData()?.close ? '子结果计算失败' : '加载中')
  return (
    <div className="flex flex-col gap-[8px] rounded-[7px] bg-[#F4EFF9] px-[20px] pb-[12px] pt-[16px]">
      <div className="text-[14px] font-[500] leading-[24px] text-[#0F172A]">{props.node.description}</div>
      <div className="text-[24px] font-[700] leading-[24px] text-[#0F172A]">{expressionResult}</div>
      <CalculatorResultComputeMode node={props.node} />
    </div>
  )
}

function CalculatorResultSingularStepLoading({ node }: { node: CalculatorNode }) {
  const { chatProgress } = useContext(ChatHistoryItemContext)
  const chatErrorData = useChatError({
    content:
      node.type === 2 && (node.data as PlanNodeFailed).metric_query_resp
        ? unreadyResponseToChatAns(
            (node.data as PlanNodeFailed).metric_query_resp as ChatResponseError,
            (node.data as PlanNodeFailed).metric_query_resp?.sceneId ?? '',
          ).content[0]
        : undefined,
    multiAgentMode: true,
  })
  const expressionResult = chatErrorData?.title ?? (chatProgress?.getLatestData()?.close ? '子结果计算失败' : '加载中')
  return (
    <div className="flex flex-col gap-[8px]">
      <div className="text-[14px] font-[500] leading-[24px] text-[#0F172A]">{node.description}</div>
      <div className="text-[24px] font-[700] leading-[24px] text-[#0F172A]">{expressionResult}</div>
    </div>
  )
}

function CalculatorResultComputeMode(props: { node: CalculatorNode }) {
  const ctx = useContext(CalculatorContext)
  const graphs = ctx?.graphs
  const { node } = props
  if (
    node.type !== 1 ||
    !node.data ||
    !(node.data as PlanNode).tool_kw_params?.mapping ||
    !(node.data as PlanNode).tool_kw_params?.expression
  )
    return null
  const data = node.data as PlanNode
  const { mapping, expression } = data.tool_kw_params!
  return (
    <div>
      <div className="text-[12px] font-[400] text-[#575757]">结果计算方式：</div>
      <div className="text-[12px] font-[400] text-[#575757]">
        {Object.keys(mapping).reduceRight((str, key) => {
          const node = graphs?.[key]
          if (!node) return str
          return str.replaceAll(key, node.description)
        }, expression)}
      </div>
    </div>
  )
}

export function CalculatorResult(props: { list: CalculatorNode[]; id: string }) {
  const { list, id } = props
  const leafNode = list.at(-1)!
  let renderNode: React.ReactNode = null
  if (leafNode.type === 2 && leafNode.data && leafNode.name === 'lookup_data') {
    renderNode = (
      <CalculatorLookup id={id} node={leafNode} response={(leafNode.data as PlanNodeFailed).metric_query_resp} />
    )
  } else if (
    !leafNode.data ||
    leafNode.type === 2 ||
    leafNode.type === 3 ||
    (leafNode.type === 1 && !(leafNode.data as PlanNode).data)
  ) {
    if (list.length === 1) {
      renderNode = <CalculatorResultSingularStepLoading node={leafNode} />
    } else {
      renderNode = <CalculatorResultMultipleStepLoading node={leafNode} />
    }
  } else {
    const data = leafNode.data as PlanNode
    if (data.meta?.['metric-query-response']) {
      renderNode = <CalculatorLookup node={leafNode} id={id} response={data.meta?.['metric-query-response']} />
    } else if (list.length === 1) {
      if (data.data.table.length === 1) {
        renderNode = <CalculatorSingularData node={leafNode} />
      } else {
        renderNode = <CalculatorMultipleData node={leafNode} />
      }
    } else {
      if (data.data.table.length === 1) {
        renderNode = <CalculatorResultMultipleStepSingularData list={list} id={id} />
      } else {
        renderNode = <CalculatorResultMultipleStepMultipleData list={list} id={id} />
      }
    }
  }
  return renderNode
}
