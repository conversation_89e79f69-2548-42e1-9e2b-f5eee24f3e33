import { TableProps, Table } from 'antd'
import { nanoid } from 'nanoid'
import React from 'react'
import { PlanNode } from 'src/client/hooks/useChatProgress'
import { formatNumber } from 'src/shared/common-utils'
import { CalculatorNode, isPercentage } from './utils'

export function CalculatorTable({ data }: { data?: PlanNode['data']; node: CalculatorNode }) {
  if (!data) return null
  const columns: TableProps['columns'] = data.schema.map((v, i) => {
    return {
      title: v.column_name,
      dataIndex: v.column_name,
      ellipsis: true,
      key: i,
      width: 200,
      render(val) {
        if (typeof val !== 'number') return val
        return formatNumber(val, '.2f' + (isPercentage(v.column_name) ? '%' : ''))
      },
    }
  })

  return (
    <Table
      bordered
      dataSource={data.table}
      columns={columns}
      rowKey={() => nanoid()}
      size="small"
      pagination={{ hideOnSinglePage: true, className: 'mb-0' }}
      scroll={{ x: 'max-content', y: 200 }}
    />
  )
}
