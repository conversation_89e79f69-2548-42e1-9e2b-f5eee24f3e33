/* eslint-disable no-unsafe-optional-chaining */
import { useUpdate } from 'ahooks'
import React, { memo, useContext, useMemo } from 'react'
import { readyResponseToChatAns, unreadyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
import BiResultContent from '../../ChatHistory/BiResultContent'
import { useBiResultContentProps } from '../hooks'
import { MultiAgentContext } from '../utils'
import { CalculatorContext, CalculatorNode } from './utils'

export const CalculatorLookup = memo(
  function CalculatorLookupMemo(props: {
    id: string
    node: CalculatorNode
    getDesc?: (defaultDesc: string) => string
    response: any
  }) {
    const { node, getDesc = (v) => v, id, response } = props
    const update = useUpdate()
    const { isViewMode } = useContext(MultiAgentContext)
    const ctx = useContext(CalculatorContext)!
    useMemo(() => {
      if (response) {
        const ansItem = response.ready
          ? readyResponseToChatAns(response, response.sceneId)
          : unreadyResponseToChatAns(response, response.sceneId)
        ctx.planData.ansItemRecord[id] = [ansItem]
        // console.log('ANS', ansItem)
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
    const ansItem = ctx.planData.ansItemRecord[id]?.[0]
    const mainChart = ansItem?.content.find((obj) => obj.type === 'chart')
    const biResultContentProps = useBiResultContentProps()
    if (!ansItem) return null
    const biResultContentPropsExt: React.ComponentProps<typeof BiResultContent> = {
      ...biResultContentProps,
      chatAnsItem: ansItem,
      showActionBar: true,
      showFeedback: false,
      showSqlButton: ansItem.content.some(({ type }) => type === 'sql'), // !isBaoWu(metricConfig?.metricTableName)
      showSwitchChartButton: Boolean(mainChart?.recommendChartTypes?.length),
      showCharInsightButton: ansItem.content.some(({ type }) => type === 'chart-insight'),
      showMainChartButtons: Boolean(mainChart),
      currentSelectedSceneId: ansItem.sceneId,
      isViewMode,
      getAnsItem: () => ctx.planData.ansItemRecord[id],
      setAnsItem: (v) => {
        ctx.planData.ansItemRecord[id] = v
        update()
      },
    }
    const desc = getDesc(node.description)
    return (
      <div className="calc-item-lookup-data">
        <div className="flex flex-col gap-[8px]">
          {desc && <div className="text-[14px] font-[500] leading-[24px] text-[#0F172A]">{desc}</div>}
          <BiResultContent {...biResultContentPropsExt} />
        </div>
      </div>
    )
  },
  // 只要组件被渲染，受外界影响
  () => true,
)
