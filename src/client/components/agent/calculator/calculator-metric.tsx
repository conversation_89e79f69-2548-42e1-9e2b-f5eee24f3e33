/* eslint-disable react-hooks/exhaustive-deps */
import { useAtomValue } from 'jotai'
import React, { useMemo } from 'react'
import { PlanNode } from 'src/client/hooks/useChatProgress'
import { metricConfigRecordAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { MetricConfig } from 'src/shared/metric-types'

export function getCalculatorMetric({
  data,
  metricConfigRecord,
}: {
  data: PlanNode
  metricConfigRecord: Record<string, MetricConfig>
}) {
  const sceneId = Array.from(Object.keys(data?.meta?.query_metric_result ?? {}))[0]
  const queryMetricResult = data?.meta?.query_metric_result?.[sceneId]?.query_metric
  const metricNames = queryMetricResult?.metricNames ?? []
  const metric = metricConfigRecord[sceneId]?.allMetrics.find((v) => metricNames.includes(v.name))
  return metric
}

export function CalculatorMetric({ data }: { data: PlanNode }) {
  const metricConfigRecord = useAtomValue(metricConfigRecordAtom)
  const metric = useMemo(() => getCalculatorMetric({ data, metricConfigRecord }), [metricConfigRecord?.allMetrics])
  if (!metric) return null
  return <div className="text-[20px] font-[500] leading-[20px] text-[#575757]">{metric.label}</div>
}
