/* eslint-disable no-console */
import { cloneDeep } from 'lodash'
import React from 'react'
import { markdownTable } from 'markdown-table'
import { Nl2agentStatus, PlanNode, PlanNodeFailed } from 'src/client/hooks/useChatProgress'
import { ReactBaseModel } from 'src/client/utils'
import { readyResponseToChatAns, unreadyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
import { MetricConfig } from 'src/shared/metric-types'
import { getThrottleLog, jsonTryParse } from 'src/shared/common-utils'
import { MatchedDataGroups } from '../match'
import { PlanData } from '../utils/agent'
import { toMessageContentAnsItemList } from '../utils'
import { getCalculatorMetric } from './calculator-metric'
import { getCalculatorValue } from './calculator-value'
import { getCalculatorTime } from './calculator-time'
import { getCalculatorGroupBy } from './calculator-groupby'

/* eslint-disable @typescript-eslint/naming-convention */
export type ToolType =
  | 'lookup_data'
  | 'select'
  | 'filter'
  | 'join'
  | 'orderby'
  | 'auto_join'
  | 'calculator'
  | 'time_range'
  | 'time_series_service'
  | 'table_tools'

export interface Nl2agentBuildMsg {
  steps: {
    child_node_names: string[]
    parent_node_names: string[]
    description: string
    name: ToolType
    param_key: string
  }[]
}

export type CalculatorNodeRecord = Record<string, CalculatorNode>

export function toCalculatorNodeRecord(
  steps: Nl2agentBuildMsg['steps'],
  dataRecord: Nl2agentStatus['nl2agent_steps_list'],
  nl2agentDeltaBuilds: Nl2agentStatus['nl2agent_delta_builds'],
): CalculatorNodeRecord {
  const record: CalculatorNodeRecord = {}
  for (const step of steps) {
    record[step.param_key] = new CalculatorNode()
      .set('description', step.description)
      .set('key', step.param_key)
      .set('name', step.name)
    const node = record[step.param_key]
    const data = dataRecord?.[step.param_key]
    if (data) {
      node.set('type', data[0]).set('data', data[1])
    }
    if (step.name === 'table_tools' && nl2agentDeltaBuilds && nl2agentDeltaBuilds[step.param_key]) {
      const [type, _, data] = nl2agentDeltaBuilds[step.param_key]
      if (type === 1 && data) {
        const deltaRecord = toCalculatorNodeRecord(data, dataRecord, nl2agentDeltaBuilds)
        node.set('deltaRecord', deltaRecord)
        const deltaLeaf = deltaRecord[data.at(-1)?.param_key]
        if (deltaLeaf && deltaLeaf.type && deltaLeaf.data) {
          node.set('type', deltaLeaf.type).set('data', deltaLeaf.data)
        }
      }
    }
  }
  for (const step of steps) {
    for (const key of step.child_node_names) {
      record[step.param_key].children.push(record[key])
    }
    for (const key of step.parent_node_names) {
      record[step.param_key].parents.push(record[key])
    }
  }
  return record
}

export function toCalculatorLists(graph: CalculatorNodeRecord, steps: Nl2agentBuildMsg['steps']): CalculatorNode[][] {
  const res: CalculatorNode[][] = []
  function getNodeSet(node: CalculatorNode) {
    const used = new Set<CalculatorNode>()
    const queue = [node]
    while (queue.length) {
      const node = queue.pop()!
      used.add(node)
      for (const parent of node.parents) {
        if (!used.has(parent)) {
          queue.push(parent)
          used.add(parent)
        }
      }
    }
    return used
  }

  // 先不用了，但是函数先保留注释，防止后面nl2agentBuildMsg改动
  // function topologicalSorted(node: CalculatorNode, nodeSet: Set<CalculatorNode>): CalculatorNode[] {
  //   const sorted: CalculatorNode[] = [node]
  //   const queue = [node]
  //   const used = new Set<CalculatorNode>([node])
  //   while (queue.length) {
  //     const node = queue.shift()!
  //     for (const parent of node.parents) {
  //       if (!used.has(parent) && parent.children.filter((v) => nodeSet.has(v)).every((v) => used.has(v))) {
  //         queue.push(parent)
  //         sorted.push(parent)
  //         used.add(parent)
  //       }
  //     }
  //   }
  //   return sorted.reverse()
  // }

  for (const node of Object.values(graph)) {
    if (node.children.length !== 0) continue
    const nodeSet = getNodeSet(node)
    const arr = Array.from(nodeSet).sort(
      (n1, n2) => steps.findIndex((v) => v.param_key === n1.key) - steps.findIndex((v) => v.param_key === n2.key),
    )
    res.push(arr)
    // res.push(topologicalSorted(node, nodeSet))
  }
  res.sort((list1, list2) => {
    const n1 = list1.at(-1)!
    const n2 = list2.at(-1)!
    return steps.findIndex((v) => v.param_key === n1.key) - steps.findIndex((v) => v.param_key === n2.key)
  })
  // const ansItemRecord: Record<string, Record<string, AnsChatItem[]>> = {}
  //   for (let i = 0; i < res.length; i++) {
  //     for (let j = 0; j < res[i].length; j++) {
  // const id = `${i}#${j}`
  //       ansItemRecord[res[i][j].key] ??= {}
  //       ansItemRecord[res[i][j].key][id] =
  //     }
  //   }
  return res
}

export class CalculatorNode extends ReactBaseModel {
  parents: CalculatorNode[] = []
  children: CalculatorNode[] = []
  description: string = ''
  name: ToolType = 'calculator'
  key: string = ''
  type?: 1 | 2 | 3
  data?: string | PlanNodeFailed | PlanNode | MatchedDataGroups
  deltaRecord?: CalculatorNodeRecord
}

export function getStepList(list?: Nl2agentStatus['nl2agent_steps_list']): Nl2agentStatus['nl2agent_steps_list'] {
  if (!list) return list
  try {
    const cloned = cloneDeep(list)
    for (const key of Object.keys(cloned)) {
      const [type, content] = cloned[key]
      switch (type) {
        case 3:
        case 2:
          cloned[key][1] = jsonTryParse(content as any) ?? String(content)
          break
        case 1:
          if (content?.meta?.query_metric_result) {
            const record: any = content?.meta?.query_metric_result
            for (const key of Object.keys(record)) {
              record[key] = JSON.parse(record[key])
            }
          }
          break
        default:
          break
      }
    }
    return cloned
  } catch (_) {
    //
  }
}

export const PERCENTAGE_STR = ['占比', '份额', '同比', '环比']

export function isPercentage(str: string) {
  return PERCENTAGE_STR.some((v) => str.includes(v))
}

const throttleLog = getThrottleLog()

export function getCalculatorContext(planData: PlanData) {
  try {
    const nl2AgentBuildMsgStr = planData.data?.nl2agent_build_msg
    if (!nl2AgentBuildMsgStr) return null
    const nl2AgentBuildMsg: Nl2agentBuildMsg = JSON.parse(nl2AgentBuildMsgStr)
    const stepList = getStepList(planData.data?.nl2agent_steps_list)
    const nl2agentDeltaBuilds = planData.data?.nl2agent_delta_builds
    const graphs = toCalculatorNodeRecord(nl2AgentBuildMsg.steps, stepList, nl2agentDeltaBuilds)
    const lists = toCalculatorLists(graphs, nl2AgentBuildMsg.steps)
    throttleLog('getCalculatorContext', {
      nl2AgentBuildMsg,
      stepList,
      nl2agentDeltaBuilds,
      graphs,
      lists,
    })
    return { nl2AgentBuildMsg, stepList, graphs, lists, planData }
  } catch (_) {
    return null
  }
}

export const CalculatorContext = React.createContext<ReturnType<typeof getCalculatorContext>>(null)

export function calculatorContextToMessage(
  ctx: ReturnType<typeof getCalculatorContext>,
  metricConfigRecord: Record<string, MetricConfig>,
): string {
  if (!ctx) return ''
  return ctx.lists
    .map((list) => {
      const last = list.at(-1)!
      if (last.type === 1) {
        const planNode = last.data as PlanNode
        if (last.name === 'lookup_data' && planNode.meta?.['metric-query-response']) {
          const resp = (last.data as PlanNode)?.meta?.['metric-query-response']
          const ansItem = resp.ready
            ? readyResponseToChatAns(resp, resp.sceneId)
            : unreadyResponseToChatAns(resp, resp.sceneId)
          return toMessageContentAnsItemList([ansItem], metricConfigRecord)
        } else if (planNode.data.table.length > 1) {
          const { schema, table } = planNode.data
          let content = ''
          if (table.length <= 20) {
            content += markdownTable([
              schema.map((v) => v.column_name),
              ...table.map((item) => schema.map((v) => item[v.column_name])),
            ])
          } else {
            content += markdownTable([
              schema.map((v) => v.column_name),
              ...table.slice(0, 10).map((item) => schema.map((v) => item[v.column_name])),
            ])
            content += `\n...省略${table.length - 20}行\n`
            content += markdownTable([
              schema.map((v) => v.column_name),
              ...table.slice(-10).map((item) => schema.map((v) => item[v.column_name])),
            ])
          }
          const time = getCalculatorTime({ data: planNode })
          const groupBy = getCalculatorGroupBy({ data: planNode, metricConfigRecord })
          const sceneId = Array.from(Object.keys(planNode?.meta?.query_metric_result ?? {}))[0]
          const where = planNode?.meta?.query_metric_result?.[sceneId]?.query_metric?.where
          return [
            last.description,
            content,
            time && `查询时间: ${time}`,
            groupBy && `聚合方式: ${groupBy}`,
            where && `where: ${where}`,
          ]
            .filter(Boolean)
            .join('\n\n')
        } else {
          const metric = getCalculatorMetric({ data: planNode, metricConfigRecord })
          const value = getCalculatorValue({ data: planNode, node: last, metricConfigRecord })
          const time = getCalculatorTime({ data: planNode })
          const groupBy = getCalculatorGroupBy({ data: planNode, metricConfigRecord })
          const sceneId = Array.from(Object.keys(planNode?.meta?.query_metric_result ?? {}))[0]
          const where = planNode?.meta?.query_metric_result?.[sceneId]?.query_metric?.where
          return [
            metric && `指标: ${metric.label}`,
            value && `${last.description}: ${value}`,
            time && `查询时间: ${time}`,
            groupBy && `聚合方式: ${groupBy}`,
            where && `where: ${where}`,
          ]
            .filter(Boolean)
            .join('\n\n')
        }
      } else if (last.type === 2) {
        const data = last.data as PlanNodeFailed
        if (data.metric_query_resp) {
          const resp = data.metric_query_resp as any
          const ansItem = unreadyResponseToChatAns(resp, resp.sceneId)
          return toMessageContentAnsItemList([ansItem], metricConfigRecord)
        }
        return ''
      }
    })
    .join('\n\n')
}
