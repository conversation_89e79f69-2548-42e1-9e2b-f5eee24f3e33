import { useAtomValue } from 'jotai'
import React from 'react'
import { PlanNode } from 'src/client/hooks/useChatProgress'
import { metricConfigRecordAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { MetricConfig } from 'src/shared/metric-types'

export function getCalculatorGroupBy({
  data,
  metricConfigRecord,
}: {
  data: PlanNode
  metricConfigRecord: Record<string, MetricConfig>
}) {
  const sceneId = Array.from(Object.keys(data?.meta?.query_metric_result ?? {}))[0]
  const queryMetricResult = data?.meta?.query_metric_result?.[sceneId]?.query_metric
  const groupBys = queryMetricResult?.groupBys
  const content = groupBys
    ?.map((name) => {
      const dim = metricConfigRecord[sceneId]?.allDimensions.find((v) => v.name === name)
      if (!dim) return null
      return dim.label
    })
    .filter(Boolean)
    .join(', ')
  return content
}

export function CalculatorGroupBy({ data }: { data: PlanNode }) {
  const metricConfigRecord = useAtomValue(metricConfigRecordAtom)
  const content = getCalculatorGroupBy({ data, metricConfigRecord })
  if (!content) return null
  return <div className="text-[14px] font-[400] text-[#575757]">聚合方式 = {content}</div>
}
