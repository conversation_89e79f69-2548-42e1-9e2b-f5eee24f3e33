/* eslint-disable react/prop-types */
import React, { ReactNode, useContext } from 'react'
import { Divider } from 'antd'
import { PlanNode } from 'src/client/hooks/useChatProgress'
import { ChatHistoryItemContext } from '../../ChatHistory/ChatHistoryItemContext'
import { CalculatorNode } from './utils'
import { CalculatorFailed } from './calculator-failed'
import { CalculatorSingularData } from './calculator-singular-data'
import { CalculatorMultipleData } from './calculator-multiple-data'
import { CalculatorLookup } from './calculator-lookup'

function CalculatorItemLoading(props: React.ComponentProps<typeof CalculatorItem>) {
  const { index, node } = props
  const { chatProgress } = useContext(ChatHistoryItemContext)
  return (
    <div className="calc-item-lookup-data">
      <div className="mx-[16px] text-[14px] font-[400] text-[#202020]">
        {index + 1}.{node.description}
      </div>
      <div className="mt-[8px] flex flex-col gap-[4px] px-[20px]">
        <div className="text-[14px] font-[500] text-[#575757]">
          {chatProgress?.getLatestData()?.close ? '加载失败' : node.type === 3 ? '等待置信度选择' : '加载中'}
        </div>
      </div>
    </div>
  )
}

export function CalculatorItem(props: { list: CalculatorNode[]; node: CalculatorNode; index: number; id: string }) {
  const { node, index, id } = props
  let renderNode: ReactNode = null
  if (!node.data || node.type === 3) {
    renderNode = <CalculatorItemLoading {...props} />
  } else if (node.type === 2) {
    renderNode = <CalculatorFailed node={node} />
  } else {
    const data = node.data as PlanNode
    if (data.meta?.['metric-query-response']) {
      renderNode = (
        <CalculatorLookup
          node={node}
          getDesc={(v) => `${index + 1}.${v}`}
          id={id}
          response={data.meta['metric-query-response']}
        />
      )
    } else if (data.data.table.length === 1) {
      renderNode = <CalculatorSingularData node={node} getDesc={(v) => `${index + 1}.${v}`} />
    } else {
      renderNode = <CalculatorMultipleData node={node} getDesc={(v) => `${index + 1}.${v}`} />
    }
  }
  return (
    <>
      {index !== 0 && <Divider dashed className="my-[0px]" />}
      {renderNode}
    </>
  )
}
