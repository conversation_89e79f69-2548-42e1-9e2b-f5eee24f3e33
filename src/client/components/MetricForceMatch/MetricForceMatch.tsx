/* eslint-disable no-console */
import React, { useMemo, useState } from 'react'
import { useAtomValue, useSetAtom } from 'jotai/react'
import { App, Divider } from 'antd'
import { produce } from 'immer'
import axios from 'axios'
import clsx from 'clsx'
import { MetricConfig, QueryParamsVerified, TimeQueryParams } from '@shared/metric-types'
import { askBIApiUrls } from '@shared/url-map'
import { isBaoWu } from '@shared/baowu-share-utils'
import { MetricOption, ParsedCondition } from '@shared/types/match-types'
import { conditionsToWherePart, parseWhereConditions } from '@shared/match-utils'
import { AnsChatItem } from 'src/client/utils'
import { chatsAtom, isProjectChosenAtom, metricConfigOfProjectAtom } from '../../pages/AskBI/askBIAtoms'
import { useAllMetrics } from '../../hooks/useAllMetrics'
import { useAllExternalReports } from '../../hooks/useAllExternalReports'
import { PlanData } from '../agent'
import { MetricList } from './components/MetricList'
import { ExternalReportList } from './components/ExternalReportList'
import { DimensionValueList } from './components/DimensionValueList'

// 暂时删除查询公司有没有数据的逻辑 enableSelectToastWhenEmptyData https://gitlab.dipeak.com/dipeak/ask-bi/-/merge_requests/3035
type CustomContent = {
  metricNames: string[]
  queryParamsVerified: QueryParamsVerified
}

interface MetricForceMatchProps {
  metricConfig: MetricConfig | null
  onQueryParamsChange: (newValue: QueryParamsVerified, selectedSceneId: string) => void
  isViewMode: boolean
  chatId: string
  onClose?: () => void
  onSubmit?: () => void
  currentConfidenceSelection?: {
    // 若存在currentConfidenceSelection，则仅允许选择维度，不可选择指标或外部报表
    sceneId: string
    metricNames: string[]
    where: string
  } | null
  contentIndex: number
  contentLength: number
  planData?: PlanData
}

const MetricForceMatch = ({
  metricConfig,
  onQueryParamsChange,
  isViewMode,
  chatId,
  onClose,
  onSubmit,
  currentConfidenceSelection,
  planData,
}: MetricForceMatchProps) => {
  const { message } = App.useApp()
  const isProjectChosen = useAtomValue(isProjectChosenAtom)
  const metricConfigOfProject = useAtomValue(metricConfigOfProjectAtom)
  const setChats = useSetAtom(chatsAtom)
  const allMetrics = useAllMetrics()
  const allExternalReports = useAllExternalReports()

  const chats = useAtomValue(chatsAtom)

  const currentAllChatAns = useMemo(() => {
    return planData?.earlyAnsItem || []
  }, [planData?.earlyAnsItem])

  const currentChat = useMemo(() => {
    return chatId ? chats.find((item) => item.id === chatId) : chats[0]
  }, [chats, chatId])

  const defaultQueryParamsVerified = (currentAllChatAns?.[0]?.content[0] as CustomContent).queryParamsVerified

  console.group('MetricForceMatch Chat Debug Info')
  console.log('currentChat', currentChat)
  console.log('currentAllChatAns', currentAllChatAns)
  console.log('defaultQueryParamsVerified', defaultQueryParamsVerified)
  console.groupEnd()

  const [currentSelectedSceneId, setCurrentSelectedSceneId] = useState(currentAllChatAns?.[0]?.sceneId ?? '')

  const innerQueryParamsVerified = useMemo(() => {
    if (!isProjectChosen) {
      return defaultQueryParamsVerified
    }

    if (!currentSelectedSceneId) {
      return defaultQueryParamsVerified
    }

    const selectedScene = currentAllChatAns.find((item) => item.sceneId === currentSelectedSceneId)
    const customContent = selectedScene?.content.find((item) => 'queryParamsVerified' in item) as
      | CustomContent
      | undefined

    return customContent?.queryParamsVerified ?? defaultQueryParamsVerified
  }, [defaultQueryParamsVerified, isProjectChosen, currentSelectedSceneId, currentAllChatAns])

  const { queryParams: { timeQueryParams } = {} } = innerQueryParamsVerified

  // 选中的 where条件 & metric名 & externalReport名
  const selectedMetricNames =
    currentConfidenceSelection?.metricNames?.filter((name) => allMetrics.some((metric) => metric.name === name)) || []

  const selectedExternalReports =
    currentConfidenceSelection?.metricNames?.filter((name) =>
      allExternalReports.some((report) => report.name === name),
    ) || []

  // 新的where 在查数的时候 根据 selectedCondition + otherCondition => conditionsToWherePart => where
  const [selectedCondition, setSelectedCondition] = useState<ParsedCondition[]>([])
  const [selectedMetric, setSelectedMetric] = useState<string[]>(selectedMetricNames)
  const [selectedExternalReport, setSelectedExternalReport] = useState<string[]>(selectedExternalReports)

  // loading状态
  const [isQueryMetric, setIsQueryMetric] = useState(false)

  // 提取会话中所有场景的指标及外部报表, 并对每个指标id关联场景id
  const { metricNames, externalReportNames } = useMemo(() => {
    const extractOptions = (items: string[], sceneId: string) =>
      items.map((item) => ({ name: item, sceneId, label: '' }))

    if (isProjectChosen) {
      return currentAllChatAns.reduce(
        (result, ans) => {
          const queryParamsVerified = (ans.content[0] as CustomContent)?.queryParamsVerified
          const ansMetricNames = queryParamsVerified?.originalQueryParams?.metricNames ?? []
          const ansExternalReportNames = queryParamsVerified?.originalQueryParams?.externalReportNames ?? []

          result.metricNames.push(...extractOptions(ansMetricNames, ans.sceneId))
          result.externalReportNames.push(...extractOptions(ansExternalReportNames, ans.sceneId))
          return result
        },
        { metricNames: [], externalReportNames: [] } as {
          metricNames: MetricOption[]
          externalReportNames: MetricOption[]
        },
      )
    } else {
      const ansMetricNames = extractOptions(
        defaultQueryParamsVerified?.originalQueryParams?.metricNames ?? [],
        currentSelectedSceneId,
      )
      const ansExternalReportNames = extractOptions(
        defaultQueryParamsVerified?.originalQueryParams?.externalReportNames ?? [],
        currentSelectedSceneId,
      )
      return { metricNames: ansMetricNames, externalReportNames: ansExternalReportNames }
    }
  }, [isProjectChosen, defaultQueryParamsVerified, currentAllChatAns, currentSelectedSceneId])

  if (!innerQueryParamsVerified) {
    return null
  }

  const externalReports = externalReportNames.map((item) => {
    const result = item
    allExternalReports.forEach((item) => {
      if (item.name === result.name) {
        result.label = item.label
      }
    })
    return result
  })

  const currentMetricConfig = isProjectChosen
    ? metricConfigOfProject?.find((e) => e.sceneId === currentSelectedSceneId)?.data
    : metricConfig

  const {
    isMetricNamesExactMatch: defaultMetricNamesExactMatch,
    isWhereExactMatch,
    where,
  } = innerQueryParamsVerified?.originalQueryParams ?? {}

  // 多场景下 返回多个匹配的场景时,默认开启指标置信度选择
  const isMetricNamesExactMatch = currentAllChatAns.length > 1 ? false : defaultMetricNamesExactMatch

  const allConditions: ParsedCondition[] = parseWhereConditions(where, currentMetricConfig)
  const isMultiCodeDimension = (c: ParsedCondition) => c.type === 'dimension' && c.value.codeValues.length > 1

  const whereConditions = allConditions.filter(isMultiCodeDimension)
  const otherConditions = allConditions.filter((c) => !isMultiCodeDimension(c))

  console.group('MetricForceMatch Debug Info2')

  console.group('Query Params and Scene Info')
  console.log('innerQueryParamsVerified:', innerQueryParamsVerified)
  console.log('isProjectChosen:', isProjectChosen)
  console.log('currentSelectedSceneId:', currentSelectedSceneId)
  console.groupEnd()

  console.group('Metric and Report Info')
  console.log('metricNames:', metricNames)
  console.log('externalReportNames:', externalReportNames)
  console.groupEnd()

  console.group('Conditions Info')
  console.log('allConditions:', allConditions)
  console.log('whereConditions:', whereConditions)
  console.log('otherConditions:', otherConditions)
  console.log('where:', where)
  console.groupEnd()

  console.group('Metric Configuration')
  console.log('currentMetricConfig:', currentMetricConfig)
  console.groupEnd()

  console.groupEnd()

  // 更新选中的 metricName
  const handleMetricSelect = (metricNameOption: MetricOption) => {
    setCurrentSelectedSceneId(metricNameOption.sceneId)
    // 外部报表和指标互斥
    setSelectedExternalReport([])
    const newSelectedMetric = selectedMetric.includes(metricNameOption.name)
      ? selectedMetric.filter((item) => item !== metricNameOption.name)
      : [metricNameOption.name] // 暂时禁用多选逻辑，现在只有单选 from zhoufanrong
    setSelectedMetric(newSelectedMetric)
  }

  // 更新选中的 condition 的 codevalue
  function handleSelectConditionCodeValue(payload: { codeValue: string; conditionName: string }) {
    setSelectedCondition((prevConditions) => {
      // 从 allConditions 里获取完整的 condition
      const targetCondition = allConditions.find((c) => c.value.name === payload.conditionName)

      // 不存在 本应该直接返回prevConditions 宝武特例加上一个condition
      if (!targetCondition) {
        return [
          ...prevConditions,
          {
            type: 'dimension',
            value: {
              name: payload.conditionName,
              label: payload.conditionName,
              codeValues: [payload.codeValue],
            },
          },
        ]
      }

      // 先查找当前是否已选
      const existingCondition = prevConditions.find((c) => c.value.name === payload.conditionName)

      if (existingCondition) {
        // 更新 codeValues（添加或删除）
        const newCodeValues = existingCondition.value.codeValues.includes(payload.codeValue)
          ? existingCondition.value.codeValues.filter((v) => v !== payload.codeValue) // 删除
          : [...existingCondition.value.codeValues, payload.codeValue] // 添加

        return prevConditions.map((c) =>
          c.value.name === payload.conditionName ? { ...c, value: { ...c.value, codeValues: newCodeValues } } : c,
        )
      }

      // 如果 condition 还没被选中过，则新增并设置 codeValues
      return [
        ...prevConditions,
        { ...targetCondition, value: { ...targetCondition.value, codeValues: [payload.codeValue] } },
      ]
    })
  }

  // 更新选中的 外部报表
  const handleExternalReportsSelect = (reportNameOption: MetricOption) => {
    setCurrentSelectedSceneId(reportNameOption.sceneId)
    // 外部报表和指标互斥
    setSelectedMetric([])
    // 外部报表最多存在一个
    setSelectedExternalReport((prevSelected) => {
      if (prevSelected[0] === reportNameOption.name) {
        return []
      }
      return [reportNameOption.name]
    })
  }

  // 执行查数
  const handleQueryMetric = () => {
    if (selectedMetric.length > 0 && selectedExternalReport.length > 0) {
      message.warning('不能同时选择指标和报表')
      return
    }
    console.info('selectedCondition', selectedCondition)
    setIsQueryMetric(true)
    // 宝武定制 如果是查同一家公司的数据 && 是复选 =>  按照  ['管理合并', '资产合并', '法人'] 顺序 取第一个
    const whereForBackend =
      selectedCondition.length > 0 ? conditionsToWherePart([...selectedCondition, ...otherConditions]) : where || ''
    const metricNamesForBackend =
      selectedExternalReport.length > 0 || metricNames.length === 0
        ? selectedExternalReport.length > 0
          ? selectedExternalReport
          : externalReportNames.map((item) => item.name)
        : selectedMetric.length > 0
          ? selectedMetric
          : metricNames.map((item) => item.name)

    if (selectedExternalReport.length > 0 || metricNames.length === 0) {
      const updatedEarlyAnsItem: AnsChatItem[] = [
        {
          role: 'assistant',
          content: [
            {
              type: 'query-external-report',
              externalReports: metricNamesForBackend,
              where: whereForBackend,
              timeQueryParams: timeQueryParams as TimeQueryParams,
              queryParamsVerified: {
                ...innerQueryParamsVerified,
                queryParams: {
                  ...innerQueryParamsVerified.queryParams,
                  where: whereForBackend,
                  externalReportNames: metricNamesForBackend,
                },
              },
            },
          ],
          sceneId: currentSelectedSceneId,
          status: 'success',
          ansTime: new Date(),
          traceId: '',
        },
      ]
      planData?.emit('onUpdateEarlyAnsItem', updatedEarlyAnsItem)
    } else {
      const newMetricNames = metricNamesForBackend
      const orderBys = innerQueryParamsVerified.originalQueryParams.orderBys
      const newOrderBys = orderBys ? [...orderBys] : orderBys
      if (newOrderBys?.length === 1 && newMetricNames?.length === 1) {
        newOrderBys[0] = newOrderBys[0]
          .split(/\s+/)
          .map((item, index) => {
            if (index === 0) {
              return newMetricNames[0]
            }
            return item
          })
          .join(' ')
      }
      const params = {
        ...innerQueryParamsVerified,
        queryParams: {
          ...innerQueryParamsVerified.queryParams,
          where: whereForBackend,
          metricNames: newMetricNames,
          orderBys: newOrderBys,
        },
      }
      console.info('(whereForBackend, newMetricNames, newOrderBys)', whereForBackend, newMetricNames, newOrderBys)
      onQueryParamsChange(params, currentSelectedSceneId)

      setChats(
        produce((drafts) => {
          const deepSeekIdx = drafts.findIndex((v) =>
            v.ans.find((v) => v.content.find((v) => v.type === 'deepseek' && v.relativeChatId === chatId)),
          )
          if (deepSeekIdx > -1) {
            drafts.splice(deepSeekIdx, 1)
          }
        }),
      )
    }

    // 更新chat的llmResponse 配合python追问
    axios.post(askBIApiUrls.converChats.updateChat, {
      chatId,
      sceneId: currentSelectedSceneId,
      where: whereForBackend,
      metricNames: metricNamesForBackend,
    })

    onSubmit && onSubmit()
  }

  const renderBottomButtons = (
    <div className="flex justify-end gap-3">
      <div
        className="flex w-1/2 cursor-pointer items-center justify-center rounded-3xl border border-[#D5D5D5] px-4 py-2"
        onClick={() => {
          setSelectedCondition([])
          setSelectedMetric([])
          onClose && onClose()
        }}
      >
        取消选择
      </div>
      <div
        className="flex w-1/2 cursor-pointer items-center justify-center rounded-3xl bg-primary px-4 py-2 text-white"
        onClick={handleQueryMetric}
      >
        我选好了
      </div>
    </div>
  )

  if (isQueryMetric) {
    return <div>查询中...</div>
  }

  const showDimensionValuesList =
    (isWhereExactMatch === false || !isWhereExactMatch) &&
    (isMetricNamesExactMatch || selectedMetric.length > 0 || selectedExternalReport.length > 0)
  console.log(
    'showDimensionValuesList',
    showDimensionValuesList,
    whereConditions,
    isBaoWu(currentMetricConfig?.metricTableName),
  )

  const defaultConditionName = 'COMPANY_INNER_CODE_DESC'
  const defaultCodeValue = '中国宝武钢铁集团有限公司-资产合并'
  const defaultCodeValueComp = isBaoWu(currentMetricConfig?.metricTableName) ? (
    <div>
      <span className="text-base">您要找的是不是该公司的数据？</span>
      <div className="mt-4 flex flex-wrap gap-3">
        <div
          className={clsx('cursor-pointer rounded-lg border border-[#BBB] px-3 py-1.5 text-base font-medium', {
            'border-[#AFC4F5] bg-[#AFC4F5]': selectedCondition
              .find((i) => i.value.name === defaultConditionName)
              ?.value.codeValues.includes(defaultCodeValue),
          })}
          onClick={() =>
            handleSelectConditionCodeValue({ conditionName: defaultConditionName, codeValue: defaultCodeValue })
          }
          key={'mock-id-metric-force-match'}
        >
          {defaultCodeValue}
        </div>
      </div>
      <Divider className="my-3" />
    </div>
  ) : null

  return (
    <div className="metric-force-match-container">
      {!currentConfidenceSelection && isMetricNamesExactMatch === false && metricNames.length > 0 && (
        <div className="metric-force-match-metric-list">
          <span className="text-base">我找到以下相关的指标项，请您选择</span>
          <MetricList
            chatId={chatId}
            metrics={metricNames}
            selectedMetrics={selectedMetric}
            allMetrics={allMetrics}
            onSelect={handleMetricSelect}
          />
          <Divider className="my-3" />
        </div>
      )}
      {!currentConfidenceSelection && isMetricNamesExactMatch === false && externalReports.length > 0 && (
        <div className="metric-force-match-external-report-list">
          <span className="text-base">我找到以下相关的外部报表项，请您选择</span>
          <ExternalReportList
            chatId={chatId}
            reports={externalReports}
            selectedReports={selectedExternalReport}
            onSelect={handleExternalReportsSelect}
          />
          <Divider className="my-3" />
        </div>
      )}
      {/* 宝武定制逻辑 【非】只有一家公司的不同帐套数据 或 是置信度复选 的时候，展示维度选择 未加上 */}
      {showDimensionValuesList &&
        (whereConditions.length > 0 ? (
          <>
            {whereConditions.map((condition) => (
              <DimensionValueList
                key={condition.value.name}
                chatId={chatId}
                condition={condition}
                selectedConditions={selectedCondition}
                onSelect={handleSelectConditionCodeValue}
                showCanMultiSelectText={
                  !(selectedExternalReport.length > 0 || (metricNames.length === 0 && externalReports.length > 0))
                }
              />
            ))}
            <Divider className="my-3" />
          </>
        ) : allConditions.length === 0 ? (
          // 宝武定制逻辑 如果没有匹配到帐套数据 默认展示 defaultCodeValueComp
          defaultCodeValueComp
        ) : null)}

      {/* 在非查看模式里使用时才显示 */}
      {!isViewMode &&
        (selectedCondition.length > 0 || selectedMetric.length > 0 || selectedExternalReport.length > 0) &&
        renderBottomButtons}
    </div>
  )
}

export default MetricForceMatch
