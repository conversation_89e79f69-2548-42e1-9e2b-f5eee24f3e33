import { DatasetDatum } from 'src/shared/common-types'
import { ProjectType, SceneType } from 'src/shared/metric-types'

export function extractDatasetInfo({
  semanticProjectInfo,
  projectId,
  sceneId,
}: {
  semanticProjectInfo: ProjectType[]
  projectId: string
  sceneId: string
}): DatasetDatum | null {
  const projectInfo = semanticProjectInfo.find((p) => p.id === projectId) as ProjectType
  const scene = projectInfo?.scenes?.find((s) => s.id === sceneId) as SceneType

  return {
    projectId,
    sceneId,
    projectName: projectInfo.name,
    sceneLabel: scene?.label,
    tableName: scene?.tableName,
    enableFollowUpQuestion: scene?.enableFollowUpQuestion,
    enableMetricExactMatch: scene?.enableMetricExactMatch,
    enableTryQueryUp: scene?.enableTryQueryUp,
    enableAccMetricToastWhenEmptyData: scene?.enableAccMetricToastWhenEmptyData,
    enableAutoHideThink: scene?.enableAutoHideThink,
    hintNum: scene?.hintNum,
    hintDenseWeight: scene?.hintDenseWeight,
    hintSparseWeight: scene?.hintSparseWeight,
    recommendQuestions: scene?.recommendQuestions,
  }
}
