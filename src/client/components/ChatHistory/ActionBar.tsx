import { InteractionOutlined, ConsoleSqlOutlined } from '@ant-design/icons'
import { ArrowUpOnSquareIcon } from '@heroicons/react/24/outline'
import { Dropdown, Popconfirm, Input } from 'antd'
import clsx from 'clsx'
import React, { useEffect, useState } from 'react'
import { useAtomValue, useSet<PERSON>tom } from 'jotai'
import Chart<PERSON>opy from 'src/client/charts/ChartCopy'
import ChartDownload from 'src/client/charts/ChartDownload'
import { AssistantChartChatItem, isSecureEnvironment, scrollToBottom } from 'src/client/utils'
import { currentFollowUpQuestionNextAtom, isDianxinAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { DISPLAY_INSIGHT, IS_H5 } from 'src/shared/constants'
import { ChartType } from 'src/shared/common-types'
import { getVisibleChartIconMap } from 'src/client/charts/Card'
import { getMultiAgentData, isFinalInferStatus, MultiAgentDataHooks } from '../agent'
import ChatDataErrorFeedback from '../ChatDataErrorFeedback'
import { SvgIcon, H5ActionBarTableIcon, H5ActionBarSQLIcon, followUpAskIcon } from '../SvgIcon'
import IconButtonGroup from '../IconButtonGroup'
import { ChatHistoryItemContext } from './ChatHistoryItemContext'

export function ActionBar(props: {
  onChartTypeChange: (value: ChartType) => void
  isViewMode: boolean
  showActionBar: boolean
  showSwitchChartButton: boolean
  showSqlButton: boolean
  showFeedback: boolean
  showFollowUpButton: boolean
  showCharInsightButton: boolean
  showMainChartButtons: boolean
  showSql: boolean
  mainChart: AssistantChartChatItem | undefined
  handleSQL: () => void
  isProjectNameDisabled: boolean
  handleChartInsight: () => void
  handleExportReport: () => Promise<void>
  handleCopyPNG: () => void
  handleDownloadPNG: () => void
  chartTitle: string
  setChartTitle: React.Dispatch<React.SetStateAction<string>>
  handleClickEmbed: () => Promise<void>
}) {
  const isDianxin = useAtomValue(isDianxinAtom)
  const { chat: currentChat } = React.useContext(ChatHistoryItemContext)
  const multiAgentData = getMultiAgentData(currentChat)
  const [visible, setVisible] = useState(isFinalInferStatus(multiAgentData?.inferStatus))
  useEffect(() => {
    if (!multiAgentData) return
    const finallyListener: MultiAgentDataHooks['onFinally'] = () => {
      setVisible(true)
    }
    multiAgentData.on('onFinally', finallyListener)
    return () => {
      multiAgentData.removeListener('onFinally', finallyListener)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [multiAgentData])
  const {
    isViewMode,
    showActionBar,
    handleCopyPNG,
    handleDownloadPNG,
    chartTitle,
    setChartTitle,
    handleClickEmbed,
    showCharInsightButton,
    showMainChartButtons,
    isProjectNameDisabled,
    handleSQL,
    showSqlButton,
    showFeedback,
    showSwitchChartButton,
    mainChart,
    onChartTypeChange,
    showSql,
    showFollowUpButton,
  } = props

  const setCurrentFollowUpQuestion = useSetAtom(currentFollowUpQuestionNextAtom)

  const iconStyle = 'h-4 w-4'
  const commonStyle =
    'flex cursor-pointer items-center rounded-md px-2 py-2 ring-1 ring-inset ring-gray-300 hover:bg-primary hover:text-white'

  const renderSwitchChartDropdown = () => {
    if (!mainChart) return
    return (
      <IconButtonGroup<ChartType>
        options={getVisibleChartIconMap(mainChart?.recommendChartTypes)}
        value={mainChart?.chartType}
        onChange={onChartTypeChange}
      />
    )
  }
  const h5ActionBarVisible = showSwitchChartButton || showSqlButton || showFeedback
  const getH5ActionBar = () => (
    <div className="flex w-full flex-nowrap items-center gap-4 py-2">
      {showSwitchChartButton && (
        <Dropdown placement="top" dropdownRender={renderSwitchChartDropdown}>
          <div>
            <SvgIcon icon={H5ActionBarTableIcon} className="h-6 w-6" />
          </div>
        </Dropdown>
      )}
      {showSqlButton && (
        <div className={clsx('text-[#344054]', { 'bg-[#DDD9F2]': showSql })} onClick={handleSQL}>
          <SvgIcon icon={H5ActionBarSQLIcon} className="h-6 w-6" />
        </div>
      )}
      <div className="flex-1" />
      {showFeedback && <ChatDataErrorFeedback chatId={currentChat.id} />}
    </div>
  )

  const pcActionBarVisible =
    showSwitchChartButton ||
    showSqlButton ||
    (DISPLAY_INSIGHT && !isProjectNameDisabled && showCharInsightButton) ||
    showFeedback

  const getPCActionBar = () => (
    <div className="flex w-full flex-nowrap items-center gap-2">
      {/* 目前仅电信 */}
      {showFollowUpButton && isDianxin && (
        <div
          className={commonStyle}
          onClick={() => {
            setCurrentFollowUpQuestion(currentChat)
            setTimeout(scrollToBottom, 100)
          }}
        >
          <SvgIcon icon={followUpAskIcon} className="mr-0.5 h-4 w-4" />
          跟进提问
        </div>
      )}
      {showSwitchChartButton && (
        <Dropdown placement="top" dropdownRender={renderSwitchChartDropdown}>
          <div className={commonStyle}>
            <InteractionOutlined className={iconStyle} />
            切换图表
          </div>
        </Dropdown>
      )}
      {showSqlButton && (
        <div className={commonStyle} onClick={handleSQL}>
          <ConsoleSqlOutlined className={iconStyle} />
          SQL
        </div>
      )}
      {/* 这里的数据解读 重命名为[分析报告] */}
      {/* {DISPLAY_INSIGHT && !isProjectNameDisabled && showCharInsightButton && (
        <>
          <div className={commonStyle} onClick={handleChartInsight}>
            <ReadOutlined className={iconStyle} />
            分析报告
          </div>
          <div className={commonStyle} onClick={handleExportReport}>
            <SvgIcon icon={downloadReportIcon} className="h-4 w-4" />
            下载报告
          </div>
        </>
      )} */}
      {mainChart && showMainChartButtons && (
        <>
          {isSecureEnvironment() && (
            <ChartCopy
              chartType={mainChart.chartType}
              tableData={mainChart.rows}
              rowsMetadata={mainChart.rowsMetadata}
              onCopyPNG={handleCopyPNG}
            />
          )}
          <ChartDownload onDownloadPNG={handleDownloadPNG} chartType={mainChart.chartType} tableData={mainChart.rows} />
          {mainChart.chartType !== 'AttrAnalysis' && mainChart.chartType !== 'AttrMetricAnalysis' && (
            <Popconfirm
              title="确认图表标题"
              description={
                <Input
                  value={chartTitle}
                  onChange={(e) => {
                    setChartTitle(e.target.value)
                  }}
                />
              }
              onConfirm={handleClickEmbed}
              okText="确认"
              cancelText="取消"
            >
              <div className={commonStyle}>
                <ArrowUpOnSquareIcon className={iconStyle} />
                保存图表
              </div>
            </Popconfirm>
          )}
        </>
      )}
      <div className="flex-1" />
      {showFeedback && <ChatDataErrorFeedback chatId={currentChat.id} />}
    </div>
  )
  if (!visible) return null
  if (isViewMode || !showActionBar) return null
  if (!(IS_H5 ? h5ActionBarVisible : pcActionBarVisible)) return null
  return (
    <div className="operation-list flex w-full items-center justify-between">
      {IS_H5 ? getH5ActionBar() : getPCActionBar()}
    </div>
  )
}
