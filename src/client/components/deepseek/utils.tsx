import { ChatResponseErrorTypes, Message } from 'src/shared/common-types'
import { MetricConfig } from 'src/shared/metric-types'
import { Chat } from 'src/client/utils'
import { toMessageContent as toMessageContentAgent } from '../agent'

export const DEEPSEEK_ERROR_LIST: string[] = [
  ChatResponseErrorTypes.METRICS_NOT_EXIST,
  ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST,
  ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST,
  ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT,
  ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST,
  ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT,
  ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED,
  ChatResponseErrorTypes.CHIT_CHAT,
]

// function toMessageContentAnsItemList(ansItemList: AnsChatItem[], metricConfig: MetricConfigResponse) {
//   let content = `<think>\n\n</think>\n`
//   for (const ansItem of ansItemList) {
//     if (ansItem.role === 'assistant') {
//       for (const contentItem of ansItem.content) {
//         if (contentItem.type === 'chat-error' && contentItem.tryQueryToSqlData.ansChatItemList) {
//           content = toMessageContentAnsItemList(contentItem.tryQueryToSqlData.ansChatItemList, metricConfig)
//         } else if (
//           (contentItem.type === 'chart' && contentItem.queryParamsVerified) ||
//           (contentItem.type === 'chat-error' && contentItem.queryParamsVerified)
//         ) {
//           const { queryParamsVerified } = contentItem
//           const table: string[][] = []
//           if (contentItem.type === 'chart') {
//             const { rowsMetadata, rows } = contentItem
//             const metrics = rowsMetadata
//               .filter((item) => isMetric(item) && item.value.type !== 'periodOverPeriod')
//               .map((item) => item.value as Metric)
//             const orderByList = formatOrderBy({
//               queryParamsVerified: queryParamsVerified,
//               defaultValue: {
//                 metricName: metrics[0].name,
//                 orderBy: 'desc',
//               },
//             })
//             // 对row中的数据进行formatNumber格式化，deepseek回答中，数据格式化过的,和问数结果单位保持一致
//             const finalRows = rows.map((v: any) => {
//               const rowCopy = Object.assign({}, v)
//               for (const meta of rowsMetadata) {
//                 const num = rowCopy[meta.value.name]
//                 if (meta.type === 'metric' && num) {
//                   rowCopy[meta.value.name] = formatNumber(num, meta.value.formatTemplate)
//                 }
//               }
//               return rowCopy
//             })
//             const sortedData = sortByOrderBy({
//               rows: finalRows as OlapRow[],
//               orderByList,
//             })
//             table.push(contentItem.rowsMetadata.map((v) => v.value.label))
//             table.push(
//               ...sortedData.map((v) => contentItem.rowsMetadata.map((meta) => (v as any)[meta.value.name] ?? '')),
//             )
//           }
//           content += `
// ## 提参结果

// - 指标: ${queryParamsVerified.queryParams.metricNames
//             .map((name) => metricConfig.allMetrics.find((v) => v.name === name)?.label)
//             .filter(Boolean)
//             .join(',')}
// - 时间: ${renderRangePicker(queryParamsVerified.queryParams, false)}
// - GroupBY: ${
//             queryParamsVerified.queryParams.groupBys
//               ?.map(
//                 (name) =>
//                   contentItem.type === 'chart' &&
//                   contentItem.rowsMetadata.find((v) => v.value.name === name)?.value.label,
//               )
//               .filter(Boolean)
//               .join(',') ?? ''
//           }
// - Limit: ${queryParamsVerified.queryParams.limit ?? ''}

// ## 查数结果

// ${markdownTable(table)}
// `
//         } else if (contentItem.type === 'deepseek' && contentItem.data.trigger === 'error') {
//           content = contentItem.data.content
//         }
//       }
//     }
//   }
//   return content
// }

export function toMessageContent(chat: Chat, metricConfigRecord: Record<string, MetricConfig>): string {
  return toMessageContentAgent(chat, metricConfigRecord)
}

export function isDeepseekErrorTriggerChatItem(chat?: Chat): boolean {
  const content = chat?.ans.at(0)?.content.at(0)
  return !!(chat && !chat.ask && content && content.type === 'deepseek' && content.data.trigger === 'error')
}

export function loadAllMessages({
  chats,
  relativeIdx = chats.length,
  metricConfigRecord,
}: {
  chats: Chat[]
  relativeIdx?: number
  metricConfigRecord: Record<string, MetricConfig>
}): Message[] {
  const messages: Message[] = []
  for (let i = 0; i < relativeIdx; i++) {
    const chat = chats[i]

    if (chat.ask && chat.ask.role === 'user') {
      messages.push({
        content: chat.ask.content,
        role: 'user',
      })
      messages.push({ role: 'assistant', content: toMessageContent(chat, metricConfigRecord) })
    }
    // DeepSeek 特判
    if (isDeepseekErrorTriggerChatItem(chat)) {
      messages.pop()
      messages.push({ role: 'assistant', content: toMessageContent(chat, metricConfigRecord) })
    }
  }
  if (relativeIdx < chats.length) {
    messages.push({ role: 'user', content: chats[relativeIdx].ask.content })
  }
  return messages
}
