/* eslint-disable no-underscore-dangle */
import React from 'react'
import { ExtractAtomValue } from 'jotai'
import { AnsChatType } from '../utils/constants'
import { chatRecordAtom } from '../atoms'
import { BaseChat } from './base-chat'
import { AskChat } from './ask-chat'

export function AnsChatUI() {
  return <div>AnsChatUI</div>
}

export class AnsChat extends BaseChat {
  static assert(chat: BaseChat): chat is AnsChat {
    return chat instanceof AnsChat
  }
  type = AnsChatType
  askChatId = ''
  askChat: AskChat | null = null
  getAskChat(chatRecord: ExtractAtomValue<typeof chatRecordAtom>) {
    return chatRecord[this.askChatId] as AskChat | undefined
  }
  toPlain() {
    const plain = super.toPlain()
    Object.assign(plain, {
      askChatId: this.askChatId,
      askChatMessage: this.askChat?.requestData.message,
      traceId: this.askChat?.requestData.traceId,
    })
    return plain
  }
  protected fromWithInstance(instance: AnsChat, o: any, _option: Record<string, any> = {}) {
    super.fromWithInstance(instance, o)
    instance.set('askChatId', o.askChatId)
  }
  from(o: any, _option: Record<string, any> = {}): AnsChat {
    const instance = new AnsChat()
    if (o) this.fromWithInstance(instance, o)
    return instance
  }
}
