import { useAtomValue } from 'jotai'
import { But<PERSON> } from 'antd'
import React, { ReactNode } from 'react'
import { brandInfoAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { getQueryState } from 'src/shared/common-utils'
import ChatHistoryItemAskEdit from '../../ChatHistoryItemAskEdit/ChatHistoryItemAskEdit'
import { AskChatType } from '../utils/constants'
import { BaseChat } from './base-chat'

// 此为该回答的后续提问，点击事件
const handleAskForSourceAnswerClick = (id: string | null) => {
  if (id) {
    const targetDiv = document.getElementById(`bi-result-wrapper-${id}`)
    if (targetDiv) {
      targetDiv.classList.add('animate-pulse-border')
      targetDiv.scrollIntoView({ behavior: 'smooth', block: 'center' })
      setTimeout(() => {
        targetDiv.classList.remove('animate-pulse-border')
      }, 2000) // 这里的时间应与动画时间保持一致
    }
  }
}

export function AskChatUI({ chat }: { chat: AskChat }) {
  const brandInfo = useAtomValue(brandInfoAtom)
  const queryState = getQueryState()

  return (
    <div className="chat-item-ask relative flex items-center self-start">
      <img className="user-avatar mr-3.5 hidden h-8 w-8 cursor-pointer md:block" src={brandInfo.chatUserIcon} alt="" />
      <div className="rounded-b-xl rounded-tr-xl bg-primary px-5 py-2 text-white">
        <div className="flex items-center justify-between gap-3">
          <div className="text-base leading-7">{chat.requestData.message}</div>
          <ChatHistoryItemAskEdit chat={chat} />
          {chat.requestData.traceId && queryState.enableLangfuse && (
            <Button
              target="_blank"
              href={`http://192.168.110.25:30007/project/clxske1ve0001lfdcd49wbj8k/sessions/${chat.requestData.traceId}`}
            >
              OpenLangfuse
            </Button>
          )}
        </div>
        {chat.requestData.parentId && (
          <p className="mt-1 text-xs tracking-wider">
            此为
            <span
              className="cursor-pointer px-1 italic tracking-wider underline"
              style={{ fontSynthesis: 'style' }} // 斜体
              onClick={() => {
                handleAskForSourceAnswerClick(chat.requestData.parentId)
              }}
            >
              该回答
            </span>
            的后续提问
          </p>
        )}
      </div>
    </div>
  )
}

export class AskChat extends BaseChat {
  static assert(chat: BaseChat): chat is AskChat {
    return chat instanceof AskChat
  }
  type = AskChatType
  requestData: Record<string, any> = {}
  toReactNode(chat: this): ReactNode {
    return <AskChatUI key={chat.id} chat={chat} />
  }
  toPlain() {
    const plain = super.toPlain()
    Object.assign(plain, { requestData: this.requestData, traceId: this.requestData.traceId })
    return plain
  }
  protected fromWithInstance(instance: AskChat, o: any) {
    super.fromWithInstance(instance, o)
    instance.set('requestData', o.requestData)
  }
  from(o: any, _option: Record<string, any>): AskChat {
    const instance = new AskChat()
    if (o) this.fromWithInstance(instance, o)
    return instance
  }
}
