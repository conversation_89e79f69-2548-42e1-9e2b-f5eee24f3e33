/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/naming-convention */
import axios from 'axios'
import { nanoid } from 'nanoid'
import { ReactNode } from 'react'
import { ReactBaseModel } from 'src/client/utils'
import { Message } from 'src/shared/common-types'
import { MetricConfig } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'

export abstract class BaseChat extends ReactBaseModel {
  static assert(chat: unknown): chat is BaseChat {
    return chat instanceof BaseChat
  }
  abstract type: string
  timeout: NodeJS.Timeout | null = null
  timeoutFunction = () => {}
  id = nanoid()
  timestamp = Date.now()
  conversationId: string | null = null
  protected fromWithInstance(instance: BaseChat, o: any, _option: Record<string, any> = {}) {
    instance.set('type', o.type).set('id', o.id).set('timestamp', o.timestamp).set('conversationId', o.conversationId)
  }
  abstract from(o: any, option?: Record<string, any>): BaseChat | null
  toReactNode(_chat: this, _index: number, _chats: BaseChat[]): ReactNode {
    return null
  }
  toMessages(_data: {
    chats: BaseChat[]
    metricConfigRecord: Record<string, MetricConfig>
    chatRecord: Record<string, BaseChat>
  }): Message[] {
    return []
  }
  toPlain() {
    const plain = {
      type: this.type,
      id: this.id,
      timestamp: this.timestamp,
      conversationId: this.conversationId,
    }
    return plain
  }
  toJSON() {
    return JSON.stringify(this.toPlain())
  }
  async upsert({ ac = new AbortController() }: { ac?: AbortController }) {
    try {
      console.group(`Upsert:${this.type}`)
      console.log(JSON.stringify(this))
      const res = await axios.post(askBIApiUrls.converChats.upsertChat, this.toPlain(), { signal: ac.signal })
      console.log('Data', res.data.data)
      return res.data.data
    } catch (e) {
      console.log('Upsert Error')
      console.error(e)
      return null
    } finally {
      console.groupEnd()
    }
  }
  async upsertDebounce({
    timeout = 1000,
    beforeUpsert,
    ...upsertOption
  }: { timeout?: number; beforeUpsert?: () => void } & Parameters<typeof this.upsert>[0] = {}) {
    if (this.timeout) clearTimeout(this.timeout)
    this.timeoutFunction = () => {
      this.timeout = null
      beforeUpsert?.()
      this.upsert(upsertOption)
    }
    this.timeout = setTimeout(() => this.timeoutFunction(), timeout)
  }
  async upsertThrottle({
    timeout = 1000,
    beforeUpsert,
    ...upsertOption
  }: Parameters<typeof this.upsertDebounce>[0] = {}) {
    if (this.timeout) return
    this.timeoutFunction = () => {
      this.timeout = null
      beforeUpsert?.()
      this.upsert(upsertOption)
    }
    this.timeout = setTimeout(() => this.timeoutFunction(), timeout)
  }
}
