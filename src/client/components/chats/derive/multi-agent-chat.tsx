import React, { useEffect, useRef, useState } from 'react'
import { nanoid } from 'nanoid'
import { useAtomValue } from 'jotai'
import { BlobWithRatio, Message } from 'src/shared/common-types'
import { ChatProgressHooks, useChatProgress } from 'src/client/hooks/useChatProgress'
import { themeAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { MetricConfig } from 'src/shared/metric-types'
import { abortControllerManager, AnsChatItem, ChatStatus } from 'src/client/utils'
import { MULTI_SCENE_CHAT_MOCK_SCENE_ID } from 'src/shared/constants'
import { ChatHistoryItemContext } from 'src/client/components/ChatHistory/ChatHistoryItemContext'
import BiResultWrapper from 'src/client/components/ChatHistory/BiResultWrapper'
import BiResultContent from 'src/client/components/ChatHistory/BiResultContent'
import { MultiAgentData } from '../../agent/utils/agent'
import { multiAgentDataToMessage } from '../../agent'
import { MultiAgentChatType } from '../utils/constants'
import { BaseChat } from './base-chat'
import { AnsChat } from './ans-chat'
import { AskChat } from './ask-chat'

export function MultiAgentChatUI({ chat }: { chat: MultiAgentChat }) {
  const chatProgress = useChatProgress({ taskId: chat.taskId })
  const theme = useAtomValue(themeAtom)

  const chartWrapperRef = useRef<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string>
  }>(null)

  const [ansItem] = useState<AnsChatItem>({
    role: 'assistant',
    content: [
      {
        type: 'multi-agent' as const,
        data: chat.data,
      },
    ],
    status: ChatStatus.success,
    sceneId: MULTI_SCENE_CHAT_MOCK_SCENE_ID,
  })

  useEffect(() => {
    if (!chatProgress) return
    if (chat.data.inferStatus === 'done' || chat.data.inferStatus === 'disabled-match') return
    const updateActiveTab: ChatProgressHooks['onAfterFetch'] = (data) => {
      const { close } = chatProgress.transformChatProgressData(data)
      if (close) {
        chatProgress.cancelRequest()
      }
    }
    chatProgress.data.hooks.on('onAfterFetch', updateActiveTab)
    chatProgress.startRequest()
    const ac = {
      abort: () => chatProgress.cancelRequest(),
    }
    abortControllerManager.add(ac)
    return () => {
      chatProgress.data.hooks.removeListener('onAfterFetch', updateActiveTab)
      abortControllerManager.delete(ac)
      ac.abort()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <ChatHistoryItemContext.Provider
      value={{
        chat: chat,
        theme,
        chatProgress,
      }}
    >
      <BiResultWrapper key={`bi-result-wrapper-${chat.id}`}>
        <div className="ask-bi-result-content w-full" id={`bi-result-wrapper-${chat.id}`}>
          <BiResultContent
            currentChat={chat}
            chatAnsItem={ansItem}
            currentSelectedSceneId={MULTI_SCENE_CHAT_MOCK_SCENE_ID}
            chartWrapperRef={chartWrapperRef}
          />
        </div>
      </BiResultWrapper>
    </ChatHistoryItemContext.Provider>
  )
}

export class MultiAgentChat extends AnsChat {
  static assert(chat: BaseChat): chat is MultiAgentChat {
    return chat instanceof MultiAgentChat
  }
  type = MultiAgentChatType
  data = new MultiAgentData()
  taskId: string = nanoid()
  toReactNode(chat: this) {
    return <MultiAgentChatUI key={chat.id} chat={chat} />
  }
  toMessages({
    chatRecord,
    metricConfigRecord,
  }: {
    chats: BaseChat[]
    metricConfigRecord: Record<string, MetricConfig>
    chatRecord: Record<string, BaseChat>
  }): Message[] {
    const askChat = chatRecord[this.askChatId] as AskChat
    const res: Message[] = []
    res.push({
      role: 'user',
      content: askChat.requestData.message,
    })
    res.push({
      role: 'assistant',
      content: multiAgentDataToMessage(this.data, metricConfigRecord),
    })
    return res
  }
  toPlain() {
    const plain = super.toPlain()
    Object.assign(plain, { taskId: this.taskId, data: this.data.toPlain() })
    return plain
  }
  protected fromWithInstance(instance: MultiAgentChat, o: any, option: Record<string, any> = {}) {
    super.fromWithInstance(instance, o, option)
    instance.set('taskId', o.taskId).set('data', MultiAgentData.from(o.data, option.metricConfigRecord))
  }
  from(o: any, option: Record<string, any> = {}): MultiAgentChat {
    const instance = new MultiAgentChat()
    if (o) this.fromWithInstance(instance, o, option)
    return instance
  }
}
