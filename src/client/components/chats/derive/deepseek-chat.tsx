import React, { useEffect, useRef, useState } from 'react'
import { CaretDownOutlined, CaretUpOutlined, SyncOutlined } from '@ant-design/icons'
import { useAtomValue } from 'jotai'
import { useBoolean } from 'ahooks'
import { ResponseEntity, EventSource } from 'src/shared'
import { abortControllerManager } from 'src/client/utils'
import { currentParamsExtractApiAtom, metricConfigRecordAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { askBIApiUrls } from 'src/shared/url-map'
import { getResolvedMarkdown, ToolMarkdownWrapper, ResultMarkdownWrapper, Markdown } from '../../agent'
import BiResultWrapper from '../../ChatHistory/BiResultWrapper'
import { DeepSeekIcon } from '../../deepseek'
import { chatRecordAtom, chatsAtom } from '../atoms'
import { toMessages } from '../utils'
import { DeepSeekChatType } from '../utils/constants'
import { BaseChat } from './base-chat'
import { AnsChat } from './ans-chat'

export function DeepSeekChatUI({ chat }: { chat: DeepSeekChat }) {
  // console.log('DeepSeekChatUI', chat, `[${chat.cot}]`)
  const currentParamsExtractApi = useAtomValue(currentParamsExtractApiAtom)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const metricConfigRecord = useAtomValue(metricConfigRecordAtom)
  const chatRecord = useAtomValue(chatRecordAtom)
  const chats = useAtomValue(chatsAtom)
  const [thinking, thinkingOps] = useBoolean(true)
  const [cot, setCot] = useState(chat.cot)
  const [status, setStatus] = useState<'ready' | 'done' | 'loading' | 'fail'>(
    chat.status === 'reload' || chat.status === 'recreated' ? 'done' : 'ready',
  )

  useEffect(() => {
    if (chat.status === 'reload') return
    if (chat.status === 'recreated') setCot('')
    setStatus('ready')
    const eventSource = new EventSource()
    const ac = {
      abort: () => eventSource?.abort(),
    }
    abortControllerManager.add(ac)
    eventSource.on('onMessage', (msg) => {
      //   console.log('onMessage', msg)
      if (msg.data) {
        const data = ResponseEntity.from<string>(JSON.parse(msg.data))
        if (data.getCode() === 0) {
          const str = data.getData() ?? ''
          setCot((old) => old + str)
        }
      }
    })
    eventSource.on('onError', (err) => {
      console.info('Cot onError', err)
      setStatus('fail')
    })
    eventSource.on('onClose', () => {
      setStatus('done')
    })

    setTimeout(() => {
      eventSource
        .fetch(askBIApiUrls.resultAnalysis, {
          method: 'post',
          body: JSON.stringify({
            messages: toMessages({
              chats,
              metricConfigRecord,
              chatRecord,
              lastChatId: chat.id,
              parentId: (chatRecord[chat.ansChatId] as AnsChat).id,
            }),
            currentParamsExtractApi,
          }),
        })
        .catch((err) => {
          console.info('SSE SSE ERR', err)
        })
        .finally(() => {})
    }, 200)

    return () => {
      abortControllerManager.delete(ac)
      ac.abort()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chat])

  const { result, think } = getResolvedMarkdown({
    cot,
    isRunning: status !== 'done',
  })

  useEffect(() => {
    chat.upsertThrottle({
      beforeUpsert: () => {
        chat.cot = cot
      },
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cot])

  return (
    <BiResultWrapper>
      <div className="flex w-full flex-col gap-4 break-all px-6 py-4">
        {status === 'ready' ? (
          <div className="flex" ref={containerRef}>
            deepseek正在尝试解答您的问题...
            <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
          </div>
        ) : status === 'fail' ? (
          <div className="flex" ref={containerRef}>
            请求失败，请稍后再试。
          </div>
        ) : (
          <div ref={containerRef}>
            <div className="flex h-[24px] items-center justify-between">
              <div className="flex h-full w-full items-center justify-start">
                <DeepSeekIcon className="h-[22px] w-[22px]" />
                <span className="ml-[8px] text-[14px] font-[400] text-[#0F172A]">该问题由DeepSeek尝试解答</span>
              </div>
              <div
                className="flex h-full shrink-0 cursor-pointer items-center justify-end"
                onClick={thinkingOps.toggle}
              >
                <span className="text-[14px] font-[400] text-[#575757]">收起</span>
                {thinking ? (
                  <CaretUpOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
                ) : (
                  <CaretDownOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
                )}
              </div>
            </div>
            {think && thinking && (
              <ToolMarkdownWrapper>
                <Markdown md={think} />
              </ToolMarkdownWrapper>
            )}
            {result && (
              <ResultMarkdownWrapper>
                <Markdown md={result} />{' '}
              </ResultMarkdownWrapper>
            )}
            {status === 'loading' && (
              <div className="mt-[16px] rounded-[4px] bg-[#E5F7F8] px-[16px] py-[12px]">
                deepseek正在思考回答中...请继续等待...
              </div>
            )}
          </div>
        )}
      </div>
    </BiResultWrapper>
  )
}

export class DeepSeekChat extends BaseChat {
  static assert(chat: BaseChat): chat is DeepSeekChat {
    return chat instanceof DeepSeekChat
  }
  type = DeepSeekChatType
  status: 'created' | 'recreated' | 'reload' = 'created'
  ansChatId = ''
  cot = ''
  toReactNode(chat: this, _index: number, _chats: BaseChat[]): React.ReactNode {
    return <DeepSeekChatUI key={chat.id} chat={chat} />
  }
  toPlain() {
    const plain = super.toPlain()
    Object.assign(plain, { ansChatId: this.ansChatId, cot: this.cot })
    return plain
  }
  protected fromWithInstance(instance: DeepSeekChat, o: any) {
    super.fromWithInstance(instance, o)
    instance.set('ansChatId', o.ansChatId).set('cot', o.cot).set('status', 'reload')
  }
  from(o: any, _option: Record<string, any>): DeepSeekChat {
    const instance = new DeepSeekChat()
    if (o) this.fromWithInstance(instance, o)
    return instance
  }
}
