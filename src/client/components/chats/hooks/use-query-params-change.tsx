import { useContext } from 'react'
import axios from 'axios'
import { useAtomValue } from 'jotai'
import { ChatHistoryItemContext } from '@components/ChatHistory/ChatHistoryItemContext'
import { PlanData } from '@components/agent'
import { chatRecordAtom } from '@components/chats'
import { readyResponseToChatAns, unreadyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
import { AnsChatItem } from 'src/client/utils'
import { ChatResponse } from 'src/shared/common-types'
import { QueryParamsVerified } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'

export function useQueryParamsChange() {
  const chatRecord = useAtomValue(chatRecordAtom)
  const { chat } = useContext(ChatHistoryItemContext)
  async function onQueryParamsChange({
    queryParamsVerified,
    setAnsItem,
    getAnsItem,
    chatId,
    sceneId = '',
    planData,
    infoTexts,
    conversationId,
  }: {
    queryParamsVerified: QueryParamsVerified
    getAnsItem?: () => AnsChatItem[]
    setAnsItem?: (v: AnsChatItem[]) => void
    chatId: string
    sceneId?: string
    planData?: PlanData
    infoTexts?: string[]
    conversationId?: string
  }) {
    const ansItem = getAnsItem?.()
    // if (!ansItem) return
    if (ansItem?.[0]?.sceneId) sceneId = ansItem[0].sceneId
    const response = await axios.post<ChatResponse>(
      askBIApiUrls.metricQuery,
      {
        queryParamsVerified,
        infoTexts,
        conversationId,
        sceneId,
        chatId,
        customize: true,
      },
      { headers: { traceId: chat.askChat?.requestData.traceId ?? chat.getAskChat(chatRecord)?.requestData.traceId } },
    )
    const data = response.data
    const chatAns = (data.ready ? readyResponseToChatAns : unreadyResponseToChatAns)(data as any, sceneId)
    planData?.emit('onUpdateEarlyAnsItem', [chatAns])
    setAnsItem?.([chatAns])
    const currentConfidenceSelection = {
      sceneId,
      metricNames: queryParamsVerified.queryParams.metricNames,
      where: queryParamsVerified.queryParams.where || '',
    }
    planData?.emit('onUpdateCurrentConfidenceSelection', currentConfidenceSelection)
    console.info('currentConfidenceSelection is', currentConfidenceSelection)
    // setTimeout(scrollToBottom, 100)
  }
  return onQueryParamsChange
}
