/* eslint-disable no-console */
import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON><PERSON> } from 'jotai'
import { useNavigate } from 'react-router-dom'
import { App } from 'antd'
import axios from 'axios'
import { nanoid } from 'nanoid'
import {
  additionalInfoAtom,
  cancelTokenSourceAtom,
  conversationId<PERSON>tom,
  currentDatasetAtom,
  currentLoginUserAtom,
  currentParamsExtractApiAtom,
  deepSeekEnableNetWorkAtom,
  isProjectChosenAtom,
  isSubmittingAtom,
  paramsExtractUrlListAtom,
  scenesListAtom,
  sceneInfoAtom,
  agentMessageAtom,
  updateMessageAndHtmlAtom,
  metricConfigRecordAtom,
  isShowMatchedParamDrawerAtom,
  isLoadingDatasetAtom,
  currentFollowUpQuestionNextAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import { scrollToBottom } from 'src/client/utils'
import { createTraceId } from 'src/shared/common-utils'
import { currentSelectFileBySceneAtom } from 'src/client/pages/AskDoc/askDocAtoms'
import { ChatResponseErrorMap, ChatResponseErrorTypes } from 'src/shared/common-types'
import { ChatRequest } from 'src/shared/types/chat'
import { askBIApiUrls, askBIPageUrls, askDocApiUrls } from 'src/shared/url-map'
import { Doc } from 'src/shared/askdoc-types'
import { chatRecordAtom, chatsAtom } from '../atoms'
import { AskChat, MultiAgentChat } from '../derive'
import { toMessages } from '../utils'
import { getMatchedDataFromResponse } from '../../agent/match'
import { TryQueryToSqlData } from '../../TryQueryToSql/try-query-to-sql'

export function useMakeAgentRequest() {
  const { message: antdMessage } = App.useApp()
  const agentMessage = useAtomValue(agentMessageAtom)
  const scenesList = useAtomValue(scenesListAtom)
  const { isBiAgent, isDocAgent } = useAtomValue(sceneInfoAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const isProjectChosen = useAtomValue(isProjectChosenAtom)
  const currentParamsExtractApi = useAtomValue(currentParamsExtractApiAtom)
  const [conversationId, setConversationId] = useAtom(conversationIdAtom)
  const paramsExtractUrlList = useAtomValue(paramsExtractUrlListAtom)
  const deepSeekEnableNetWork = useAtomValue(deepSeekEnableNetWorkAtom)
  const selectDoc = useAtomValue(currentSelectFileBySceneAtom)
  const currentLoginUser = useAtomValue(currentLoginUserAtom)
  const additionalInfo = useAtomValue(additionalInfoAtom)
  const [currentFollowUpQuestion, setCurrentFollowUpQuestion] = useAtom(currentFollowUpQuestionNextAtom)
  const [isSubmitting, setIsSubmitting] = useAtom(isSubmittingAtom)
  const metricConfigRecord = useAtomValue(metricConfigRecordAtom)
  const setCancelTokenSourceAtom = useSetAtom(cancelTokenSourceAtom)
  const isShowMatchedParamDrawer = useSetAtom(isShowMatchedParamDrawerAtom)
  const updateMessageAndHtml = useSetAtom(updateMessageAndHtmlAtom)
  const [chats, setChats] = useAtom(chatsAtom)
  const isLoadingDataset = useAtomValue(isLoadingDatasetAtom)
  const chatRecord = useAtomValue(chatRecordAtom)
  const navigate = useNavigate()

  async function getDocReference() {
    let fileIds: string[] | undefined = undefined
    let dirIds: number[] | undefined = undefined
    if (isDocAgent) {
      if (selectDoc) {
        fileIds = [selectDoc.id.toString()]
      }
      if (!fileIds && !dirIds) {
        console.info('获取全部文档')

        const responseList = await Promise.all(
          scenesList
            .filter((v) => v.agent?.includes('Doc'))
            .map((v) =>
              axios.post(askDocApiUrls.getDocDirList, {
                roleIds: currentLoginUser?.groups.map((item) => item.id) || [],
                dirId: -1,
                page: 1,
                pageSize: 99999,
                name: '',
                fileType: '',
                sceneId: v.id,
              }),
            ),
        )

        const files: Doc[] = responseList.flatMap((v) => v.data.data.files)
        fileIds = files.filter((v) => !v.isDir).map((v) => v.id.toString())
        dirIds = files.filter((v) => v.isDir).map((v) => v.id)
      }
    }
    return {
      fileIds,
      dirIds,
    }
  }

  async function upsertConversion(requestData: Record<string, any>) {
    // 更新conversation的数据 异步执行不阻塞
    const upsertResp = await axios
      .post(askBIApiUrls.convers.upsertNext, {
        requestData,
        isNewConversation: !conversationId,
      })
      .catch((error) => {
        console.error('Record Conversation History Failed', error)
        antdMessage.warning('会话历史记录失败，不影响问答')
        return null
      })
    return upsertResp
  }

  async function makeAgentRequest({ message, isMiniMode }: { message: string; isMiniMode: boolean }) {
    message ||= agentMessage
    if (message.length < 1) return
    if (isSubmitting) return
    if (isLoadingDataset) {
      antdMessage.warning('数据源正在加载中，请稍候...')
      return
    }
    if (currentDataset == null) {
      antdMessage.error('当前用户无数据源权限或数据源信息正在加载中')
      return
    }
    let multiAgentChat!: MultiAgentChat
    let askChat!: AskChat
    try {
      console.group('makeAgentRequest')
      console.info(`message:[${message}]`)

      if (isMiniMode) {
        navigate(askBIPageUrls.chatNew)
      }

      const traceId = createTraceId()
      const taskId = nanoid()
      const requestData: ChatRequest = {
        message,
        conversationId: conversationId ?? nanoid(),
        taskId,
        traceId,
        parentId: currentFollowUpQuestion?.id,
        sceneId: isProjectChosen ? undefined : currentDataset?.sceneId,
        projectId: currentDataset?.projectId ?? '',
        enableFollowUpQuestion: scenesList.some((v) => v.enableFollowUpQuestion),
        enableMetricExactMatch: scenesList.some((v) => v.enableMetricExactMatch),
        enableTryQueryUp: scenesList.some((v) => v.enableTryQueryUp),
        enableAccMetricToastWhenEmptyData: scenesList.some((v) => v.enableAccMetricToastWhenEmptyData),
        currentParamsExtractApi: currentParamsExtractApi ?? paramsExtractUrlList?.[0] ?? '',
        enableInternetSearch: deepSeekEnableNetWork,
        enableDocSearch: isDocAgent,
        enableBi: isBiAgent,
        additionalInfo: {
          hintDynamicLimit: additionalInfo.hintDynamicLimit,
          hintDenseWeight: additionalInfo.hintDenseWeight,
          hintSparseWeight: 1 - additionalInfo.hintDenseWeight,
        },
        messages: [
          {
            role: 'user',
            content: message,
          },
        ],
      }
      askChat = new AskChat().set('requestData', requestData).set('conversationId', requestData.conversationId)
      multiAgentChat = new MultiAgentChat()
        .set('askChatId', askChat.id)
        .set('taskId', taskId)
        .set('conversationId', requestData.conversationId)
        .set('askChat', askChat)
      setChats((prev) => prev.concat(askChat, multiAgentChat))
      setConversationId(requestData.conversationId)
      updateMessageAndHtml('', false)
      isShowMatchedParamDrawer(false)
      setCurrentFollowUpQuestion(null)

      const { fileIds, dirIds } = await getDocReference()
      // await sleep(2000)
      requestData.fileIds = fileIds
      requestData.dirIds = dirIds
      if (requestData.enableFollowUpQuestion) {
        requestData.messages = toMessages({
          chats,
          metricConfigRecord,
          parentId: currentFollowUpQuestion?.id,
          chatRecord,
        }).concat(requestData.messages)
      }
      const upsertedConver = await upsertConversion(requestData)
      console.log('upsertedConver', upsertedConver)
      askChat.upsertDebounce()
      setIsSubmitting(true)
      const ac = new AbortController()
      setCancelTokenSourceAtom(ac)
      setTimeout(scrollToBottom, 100)
      const response = await axios.post(askBIApiUrls.agent.chat, requestData, {
        signal: ac.signal,
        headers: { traceId },
      })
      console.log('Chat Response', response)
      const data = response.data
      if (
        ChatResponseErrorMap[data.data.code as keyof typeof ChatResponseErrorMap] ===
        ChatResponseErrorTypes.SENSITIVE_QUESTION
      ) {
        const id = upsertedConver?.data?.data?.id
        if (id) await axios.delete(askBIApiUrls.convers.delete(id))
        multiAgentChat.data.emit('onError', {
          type: 'sensitive-question',
          chatErrorProps: {
            content: {
              type: 'chat-error',
              unreadyReason: data?.data?.data?.unreadyReason,
              errType: 'SENSITIVE_QUESTION',
              tryQueryToSqlData: new TryQueryToSqlData(),
            },
            currentDataset: null,
            chatId: multiAgentChat.id,
          },
        })
        return
      } else if (data.data.code && Number(data.data.code) !== 0) throw data.data
      const matchedData = getMatchedDataFromResponse(data)
      console.log('matchedData', matchedData)
      if (matchedData) {
        multiAgentChat.data.emit('onNeedMatch', matchedData)
      }
    } catch (e: any) {
      console.log('===> MakeAgentRequest Error')
      if (e?.name === 'CanceledError') {
        multiAgentChat.data.emit('onError', {
          type: 'common',
          chatErrorProps: {
            content: {
              type: 'chat-error',
              unreadyReason: '问数已取消',
              errType: 'E_MAKE_AGENT_REQUEST_CANCELLED',
              tryQueryToSqlData: new TryQueryToSqlData(),
            },
            currentDataset: null,
            chatId: multiAgentChat.id,
          },
        })
      } else {
        console.error(e)
        multiAgentChat.data.emit('onError', {
          type: 'canceled',
          chatErrorProps: {
            content: {
              type: 'chat-error',
              unreadyReason: '网络已断开',
              errType: 'LLM_ERROR',
              tryQueryToSqlData: new TryQueryToSqlData(),
            },
            currentDataset: null,
            chatId: multiAgentChat.id,
          },
        })
      }
    } finally {
      setIsSubmitting(false)
      console.groupEnd()
    }
  }

  return makeAgentRequest
}
