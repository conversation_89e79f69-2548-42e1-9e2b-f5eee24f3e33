/* eslint-disable no-console */
import { MetricConfig } from 'src/shared/metric-types'
import { Message } from 'src/shared/common-types'
import { BaseChat, AnsChat, MultiAgentChat } from '../derive'

export * from './constants'
export * from './transform'

export function toMessages(data: {
  lastChatId?: string
  parentId?: string
  chats: BaseChat[]
  metricConfigRecord: Record<string, MetricConfig>
  chatRecord: Record<string, BaseChat>
}) {
  try {
    console.group('ToMessages')
    console.log('data', data)
    let { chats, metricConfigRecord, parentId, chatRecord, lastChatId } = data
    const enableFollowUp = !!parentId
    const followUpChatSet = new Set<string>()
    if (enableFollowUp) {
      while (parentId) {
        followUpChatSet.add(parentId)
        const parentChat = chatRecord[parentId]
        if (AnsChat.assert(parentChat)) {
          parentId = parentChat.getAskChat(chatRecord)?.requestData.parentId
        } else {
          parentId = undefined
        }
      }
    }
    console.log('enableFollowUp', enableFollowUp, followUpChatSet)
    const lastIdx = chats.findIndex((chat) => chat.id === lastChatId) >>> 0
    console.log('lastIdx', lastIdx)
    const messages = chats
      .slice(0, lastIdx)
      .filter((chat) => {
        if (enableFollowUp) return followUpChatSet.has(chat.id)
        if (MultiAgentChat.assert(chat)) return true
        return false
      })
      .flatMap((chat) => {
        return chat.toMessages({
          chats,
          metricConfigRecord,
          chatRecord,
        })
      }) as Message[]
    console.log('messages', messages)
    return messages
  } catch (err) {
    console.log('ToMessageError')
    console.error(err)
    return []
  } finally {
    console.groupEnd()
  }
}
