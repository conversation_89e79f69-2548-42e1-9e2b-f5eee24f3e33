import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mo<PERSON> } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import axios from 'axios'
import clsx from 'clsx'
import { useAtomValue, useSetAtom } from 'jotai/react'
import Conversations from 'src/client/pages/AskBI/Chat/Conversations'
import { converDrawerConfigAtom } from 'src/client/pages/AskBI/conver-atoms/converAtoms'
import {
  agentStructuredMessageAtom,
  brandInfoAtom,
  conversationIdAtom,
  currentLoginUserAtom,
  isSubmittingAtom,
  loginAppIdAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import { abortControllerManager } from 'src/client/utils'
import { clearAllCookies } from 'src/shared/auth'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { useInitChats } from '../chats'

export default function ConversationDrawerContent() {
  const { message } = App.useApp()
  const currentLoginUserInfo = useAtomValue(currentLoginUserAtom)
  const { initChats } = useInitChats()
  const setConversationId = useSetAtom(conversationIdAtom)
  const loginAppId = useAtomValue(loginAppIdAtom)

  const brandInfo = useAtomValue(brandInfoAtom)
  const setMessage = useSetAtom(agentStructuredMessageAtom)
  const setIsSubmitting = useSetAtom(isSubmittingAtom)
  const setConverDrawerConfig = useSetAtom(converDrawerConfigAtom)
  const [modal, contextHolder] = Modal.useModal()
  // 清空 conversation & chart ID , 初始化chats数组
  const clearHistory = () => {
    setConversationId(null)
    setMessage('')
    setIsSubmitting(false)
    initChats()
    setConverDrawerConfig({ open: false })
  }

  // 退出登录接口
  const handleLogout = async () => {
    try {
      // 在退出登陆前终止所有可追溯的请求，防止阻塞下面的接口
      abortControllerManager.forEach((ac) => ac.abort())
      await axios.post(askBIApiUrls.auth.logout)
      localStorage.clear()
      clearAllCookies()
      window.location.href = loginAppId ? `${askBIPageUrls.login}?appid=${loginAppId}` : askBIPageUrls.login
    } catch (error: any) {
      console.error(error)
      message.error(`退出登录失败：${error?.message}`)
    }
  }

  // 新建工作簿点击事件
  const handleNewChat = () => {
    modal.confirm({
      title: `新建工作簿会清空当前聊天记录，是否确认？`,
      icon: <ExclamationCircleFilled />,
      okText: '确认',
      cancelText: '取消',
      centered: true,
      className: 'llm-toggle-confirm bg-white dark:bg-slate-900 dark:text-slate-100',
      onOk: clearHistory,
    })
  }

  return (
    <div className="h-full w-full rounded-2xl border py-9 shadow-sm">
      <div className="flex h-full w-full flex-col justify-between">
        <div className="px-6">
          <Button onClick={handleNewChat} block type="primary" className="h-[46px] rounded-2xl">
            新建会话
          </Button>
        </div>
        <div className="my-4 flex-1 overflow-auto px-6">
          <Conversations />
        </div>
        <div className="flex h-9 items-center px-6">
          <div
            className={clsx('flex items-center text-base', {
              invisible: typeof brandInfo?.header === 'object' && brandInfo?.header?.userInfo === false,
            })}
          >
            <div
              className={clsx('mr-2 flex size-9 cursor-pointer items-center justify-center rounded-full bg-primary')}
            >
              <span className="text-base leading-4 text-white">
                {currentLoginUserInfo?.username.slice(0, 1).toLocaleUpperCase() || 'N'}
              </span>
            </div>
            <span className="flex items-center leading-4">{currentLoginUserInfo?.username}</span>
          </div>
          <Divider type="vertical" className="h-4" />
          <Button
            type="link"
            className="px-1"
            danger
            onClick={() => {
              handleLogout()
            }}
          >
            登出
          </Button>
        </div>
      </div>

      {contextHolder}
    </div>
  )
}
