import * as Sentry from '@sentry/react'

try {
  // hosted by dipeak
  const isSentryEnabled = /dipeak\.com|localhost/.test(window.location.host)
  if (isSentryEnabled) {
    Sentry.init({
      dsn: 'https://<EMAIL>/2',
      environment: window.location.hostname.includes('localhost') ? 'development' : 'production',
      tracesSampleRate: 1.0,
      sendDefaultPii: true,
      replaysOnErrorSampleRate: 1,
      replaysSessionSampleRate: 1,
      integrations: [
        // record user interactions
        Sentry.replayIntegration(),
        // capture performance data for the browser
        Sentry.browserTracingIntegration(),
      ],
    })
  }
} catch (e) {
  console.error('sentry init error', e)
}
