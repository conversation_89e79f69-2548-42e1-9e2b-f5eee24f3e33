import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import clsx from 'clsx'
import { App, Button, Col, Form, Input, InputNumber, Modal, Radio, Row, Select, Space, Switch, Typography } from 'antd'
import { useAtom } from 'jotai'
import { MinusCircleOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons'
import axios from 'axios'
import { useRequest } from 'ahooks'
import get from 'lodash/get'
import { pick } from 'lodash'
import { SemanticMetric } from '@prisma/client'
import { FormatNumberReg, IntNotZeroReg, IntReg } from 'src/shared/constants'
import { metricListAtomForOption } from 'src/client/pages/AskBI/askBIAtoms'
import { ColumnDescType, MetricConfig, MetricModelType, MetricModelDimensionType } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, measureIcon } from 'src/client/components/SvgIcon'
import { assertExhaustive, formatNumber } from 'src/shared/common-utils'
import { APIResponse } from 'src/shared/common-types'
import { Synonyms } from 'src/client/components/Synonyms'
import { customerFilterValue } from 'src/shared/customer-resolver'
import CustomerHiddenWrap from 'src/client/components/CustomerHiddenWrap'
import FormLayout from './FormLayout'
import { adaptOldMetricWindowDesc } from './conf'

const supportMetricTypeNames = {
  simple: '原子指标',
  ratio: '比值指标',
  derived: '派生指标',
  // rank: '排名指标',
  list: '列表指标',
} as const
type MetricTypeNameKey = keyof typeof supportMetricTypeNames
const Option = Select.Option
const defaultFilterFunc = () => true
const searchFilter = (searchKeys: string[], input: string, option?: Record<string, string>) =>
  (searchKeys || []).some((key) => (option?.[key] ?? '').toLowerCase().includes(input.toLowerCase()))

const SlideOrSizeFormItem = ({
  label,
  name,
  timeUnitName,
  timeUnitList,
}: {
  label: string
  name: string | string[]
  timeUnitName: string | string[]
  timeUnitList?: string[]
}) => {
  return (
    <Row gutter={18}>
      <Col span={12}>
        <Form.Item
          layout="horizontal"
          label={label}
          name={name}
          initialValue={1}
          rules={[
            { required: true, message: '请输入' },
            { pattern: IntNotZeroReg, message: '请输入整数' },
          ]}
        >
          <InputNumber className="w-full" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          label={' '}
          initialValue={timeUnitList?.[0]}
          layout="horizontal"
          name={timeUnitName}
          rules={[{ required: true, message: '请选择' }]}
        >
          <Select
            placeholder="请选择"
            className="min-w-[200px]"
            showSearch
            allowClear
            filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
          >
            {timeUnitList?.map((timeUnit) => {
              return (
                <Option key={timeUnit} value={timeUnit} label={timeUnit}>
                  {timeUnit}
                </Option>
              )
            })}
          </Select>
        </Form.Item>
      </Col>
    </Row>
  )
}

const MetricsCreate = ({
  className,
  createMetricModalOpen = true,
  setCreateMetricModalOpen = () => {
    return
  },
  onOk,
  isEdit,
  values,
  baseInfo,
}: {
  factTable?: string
  catalog?: string
  databaseName?: string
  table?: string
  onOk?: () => void
  className?: string
  tableColumns?: ColumnDescType[]
  createMetricModalOpen: boolean
  setCreateMetricModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  isEdit?: boolean
  values?: SemanticMetric
  baseInfo: {
    semanticProjectId: string
    semanticSceneId: string
  }
}) => {
  const [form] = Form.useForm()
  const [_metricListAtomForOption] = useAtom(metricListAtomForOption)
  const MetricsOptions: { label: string; value: MetricTypeNameKey }[] = customerFilterValue(
    'createMetricTypesFilter',
    Object.entries(supportMetricTypeNames).map(([key, value]) => ({
      label: value,
      value: key as MetricTypeNameKey,
    })),
  )
  const { message: antdMessage } = App.useApp()

  const willCloseWindowAfterCreate = useRef(false)
  const [semanticProjectId, setSemanticProjectId] = useState('')
  const [semanticModelId, setSemanticModelId] = useState('')
  const [semanticSceneId, setSemanticSceneId] = useState('')

  const { data: metricsList } = useRequest(
    async () => {
      const response = await axios.get<APIResponse<MetricConfig>>(askBIApiUrls.auth.metrics, {
        params: { sceneId: semanticSceneId },
      })
      return response.data?.data?.allMetrics
    },
    {
      ready: Boolean(semanticProjectId && semanticModelId && semanticSceneId),
      refreshDeps: [semanticProjectId, semanticModelId, semanticSceneId],
      onError: (error) => {
        console.error('get metricArrayError =', error)
      },
    },
  )
  const { loading: isModelLoading, data: modelData } = useRequest(
    () =>
      axios
        .get<APIResponse<MetricModelType>>(askBIApiUrls.model.meta, {
          params: {
            name: semanticModelId,
          },
        })
        .then((res) => res.data?.data),
    {
      ready: Boolean(semanticModelId),
      refreshDeps: [semanticModelId],
    },
  )

  // 目前只取了dimensions，后续根据需要自行扩展
  const originDataModelColumnDesc = useMemo(() => {
    return modelData?.dataModelDesc?.dimensions || []
  }, [modelData])

  const ColumnFormItem = ({
    label,
    columns = originDataModelColumnDesc,
    name,
    required = true,
    mode = 'single',
    filterFunc = defaultFilterFunc,
  }: {
    label: string
    name: string | string[]
    required?: boolean
    filterFunc?: (column: Partial<MetricModelDimensionType>) => boolean
    columns?: Partial<MetricModelDimensionType>[]
    mode?: 'multiple' | 'single'
  }) => {
    const selectInComponent = () => (
      <Select
        fieldNames={{ label: 'name', value: 'name' }}
        showSearch
        labelRender={(option) => {
          return option.value
        }}
        options={(columns || []).filter(filterFunc)}
        popupMatchSelectWidth={false}
        placeholder="请选择"
      />
    )
    return (
      <Form.Item
        label={label}
        rules={[{ required, message: '请选择' }]}
        {...Object.assign({}, mode === 'single' ? { name } : {})}
      >
        {mode === 'single' ? (
          selectInComponent()
        ) : (
          <Form.List name={name} initialValue={[]}>
            {(fields, { add, remove }) => {
              return (
                <>
                  {fields.map(({ name, ...restField }) => (
                    <Row key={name} style={{ width: '100%', display: 'flex', marginBottom: 8 }} gutter={[10, 10]}>
                      <Col flex={'auto'}>
                        <Form.Item {...restField} key={restField.key} name={[name, 'name']}>
                          <Select
                            popupMatchSelectWidth={false}
                            style={{ width: 418 }}
                            fieldNames={{ label: 'name', value: 'name' }}
                            showSearch
                            labelRender={(option) => {
                              return option.value
                            }}
                            options={(columns || []).filter(filterFunc)}
                            placeholder="请选择"
                          />
                        </Form.Item>
                      </Col>
                      <Col flex={'auto'} className="mt-[8px] flex items-baseline justify-center">
                        <MinusCircleOutlined
                          className="ml-1"
                          onClick={() => {
                            remove(name)
                          }}
                        />
                      </Col>
                    </Row>
                  ))}
                  <Row className="w-full">
                    <Col span={24}>
                      <Button type="dashed" onClick={() => add({})} block icon={<PlusOutlined />}>
                        添加列名
                      </Button>
                    </Col>
                  </Row>
                </>
              )
            }}
          </Form.List>
        )}
      </Form.Item>
    )
  }
  const timeTypeMap = {
    TUMBLE: 'Tumble',
    HOP: 'Hop',
    OVER_AGG: 'OVER AGG',
    // NOT_CONFIG: '不配置',
  } as const
  const rangeTypeMap = {
    RANGE_INTERVAL: 'RANGE INTERVAL',
    ROW_INTERVAL: 'ROW INTERVAL',
  }

  const { data: timeUnitList } = useRequest(() => {
    return axios.get<APIResponse<string[]>>(askBIApiUrls.xengine.timeGranularity.list).then((res) => {
      const list = ['SECONDS', 'MINUTES', 'HOURS', 'DAYS']
      return (res.data?.data || []).filter((i) => list.includes(i))
    })
  })

  useEffect(() => {
    axios.get(askBIApiUrls.auth.scene.rest, { params: { id: baseInfo.semanticSceneId } }).then((res) => {
      const data = res.data.data
      const modelNames = data?.modelNames || []
      if (modelNames[0]) {
        setSemanticModelId(modelNames[0])
      }
    })
    setSemanticProjectId(baseInfo.semanticProjectId || '')
    setSemanticSceneId(baseInfo.semanticSceneId || '')
  }, [baseInfo])

  const initValues = useCallback(
    (values: SemanticMetric) => {
      let formattedValues
      let windowDesc
      try {
        if (values.windowDescConfigStr) {
          windowDesc = adaptOldMetricWindowDesc(JSON.parse(values.windowDescConfigStr), originDataModelColumnDesc)
        }
      } catch (e) {
        console.error(e)
      }

      switch (values.type) {
        case 'simple': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'filter',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
            windowDesc,
          }
          break
        }
        case 'derived': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'filter',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        case 'list': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        case 'ratio': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        default: {
          return
        }
      }
      form.setFieldsValue(formattedValues)
    },
    [form, originDataModelColumnDesc],
  )

  useEffect(() => {
    if (createMetricModalOpen) {
      if (isEdit && values) {
        initValues(values)
      } else {
        form.resetFields()
      }
    }
  }, [createMetricModalOpen, isEdit, values, initValues, form])

  const { run: createOrUpdateMetric, loading: isCreatingOrUpdatingMetric } = useRequest(
    (arg) => {
      const t = { ...arg }
      if (t.windowDesc && t.windowDesc.windowType !== 'NOT_CONFIG') {
        t.windowDescConfigStr = JSON.stringify(t.windowDesc)
      }
      delete t.windowDesc

      delete t.metricType
      const metric = {
        ...t,
        type: arg.metricType,
        synonyms: t?.synonyms ?? [],
      }
      // 匹配变量名的正则表达式
      const pattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g
      if (metric.type === 'derived') {
        metric.typeParams.metrics = (metric.typeParams.expr.match(pattern) || []).map((name: string) => {
          return { name }
        })
      }
      if (isEdit && values) {
        return axios.put(askBIApiUrls.metrics.update(values.id), {
          ...metric,
          semanticSceneId,
          semanticProjectId,
        })
      }
      return axios.post(askBIApiUrls.metrics.create, {
        metricInfo: metric,
        modelId: semanticModelId,
        semanticSceneId,
        semanticProjectId,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        const msg = `${isEdit ? '编辑' : '新增'}指标成功！`
        antdMessage.success(msg, 0.5)
        form.resetFields()
        onOk?.()
        setCreateMetricModalOpen(!willCloseWindowAfterCreate.current)
      },
      onError: (error) => {
        const defaultMsg = `${isEdit ? '更新' : '新增'}指标失败！`
        antdMessage.error(defaultMsg + error.message)
      },
    },
  )
  const formatTemplate = Form.useWatch('formatTemplate', form)

  return (
    <>
      <Modal
        destroyOnClose
        onCancel={() => setCreateMetricModalOpen(false)}
        width={1000}
        title={`新增指标`}
        open={createMetricModalOpen}
        footer={
          <Space>
            <Button onClick={() => setCreateMetricModalOpen(false)}>取消</Button>
            {!isEdit && (
              <Button
                loading={isCreatingOrUpdatingMetric}
                onClick={() => {
                  form.submit()
                  willCloseWindowAfterCreate.current = false
                }}
              >
                保存并创建下一个
              </Button>
            )}

            <Button
              type="primary"
              loading={isCreatingOrUpdatingMetric}
              onClick={() => {
                form.submit()
                willCloseWindowAfterCreate.current = true
              }}
            >
              保存
            </Button>
          </Space>
        }
      >
        <Form
          className={`my-6 w-full ${className}`}
          form={form}
          onFinish={createOrUpdateMetric}
          initialValues={{ metricType: 'simple', isCumulative: true }}
          layout={'vertical'}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
        >
          <FormLayout required={true} title={'选择指标类型'}>
            <Form.Item name="metricType" className="m-0">
              <Radio.Group>
                {MetricsOptions.map(({ label, value }) => {
                  return (
                    <Radio value={value} key={value}>
                      {label}
                    </Radio>
                  )
                })}
              </Radio.Group>
            </Form.Item>
          </FormLayout>
          <Row gutter={24}>
            <Col span={12}>
              <FormLayout required={false} title={'基础信息'}>
                <Row gutter={12}>
                  <Col span={12}>
                    <Form.Item label="指标名称" name="label" rules={[{ required: true, message: '请输入指标名称' }]}>
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="指标 ID" name="name" rules={[{ required: true, message: '请输入指标 ID' }]}>
                      <Input placeholder="请输入指标 ID" />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item label="描述" name="description" rules={[{ message: '请输入描述' }]}>
                  <Input.TextArea placeholder="请输入描述" />
                </Form.Item>
                <CustomerHiddenWrap type="createMetricCumulativeFormatTemplateHide">
                  <Row gutter={12}>
                    <Col span={12}>
                      <Form.Item label="是否可累加" name="isCumulative">
                        <Switch checkedChildren="是" unCheckedChildren="否" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="单位"
                        name="formatTemplate"
                        tooltip={
                          <div>
                            {['.1f', ',.2f', '¥.3f元', formatTemplate].filter(Boolean).map((key) => {
                              const num = 100.38
                              return (
                                <div key={key}>
                                  {key} = {FormatNumberReg.test(key) ? formatNumber(num, key) : ''}
                                </div>
                              )
                            })}
                          </div>
                        }
                        rules={[{ pattern: FormatNumberReg, message: '单位格式存在问题' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                </CustomerHiddenWrap>
                <CustomerHiddenWrap type="createMetricSynonymsHide">
                  <Form.Item name="synonyms" label="同义词">
                    <Synonyms />
                  </Form.Item>
                </CustomerHiddenWrap>
              </FormLayout>
            </Col>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const metricType = getFieldValue('metricType') as MetricTypeNameKey
                switch (metricType) {
                  case 'simple':
                    /* 原子指标右侧 原有的类型可以符合预期 */
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title={'度量'}>
                          <Form.Item label="度量" name={['typeParams', 'measure']} rules={[{ required: true }]}>
                            <Select
                              placeholder="请选择度量"
                              loading={isModelLoading}
                              showSearch
                              allowClear
                              filterOption={(...args) => searchFilter(['nameZh', 'value'], ...args)}
                              optionRender={(option) => (
                                <>
                                  <div className="flex items-center">
                                    <SvgIcon icon={measureIcon} className="mr-[4px] h-[20px] w-[20px]" />
                                    <span>{option.data?.name || ''}</span>
                                  </div>
                                  <div className="mb-[4px] ml-6 self-stretch text-[13px] font-semibold">
                                    {option.data?.nameZh || ''}
                                  </div>
                                </>
                              )}
                              options={get(modelData, 'dataModelDesc.measures', []).map(
                                (item: { name: string; nameZh: string }) => ({
                                  value: item.name,
                                  name: item.name,
                                  nameZh: item.nameZh,
                                }),
                              )}
                            />
                          </Form.Item>
                        </FormLayout>
                        <CustomerHiddenWrap type="createMetricFilterHide">
                          <FormLayout required={false} title={'筛选'} withSwitch={true} defaultVisible={true}>
                            <Form.Item name={'filter'} rules={[{ message: '请输入' }]}>
                              <Input.TextArea placeholder="请输入" />
                            </Form.Item>
                          </FormLayout>
                        </CustomerHiddenWrap>
                        {modelData?.dataModelDesc?.dataModelDescType !== 'BATCH' && (
                          <>
                            <Form.Item
                              layout="horizontal"
                              label="时间类型"
                              rules={[{ required: true, message: '请选择' }]}
                              name={['windowDesc', 'windowType']}
                              className="m-0"
                            >
                              <Radio.Group>
                                {Object.keys(timeTypeMap).map((key) => {
                                  return (
                                    <Radio value={key} key={key}>
                                      {timeTypeMap[key as keyof typeof timeTypeMap]}
                                    </Radio>
                                  )
                                })}
                              </Radio.Group>
                            </Form.Item>

                            <Form.Item noStyle shouldUpdate>
                              {({ getFieldValue }) => {
                                const metricType = getFieldValue([
                                  'windowDesc',
                                  'windowType',
                                ]) as keyof typeof timeTypeMap
                                switch (metricType) {
                                  case 'TUMBLE':
                                    return (
                                      <>
                                        <ColumnFormItem
                                          label={'时间列'}
                                          name={['windowDesc', 'windowTimeDim', 'name']}
                                          filterFunc={(d) => Boolean(d?.columnDesc?.type?.includes('TIMESTAMP'))}
                                        />
                                        <SlideOrSizeFormItem
                                          label="Size"
                                          name={['windowDesc', 'size']}
                                          timeUnitName={['windowDesc', 'timeUnit']}
                                          timeUnitList={timeUnitList}
                                        />
                                      </>
                                    )
                                  case 'HOP':
                                    return (
                                      <div>
                                        <ColumnFormItem
                                          label={'时间列'}
                                          filterFunc={(d) => Boolean(d.columnDesc?.type?.includes('TIMESTAMP'))}
                                          name={['windowDesc', 'windowTimeDim', 'name']}
                                        />
                                        <SlideOrSizeFormItem
                                          label="Slide"
                                          name={['windowDesc', 'slide']}
                                          timeUnitName={['windowDesc', 'timeUnitOfSlide']}
                                          timeUnitList={timeUnitList}
                                        />
                                        <SlideOrSizeFormItem
                                          label="Size"
                                          name={['windowDesc', 'size']}
                                          timeUnitName={['windowDesc', 'timeUnit']}
                                          timeUnitList={timeUnitList}
                                        />
                                      </div>
                                    )
                                  case 'OVER_AGG':
                                    return (
                                      <div>
                                        <ColumnFormItem
                                          label={'Partition'}
                                          required={false}
                                          mode="multiple"
                                          filterFunc={(d) => !d.columnDesc?.type?.includes('TIMESTAMP')}
                                          name={['windowDesc', 'overAggDesc', 'partitionByDims']}
                                        />
                                        <ColumnFormItem
                                          label={'Order'}
                                          name={['windowDesc', 'overAggDesc', 'orderByDim', 'name']}
                                          filterFunc={(d) =>
                                            Boolean(d?.columnDesc?.type?.includes('TIMESTAMP') && !d.function)
                                          }
                                        />
                                        <Form.Item
                                          label="Range"
                                          name={['windowDesc', 'overAggDesc', 'rangeIntervalDesc', 'rangeIntervalType']}
                                          className="m-0"
                                          rules={[{ required: true, message: '请选择' }]}
                                        >
                                          <Radio.Group>
                                            {Object.keys(rangeTypeMap).map((key) => {
                                              return (
                                                <Radio value={key} key={key}>
                                                  {rangeTypeMap[key as keyof typeof rangeTypeMap]}
                                                </Radio>
                                              )
                                            })}
                                          </Radio.Group>
                                        </Form.Item>
                                        <Form.Item noStyle shouldUpdate>
                                          {({ getFieldValue }) => {
                                            const metricType = getFieldValue([
                                              'windowDesc',
                                              'overAggDesc',
                                              'rangeIntervalDesc',
                                              'rangeIntervalType',
                                            ]) as keyof typeof rangeTypeMap
                                            switch (metricType) {
                                              case 'RANGE_INTERVAL':
                                                return (
                                                  <SlideOrSizeFormItem
                                                    label="Size"
                                                    name={[
                                                      'windowDesc',
                                                      'overAggDesc',
                                                      'rangeIntervalDesc',
                                                      'rangeIntervalSize',
                                                    ]}
                                                    timeUnitName={[
                                                      'windowDesc',
                                                      'overAggDesc',
                                                      'rangeIntervalDesc',
                                                      'rangeTimeIntervalUint',
                                                    ]}
                                                    timeUnitList={timeUnitList}
                                                  />
                                                )
                                              case 'ROW_INTERVAL':
                                                return (
                                                  <Form.Item
                                                    name={[
                                                      'windowDesc',
                                                      'overAggDesc',
                                                      'rangeIntervalDesc',
                                                      'rangeIntervalSize',
                                                    ]}
                                                    className="m-0"
                                                    rules={[
                                                      { required: true, message: '请输入' },
                                                      { pattern: IntReg, message: '请输入整数' },
                                                    ]}
                                                  >
                                                    <InputNumber
                                                      className="w-auto"
                                                      addonBefore="向前"
                                                      addonAfter="行"
                                                      defaultValue={1}
                                                    />
                                                  </Form.Item>
                                                )
                                              default:
                                                return false
                                            }
                                          }}
                                        </Form.Item>
                                      </div>
                                    )
                                  default:
                                    return false
                                }
                              }}
                            </Form.Item>
                          </>
                        )}
                      </Col>
                    )
                  case 'derived':
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title="表达式">
                          <Form.Item name={['typeParams', 'expr']} rules={[{ message: '请输入' }]}>
                            <Input.TextArea placeholder="请输入" />
                          </Form.Item>
                        </FormLayout>
                      </Col>
                    )
                  case 'ratio':
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title="度量">
                          <Form.Item
                            label="分子"
                            name={['typeParams', 'numerator']}
                            className="min-w-[200px]"
                            rules={[{ required: true, message: '分子' }]}
                          >
                            <Select
                              placeholder="请选择指标作为分子（原子指标）"
                              notFoundContent="暂无指标，请先创建指标"
                              className="min-w-[200px]"
                              allowClear
                              showSearch
                              filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
                            >
                              {/* 分子只支持 simple 类型的指标 */}
                              {metricsList
                                ?.filter((metric) => metric.type === 'simple')
                                .map((metric) => {
                                  return (
                                    <Option key={metric.name} value={metric.name} label={metric.label}>
                                      {metric.label} ({metric.name})
                                    </Option>
                                  )
                                })}
                            </Select>
                          </Form.Item>

                          <Form.Item
                            label="分母"
                            name={['typeParams', 'denominator']}
                            className="min-w-[200px]"
                            rules={[{ required: true, message: '分母' }]}
                          >
                            <Select
                              placeholder="请选择指标作为分母"
                              notFoundContent="暂无指标，请先创建指标"
                              className="min-w-[200px]"
                              showSearch
                              allowClear
                              filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
                            >
                              {metricsList?.map((metric) => {
                                return (
                                  <Option key={metric.name} value={metric.name} label={metric.label}>
                                    {metric.label} ({metric.name})
                                  </Option>
                                )
                              })}
                            </Select>
                          </Form.Item>
                        </FormLayout>
                      </Col>
                    )
                  case 'list':
                    /* 列表指标右侧 */
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title={'指标'}>
                          <Form.List name={['typeParams', 'metrics']} initialValue={[{}]}>
                            {(outerFields, { add, remove }) => (
                              <>
                                {outerFields.map(({ key, name: outerName }, index) => (
                                  <Row key={key} className="mb-4">
                                    <Col flex={1}>
                                      <Form.Item
                                        name={[outerName, 'name']}
                                        className="mb-[8px]"
                                        rules={[{ required: true, message: '请选择指标' }]}
                                      >
                                        <Select
                                          allowClear
                                          notFoundContent="暂无指标，请先创建指标"
                                          placeholder="请选择指标"
                                          className="min-w-[200px]"
                                          showSearch
                                          filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
                                        >
                                          {metricsList?.map((i: { name: string; label: string }) => (
                                            <Option key={i.name} value={i.name} label={i.label}>
                                              {i.label} ({i.name})
                                            </Option>
                                          ))}
                                        </Select>
                                      </Form.Item>
                                    </Col>
                                    <Col
                                      className={clsx('mt-[5px] flex items-baseline justify-around', {
                                        'w-[45px]': outerFields.length > 1,
                                        'w-[30px]': outerFields.length === 1,
                                      })}
                                    >
                                      {outerFields.length > 1 ? (
                                        <Typography.Link>
                                          <MinusOutlined className="cursor-pointer" onClick={() => remove(index)} />
                                        </Typography.Link>
                                      ) : null}
                                    </Col>
                                  </Row>
                                ))}
                                <Typography.Link
                                  onClick={() => {
                                    add()
                                  }}
                                >
                                  <PlusOutlined className="mr-1 cursor-pointer" />
                                  添加指标
                                </Typography.Link>
                              </>
                            )}
                          </Form.List>
                        </FormLayout>
                      </Col>
                    )
                  default:
                    return assertExhaustive(metricType)
                }
              }}
            </Form.Item>
          </Row>
        </Form>
      </Modal>
    </>
  )
}

export default MetricsCreate
