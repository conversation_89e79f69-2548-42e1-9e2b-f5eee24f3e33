import React, { useState, useRef, useCallback } from 'react'
import {
  App,
  Button,
  Popover,
  Space,
  Typography,
  Dropdown,
  type MenuProps,
  TableProps,
  Tooltip,
  Tag,
  Switch,
  Drawer,
  Form,
} from 'antd'
import { PlusOutlined, MoreOutlined } from '@ant-design/icons'
import { useBoolean, useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { Link } from 'react-router-dom'
import { get, cloneDeep } from 'lodash-es'
import GreyFooterModel from '@components/GreyFooterModel'
import { routerMap } from '@XEngineRouter/routerMap'
import { type MetricModelType } from '@shared/metric-types'
import request from '@shared/xengine-axios'
import { askBIApiUrls } from '@shared/url-map'
import { REFRESH_CRON_MODE_MAP } from '@constant/enums'
import { downloadFile } from '@shared/common-utils'
import { SvgIcon, warningIcon, metricmodelIcon } from 'src/client/components/SvgIcon'
import CountSearchTable, { type ApiArgsType } from 'src/client/components/CountSearchTable'
import ReadingMetricModelDrawer from 'src/client/pages/MetricStore/components/ReadingMetricModelDrawer'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import AdminPage from 'src/client/components/AdminPage'
import { tableTypeMap } from 'src/chrome/AskBI/src/constants'
import { customerFilterValue, customerIsSupportType } from 'src/shared/customer-resolver'
import MetricModelMaterialize, {
  formatMetricModelMaterializeFormData,
  initMetricModelMaterializeFormData,
} from 'src/client/pages/MetricStore/components/MetricModelMaterialize'
import MetricModelCreate from 'src/client/pages/MetricStore/components/CreateMetricModelDrawer/CreateMetricModelDrawer'

export default function List() {
  const { message } = App.useApp()
  const [materializeForm] = Form.useForm()
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const currentDeleteModelRef = useRef<MetricModelType>()
  const actionRef = useRef<{ reload: (args: ApiArgsType) => void; refresh: () => void }>()
  const [isMetricModelCreateEdit, setIsMetricModelCreateEdit] = useState(false)
  const [createModelType, setCreateModelType] = useState<'createMetricModel' | 'createNestModel'>('createMetricModel')
  const [previewMetricModelDrawerOpen, setPreviewMetricModelDrawerOpen] = useState(false)
  const [currentPreviewModelName, setCurrentPreviewModelName] = useState('')
  const [uploadCSVModalOpen, setUploadCSVModalOpen] = useState(false)
  const [metricModelMaterializeDrawerOpen, metricModelMaterializeDrawerOpenOps] = useBoolean(false)

  const { loading: deleteModelLoading, run: deleteModel } = useRequest(
    async () => {
      const name = currentDeleteModelRef.current?.name
      if (name) {
        return request.delete(askBIApiUrls.model.delete, {
          params: { modelName: name },
        })
      }
    },

    {
      manual: true,
      onSuccess() {
        message.success('删除成功')
        actionRef.current?.refresh()
        setDeleteModalOpen(false)
      },
      onError(e) {
        message.error(e?.message || '删除失败')
      },
    },
  )

  const {
    loading: modelDetailLoading,
    run: getModelDetail,
    data: currentUpdateModel,
  } = useRequest(
    (name: string) => request.get<{ name: string }, MetricModelType>(askBIApiUrls.model.meta, { params: { name } }),
    {
      manual: true,
      onSuccess(data) {
        setCreateModelType('createMetricModel')
        if (metricModelMaterializeDrawerOpen) {
          materializeForm.setFieldsValue(initMetricModelMaterializeFormData({}, data))
        }
      },
    },
  )

  const { loading: updateMetricmodelLoading, run: updateMetricmodel } = useRequest(
    (values) => {
      return request.post(
        askBIApiUrls.semanticModel.modelUpdate,
        formatMetricModelMaterializeFormData(values, cloneDeep(currentUpdateModel)),
      )
    },
    {
      manual: true,
      onSuccess() {
        message.success('修改物化配置成功')
        metricModelMaterializeDrawerOpenOps.setFalse()
        actionRef.current?.refresh()
      },
      onError(e) {
        message.error(e?.message || '修改物化配置失败')
      },
    },
  )

  const { loading: downloadLoading, run: downloadMetricmodel } = useRequest(
    (modelName) => {
      const url = customerIsSupportType('isUseSimpleMetricmodel')
        ? askBIApiUrls.model.simpleModelExport
        : askBIApiUrls.model.modelExport
      return downloadFile(`${url}?modelName=${modelName}`, undefined, {
        onSuccess() {
          message.success(`${modelName}指标模型下载成功`)
        },
        onError(err) {
          message.error(err.message || `${modelName}指标模型下载失败`)
        },
      })
    },
    {
      manual: true,
    },
  )

  const metricModelColumns = [
    {
      title: '指标模型',
      dataIndex: 'name',
      render(name) {
        return (
          <div className="flex items-center">
            <SvgIcon icon={metricmodelIcon} className="mr-2 h-5 w-6" />
            <Typography.Link
              onClick={() => {
                setCurrentPreviewModelName(name)
                setPreviewMetricModelDrawerOpen(true)
              }}
            >
              {name}
            </Typography.Link>
          </div>
        )
      },
    },
    {
      title: '指标状态',
      dataIndex: 'available',
      render: (status: boolean) => {
        return status ? (
          <Tag color="green">正常</Tag>
        ) : (
          <Tooltip title="上游依赖表不完整，存在被删除的情况，请检查所有链路">
            <Tag color="red">异常</Tag>
          </Tooltip>
        )
      },
    },
    {
      title: '类型',
      dataIndex: 'computeType',
      width: 200,
      render(value) {
        return tableTypeMap[value as keyof typeof tableTypeMap]
      },
    },
    {
      title: '物化',
      dataIndex: 'publishMv',
      width: 180,
      render(publishMv: boolean, record) {
        const [catalogName, dbName, mvName] = get(record, 'qualifiedMvNames[0]')?.split('.') || []
        return (
          <Space>
            <Switch size="small" checked={publishMv} className="mr-1" disabled />
            <Typography.Link
              onClick={() => {
                metricModelMaterializeDrawerOpenOps.setTrue()
                getModelDetail(record.name)
              }}
            >
              修改
            </Typography.Link>
            {publishMv && (
              <Link
                to={`${routerMap.smartx.materialViewDetail.path}?${new URLSearchParams({
                  catalogName,
                  dbName,
                  mvName,
                }).toString()}`}
              >
                物化视图
              </Link>
            )}
          </Space>
        )
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
      render(value) {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
      },
    },
    {
      title: '操作',

      width: 60,
      render: (record) => {
        return (
          <Popover
            placement="top"
            content={
              <div className="w-[80px]">
                <Button
                  block
                  type="text"
                  onClick={() => {
                    setCurrentPreviewModelName(record.name)
                    setPreviewMetricModelDrawerOpen(true)
                  }}
                >
                  查看模型
                </Button>
                <Button
                  block
                  type="text"
                  danger
                  onClick={() => {
                    setDeleteModalOpen(true)
                    currentDeleteModelRef.current = record as MetricModelType
                  }}
                >
                  删除模型
                </Button>
                <Button
                  block
                  type="text"
                  onClick={() => {
                    setCreateModalOpen(true)
                    getModelDetail(record.name)
                    setIsMetricModelCreateEdit(true)
                  }}
                >
                  编辑模型
                </Button>
                <Button block type="text" loading={downloadLoading} onClick={() => downloadMetricmodel(record.name)}>
                  导出模型
                </Button>
              </div>
            }
          >
            <MoreOutlined className="cursor-pointer" />
          </Popover>
        )
      },
    },
  ] as TableProps['columns']

  const items: MenuProps['items'] = customerFilterValue('metricModelCreateTypeFilter', [
    {
      key: 'createMetricModel',
      label: (
        <span
          onClick={() => {
            setCreateModelType('createMetricModel')
            setCreateModalOpen(true)
            setIsMetricModelCreateEdit(false)
          }}
        >
          创建指标模型
        </span>
      ),
    },
    {
      key: 'createNestModel',
      label: (
        <span
          onClick={() => {
            setCreateModelType('createNestModel')
            setCreateModalOpen(true)
            setIsMetricModelCreateEdit(false)
          }}
        >
          创建嵌套模型
        </span>
      ),
    },
    {
      key: 'CSVUpload',
      label: (
        <span
          onClick={() => {
            setUploadCSVModalOpen(true)
          }}
        >
          CSV文件创建
        </span>
      ),
    },
  ])

  return (
    <AdminPage
      title="指标模型管理"
      extra={
        <Dropdown menu={{ items }} placement="bottomRight" arrow>
          <Button type="primary" icon={<PlusOutlined />}>
            指标模型
          </Button>
        </Dropdown>
      }
    >
      <CountSearchTable
        isFullList={false}
        actionRef={actionRef}
        placeholder="指标模型名称"
        api={useCallback((values) => {
          return request.get<{ current: number; pageSize: number }, { list: MetricModelType[]; total: number }>(
            askBIApiUrls.model.list,
            {
              params: { current: values.current, pageSize: values.pageSize, name: values.search },
            },
          )
        }, [])}
        className="mt-2"
        searchKey="name"
        tableProps={{
          rowKey: 'name',
          columns: metricModelColumns,
        }}
      />
      <MetricModelCreate
        loading={modelDetailLoading}
        isEdit={isMetricModelCreateEdit}
        values={currentUpdateModel}
        type={createModelType}
        drawerOpen={createModalOpen}
        setDrawerOpen={setCreateModalOpen}
        onSuccess={actionRef.current?.refresh}
      />
      <ReadingMetricModelDrawer
        open={previewMetricModelDrawerOpen}
        name={currentPreviewModelName}
        onClose={() => {
          setPreviewMetricModelDrawerOpen(false)
        }}
      />
      {/* 删除Modal */}
      <GreyFooterModel
        open={deleteModalOpen}
        title="确认删除"
        onOk={deleteModel}
        okButtonProps={{
          loading: deleteModelLoading,
        }}
        onCancel={() => setDeleteModalOpen(false)}
      >
        <div className="mb-[40px] mt-[20px] flex justify-center">
          <Space className="m-auto w-[300px]">
            <SvgIcon icon={warningIcon} className="h-6 w-6" />
            <div>删除模型后，用该模型的场景将失效</div>
          </Space>
        </div>
      </GreyFooterModel>

      <ValidatedUploadFile
        uploadApi={(uploadFile) => {
          const formData = new FormData()
          uploadFile && formData.append('file', uploadFile)
          const url = customerIsSupportType('isCsvMetricmodelUploadSimple')
            ? askBIApiUrls.model.simpleCSVUpload
            : askBIApiUrls.model.csvUpload
          return request.post(url, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          })
        }}
        onSuccess={async () => {
          message.success('创建成功')
          setUploadCSVModalOpen(false)
          await new Promise((resolve) => setTimeout(resolve, 1000))
          actionRef.current?.refresh && actionRef.current.refresh()
        }}
        modalProps={{
          title: 'CSV文件上传',
          open: uploadCSVModalOpen,
          onCancel: () => setUploadCSVModalOpen(false),
        }}
        size={50}
        acceptTypes={['.csv']}
        samples={[
          {
            fileName: '指标模型CSV创建示例.csv',
            fileUrl: `${askBIApiUrls.model.CSVUploadTemplate}?type=${customerIsSupportType('isUseSimpleMetricmodel') ? 'SIMPLE_DATA_MODEL' : 'BI_DATA_MODEL'}`,
          },
        ]}
      />
      <Drawer
        title="物化配置"
        open={metricModelMaterializeDrawerOpen}
        placement="bottom"
        height="100vh"
        loading={modelDetailLoading}
        classNames={{
          body: 'bg-[#F4F4F4] dark:bg-gray-800',
        }}
        extra={
          <Button
            type="primary"
            onClick={async () => updateMetricmodel(await materializeForm.validateFields())}
            loading={updateMetricmodelLoading}
          >
            确认物化
          </Button>
        }
        onClose={metricModelMaterializeDrawerOpenOps.setFalse}
      >
        <div className="m-auto w-[800px]">
          <Form
            form={materializeForm}
            layout="vertical"
            initialValues={{
              publishMv: false,
              refreshType: 'INCREMENT',
              refreshCronMode: REFRESH_CRON_MODE_MAP['SIMPLE'],
            }}
          >
            <MetricModelMaterialize metricmodel={currentUpdateModel as MetricModelType} form={materializeForm} />
          </Form>
        </div>
      </Drawer>
    </AdminPage>
  )
}
