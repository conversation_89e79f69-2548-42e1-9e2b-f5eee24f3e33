import React, { useState, useEffect } from 'react'
import { Button, Input, Modal, message, Skeleton, Form } from 'antd'
import { useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { useSet<PERSON>tom } from 'jotai'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, robotIcon, bookIcon, scenarioUserIcon } from 'src/client/components/SvgIcon'
import { requestDefaultDatasetAtom } from '../../AskBI/askBIAtoms'
import { RenderConfigForm } from '../../AskBI/ConfigManagement'

type PropsType = {
  scenarioId: string
  open: boolean
  onClose: () => void
}

type ScenarioType = {
  id: number
  agent: 'BI' | 'DOC'
  creationTime: string
  creationUser: string
  description: string
}
export default function ScenarioDetailModal(props: PropsType) {
  const requestDefaultDataset = useSet<PERSON>tom(requestDefaultDataset<PERSON>tom)
  const { open, onClose, scenarioId } = props
  const [values, setValues] = useState<ScenarioType>()
  const [isEdit, setIsEdit] = useState(false)
  const [configForm] = Form.useForm()

  const { run: getScenario, loading: getScenarioLoading } = useRequest(
    async () => {
      const res = await axios.get(askBIApiUrls.auth.scene.rest, { params: { id: scenarioId } })
      return res.data.data
    },
    {
      manual: true,
      onSuccess(data) {
        if (data) {
          setValues({
            id: data.id,
            agent: data.agent as ScenarioType['agent'],
            creationTime: new Date(data.createdAt).getTime().toString(),
            creationUser: data.createdBy ?? '',
            description: data.description ?? '',
          })
        }
      },
    },
  )

  useEffect(() => {
    if (open) {
      getScenario()
    }
  }, [open, getScenario])

  const { loading: updateScenarioLoading, run: updateScenario } = useRequest(
    async () => {
      const res = await axios.put(askBIApiUrls.auth.scene.rest, {
        id: scenarioId,
        description: values?.description,
      })
      if (res.data.code !== 0) throw new Error(res.data.msg)
    },
    {
      manual: true,
      onSuccess() {
        setIsEdit(false)
        getScenario()
        requestDefaultDataset()
        message.success('修改成功')
      },
      onError() {
        getScenario()
        message.error('修改失败')
      },
    },
  )

  const { run: updateSceneConfig } = useRequest(
    async () => {
      const config = await configForm.getFieldsValue()
      const res = await axios.post(askBIApiUrls.configManagement.updateByType, {
        type: 'scene',
        typeId: scenarioId,
        config,
      })
      if (res.data.code !== 0) throw new Error(res.data.msg)
    },
    { manual: true },
  )

  function cancelUpdate() {
    setValues(undefined)
    setIsEdit(false)
    onClose && onClose()
  }

  function renderFooterButtons() {
    if (isEdit) {
      return (
        <>
          <Button
            className="font-[PingFang SC] mr-[6px] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
            onClick={cancelUpdate}
          >
            取消
          </Button>
          <Button
            className="font-[PingFang SC] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
            loading={updateScenarioLoading}
            onClick={() => {
              updateScenario()
              updateSceneConfig()
            }}
          >
            保存
          </Button>
        </>
      )
    }

    return (
      <>
        <Button
          className="font-[PingFang SC] mr-[6px] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
          onClick={() => setIsEdit(true)}
        >
          编辑
        </Button>
        <Button
          className="font-[PingFang SC] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
          onClick={cancelUpdate}
        >
          关闭
        </Button>
      </>
    )
  }

  return (
    <Modal
      onCancel={cancelUpdate}
      className="grey-modal-footer"
      title={<div className="pl-5">场景详情</div>}
      open={open}
      footer={<div className="rounded-b bg-[#F4F4F4] px-5 py-3 dark:bg-gray-800">{renderFooterButtons()}</div>}
    >
      {getScenarioLoading ? (
        <Skeleton active className="p-5" />
      ) : (
        <div className="p-5">
          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">描述</div>
          </div>
          {!isEdit && <div className="mb-4 text-[13px] leading-5 text-[#575757]">{values?.description || '-'}</div>}
          {isEdit && (
            <Input.TextArea
              value={values?.description}
              className="mb-4"
              onChange={(e) => {
                if (values) {
                  setValues({
                    ...values,
                    description: e.target.value,
                  })
                }
              }}
            />
          )}
          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">关联agent</div>
          </div>
          {values?.agent === 'BI' && (
            <div className="mb-4 inline-flex items-center rounded-sm bg-[#E8F7FF] px-2">
              <SvgIcon icon={robotIcon} className="h-3 w-3" />
              <span className="ml-1 text-xs font-medium leading-5 text-[#3491FA]">数据洞察</span>
            </div>
          )}
          {values?.agent === 'DOC' && (
            <div className="mb-4 inline-flex items-center rounded-sm bg-[#E8FFFB] px-2">
              <SvgIcon icon={bookIcon} className="h-3 w-3" />
              <span className="ml-1 text-xs font-medium leading-5 text-[#0FC6C2]">知识洞察</span>
            </div>
          )}

          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">创建人</div>
          </div>

          <div className="mb-4 inline-flex rounded-sm bg-[#F1F1F1] px-[6px] py-[2px]">
            <SvgIcon icon={scenarioUserIcon} className="h-5 w-5" />
            <span className="ml-1 leading-5">{values?.creationUser}</span>
          </div>
          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">创建时间</div>
          </div>
          <span className="leading-5 text-[#575757]">
            {values?.creationTime ? dayjs(values?.creationTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </span>

          <div className="mt-8">
            <RenderConfigForm type="scene" typeId={scenarioId} form={configForm} disabled={!isEdit} />
          </div>
        </div>
      )}
    </Modal>
  )
}
