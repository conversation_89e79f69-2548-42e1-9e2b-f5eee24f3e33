import React, { useState, useCallback } from 'react'
import { App, Radio } from 'antd'
import { useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { EyeOutlined } from '@ant-design/icons'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'
import { MetricModelType } from 'src/shared/metric-types'
import ReadingMetricModelDrawer from 'src/client/pages/MetricStore/components/ReadingMetricModelDrawer'
import { SvgIcon, warningIcon } from 'src/client/components/SvgIcon'
import CountSearchTable from 'src/client/components/CountSearchTable'
import GreyFooterModal from 'src/client/components/GreyFooterModel'

type PropsType = {
  open: boolean
  onClose?: () => void
  onSuccess?: (modelNames: string[]) => void
  scenarioId: string
}
export default function ReplaceMetricModelModal(props: PropsType) {
  const { open, onClose, onSuccess, scenarioId } = props
  const { message } = App.useApp()
  const [selectModelNames, setSelectMetricModelNames] = useState<string[]>([])
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [previewName, setPreviewName] = useState('')
  const [replaceMetricModelConfirmOpen, setReplaceMetricModelConfirmOpen] = useState(false)
  // 先获取详情然后再发请求
  const { loading: updateScenarioLoading, run: updateScenario } = useRequest(
    async () => {
      const modelDetail = await axios
        .get(`${askBIApiUrls.model.meta}?name=${encodeURIComponent(selectModelNames[0])}`)
        .then((res) => {
          // 这里把 timeColumnDesc 都改成小写
          if (res.data.data.dataModelDesc.timeColumnDesc && res.data.data.dataModelDesc.timeColumnDesc.granularity) {
            res.data.data.dataModelDesc.timeColumnDesc.granularity =
              res.data.data.dataModelDesc.timeColumnDesc.granularity.toLowerCase()
          }
          return res.data.data
        })

      return request.put(askBIApiUrls.auth.scene.rest, {
        data: {
          id: scenarioId,
          modelNames: selectModelNames,
          tableName: modelDetail.dataModelDesc.factTable,
          timeDimensionFormat: modelDetail.dataModelDesc.timeColumnDesc?.format || '',
          timeDimensionType: modelDetail.dataModelDesc.timeColumnDesc?.type || '',
          timeGranularityMin: modelDetail.dataModelDesc.timeColumnDesc?.granularity || '',
        },
      })
    },
    {
      onSuccess() {
        message.success('模型替换成功')
        onSuccess && onSuccess(selectModelNames)
      },
      onError() {
        message.error('模型替换失败')
      },
      manual: true,
    },
  )

  return (
    <>
      {/* 替换模型确认 */}
      <GreyFooterModal
        open={replaceMetricModelConfirmOpen}
        onCancel={() => {
          setReplaceMetricModelConfirmOpen(false)
        }}
        title={'确认替换'}
        onOk={updateScenario}
        okButtonProps={{
          loading: updateScenarioLoading,
        }}
        styles={{
          body: { padding: '0 24px' },
        }}
      >
        <div className="mb-[40px] mt-[20px] flex items-center justify-center">
          <SvgIcon icon={warningIcon} className="mr-4 h-6 w-6" />
          <div>
            <p>替换模型可能造成已有指标无法查询</p>
            <p>请确认后替换</p>
          </div>
        </div>
      </GreyFooterModal>
      <GreyFooterModal
        width={840}
        onCancel={() => {
          onClose && onClose()
        }}
        title={'替换模型'}
        open={open}
        styles={{
          body: { padding: '0 24px' },
        }}
        onOk={() => setReplaceMetricModelConfirmOpen(true)}
      >
        <CountSearchTable
          isFullList={false}
          placeholder="指标模型名称"
          api={useCallback((values) => {
            return request.get<{ current: number; pageSize: number }, { list: MetricModelType[]; total: number }>(
              askBIApiUrls.model.list,
              {
                params: { current: values.current, pageSize: values.pageSize, name: values.search },
              },
            )
          }, [])}
          className="mt-2"
          searchKey="name"
          tableProps={{
            rowKey: 'name',
            scroll: { x: 'auto' },
            columns: [
              {
                title: '指标模型',
                dataIndex: 'name',
                render(name) {
                  return (
                    <div className="flex flex-1 items-center">
                      <Radio
                        checked={selectModelNames.includes(name)}
                        onClick={() => {
                          setSelectMetricModelNames([name])
                        }}
                      />
                      <span>{name}</span>
                      <div
                        className="flex cursor-pointer items-center text-[#503CE4]"
                        onClick={() => {
                          setPreviewName(name)
                          setDrawerOpen(true)
                        }}
                      >
                        <EyeOutlined className="ml-[16px] mr-[6px]" />
                        <span>预览</span>
                      </div>
                    </div>
                  )
                },
              },
              {
                title: '创建时间',
                dataIndex: 'createTime',
                width: 200,
                render(createTime: number) {
                  return <>{createTime ? dayjs(createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</>
                },
              },
            ],
          }}
        />
      </GreyFooterModal>
      <ReadingMetricModelDrawer open={drawerOpen} name={previewName} onClose={() => setDrawerOpen(false)} />
    </>
  )
}
