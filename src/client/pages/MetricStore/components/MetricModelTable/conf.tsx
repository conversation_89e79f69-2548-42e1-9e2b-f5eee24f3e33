import React, { useEffect, useContext, useState } from 'react'
import { Input, type GetRef, Form, Cascader, CascaderProps, Tag } from 'antd'
import { nanoid } from 'nanoid'
import { pick } from 'lodash'
import { type MetricFuncType, type MetricModelType } from '@shared/metric-types'
import { assertExhaustive, isAlphaNumberAndAlphaStart } from '@shared/common-utils'
import { NUMBER_COLUMN_TYPES, DATE_COLUMN_TYPES } from '@constant/enums'
import {
  columnStringIcon,
  SvgIcon,
  columnNumberIcon,
  columnCalendarIcon,
  dimensionIcon,
  measureIcon,
  timeIcon,
} from 'src/client/components/SvgIcon'
import { TimeGranularityMinDateOptions } from 'src/shared/common-types'
import { AvailableFormatColumnData } from 'src/shared/xengine-types'

enum OptionValueForColumnTypeDefine {
  Measures = 'measures',
  Dimensions = 'dimensions',
  TimeDimensions = 'timeDimensions',
}

export type FuncType = MetricFuncType & { tId: string }
export type MetricModelColumnType = {
  tId: string
  metricType?: 'dimensions' | 'measures'
  dimensionType?: 'CATEGORY' | 'TIME' | 'TIME_DEFAULT'
  timeFormatPattern?: string // 时间格式 only for timeDimensions
  granularity?: string // 时间粒度 only for timeDimensions
  columnName: string
  columnAlias?: string
  columnType: string
  formValue?: any
  dimensions?: (MetricFuncType & {
    pTId: string
    tId: string
    dimensionType?: string
    timeFormatPattern?: string
    granularity?: string
    // columnDesc: { name: string; type: string; comment: string }
  })[]
  disabled?: boolean
  measures?: (MetricFuncType & { pTId: string; tId: string })[]
  children?: (MetricFuncType & { pTId: string; tId: string })[]
}

export type VTableColumnType = {
  name: string
  comment?: string
  columnType: string
  dimensions?: MetricModelColumnType['dimensions']
  measures?: MetricModelColumnType['measures']
}

export type ColumnType =
  | 'VARCHAR'
  | 'CHAR'
  | 'DECIMAL'
  | 'TINYINT'
  | 'SMALLINT'
  | 'INTEGER'
  | 'BIGINT'
  | 'FLOAT'
  | 'DOUBLE'
  | 'DATETIME'
  | 'DATE'
  | 'TIME'
  | 'TIMESTAMP'

export type FormInstance<T> = GetRef<typeof Form<T>>

const EditableContext = React.createContext<FormInstance<any> | null>(null)

// 根据getListIndex获取的indexes从列表中获取值
export function getValueFromMetricModelTableData(data: MetricModelColumnType[], indexes: number[], key: string) {
  if (!data || !indexes || !key) {
    return undefined
  }
  const [pIndex, childIndex] = indexes

  const row = data[pIndex]
  if (!row) {
    return undefined
  }
  if (childIndex === undefined) {
    return row[key as keyof typeof row]
  }

  const metricAttr = row.metricType
  const functions = metricAttr && row[metricAttr]
  const childRow = functions && functions[childIndex]
  return childRow && childRow[key as keyof typeof childRow]
}

// 根据表格的tId获取在列表中的index值
export const getListIndex = (
  record: { pTId?: string; tId: string; [key: string]: any },
  fullData?: MetricModelColumnType[],
) => {
  if (!record || !record.tId) {
    return []
  }
  const findIds = [record.pTId, record.tId]
  let target = fullData || []
  const responseIndexes: number[] = []
  findIds.forEach((id) => {
    if (id) {
      const index = target.findIndex((item) => item.tId === id)
      if (index >= 0) {
        const item = target[index]
        const metricType = item.metricType
        target = item[metricType as keyof typeof item] || []
        responseIndexes.push(index)
      }
    }
  })
  return responseIndexes
}

// todo: props的类型
export const EditableRow = (props: any) => {
  const [form] = Form.useForm()
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  )
}
export const EditableCell = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  fullData,
  isAvailable,
  ...restProps
}: {
  title: string
  editable: boolean
  children: React.ReactNode
  dataIndex: string
  // todo：类型推断修复
  record: MetricModelColumnType & { pTId: string }
  handleSave: (record: MetricModelColumnType) => void
  fullData: MetricModelColumnType[]
  isAvailable: boolean
}) => {
  const form = useContext(EditableContext)!
  let childNode = children
  const firstFunc = record?.[record?.metricType || 'measures']?.[0]
  const data = record && (record.pTId ? record : firstFunc ? firstFunc : record)
  const indexes = getListIndex(data as MetricModelColumnType, fullData)
  const value = getValueFromMetricModelTableData(fullData, indexes, dataIndex)

  const save = async () => {
    const values = await form.validateFields()
    handleSave({ ...record, ...values })
  }
  useEffect(() => {
    form.setFieldsValue({ [dataIndex]: value })
  }, [form, dataIndex, value])

  if (isAvailable) {
    childNode = (
      <Form.Item
        key={dataIndex}
        name={dataIndex}
        className="m-0"
        rules={[
          {
            validator(_, value) {
              if (dataIndex === 'name') {
                if (!value) {
                  return Promise.resolve()
                }
                const valid = isAlphaNumberAndAlphaStart(value)
                return valid ? Promise.resolve() : Promise.reject('ID需以数字字母下划线组成，并以字母开头')
              }
              return Promise.resolve()
            },
          },
        ]}
      >
        <Input
          // disabled={record.disabled}
          placeholder="点击编辑内容"
          variant="filled"
          onPressEnter={save}
          onBlur={save}
        />
      </Form.Item>
    )
  }
  return <td {...restProps}>{childNode}</td>
}

// 获取metricType
export function getMetricType(column: VTableColumnType): 'dimensions' | 'measures' | undefined {
  const measures = column?.measures as MetricFuncType[]
  const dimensions = column?.dimensions as MetricFuncType[]
  if (measures?.length > 0) {
    return 'measures'
  }
  if (dimensions?.length > 0) {
    return 'dimensions'
  }
  return undefined
}

// 格式化同义词
export function formatSynonymsText(synonymsText: string, synonymsNumber: number, maxShowNumber?: number) {
  const maxShow = maxShowNumber || 14
  if (synonymsText.length > maxShow) {
    return `${synonymsText.slice(0, maxShowNumber)}...${synonymsNumber}个`
  }
  return synonymsText
}

// 初始化数据
export function resolveInitColumnData(originColumnData: VTableColumnType[]): MetricModelColumnType[] {
  if (!originColumnData) {
    return []
  }
  const columns = originColumnData.map((column) => {
    const metricType = getMetricType(column)
    const columnTId = nanoid()
    const dimensions = (column.dimensions || []).map((item) => {
      return {
        ...item,
        tId: nanoid(),
        pTId: columnTId,
      }
    })
    const measures = (column.measures || []).map((item) => {
      return {
        ...item,
        tId: nanoid(),
        pTId: columnTId,
      }
    })
    const childrenMetricFuncInfo = metricType && (metricType === 'dimensions' ? dimensions.slice(1) : measures.slice(1))
    const firstDimension = dimensions[0] || {}
    return {
      tId: columnTId,
      columnName: column.name,
      columnType: column.columnType,
      metricType,
      dimensions: dimensions,
      measures: measures,
      children: childrenMetricFuncInfo,
      columnAlias: column.comment,
      dimensionType: firstDimension.dimensionType,
      timeFormatPattern: firstDimension.timeFormatPattern,
      granularity: firstDimension.granularity,
    }
  }) as MetricModelColumnType[]
  return columns
}

// 渲染列的icon
export const renderColumnTypeIcon = (type: string) => {
  const columnTypeUpperCase = type?.toUpperCase() || ''
  if (DATE_COLUMN_TYPES.find((item) => columnTypeUpperCase.includes(item))) {
    return <SvgIcon icon={columnCalendarIcon} className="h-5 w-5" />
  }
  if (NUMBER_COLUMN_TYPES.find((item) => columnTypeUpperCase.includes(item))) {
    return <SvgIcon icon={columnNumberIcon} className="h-5 w-5" />
  }
  return <SvgIcon icon={columnStringIcon} className="h-5 w-5" />
}

const timeGranularityOrder = ['YEAR', 'MONTH', 'DAY', 'HOUR']

const timeOrder = ['S', 's', 'H', 'h', 'D', 'd', 'MM', 'Y', 'y']
export function formatAvailableMaterializeTimeColumn(data: AvailableFormatColumnData['availableFormats']) {
  // todo：暂时使用这个变量，等bi侧(kongsa)支持小时时间粒度后使用TimeGranularityMinDateOptions
  const tempTimeGranularityMinDateOptions = [
    {
      value: 'HOUR',
      label: '小时',
    },
    {
      value: 'DAY',
      label: '日',
    },
    {
      value: 'MONTH',
      label: '月',
    },
    {
      value: 'YEAR',
      label: '年',
    },
  ]
  return (data ? tempTimeGranularityMinDateOptions : []).map((i) => {
    const start = timeGranularityOrder.findIndex((t) => t === i.value)
    const supportTimeGranularity = timeGranularityOrder.slice(start)
    const children = data
      ?.filter((d) => supportTimeGranularity.includes(d.minGranularity))
      ?.map((d) => {
        const label = `${d.displayName} ${d.biSupported ? '(BI)' : ''}`
        return {
          value: d.displayName,
          label,
        }
      })
      ?.sort((a, b) => {
        const orderA = timeOrder.findIndex((d) => a.value.includes(d))
        const orderB = timeOrder.findIndex((d) => b.value.includes(d))
        return orderB - orderA
      })
    const res = {
      label: i.label,
      value: i.value,
    } as Option
    if (children?.length) {
      res.children = children
    }
    return res
  })
}
export function validateMetricModeTableData() {}

const timeFormatOptions = {
  day: ['yyyyMMdd', 'yyyy-MM-dd', 'yyyy/MM/dd', 'yyyy_MM_dd'],
  month: ['yyyyMM', 'yyyy-MM', 'yyyy/MM', 'yyyyMMdd', 'yyyy-MM-dd', 'yyyy/MM/dd', 'yyyy_MM_dd'],
  year: ['yyyy', 'yyyyMM', 'yyyy-MM', 'yyyy/MM', 'yyyyMMdd', 'yyyy-MM-dd', 'yyyy/MM/dd', 'yyyy_MM_dd'],
}
export const timeGranularity = TimeGranularityMinDateOptions.map((i) => {
  return {
    ...i,
    value: i.value.toUpperCase(),
    children: timeFormatOptions[i.value].map((i) => {
      return { value: i, label: i }
    }),
  }
})

const columnTypeOptions: Option[] = [
  {
    value: OptionValueForColumnTypeDefine.Measures,
    label: '度量',
  },
  {
    value: OptionValueForColumnTypeDefine.Dimensions,
    label: '类目维度',
  },
  {
    value: OptionValueForColumnTypeDefine.TimeDimensions,
    label: '时间维度',
    children: timeGranularity,
  },
] as const
function countFuncs(data: MetricModelColumnType[]) {
  let measureCnt = 0
  let categoryDimensionCnt = 0
  let timeDimensionCnt = 0
  ;(data || []).forEach((item) => {
    const metricType: MetricModelColumnType['metricType'] = item.metricType
    const funcs = metricType && item[metricType]
    const cnt = funcs?.length || 0
    switch (metricType) {
      case 'measures':
        measureCnt += cnt
        break
      case 'dimensions':
        if (item.dimensionType === 'CATEGORY') {
          categoryDimensionCnt += cnt
        } else if (item.dimensionType === 'TIME') {
          timeDimensionCnt += cnt
        }
        break
      case undefined: // 因为历史定义是可以为 undefined 的
        console.info(`Unhandled metric type: ${metricType}`)
        break
      default:
        return assertExhaustive(metricType)
    }
  })
  return {
    measureCnt,
    categoryDimensionCnt,
    timeDimensionCnt,
  }
}
export function renderCountTableHeader(headerType: 'detail' | 'count', data: MetricModelColumnType[]) {
  if (headerType === 'count') {
    const { measureCnt, categoryDimensionCnt, timeDimensionCnt } = countFuncs(data)
    return (
      <div className="mb-4 flex items-center">
        <div className="mr-2 text-[13px] leading-5 text-[#575757]">已定义</div>
        <div className="mr-2 flex items-center rounded bg-[#D5DDF3] p-1">
          <SvgIcon icon={measureIcon} className="mr-1 h-4 w-4" />
          <div className="text-[#1B3373]">
            <span className="mr-2">度量</span>
            <span className="font-medium">{measureCnt}</span>
          </div>
        </div>
        <div className="mr-2 flex items-baseline rounded bg-[#D0F2EA] p-1">
          <SvgIcon icon={timeIcon} className="mr-1 h-4 w-4" />
          <div className="flex text-[#0D7254]">
            <span className="mr-2">时间维度</span>
            <span className="font-medium">{timeDimensionCnt}</span>
          </div>
        </div>
        <div className="flex items-center rounded bg-[#D0F2EA] p-1">
          <SvgIcon icon={dimensionIcon} className="mr-1 h-4 w-4" />
          <div className="flex text-[#0D7254]">
            <span className="mr-2">类目维度</span>
            <span className="font-medium">{categoryDimensionCnt}</span>
          </div>
        </div>
      </div>
    )
  }
}
function getLastDotStr(str: string) {
  const initStr = typeof str === 'string' && str ? str : ''
  return initStr.split('.').pop()
}
// 格式化metricModelMeta的值给MetricModelTable使用
export function formatMetricModelMetaToTable(data: MetricModelType, isResolvedColumns?: boolean): VTableColumnType[] {
  const { measures = [], dimensions = [] } = data?.dataModelDesc || {}
  const map = new Map()
  ;(measures || []).forEach((initMeasure) => {
    const columnDesc = initMeasure.columnDesc
    const columnName = getLastDotStr(columnDesc?.name)
    if (columnName) {
      let column = map.get(columnName)
      if (!column) {
        column = {
          name: columnName,
          comment: columnDesc.comment,
          columnType: columnDesc.type,
          measures: [],
        }
        map.set(columnName, column)
      }
      if (!column.measures) {
        column.measures = []
      }
      const measure = pick(initMeasure, [
        'name',
        'alias',
        'function',
        'comment',
        'formatTemplate',
        'synonyms',
        'nameZh',
        'columnDesc',
        'createMetric',
      ])
      column.measures.push(measure)
    }
  })
  ;(dimensions || []).forEach((initMeasure) => {
    const columnDesc = initMeasure.columnDesc
    const columnName = getLastDotStr(columnDesc?.name)
    if (columnName) {
      let column = map.get(columnName)
      if (!column) {
        column = {
          name: columnName,
          comment: columnDesc.comment,
          columnType: columnDesc.type,
          dimensions: [],
        }
        map.set(columnName, column)
      }
      if (!column.dimensions) {
        column.dimensions = []
      }
      const dimension = pick(initMeasure, [
        'name',
        'alias',
        'function',
        'comment',
        'formatTemplate',
        'synonyms',
        'nameZh',
        'granularity',
        'timeDimension',
        'timeFormatPattern',
        'dimensionType',
        'columnDesc',
        'filterSwitch',
      ])
      column.dimensions.push(dimension)
    }
  })
  if (isResolvedColumns) {
    const columns = data.columns || []
    const resolvedColumns = columns
      .filter((col) => !map.has(col.name))
      .map((col) => ({
        name: col.name,
        comment: col.comment,
        columnType: col.columnType,
      }))
    return [...map.values()].concat(resolvedColumns)
  }
  return [...map.values()]
}

export const displayRender: CascaderProps<Option>['displayRender'] = (labels, selectedOptions = []) => {
  return (
    <div className="flex items-center">
      {labels.map((label, i) => {
        const option = selectedOptions[i]
        // 因为 selectedOptions 默认为空 所以需要添加可选链
        switch (option?.value) {
          case OptionValueForColumnTypeDefine.Measures:
            return (
              <>
                <SvgIcon icon={measureIcon} className="mr-1 h-4 w-4" />
                <span key={option.value}>{label}</span>
              </>
            )
          case OptionValueForColumnTypeDefine.Dimensions:
            return (
              <>
                <SvgIcon icon={dimensionIcon} className="mr-1 h-4 w-4" />
                <span key={option.value}>{label}</span>
              </>
            )
          case OptionValueForColumnTypeDefine.TimeDimensions:
            return (
              <div key={option.value} className="mr-2 flex items-center">
                <SvgIcon icon={timeIcon} className="mr-2 h-5 w-5" />
                {label}
              </div>
            )
          default:
            return <Tag key={option?.value}>{label}</Tag>
        }
      })}
    </div>
  )
}
interface Option {
  value: string
  label: string
  children?: Option[]
}
// 渲染metricType的列
export function MetricTypeColumn(props: {
  record: MetricModelColumnType
  isEdit: boolean
  availableTimeColumnData?: AvailableFormatColumnData[]
  onChange: (
    metricType: 'dimensions' | 'measures' | '',
    record: MetricModelColumnType,
    dimensionType?: 'CATEGORY' | 'TIME' | 'TIME_DEFAULT',
    granularity?: string,
    timeFormatPattern?: string,
  ) => void
}) {
  const { record, isEdit, onChange } = props
  const { metricType } = record
  const [value, setValue] = useState<string[]>()
  const [timeFormatPattern, setTimeFormatPattern] = useState<string>()
  const [dimensionType, setDimensionType] = useState<string>()
  const [granularity, setGranularity] = useState<string>()
  useEffect(() => {
    let initValue: string[] = []
    let dimensionType
    let granularity
    let timeFormatPattern
    if (metricType === 'dimensions' && record.dimensions && record.dimensions.length > 0) {
      dimensionType = record.dimensions[0].dimensionType
      granularity = record.dimensions[0].granularity
      timeFormatPattern = record.dimensions[0].timeFormatPattern
      setDimensionType(dimensionType)
      setTimeFormatPattern(timeFormatPattern)
      setGranularity(granularity)
    }
    if (record.metricType === 'measures') {
      initValue = ['measures']
    } else if (record.metricType === 'dimensions') {
      const cur = record?.dimensions?.[0] ?? record
      if (cur.dimensionType === 'CATEGORY') {
        initValue = ['dimensions']
      } else if (cur.dimensionType === 'TIME' || cur.dimensionType === 'TIME_DEFAULT') {
        initValue = ['timeDimensions', cur.granularity || '', cur.timeFormatPattern || '']
      }
    }
    setValue(initValue)
  }, [record, metricType])

  const options = columnTypeOptions.map((i) => {
    if (i.value === OptionValueForColumnTypeDefine.TimeDimensions) {
      return {
        ...i,
        children: formatAvailableMaterializeTimeColumn(
          props.availableTimeColumnData?.find((d) => d.columnName === record.columnName)?.availableFormats,
        ),
      }
    }
    return i
  })

  if (isEdit && record.columnName) {
    return (
      <div className="flex items-center">
        <Cascader
          disabled={record.disabled}
          style={{ width: '100%' }}
          options={options}
          value={value}
          expandTrigger="hover"
          onChange={(value) => {
            if (!value) {
              // 删除 度量维度的时候 清空整行数据
              onChange('', record)
            } else if (value[0] === 'measures') {
              onChange('measures', record)
            } else if (value[0] === 'dimensions') {
              onChange('dimensions', record, 'CATEGORY')
            } else if (value[0] === 'timeDimensions') {
              onChange('dimensions', record, 'TIME', value[1], value[2])
            }
          }}
          displayRender={displayRender}
          placeholder="请选择"
        />
      </div>
    )
  }
  return (
    <>
      {metricType === 'dimensions' && dimensionType === 'CATEGORY' && (
        <div className="flex items-center">
          <SvgIcon icon={dimensionIcon} className="mr-2 h-4 w-4" />
          类目维度
        </div>
      )}
      {metricType === 'dimensions' && (dimensionType === 'TIME' || dimensionType === 'TIME_DEFAULT') && (
        <div className="flex flex-wrap items-center gap-1">
          <SvgIcon icon={timeIcon} className="h-5 w-5" />
          {dimensionType === 'TIME_DEFAULT' && '主'}时间维度 <Tag>{granularity}</Tag> <Tag>{timeFormatPattern}</Tag>
        </div>
      )}
      {metricType === 'measures' && (
        <div className="flex items-center">
          <SvgIcon icon={measureIcon} className="mr-2 h-4 w-4" />
          度量
        </div>
      )}
    </>
  )
}
