/**
 * @description Login page
 */
import React, { CSSProperties, useEffect, useState } from 'react'
import { Button, Divider, Input, message, Form } from 'antd'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAtom, useSet<PERSON>tom, useAtomValue } from 'jotai'
import debounce from 'lodash/debounce'
import clsx from 'clsx'
// import LoginBg from '/img/login-bg.webp'
import LoginBg from '/img/new-bg.webp'
import HuaxiaLoginBg from '/img/login-huaxia.webp'
import { SvgIcon, dipeakMobileIcon, loginProxyIcon, xengineLogo } from '@components/SvgIcon'
import { ASK_BI_BASE } from 'src/shared/url-map'
import { chatPath, CompanyName, HUAXIA_APP_ID, IS_H5, RecordNumber, XEngineHomePath } from 'src/shared/constants'
import { login, IS_HUAXIA } from 'src/client/utils'
import CustomerShowWrap from '../components/CustomerShowWrap'
import { brandInfoAtom, envAtom, loginAppIdAtom } from './AskBI/askBIAtoms'
// import YuanjingLogo from '/img/yuanjingLogo.webp'
// import YangtzeComputing from '/img/yangtze-computing-logo.webp'
// import LoginSloganLogo from '/img/login-slogan-logo.svg'
import './Login.css'
import { appearanceAtom } from './System/Appearance/appearanceSettingAtom'
import { LogoType } from './System/Appearance/constant'

const hashToMessageMapping = {
  '#no-auth-code': 'auth code 不存在',
  '#token-verify-failed': 'token校验错误',
  '#token-parse-failed': 'token解析失败',
  '#account-invalid': '账号失效',
  '#login-failed': '登录失败，请重试',
} as const

export default function Login() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [form] = Form.useForm()
  const brandInfo = useAtomValue(brandInfoAtom)
  const setLoginAppId = useSetAtom(loginAppIdAtom)
  const [showLoading, setShowLoading] = useState<boolean>(false)
  const [env] = useAtom(envAtom)
  const appId = searchParams.get('appId')

  const isYuanjing = appId === 'yuanjing'
  const isYangtzeComputing = appId === 'yangtze-computing'
  const defaultHomePath = env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XEngineHomePath : chatPath

  // const logoSrc = isYuanjing ? YuanjingLogo : brandInfo?.logo
  // 是否显示联合logo 如果是长江计算的，就显示联合logo
  // const [showJointLogo, _setShowJointLogo] = useState<boolean>(true)
  // const [showJointLogo, _setShowJointLogo] = useState<boolean>(isYangtzeComputing)

  const appearanceConfig = useAtomValue(appearanceAtom)

  useEffect(() => {
    const hashValue = window.location.hash
    const messageContent = hashToMessageMapping[hashValue as keyof typeof hashToMessageMapping]
    // hash存在并且命中了message key
    if (hashValue && messageContent) {
      message.error(messageContent).then(() => {
        history.pushState({}, document.title, window.location.pathname)
      })
    }
  }, [])

  // 确认登录
  const handleLogin = async (params: { username: string; password: string; proxy: string }, hostname: string) => {
    try {
      const data = await login({ ...params, hostname })
      setShowLoading(false)
      if (data && data.username) {
        message.success('登录成功')
        if (IS_HUAXIA) {
          setLoginAppId(HUAXIA_APP_ID)
          window.location.href = `${ASK_BI_BASE}${defaultHomePath}`
          return
        }
        await new Promise((resolve) => setTimeout(resolve, 800))
        navigate(`${ASK_BI_BASE}${defaultHomePath}`)
      }
    } catch (error: any) {
      setShowLoading(false)
      console.error('handleLogin =', error)
      message.error(error?.message ?? error?.toString() ?? '未知错误')
    }
  }

  // 表单点击确认
  const onFinish = async () => {
    !IS_H5 && (await form.validateFields())
    setShowLoading(true)
    const fields = form.getFieldsValue()
    const { username, password } = fields
    if (username && password) {
      // 调用登录接口
      handleLogin(fields, window.location.hostname)
    } else {
      message.error('请输入账号和密码')
    }
  }

  const handlePufaLogin = () => {
    const redirectSsoUrl = env?.VITE_AUTH_SPDB_LOGIN
    redirectSsoUrl && (window.location.href = redirectSsoUrl)
  }

  // 表单确认按钮防抖
  const debouncedOnFinish = debounce(onFinish, 500)

  const renderForm = () => {
    return (
      <div
        className={clsx(
          'login-form flex w-full flex-1 flex-col items-center justify-center justify-self-center p-6 md:w-[500px]',
          {
            'mx-7': !IS_HUAXIA,
          },
        )}
      >
        <div className="w-[353px] rounded-xl bg-white p-9 sm:w-[500px]">
          <div className="mb-4 flex justify-center">
            {appearanceConfig.logo && appearanceConfig.logoType !== LogoType.MULTI ? (
              <img className="h-6" src={appearanceConfig.logo} alt="" />
            ) : (
              <>
                {env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? (
                  <SvgIcon icon={xengineLogo} className="h-6 w-[138px]" />
                ) : (
                  <SvgIcon icon={dipeakMobileIcon} className="h-6 w-[138px]" />
                )}
              </>
            )}
            {appearanceConfig.logo && appearanceConfig.logoType === LogoType.MULTI ? (
              <img className="ml-4 h-6" src={appearanceConfig.logo} alt="" />
            ) : null}
            {/* <img
              src={logoSrc}
              alt=""
              className={clsx('z-10 w-auto cursor-pointer', {
                'h-10': brandInfo && brandInfo.brandName === 'China Telecom',
                'h-6': brandInfo && brandInfo.brandName === 'DIPEAK',
                'h-9': isYuanjing,
              })}
            /> */}
          </div>
          <p className="mb-9 text-center text-2xl font-semibold text-black">登录</p>
          <Form form={form} name="basic" onFinish={debouncedOnFinish} autoComplete="off">
            <Form.Item name="username" rules={[{ required: true, message: '请输入账号' }]}>
              <Input placeholder="输入账号" autoComplete="username" size="large" maxLength={30} />
            </Form.Item>
            <Form.Item name="password" rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password
                placeholder="输入密码"
                autoComplete="current-password"
                size="large"
                maxLength={30}
                suffix={<SvgIcon icon={loginProxyIcon} className={`h-5 w-5 text-[#8E8E93]`} />}
              />
            </Form.Item>
            <Form.Item className="login-btn">
              <Button
                type="primary"
                htmlType="submit"
                className="w-full font-bold"
                loading={showLoading}
                size="large"
                onKeyDown={debouncedOnFinish}
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
        <CustomerShowWrap type="pufaLogin">
          <div className="bottom-6 w-4/5 md:w-3/5">
            <Divider plain className="text-xs">
              浦发银行
            </Divider>
            <Button className="w-full border-[#6a58ec] font-normal" size="large" onClick={handlePufaLogin}>
              <div className="flex items-center justify-center">
                <span>UIAS登录</span>
              </div>
            </Button>
          </div>
        </CustomerShowWrap>
      </div>
    )
  }

  const defaultUI = (
    <div className="flex h-screen w-screen md:justify-center xl:justify-start">
      {/* <div className="header top-0 flex h-20 w-full items-center px-8">
        {showJointLogo && (
          <img
            src={YangtzeComputing}
            alt=""
            className={clsx('z-10 ml-3 w-auto cursor-pointer', {
              'h-[42px]': isYangtzeComputing,
            })}
          />
        )}
      </div> */}

      <div className="xl:flex-start relative z-10 flex h-full w-full flex-col items-center md:bg-transparent xl:w-[520px] xl:bg-white">
        {renderForm()}
        {brandInfo && brandInfo.brandName === 'DIPEAK' && !isYuanjing && !isYangtzeComputing && (
          <div className="footer bottom-4 justify-self-end py-4 text-center text-[#C4C4C4] opacity-50">
            <p className="text-xs">{`${CompanyName}｜${RecordNumber}`}</p>
          </div>
        )}
      </div>

      <div
        className="z-1 absolute bottom-0 top-0 h-full w-full flex-col items-center justify-end overflow-hidden bg-[#F7F9FB] md:right-0 md:flex xl:w-[--custom-width]"
        style={
          {
            '--custom-width': 'calc(100% - 520px)',
          } as CSSProperties
        }
      >
        <div
          className={clsx('absolute right-0 top-0 z-0 h-full w-full bg-cover bg-center')}
          style={{
            backgroundImage: `url('${LoginBg}')`,
          }}
        />
        {/* <div className="relative z-10 m-[50px] hidden justify-self-end rounded-2xl bg-white p-10 text-base text-black xl:block">
          <p className="mb-4">用AI洞察一切数据</p>
          {brandInfo && brandInfo.brandName === 'DIPEAK' && !isYuanjing && !isYangtzeComputing && (
            <div>
              <p>
                {`${brandInfo.companyName}的愿景是让数据智能像水电一样简单，企业面对巨量复杂 数据的洞察力突破，是我们的目标。`}
              </p>
              <div className="mt-6 flex items-center self-end justify-self-end">
                <div className="slash mr-3 h-0.5 w-[65px] bg-black" />
                <img src={LoginSloganLogo} alt="" />
              </div>
            </div>
          )}
        </div> */}
      </div>
    </div>
  )

  const huaXiaUI = (
    <div className="relative h-full w-full overflow-hidden bg-white shadow-lg">
      <div className="relative">
        <img src={HuaxiaLoginBg} alt="背景" className="h-full w-full object-contain" />
        <h1
          className="absolute left-1/2 top-24 -translate-x-1/2 whitespace-nowrap text-center text-5xl font-bold text-white"
          style={{ fontFamily: '"Alimama ShuHeiTi", sans-serif' }}
        >
          数据智能体
        </h1>
        <h1 className="absolute left-1/2 top-36 -translate-x-1/2 whitespace-nowrap text-center text-2xl font-bold text-white">
          Data Intelligence Agent
        </h1>
      </div>
      <div className="absolute top-60 w-full">
        <div className={'mx-8 rounded-3xl bg-white'}>{renderForm()}</div>
      </div>
      <div className="footer absolute bottom-12 left-1/2 -translate-x-1/2 whitespace-nowrap text-center text-xs text-[#C4C4C4]">
        <p>
          {CompanyName} | {RecordNumber}
        </p>
      </div>
    </div>
  )

  return (
    <div id="login-page" className="relative flex h-screen w-screen flex-col items-center bg-white">
      {IS_HUAXIA && IS_H5 ? huaXiaUI : defaultUI}
    </div>
  )
}
