import React, { useState } from 'react'
import { useAtom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import { ConverChat } from '@prisma/client'
import { App, Button, Dropdown, Input, Modal } from 'antd'
import clsx from 'clsx'
import axios from 'axios'
import { CheckCircleIcon, EllipsisVerticalIcon } from '@heroicons/react/24/outline'
import { APIResponse, ConverWithDataset, DatasetDatum, LlmType } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { useConversation } from 'src/client/hooks/useConversation'
import { BaseChat, chatsAtom, transformChat, useInitChats } from 'src/client/components/chats'
import TextTruncate from 'src/client/components/TextTruncate'
import { converDrawerConfigAtom, conversationsAtom } from '../conver-atoms/converAtoms'
import {
  cancelTokenSourceAtom,
  conversationId<PERSON>tom,
  currentDataset<PERSON>tom,
  isSubmitting<PERSON><PERSON>,
  llmType<PERSON><PERSON>,
  showChatHistory<PERSON>ist<PERSON>tom,
} from '../askBIAtoms'
import ConversationIconUrl from '/img/conversation.svg'

const Conversations = () => {
  const { getInitChats, initChats } = useInitChats()
  const { message } = App.useApp()
  const conversations = useAtomValue(conversationsAtom)
  const setShowChatHistory = useSetAtom(showChatHistoryListAtom)
  const setConverDrawerConfig = useSetAtom(converDrawerConfigAtom)
  const [currentConversationId, setConversationId] = useAtom(conversationIdAtom)
  const setChats = useSetAtom(chatsAtom)
  const setCurrentDataset = useSetAtom(currentDatasetAtom)
  const setLlmType = useSetAtom(llmTypeAtom)
  const setIsSubmitting = useSetAtom(isSubmittingAtom)
  const setCancelTokenSource = useSetAtom(cancelTokenSourceAtom)
  const { loadConversationList } = useConversation()

  /**
   * 用户切换conversation 将conversationId chats llmType currentDataset 设置成历史会话的默认值
   * 复位 cancelTokenSource isSubmitting
   */
  const handelChangeConversation = async (conversationId: string, llmType: LlmType, dataset: DatasetDatum) => {
    message.loading({
      content: `正在加载会话，请稍候……`,
      key: conversationId,
    })
    const converChatsResponse = await axios.get<APIResponse<ConverChat[]>>(
      askBIApiUrls.converChats.listByConversationId(conversationId),
    )
    const conversations = converChatsResponse.data.data
    const chats = (conversations ?? []).map((v) => transformChat(v.response)).filter(Boolean) as BaseChat[]
    setConversationId(conversationId)
    setLlmType(llmType)
    setCurrentDataset(dataset)
    setChats(getInitChats().concat(chats))
    setIsSubmitting(false)
    setCancelTokenSource(new AbortController())
    setShowChatHistory(false)
    setConverDrawerConfig({ open: false })
    message.success({
      content: `会话加载成功`,
      key: conversationId,
      duration: 0.5,
    })
  }

  const handleDeleteConversation = (conversationId: string) => {
    message.loading({
      content: `正在删除会话，请稍候……`,
      key: conversationId,
    })
    return axios
      .delete(askBIApiUrls.convers.delete(conversationId))
      .then(() => {
        message.success({
          content: `会话删除成功`,
          key: conversationId,
          duration: 0.5,
        })
      })
      .catch((error) => {
        message.error({
          content: `删除失败，请联系管理员处理`,
          key: conversationId,
          duration: 0.5,
        })
        console.error('删除会话遇到问题，错误信息为：', error)
      })
      .finally(() => {
        // 删除会话 => 加载列表 => 如果删除了当前正在
        loadConversationList()
        if (conversationId === currentConversationId) {
          setConversationId(null)
          initChats()
        }
      })
  }

  // if (isError) {
  //   return <div>获取会话历史列表失败，请联系管理员处理！</div>
  // }

  // if (isLoading) {
  //   return <div>Loading...</div>
  // }

  return (
    <div className="flex flex-col gap-2">
      <RenderConversationList
        conversations={conversations?.todayConversationListWithDataset || []}
        onChange={handelChangeConversation}
        onDelete={handleDeleteConversation}
        title="今天"
      />
      <RenderConversationList
        conversations={conversations?.thirtyDaysAgoConversationListWithDataset.slice(0, 10) || []}
        onChange={handelChangeConversation}
        onDelete={handleDeleteConversation}
        title="30天内"
      />
    </div>
  )
}

/** 渲染conversation list */
function RenderConversationList(props: {
  conversations: ConverWithDataset[]
  onChange: (conversationId: string, llmType: LlmType, dataset: DatasetDatum) => void
  onDelete: (conversationId: string) => void
  title: string
}) {
  const { conversations, onChange, onDelete, title } = props

  if (conversations.length === 0) {
    return
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="text-base font-bold">{title}</div>
      {conversations.map((conversation, index) => {
        return (
          <RenderConversationItem key={index} conversation={conversation} onChange={onChange} onDelete={onDelete} />
        )
      })}
    </div>
  )
}

/** 渲染conversation item */
function RenderConversationItem(props: {
  conversation: ConverWithDataset
  onChange: (conversationId: string, llmType: LlmType, dataset: DatasetDatum) => void
  onDelete: (conversationId: string) => void
}) {
  const { conversation, onChange, onDelete } = props
  const { message: antdMessage } = App.useApp()
  const [modal, contentHolder] = Modal.useModal()
  const [loading, setLoading] = useState(false)
  const currentConversationId = useAtomValue(conversationIdAtom)
  const [newTitle, setNewTitle] = useState(conversation.title)
  const [isModalShow, setIsModalShow] = useState(false)
  const { loadConversationList } = useConversation()

  const handleRename = () => {
    setIsModalShow(true)
  }

  const handleBlur = async () => {
    try {
      setLoading(true)
      await axios.put(askBIApiUrls.convers.update(conversation.id), { title: newTitle })
      setIsModalShow(false)
      setLoading(false)
      loadConversationList()
    } catch (error) {
      console.error('修改标题失败:', error)
      antdMessage.error('修改标题失败！')
    }
  }

  const onTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewTitle(e.target?.value)
  }

  const handleDelConfirm = () => {
    modal.confirm({
      title: '删除历史会话',
      content: `删除后会话历史记录都将清楚，无法恢复，请确认。`,
      okText: '确认',
      cancelText: '取消',
      centered: true,
      onOk: () => {
        onDelete(conversation.id)
      },
    })
  }

  const menus = [
    {
      label: (
        <Button onClick={handleRename} type="text">
          重命名
        </Button>
      ),
      key: '0',
    },
    {
      label: (
        <Button danger type="text" onClick={handleDelConfirm}>
          删除
        </Button>
      ),
      key: '1',
    },
  ]

  const handleChangeConversation = () => {
    onChange(conversation.id, conversation.llmType as LlmType, conversation.dataset)
  }

  return (
    <div
      className={clsx('conversation-item group flex h-[26px] cursor-pointer items-center justify-between rounded-md')}
    >
      <div className="flex flex-1 items-center justify-between">
        <div className="flex flex-1 items-center text-[#1C1C1E]" onClick={handleChangeConversation}>
          <div className="size-[28px]">
            <img src={ConversationIconUrl} alt="会话" />
          </div>
          <div className="flex-1 px-1 py-[3px] leading-[22px]">
            <TextTruncate className="w-[200px]">{conversation.title || '新聊天_未命名'}</TextTruncate>
          </div>
        </div>
        <div className="flex w-10 items-center justify-end text-slate-600">
          {conversation.id === currentConversationId && (
            <div>
              <CheckCircleIcon className="size-4 text-[#32D583]" />
            </div>
          )}
          <Dropdown menu={{ items: menus }}>
            <div className="flex items-center justify-center pl-2">
              <EllipsisVerticalIcon className="size-4" />
            </div>
          </Dropdown>
        </div>
      </div>

      <Modal
        title="重命名"
        centered
        open={isModalShow}
        onOk={handleBlur}
        confirmLoading={loading}
        onCancel={() => setIsModalShow(false)}
      >
        <div className="mb-2"> 请输入新标题：</div>
        <Input value={newTitle} maxLength={16} onChange={onTitleChange} />
      </Modal>
      {contentHolder}
    </div>
  )
}

// /**
//  * 将conver_chat转化为前端的chat数组
//  * 与chatBox的onSubmit共用数据转换方法
//  */
// function conversationChatsToChats(conversationChats: ConverChat[]): Chat[] {
//   const chatsArray: Chat[] = []

//   conversationChats.forEach((conversationChat) => {
//     const chat: Chat = {
//       id: conversationChat.id || '',
//       ask: {
//         role: 'user',
//         content: conversationChat.ask,
//         jsonContent: conversationChat.ask,
//       },
//       ans: [
//         {
//           role: 'assistant',
//           content: [],
//           sceneId: '',
//           status: 'success',
//           ansTime: conversationChat.updatedAt,
//         },
//       ],
//       selectedSceneId: '',
//       docAns: {
//         role: 'assistant',
//         content: [],
//         status: 'success',
//         ansTime: conversationChat.updatedAt,
//       },
//       askTime: conversationChat.createdAt,
//     }

//     const response = conversationChat.response as unknown as ChatResponse[]
//     const docResponse = conversationChat.docResponse as unknown as ChatResponse[]
//     const responseItem = response[0]
//     const docResponseItem = docResponse[0] as unknown as DocResultType
//     // 分为4种展示方式
//     if (conversationChat && docResponseItem) {
//       chat.docAns = {
//         role: 'assistant',
//         content: [{ type: 'doc-result', result: docResponseItem }],
//         status: ChatStatus.success,
//         ansTime: conversationChat.updatedAt,
//       }
//     } else {
//       let chatAns: AnsChatItem
//       if (responseItem.ready === true) {
//         chatAns = readyResponseToChatAns(responseItem, '')
//       } else if (responseItem.ready === false) {
//         chatAns = unreadyResponseToChatAns(responseItem, '')
//       } else {
//         chatAns = {
//           sceneId: '',
//           status: 'failure',
//           ansTime: conversationChat.updatedAt,
//           role: 'assistant',
//           content: [
//             {
//               type: 'text',
//               text: '未知的状态：' + JSON.stringify(responseItem),
//             },
//           ],
//         }
//       }
//       chat.ans = [chatAns]
//     }

//     chatsArray.push(chat)
//   })

//   return chatsArray
// }

export default Conversations
