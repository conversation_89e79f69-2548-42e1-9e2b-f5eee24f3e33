/**
 * @description 聊天页面
 */
import React, { useEffect, useRef, useState } from 'react'
import clsx from 'clsx'
import axios from 'axios'
import { Drawer, Tooltip, App, Modal, Input, Popover, Radio, RadioChangeEvent, Space } from 'antd'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { CloseOutlined, ExclamationCircleFilled } from '@ant-design/icons'
import DigitalHuman from 'src/client/pages/AskBI/LingjingHuman/DigitalHuman'
import { askBIApiUrls } from 'src/shared/url-map'
import ChatPopover from 'src/client/components/ChatPopover'
import ChartTooltip from 'src/client/components/ChartTooltip'
import ChartThemeToggle from 'src/client/components/ChartThemeToggle'
import {
  SvgIcon,
  changeServiceApiIcon,
  sideAddChartIcon,
  sideChartColorIcon,
  sideChartIntroductionIcon,
  sideChatHistoryIcon,
  sideSaveChartIcon,
  smallRocketIcon,
} from 'src/client/components/SvgIcon'
import QuestionnaireModal from 'src/client/components/QuestionnaireModal'
import { useInitChats } from 'src/client/components/chats'
import { IS_H5 } from 'src/shared/constants'
import { useConversation } from 'src/client/hooks/useConversation'
import {
  agentStructuredMessageAtom,
  chatsAtom,
  conversationIdAtom,
  currentParamsExtractApiAtom,
  isSubmittingAtom,
  paramsExtractUrlListAtom,
  showChatHistoryListAtom,
  questionnaireAtom,
  themeAtom,
  currentDatasetAtom,
  isProjectChosenAtom,
} from '../askBIAtoms'
import { askdocAnswerLinkKeyTagAtom, docPreviewOpenTagAtom } from '../../AskDoc/askDocAtoms'
import DocDetail from '../../AskDoc/DocDetail/DocDetail'
import ChatBox from './ChatBox'
import Conversations from './Conversations'

const sideIconClassName =
  'flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-borderColor bg-white dark:bg-slate-800 dark:hover:bg-slate-700'

function ChatPage({ className }: { className?: string }) {
  const { message: antdMessage } = App.useApp()

  const [isChatBoxMiniMode, _setIsChatBoxMiniMode] = useState<boolean>(false)
  const [conversationTitle, setConversationTitle] = useState<string>('')
  const [showEditConversationTitleModal, setShowEditConversationTitleModal] = useState<boolean>(false)
  const { initChats } = useInitChats()
  const chats = useAtomValue(chatsAtom)
  const [chatHistoryOpen, setChatHistoryOpen] = useAtom(showChatHistoryListAtom)
  const [conversationId, setConversationId] = useAtom(conversationIdAtom)
  const [docPreviewOpenTag, setDocPreviewOpenTag] = useAtom<boolean>(docPreviewOpenTagAtom)

  const theme = useAtomValue(themeAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const isProjectChosen = useAtomValue(isProjectChosenAtom)
  const setMessage = useSetAtom(agentStructuredMessageAtom)
  const setIsSubmitting = useSetAtom(isSubmittingAtom)
  const paramsExtractUrlList = useAtomValue(paramsExtractUrlListAtom)
  const askdocAnswerLinkKeyTag = useAtomValue(askdocAnswerLinkKeyTagAtom)
  const [popoverClicked, setPopoverClicked] = useState<boolean>(false)
  const [currentParamsExtractApi, setCurrentParamsExtractApi] = useAtom(currentParamsExtractApiAtom)
  const questionnaireValue = useAtomValue(questionnaireAtom)
  const [showQuestionnaireModal, setShowQuestionnaireModal] = useState<boolean>(false)

  const tableTreePopoverRef = useRef<{ closePopover: () => void }>(null)
  const chartThemePopoverRef = useRef<{ closePopover: () => void }>(null)
  const { loadConversationList } = useConversation()

  useEffect(() => {
    if (currentDataset) {
      loadConversationList()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDataset, isProjectChosen])

  useEffect(() => {
    if (askdocAnswerLinkKeyTag) {
      setDocPreviewOpenTag(true)
    }
  }, [askdocAnswerLinkKeyTag, setDocPreviewOpenTag])

  // 关闭Popover浮层
  const handleClosePopover = () => {
    tableTreePopoverRef.current?.closePopover()
    chartThemePopoverRef.current?.closePopover()
  }

  // 清空 conversation & chart ID , 初始化chats数组
  const clearHistory = () => {
    setConversationId(null)
    setMessage('')
    setIsSubmitting(false)
    initChats()
  }

  // 新建工作簿点击事件
  const handleNewChat = () => {
    Modal.confirm({
      title: `新建工作簿会清空当前聊天记录，是否确认？`,
      icon: <ExclamationCircleFilled />,
      okText: '确认',
      cancelText: '取消',
      centered: true,
      className: 'llm-toggle-confirm bg-white dark:bg-slate-900 dark:text-slate-100',
      onOk: clearHistory,
    })
  }

  const renderEditConversationTitleModal = (
    <Modal
      title="会话名称"
      open={showEditConversationTitleModal}
      onOk={async () => {
        if (conversationId && conversationTitle.length > 0) {
          try {
            await axios.put(askBIApiUrls.convers.update(conversationId), {
              isDraft: false,
              title: conversationTitle,
            })
            antdMessage.success('保存成功！')
          } catch (error: any) {
            antdMessage.error('保存会话历史失败：' + error.message)
          }
        } else {
          antdMessage.info('请输入会话名称！', 1)
        }
        setConversationTitle('')
        setShowEditConversationTitleModal(false)
      }}
      onCancel={() => {
        setConversationTitle('')
        setShowEditConversationTitleModal(false)
      }}
      okText="确认"
      cancelText="取消"
      maskClosable={false}
    >
      <Input
        className="w-[80%] dark:border-slate-400 dark:shadow-sm dark:hover:border-slate-300"
        value={conversationTitle}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
          setConversationTitle(event.target.value)
        }}
      />
    </Modal>
  )

  /**
   * Popover点击事件，点击后获取参数提取的 URL 列表
   */
  const handlePopoverClick = () => {
    if (paramsExtractUrlList) {
      const defaultApi = paramsExtractUrlList[0]
      const currentApi = localStorage.getItem('currentParamsExtractApi')?.replace(/"/g, '').trim() || ''
      setCurrentParamsExtractApi(paramsExtractUrlList.includes(currentApi) ? currentApi : defaultApi)
      setPopoverClicked(true)
    }
  }

  /**
   * 提参api列表点击事件，点击后切换提参服务
   */
  const handleParamsExtractApi = (item: string) => {
    setCurrentParamsExtractApi(item)
    antdMessage.success(`切换到 ${item} 提参服务！`)
    setPopoverClicked(false)
  }

  /**
   * 当单选框发生变化时触发该函数
   *
   * @param e 单选框变化事件对象
   */
  const onChange = (e: RadioChangeEvent) => {
    handleParamsExtractApi(e.target.value)
  }

  const renderPopoverContent = (
    <Radio.Group onChange={onChange} value={currentParamsExtractApi}>
      <Space direction="vertical" style={{ width: '100%' }}>
        {(paramsExtractUrlList || []).map((item) => (
          <Radio key={item} value={item}>
            {item}
          </Radio>
        ))}
      </Space>
    </Radio.Group>
  )

  const titleWithCloseIcon = (
    <div className="flex items-center justify-between">
      <p>切换提参服务</p>
      <CloseOutlined
        onClick={() => {
          setPopoverClicked(false)
        }}
      />
    </div>
  )

  const renderSidebarIcons = (
    <div className="hidden md:block">
      <div className="relative flex flex-col items-center gap-6 p-2">
        <div
          className={sideIconClassName}
          onClick={() => {
            handleNewChat()
          }}
        >
          <Tooltip placement="left" title="新工作簿">
            <SvgIcon className="h-6 w-6 text-black dark:text-white" icon={sideAddChartIcon} />
          </Tooltip>
        </div>

        <div
          className={sideIconClassName}
          onClick={() => {
            // conversationId存在 并且聊天历史长度>1
            if (conversationId && chats.length > 1) {
              setShowEditConversationTitleModal(true)
            } else {
              antdMessage.info('请先提问', 1)
            }
          }}
        >
          <Tooltip placement="left" title="保存工作簿">
            <SvgIcon className="h-6 w-6 text-black dark:text-white" icon={sideSaveChartIcon} />
          </Tooltip>
        </div>

        <div
          className={sideIconClassName}
          onClick={() => {
            setChatHistoryOpen(true)
          }}
        >
          <Tooltip placement="left" title="会话历史">
            <SvgIcon className="h-6 w-6 text-black dark:text-white" icon={sideChatHistoryIcon} />
          </Tooltip>
        </div>

        <div className="relative">
          <ChatPopover
            ref={chartThemePopoverRef}
            icon={
              <Tooltip placement="left" title="图表色系">
                <div className={sideIconClassName}>
                  <SvgIcon className="h-6 w-6 text-black dark:text-white" icon={sideChartIntroductionIcon} />
                </div>
              </Tooltip>
            }
          >
            <ChartThemeToggle onClosePopover={handleClosePopover} />
          </ChatPopover>
        </div>

        <div className="relative">
          <ChatPopover
            icon={
              <Tooltip placement="left" title="图表介绍">
                <div className={sideIconClassName}>
                  <SvgIcon className="h-6 w-6 text-black dark:text-white" icon={sideChartColorIcon} />
                </div>
              </Tooltip>
            }
          >
            <ChartTooltip theme={theme} />
          </ChatPopover>
        </div>

        {location.hostname === 'askbi-pre.dipeak.com' && (
          <div className="relative">
            <Popover
              placement="left"
              title={titleWithCloseIcon}
              content={renderPopoverContent}
              open={popoverClicked}
              onOpenChange={handlePopoverClick}
            >
              <div className={sideIconClassName} onClick={handlePopoverClick}>
                <SvgIcon className="h-5 w-5 text-black dark:text-white" icon={changeServiceApiIcon} />
              </div>
            </Popover>
          </div>
        )}
        {renderEditConversationTitleModal}
      </div>
    </div>
  )

  return (
    <main className={clsx('chat-page flex flex-grow place-items-stretch overflow-y-auto bg-[#F5F5F5]', className)}>
      <div
        className={clsx(
          'chat-page-inner mx-auto flex',
          docPreviewOpenTag ? 'max-w-full' : 'md:max-w-[calc(100%_-_76px)]',
        )}
      >
        <div
          className={clsx(
            'flex w-screen transition-all duration-700 ease-out',
            docPreviewOpenTag ? 'translate-x-0 overflow-hidden md:max-w-full' : 'translate-x-0 md:max-w-screen-xl',
          )}
        >
          <div
            className={clsx(
              'transition-all duration-700 ease-out',
              docPreviewOpenTag ? 'max-w-7/12 w-7/12' : 'w-0 overflow-hidden',
            )}
          >
            {docPreviewOpenTag && <DocDetail />}
          </div>
          <div
            className={clsx(
              'flex-grow flex-col transition-all duration-700 ease-out',
              docPreviewOpenTag ? 'w-5/12' : 'mx-auto w-full flex-col',
            )}
          >
            <ChatBox
              key="chat"
              isMiniMode={isChatBoxMiniMode}
              className={clsx('flex-1', docPreviewOpenTag && 'bg-[#F5F5F7]')}
            />
          </div>
          {docPreviewOpenTag && renderSidebarIcons}
        </div>
      </div>
      <DigitalHuman />
      <Drawer
        title="会话历史"
        placement="left"
        onClose={() => {
          setChatHistoryOpen(false)
        }}
        open={!IS_H5 && chatHistoryOpen}
      >
        {/* 只在非h5下展示 */}
        <Conversations />
      </Drawer>
      {!docPreviewOpenTag && renderSidebarIcons}
      {questionnaireValue && (
        <>
          <div
            className="absolute bottom-[78px] right-5 flex h-20 w-20 cursor-pointer flex-col items-center justify-center rounded-lg bg-[#6A58EC]"
            onClick={() => {
              setShowQuestionnaireModal(true)
            }}
          >
            <SvgIcon className="text-white" icon={smallRocketIcon} />
            <p className="mt-1 text-xs font-medium text-white">深入了解产品</p>
          </div>

          <QuestionnaireModal
            isQuestionnaireModalOpen={showQuestionnaireModal}
            setIsQuestionnaireModalOpen={setShowQuestionnaireModal}
          />
        </>
      )}
    </main>
  )
}

export default ChatPage
