import { useRequest } from 'ahooks'
import { Popover, Result, Skeleton, Table } from 'antd'
import axios from 'axios'
import clsx from 'clsx'
import React from 'react'
import Spark<PERSON>hart from 'src/client/components/Spark<PERSON>hart'
import TextHighlight from 'src/client/components/TextHighlight'
import TextTruncate from 'src/client/components/TextTruncate'
import { APIResponse } from 'src/shared/common-types'
import { assertExhaustive } from 'src/shared/common-utils'
import { Dimension, DimensionTypeNames, MetricConfig } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'

export default function DimensionDetail({
  dimensionName,
  metricConfig,
}: {
  dimensionName: string
  metricConfig: MetricConfig | null
}) {
  const dimension = metricConfig?.allDimensions.find((d) => d.name === dimensionName)
  if (dimension == null) {
    return <Result status="404" title="未找到维度" subTitle={`未找到维度 ${dimensionName}`} />
  }

  return <DimensionDetailCard dimension={dimension} tableName={metricConfig?.metricTableName} />
}

function DimensionDetailCard({ dimension, tableName }: { tableName?: string; dimension: Dimension }) {
  const {
    data: dimensionData,
    error: dimensionDataError,
    loading: isDimensionDataLoading,
  } = useRequest<{ xAxisData: string[]; data: number[] } | undefined, unknown[]>(async () => {
    if (dimension == null) {
      return
    }
    // 虚拟时间维度不需要请求数据
    if (dimension.type === 'virtual-time') {
      return
    }
    try {
      if (!tableName) {
        throw new Error('无法查询')
      }
      const response = await axios.get<APIResponse<{ xAxisData: string[]; data: number[] }>>(
        askBIApiUrls.dimension.trend(tableName, dimension.expr),
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取维度数据出错 error =', error)
    }
  })

  let synonymStr = ''
  if (dimension.synonyms && Array.isArray(dimension.synonyms) && dimension.synonyms.length > 0) {
    synonymStr = dimension.synonyms.join('，')
  }

  // 时间度量和维度度量
  const renderDimensionData = () => {
    const dimensionType = dimension.type
    switch (dimensionType) {
      case 'time':
      case 'categorical':
      case 'virtual-time':
      case 'time_default':
        return dimensionData == null ? (
          <div className={clsx('flex', `h-[50px]`, `w-[150px]`)} />
        ) : (
          <SparkChart
            chartType="bar"
            width={150}
            height={50}
            className="ml-auto"
            xAxisData={dimensionData.xAxisData}
            data={dimensionData.data}
          />
        )
      default:
        return assertExhaustive(dimensionType)
    }
  }

  if (dimensionDataError) {
    return <Result status="error" title="获取维度数据出错" subTitle={dimensionDataError.message} />
  }

  if (isDimensionDataLoading) {
    return <Skeleton active />
  }

  return (
    <div
      key={dimension.name}
      className="flex min-w-[300px] flex-col rounded-md p-2 ring-1 ring-gray-200 dark:bg-slate-700 dark:ring-gray-700 md:bg-white"
    >
      <div className="dimension-header flex flex-row truncate text-gray-600 dark:text-gray-300">
        <TextTruncate className="w-1/2" placement="topLeft">
          <TextHighlight text={dimension.name} />
        </TextTruncate>
        <div className="mx-2 my-1 w-[1px] bg-gray-200" />
        {synonymStr.length > 0 && (
          <Popover content={synonymStr} trigger="hover" placement="top">
            <div className="truncate">同义词：{synonymStr}</div>
          </Popover>
        )}
      </div>
      <div className="dimension-data flex gap-2 py-2">
        {/* <Popover content={dimension.label} trigger="hover" placement="top">
          <span className="truncate ">{dimension.label}</span>
        </Popover> */}
        <TextTruncate className="max-w-[50%] text-2xl font-bold" placement="topLeft">
          <TextHighlight text={dimension.label} />
        </TextTruncate>
        {renderDimensionData()}
      </div>
      <div className="dimension-detail-table">
        {dimensionData && (
          <Table
            bordered
            columns={[
              {
                title: '码值',
                dataIndex: 'name',
                key: 'name',
              },
              {
                title: '频率',
                dataIndex: 'value',
                key: 'value',
              },
            ]}
            dataSource={dimensionData.xAxisData
              .map((name, index) => {
                return {
                  key: index,
                  name: name,
                  value: dimensionData.data[index],
                }
              })
              .sort((a, b) => b.value - a.value)}
            size="small"
            pagination={{ hideOnSinglePage: true, pageSize: 5 }}
          />
        )}
      </div>
      <div className="dimension-footer flex flex-row gap-4 text-sm text-gray-600 dark:text-gray-300">
        <div className="flex whitespace-nowrap rounded-md bg-gray-200 px-2 dark:bg-gray-700">
          {DimensionTypeNames[dimension.type]}
        </div>
        <TextTruncate className="flex-grow">{dimension.description}</TextTruncate>
      </div>
    </div>
  )
}
