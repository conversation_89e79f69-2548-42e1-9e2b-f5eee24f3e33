/**
 * 存储chat相关的一些工具函数
 */
import { produce } from 'immer'
import axios from 'axios'
import dayjs from 'dayjs'
import {
  ChatResponse,
  ChatResponseError,
  ChatResponseQueryMetric,
  LlmType,
  OlapRow,
  RowsMetadata,
} from '@shared/common-types'
import { assertExhaustive, detectTimeType } from 'src/shared/common-utils'
import { DATE_ALIAS, MetricConfig, QueryParamsVerified } from 'src/shared/metric-types'
import { convertTimeToSpecificDate } from 'src/shared/time-utils'
import { askBIApiUrls } from 'src/shared/url-map'
import { getQueriedMetricNames, getUsefulData } from 'src/shared/utils/data-process-utils'
import {
  AnalyzeReportTypes,
  AnsChatItem,
  AssistantChatItem,
  AssistantMetricNotExistChatItem,
  ChatStatus,
} from 'src/client/utils'
import { MultiAgentData } from 'src/client/components/agent/utils/agent'
import { TryQueryToSqlData } from 'src/client/components/TryQueryToSql/try-query-to-sql'

function getMetricNotExist(queryParamsVerified: QueryParamsVerified): AssistantMetricNotExistChatItem | null {
  const { notExistGroupBys, notExistMetricNames } = queryParamsVerified.queryParams
  let names = [...(notExistGroupBys || []), ...(notExistMetricNames || [])]
  names = [...new Set(names)]

  if (names.length === 0) {
    return null
  }
  return {
    type: 'not-exist-metric',
    names,
  }
}

/**
 * 对数据做后置处理，immutable 操作
 * todo 在sql优化前,是否要把null|''的数据过滤掉, 因为是本来就不存在的数据
 */
export function postProcessChatResponse(rowsMetadata: RowsMetadata, rows: OlapRow[]): OlapRow[] {
  return produce(rows, (draftRows) => {
    rowsMetadata.forEach((meta) => {
      if (meta.type === 'dimension') {
        const dimensionName = meta.value.name
        draftRows.forEach((row) => {
          if (row[dimensionName] === null || row[dimensionName] === '') {
            row[dimensionName] = '其他'
          }
        })
      }
      //  else {
      //   const metricName = meta.value.name
      //   draftRows.forEach((row) => {
      //     // 是空就是空,不要做别的处理
      //     if (row[metricName] === null || row[metricName] === '') {
      //       // row[metricName] = 0
      //     }
      //   })
      // }
    })
  })
}

/** 将成功的response转化为chatAns */
export function readyResponseToChatAns(
  response: Exclude<ChatResponse, ChatResponseError>,
  sceneId: string,
  traceId?: string,
): AnsChatItem {
  const taskType = response.taskType
  const commonResponse = {
    sceneId,
    status: ChatStatus.success,
    ansTime: new Date(),
    traceId,
    originLlmResponse: (response as any)?.originLlmResponse,
  }
  switch (taskType) {
    case 'percentage':
    case 'period-on-period':
      return {
        role: 'assistant',
        ...commonResponse,
        content: [
          {
            type: 'multi-agent',
            data: new MultiAgentData(),
          },
        ],
      }

    case 'query-metric': {
      const rows = postProcessChatResponse(response.rowsMetadata, response.rows)
      const chatContent: AssistantChatItem[] = []
      // 当有指标不存在的时候，增加一个指标不存在的提示
      const metricNotExist = response.queryParamsVerified && getMetricNotExist(response.queryParamsVerified)
      if (metricNotExist) {
        chatContent.unshift(metricNotExist)
      }
      chatContent.push({
        type: 'chart',
        chartType: response.chartType,
        originalChartType: response.chartType,
        chartTitle: response.chartTitle,
        rows: rows,
        originRows: rows,
        sql: response.sql,
        sceneId: response.sceneId,
        infoTexts: response.infoTexts,
        recommendChartTypes: response.recommendChartTypes,
        rowsMetadata: response.rowsMetadata,
        queryParamsVerified: response.queryParamsVerified,
        taskType: response.taskType,
        isPartialRow: response.isPartialRow,
        partialRowMsg: response.partialRowMsg,
      })
      // 判断是否存在 sql
      if (response.sql) {
        chatContent.push({
          type: 'sql',
          sql: response.sql,
          sceneId: response.sceneId,
        })
      }
      return {
        role: 'assistant',
        content: chatContent,
        ...commonResponse,
        confidenceOriginData: response.confidenceOriginData,
      }
    }
    case 'attribution-analysis': {
      const chatContent: AssistantChatItem[] = []
      chatContent.push({
        type: 'chart',
        chartType: 'AttrAnalysis',
        originalChartType: 'AttrAnalysis',
        recommendChartTypes: ['AttrAnalysis'],
        chartTitle: response.chartTitle,
        rows: response.rows,
        originRows: response.rows,
        rowsMetadata: [],
        sceneId: response.sceneId,
        taskType: 'attribution-analysis',
        isPartialRow: response.isPartialRow,
        partialRowMsg: response.partialRowMsg,
        infoTexts: [],
      })
      if (response.sql) {
        chatContent.push({
          type: 'sql',
          sql: response.sql,
          sceneId: response.sceneId,
        })
      }
      return {
        role: 'assistant',
        content: chatContent,
        ...commonResponse,
      }
    }
    case 'attribution-metric-analysis': {
      const chatContent: AssistantChatItem[] = []
      chatContent.push({
        type: 'chart',
        chartType: 'AttrMetricAnalysis',
        originalChartType: 'AttrMetricAnalysis',
        recommendChartTypes: ['AttrMetricAnalysis'],
        chartTitle: response.chartTitle,
        rows: response.rows,
        originRows: response.rows,
        rowsMetadata: [],
        sceneId: response.sceneId,
        taskType: 'attribution-metric-analysis',
        isPartialRow: response.isPartialRow,
        partialRowMsg: response.partialRowMsg,
        infoTexts: [],
      })
      return {
        role: 'assistant',
        content: chatContent,
        ...commonResponse,
      }
    }
    case 'metric-exact-match': {
      return {
        role: 'assistant',
        content: [
          {
            type: 'metric-force-match',
            metricNames: response.metricNames,
            queryParamsVerified: response.queryParamsVerified,
          },
        ],
        ...commonResponse,
      }
    }
    case 'query-external-report': {
      return {
        role: 'assistant',
        content: [
          {
            type: 'query-external-report',
            externalReports: response.externalReports,
            where: response.where,
            timeQueryParams: response.timeQueryParams,
            queryParamsVerified: response.queryParamsVerified,
          },
        ],
        ...commonResponse,
      }
    }
    case 'data-overview':
    case 'table-list':
    case 'dimension-list':
    case 'dimension-detail':
    case 'metric-list':
    case 'metric-detail':
    case 'metric-tree': {
      return {
        role: 'assistant',
        content: [
          {
            type: taskType,
            rows: response.rows,
            isPartialRow: response?.isPartialRow || false,
            partialRowMsg: response?.partialRowMsg || '',
          },
        ],
        ...commonResponse,
      }
    }
    case 'chitchat': {
      return {
        role: 'assistant',
        content: [{ type: 'text', text: '非数据分析类问题，小助手无法回答，请换一个问吧。' }],
        ...commonResponse,
      }
    }
    case 'llm-error': {
      return {
        role: 'assistant',
        content: [{ type: 'text', text: response.content }],
        ...commonResponse,
      }
    }
    case 'doc-report': {
      if (response.rows?.isReportGenerated && response.rows?.result) {
        return {
          role: 'assistant',
          content: [
            {
              type: 'doc-report',
              rows: response.rows,
              status: AnalyzeReportTypes.reportGeneratedSuccess,
            },
          ],
          ...commonResponse,
        }
      }
      return {
        role: 'assistant',
        content: [
          {
            type: 'doc-report',
            rows: response.rows,
            status: response.rows?.result?.requestInfo
              ? AnalyzeReportTypes.reportGenerated
              : AnalyzeReportTypes.analyzeSuccess,
          },
        ],
        ...commonResponse,
      }
    }

    // case 'multi-agent': {
    //   const data = MultiAgentData.from(response.data, stateStore.get(metricConfigRecordAtom))

    //   return {
    //     role: 'assistant',

    //     content: [
    //       {
    //         type: 'multi-agent',
    //         data: data!,
    //       },
    //     ],
    //     ...commonResponse,
    //   }
    // }
    default:
      return assertExhaustive(taskType)
  }
}

/**
 * 将失败的response转化为chatAns
 * suggestions 可以为空，空则不推荐问题
 */
export function unreadyResponseToChatAns(response: ChatResponseError, sceneId: string): AnsChatItem {
  const unReadyAnsContent: AssistantChatItem[] = [
    {
      type: 'chat-error',
      ...response,
      metricNames: response.metricNames,
      queryParamsVerified: response.queryParamsVerified,
      originLlmResponse: response.originLlmResponse,
      tryQueryToSqlData: new TryQueryToSqlData(),
    },
  ]
  return {
    role: 'assistant',
    content: unReadyAnsContent,
    sceneId,
    status: ChatStatus.success,
    ansTime: new Date(),
    confidenceOriginData: response.confidenceOriginData,
  }
}

/**
 * 尝试向上找数据
 */
export async function tryQueryToSql(config: {
  queryParamsVerified: QueryParamsVerified
  maxResultCount: number
  llmType: LlmType | null
  conversationId: string | null
  chatId: string
  sceneId: string
  metricConfig: MetricConfig | null
  infoTexts: string[]
}): Promise<{
  data: AnsChatItem[]
  type?: NonNullable<ReturnType<typeof detectTimeType>>
  maxCount?: number
  queryParamsVerified?: QueryParamsVerified | null
}> {
  const {
    queryParamsVerified,
    queryParamsVerified: { queryParams },
    maxResultCount,
    llmType,
    conversationId,
    sceneId,
    chatId,
    metricConfig,
    infoTexts,
  } = config
  const timeType = detectTimeType(queryParams)
  if (!timeType) return { data: [] }
  // 向上找N个单位，N是周繁荣定的
  const MaxCountMap: Record<NonNullable<ReturnType<typeof detectTimeType>>, number> = {
    day: 15,
    month: 12,
    year: 1,
    quarter: 4,
    'half-year': 1,
  } as const
  const maxCount = MaxCountMap[timeType]
  const specTimeStartFunction = convertTimeToSpecificDate(queryParams.timeQueryParams!.timeStartFunction, 'start')
  const specTimeEndFunction = convertTimeToSpecificDate(queryParams.timeQueryParams!.timeEndFunction, 'end')
  const startDayjs = dayjs(
    new Date(specTimeStartFunction.year, specTimeStartFunction.month - 1, specTimeStartFunction.day),
  )
  const endDayjs = dayjs(new Date(specTimeEndFunction.year, specTimeEndFunction.month - 1, specTimeEndFunction.day))

  const arr: AnsChatItem[] = []
  let finalQueryParamsVerified: QueryParamsVerified | null = null
  if (timeType === 'month') {
    const newQueryParamsVerified = produce(queryParamsVerified, (queryParamsVerified) => {
      const realTimeType = timeType
      const startDate = startDayjs.subtract(maxCount, realTimeType).startOf(realTimeType)
      queryParamsVerified.queryParams.timeQueryParams!.timeStartFunction = {
        type: 'specificDate',
        year: startDate.year(),
        month: startDate.month() + 1,
        day: startDate.date(),
      }
      const endDate = endDayjs.subtract(1, realTimeType).endOf(realTimeType)
      queryParamsVerified.queryParams.timeQueryParams!.timeEndFunction = {
        type: 'specificDate',
        year: endDate.year(),
        month: endDate.month() + 1,
        day: endDate.date(),
      }
      queryParamsVerified.queryParams.timeQueryParams!.timeGranularity = timeType
    })
    const res = await axios.post<ChatResponse>(askBIApiUrls.metricQuery, {
      queryParamsVerified: newQueryParamsVerified,
      llmType,
      conversationId,
      sceneId,
      chatId,
      infoTexts,
    })
    if (res.data.ready) {
      const data = res.data as ChatResponseQueryMetric
      if (data.rows.length >= 0) {
        const queriedMetricNames = getQueriedMetricNames(newQueryParamsVerified.queryParams.metricNames, metricConfig)
        const rows = getUsefulData(data.rows, queriedMetricNames)
        console.info('\n getUsefulData--rows--->>>', rows)
        const nearestDayjs = dayjs(rows.at(-1)![DATE_ALIAS])
        console.info('\n getUsefulData--nearestDayjs--->>>', nearestDayjs)
        finalQueryParamsVerified = produce(queryParamsVerified, (queryParamsVerified) => {
          const startDate = nearestDayjs.startOf(timeType)
          const endDate = nearestDayjs.endOf(timeType)
          queryParamsVerified.queryParams.timeQueryParams!.timeStartFunction = {
            type: 'specificDate',
            year: startDate.year(),
            month: startDate.month() + 1,
            day: startDate.date(),
          }
          queryParamsVerified.queryParams.timeQueryParams!.timeEndFunction = {
            type: 'specificDate',
            year: endDate.year(),
            month: endDate.month() + 1,
            day: endDate.date(),
          }
        })
        const res = await axios.post<ChatResponse>(askBIApiUrls.metricQuery, {
          queryParamsVerified: finalQueryParamsVerified,
          llmType,
          conversationId,
          sceneId,
          chatId,
          infoTexts,
        })
        if (res.data.ready) arr.push(readyResponseToChatAns(res.data, sceneId))
      }
    }
  } else
    for (let count = 1; count <= maxCount && arr.length < maxResultCount; count++) {
      finalQueryParamsVerified = produce(queryParamsVerified, (queryParamsVerified) => {
        const isQuarter = timeType === 'quarter'
        const isHalfYear = timeType === 'half-year'
        const realTimeType = isQuarter || isHalfYear ? 'month' : timeType
        const startDate = startDayjs
          .subtract(count * (isQuarter ? 3 : isHalfYear ? 6 : 1), realTimeType)
          .startOf(realTimeType)
        queryParamsVerified.queryParams.timeQueryParams!.timeStartFunction = {
          type: 'specificDate',
          year: startDate.year(),
          month: startDate.month() + 1,
          day: startDate.date(),
        }
        const endDate = endDayjs
          .subtract(count * (isQuarter ? 3 : isHalfYear ? 6 : 1), realTimeType)
          .endOf(realTimeType)
        queryParamsVerified.queryParams.timeQueryParams!.timeEndFunction = {
          type: 'specificDate',
          year: endDate.year(),
          month: endDate.month() + 1,
          day: endDate.date(),
        }
      })
      const res = await axios.post<ChatResponse>(askBIApiUrls.metricQuery, {
        queryParamsVerified: finalQueryParamsVerified,
        llmType,
        conversationId,
        sceneId,
        chatId,
        infoTexts,
      })
      if (res.data.ready) arr.push(readyResponseToChatAns(res.data, sceneId))
    }
  return { data: arr, type: timeType, maxCount, queryParamsVerified: finalQueryParamsVerified }
}
