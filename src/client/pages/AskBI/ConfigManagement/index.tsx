import React, { useEffect } from 'react'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { App, Form, FormInstance } from 'antd'
import { ConfigType, ConfigValueType, AbstractConfig } from '@shared/config-management-constants'
import { APIResponse } from '@shared/common-types'
import { askBIApiUrls } from '@shared/url-map'
import { ClientConfigManagementMap, ConfigManagementClient } from './constant'

function fetchConfigList(type: ConfigType, typeId: string) {
  return axios.get<APIResponse<Record<string, ConfigValueType>>>(askBIApiUrls.configManagement.listByType, {
    params: { type, typeId },
  })
}

export const RenderConfigForm = ({
  type,
  typeId,
  form,
  disabled,
}: {
  type: ConfigType
  typeId: string
  form: FormInstance<any>
  disabled?: boolean
}) => {
  const { message } = App.useApp()
  const configManagementMap = ClientConfigManagementMap[type]

  const { run: loadConfig } = useRequest(() => fetchConfigList(type, typeId), {
    manual: true,
    onSuccess(res) {
      if (res.data.code !== 0) {
        message.error(`加载场景配置失败：${res.data.msg}`)
        return
      }
      form.setFieldsValue(res.data.data ?? {})
    },
    onError(err) {
      message.error(`加载场景配置请求失败：${err.message}`)
    },
  })

  useEffect(() => {
    loadConfig()
  }, [loadConfig, type, typeId])

  return (
    <Form form={form} layout="vertical" className="flex flex-col">
      {(Object.entries(configManagementMap) as [keyof typeof configManagementMap, AbstractConfig][]).map(
        ([key, config]) => {
          const client = config as unknown as ConfigManagementClient
          return (
            <Form.Item key={key} name={key} label={config.label}>
              {client.renderForm(form.getFieldValue(key), (v) => form.setFieldValue(key, v), disabled)}
            </Form.Item>
          )
        },
      )}
    </Form>
  )
}
