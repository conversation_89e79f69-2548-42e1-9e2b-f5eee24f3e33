import React from 'react'
import clsx from 'clsx'
import { Radio, Button, InputNumber, Input } from 'antd'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons'
import {
  ConfigValueType,
  getConfigDefinitionMap,
  BooleanConfig,
  StringConfig,
  NumberConfig,
  StringListConfig,
  ConfigFactory,
} from '@shared/config-management-constants'

export interface ConfigManagementClient<T = ConfigValueType> {
  renderForm(value: T, onChange: (v: T) => void, disabled?: boolean): React.ReactNode
  render(value: T): React.ReactNode
}

export class BooleanConfigClient extends BooleanConfig implements ConfigManagementClient<boolean> {
  renderForm(value: boolean, onChange: (v: boolean) => void, disabled?: boolean): React.ReactNode {
    return (
      <Radio.Group value={value} disabled={disabled} onChange={(e) => onChange(e.target.value)} buttonStyle="solid">
        <Radio value={true}>开启</Radio>
        <Radio value={false}>关闭</Radio>
      </Radio.Group>
    )
  }
  render(value: boolean): React.ReactNode {
    return (
      <div>
        <span className="font-medium">{this.label}：</span>
        <span>{value ? '开启' : '关闭'}</span>
      </div>
    )
  }
}

export class StringConfigClient extends StringConfig implements ConfigManagementClient<string> {
  renderForm(value: string, onChange: (v: string) => void, disabled?: boolean): React.ReactNode {
    return <Input value={value} disabled={disabled} onChange={(e) => onChange(e.target.value)} />
  }
  render(value: string): React.ReactNode {
    return (
      <div>
        <span className="font-medium">{this.label}：</span>
        <span>{value || '-'}</span>
      </div>
    )
  }
}

export class NumberConfigClient extends NumberConfig implements ConfigManagementClient<number> {
  renderForm(value: number, onChange: (v: number) => void, disabled?: boolean): React.ReactNode {
    const props: Record<string, any> = {
      value,
      onChange: (v: number | null) => onChange(v ?? this.defaultValue),
      disabled: disabled,
    }
    if (this.min !== undefined) props.min = this.min
    if (this.max !== undefined) props.max = this.max
    if (this.decimals !== undefined) props.precision = this.decimals
    return <InputNumber {...props} />
  }
  render(value: number): React.ReactNode {
    return (
      <div>
        <span className="font-medium">{this.label}：</span>
        <span>{value}</span>
      </div>
    )
  }
}

export function StringListInput({
  value = [],
  onChange,
  disabled = false,
}: {
  value?: string[]
  onChange?: (v: string[]) => void
  disabled?: boolean
}) {
  return (
    <div className="flex flex-col gap-[4px]">
      {value.map((s, i) => (
        <div key={i} className="flex gap-[6px]">
          <Input
            className="w-full"
            placeholder="请输入项"
            value={s}
            onChange={(e) => {
              if (disabled) return
              const newValue = [...value]
              newValue[i] = e.target.value
              onChange?.(newValue)
            }}
            disabled={disabled}
          />
          <div
            className={clsx(
              'mt-[5px] flex items-baseline justify-around',
              disabled ? 'cursor-not-allowed text-gray-400' : 'cursor-pointer',
            )}
            onClick={() => {
              if (disabled) return
              const newValue = [...value]
              newValue.splice(i, 1)
              onChange?.(newValue)
            }}
          >
            <MinusOutlined />
          </div>
        </div>
      ))}
      <Button
        onClick={() => {
          if (disabled) return
          onChange?.([...value, ''])
        }}
        className="w-[120px]"
        disabled={disabled}
      >
        <PlusOutlined className="mr-2" />
        新增项
      </Button>
    </div>
  )
}

export class StringListConfigClient extends StringListConfig implements ConfigManagementClient<string[]> {
  renderForm(_value: string[], _onChange: (v: string[]) => void, disabled?: boolean): React.ReactNode {
    return <StringListInput disabled={disabled} />
  }
  render(value: string[]): React.ReactNode {
    return (
      <div>
        <div className="font-medium">{this.label}：</div>
        <ul className="ml-2 list-disc">
          {value?.length ? value.map((item, i) => <li key={i}>{item}</li>) : <li>无内容</li>}
        </ul>
      </div>
    )
  }
}

export const clientConfigFactory: ConfigFactory = {
  BooleanConfig: BooleanConfigClient,
  NumberConfig: NumberConfigClient,
  StringListConfig: StringListConfigClient,
  StringConfig: StringConfigClient,
}

export const ClientConfigManagementMap = getConfigDefinitionMap(clientConfigFactory)
