import React from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { App, Button, Input, InputRef, Result, Skeleton } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { useAtomValue } from 'jotai'
import axios from 'axios'
import { getVisibleChartIconMap } from 'src/client/charts/Card'
import { SvgIcon, editIcon } from 'src/client/components/SvgIcon'
import QueryParamsSelector from 'src/client/components/QueryParamsSelector'
import IconButtonGroup from 'src/client/components/IconButtonGroup'
import ChartWrapper from 'src/client/charts/ChartWrapper'
import { AssistantChartChatItem } from 'src/client/utils'
import AdminCard from 'src/client/components/AdminCard'
import { MetricConfig, QueryParamsVerified } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import {
  APIResponse,
  ChartType,
  ChatResponseQueryMetric,
  ReadyChartResponse,
  ChartCreateInput,
} from 'src/shared/common-types'
import { readyResponseToChatAns } from '../Chat/utils'
import { currentChartThemeTypeAtom, themeAtom } from '../askBIAtoms'

const ChartEdit = () => {
  const { chartId } = useParams()
  const location = useLocation()
  const navigate = useNavigate()

  // 确定当前是copy还是edit 在保存图表时 有不同的逻辑
  const isEditing = location.pathname.includes('/edit/')

  const theme = useAtomValue(themeAtom)
  const { message: antdMessage } = App.useApp()
  const currentChartThemeType = useAtomValue(currentChartThemeTypeAtom)
  const [title, setTitle] = React.useState<string>('')

  const [chartType, setChartType] = React.useState<ChartType>('LineChart')
  const [chartData, setChartData] = React.useState<AssistantChartChatItem | null>(null)
  const [recommendChartTypes, setRecommendChartTypes] = React.useState<ChartType[]>([])

  const {
    data: originChartData,
    loading: isLoadingChartDetail,
    error: loadChartDetailError,
  } = useRequest(
    async () => {
      if (chartId != null) {
        const response = await axios.get<APIResponse<ReadyChartResponse>>(askBIApiUrls.charts.detail(chartId))
        const responseChartData = response.data.data
        if (responseChartData) {
          const queryParamsVerified = {
            queryParams: responseChartData.queryParams,
            originalQueryParams: responseChartData.queryParams,
            extraParams: {
              extraMetricNames: [],
              extraGroupBys: [],
              extraOrderBys: [],
            },
          }
          loadMetricConfig(responseChartData.sceneId)
          setTitle(responseChartData.chartTitle)
          setChartType(responseChartData.chartType)
          setRecommendChartTypes(responseChartData.recommendChartTypes)
          setChartData({
            ...responseChartData,
            type: 'chart',
            originalChartType: responseChartData.chartType,
            queryParamsVerified,
          })
        }
        return responseChartData
      } else {
        antdMessage.error('加载图表详情数据失败')
      }
    },
    {
      onError: (error) => {
        console.error('Load chart detail with error:', error)
      },
    },
  )

  const {
    run: loadNewChartData,
    loading: _isLoadingNewChartData,
    error: _loadNewChartDataError,
  } = useRequest(
    async (queryParamsVerified: QueryParamsVerified) => {
      if (chartData) {
        const response = await axios.post<ChatResponseQueryMetric>(askBIApiUrls.queryMetric, {
          queryParamsVerified,
          sceneId: chartData.sceneId,
        })
        const assistantChartChatItem = readyResponseToChatAns(response.data, chartData.sceneId)
          .content[0] as AssistantChartChatItem
        setChartData(assistantChartChatItem)
        // 更新推荐的图表
        setChartType(response.data.chartType)
        setRecommendChartTypes(response.data.recommendChartTypes)
      }
    },
    {
      onError: (error) => {
        console.error('Load new chart data with error:', error)
      },
    },
  )

  const {
    run: loadMetricConfig,
    data: metricConfig,
    loading: _isLoadingMetricConfig,
    error: _loadMetricConfigError,
  } = useRequest(
    async (sceneId: string) => {
      const response = await axios.get<APIResponse<MetricConfig>>(askBIApiUrls.auth.metrics, {
        params: { sceneId },
      })
      return response.data.data
    },
    {
      manual: true,
      onError: (error) => {
        console.error('Load metric config with error:', error)
      },
    },
  )

  const handleCreateChart = async () => {
    if (chartData) {
      const chartToCreate: Omit<ChartCreateInput, 'username'> = {
        chartType,
        chartTitle: title,
        recommendChartTypes: chartData.recommendChartTypes.join(','),
        sql: chartData.sql || '',
        rowsMetadata: JSON.stringify(chartData.rowsMetadata),
        chartThemeType: currentChartThemeType,
        // llmType: originChartData?.llmType,
        taskType: chartData.taskType,
        semanticSceneId: chartData.sceneId,
        queryParams: chartData.queryParamsVerified?.queryParams as any,
        ask: originChartData?.ask || '',
      }

      if (isEditing && originChartData?.id) {
        await axios
          .put(askBIApiUrls.charts.update(originChartData.id), chartToCreate)
          .then(() => {
            antdMessage.success('修改图表成功！')
          })
          .catch((error) => {
            antdMessage.error('修改图表失败！' + error.message)
          })
      } else {
        await axios
          .post(askBIApiUrls.charts.create, { ...chartToCreate })
          .then(() => {
            antdMessage.success('保存图表成功！')
          })
          .catch((error) => {
            antdMessage.error('创建图表失败！' + error.message)
          })
      }
    } else {
      antdMessage.error(isEditing ? '修改图表失败' : '创建图表失败！')
    }
  }

  // 参数改变 重新获取图表数据
  const handleQueryParamsChange = (newValue: QueryParamsVerified) => {
    loadNewChartData(newValue)
  }

  // 改变图表的chartType
  const handleChartTypeChange = (newValue: ChartType) => {
    setChartType(newValue)
    if (chartData) {
      setChartData({
        ...chartData,
        chartType: newValue,
      })
    }
  }

  if (isLoadingChartDetail) {
    return (
      <div className="mx-auto w-full max-w-screen-xl">
        <Skeleton active />
      </div>
    )
  }

  if (loadChartDetailError) {
    return (
      <div className="mx-auto w-full max-w-screen-xl">
        <Result status="error" title="加载图表详情失败" subTitle={loadChartDetailError.message} />
      </div>
    )
  }

  return (
    <div className="mx-auto flex w-full max-w-screen-xl flex-col gap-2">
      <div
        className="ml-1 flex w-fit cursor-pointer select-none items-center gap-1"
        onClick={() => {
          navigate(-1)
        }}
      >
        <ArrowLeftOutlined />
        <span>返回</span>
      </div>
      {originChartData != null && chartData && (
        <AdminCard
          title={
            chartData && (
              <EditChartTitle
                title={title}
                onChange={(i) => {
                  setTitle(i)
                }}
              />
            )
          }
          actions={
            <Button
              type="primary"
              size="middle"
              onClick={() => {
                handleCreateChart()
              }}
            >
              保存图表
            </Button>
          }
        >
          <QueryParamsSelector
            queryParamsVerified={{
              queryParams: originChartData.queryParams,
              originalQueryParams: originChartData.queryParams,
              extraParams: {
                extraMetricNames: [],
                extraGroupBys: [],
                extraOrderBys: [],
              },
            }}
            metricConfig={metricConfig}
            onChange={handleQueryParamsChange}
          />
          <ChartWrapper
            data={chartData}
            theme={theme}
            onTableDataChange={(data) => {
              console.info(data)
            }}
          />
          <div className="flex items-center gap-1">
            <span className="font-bold">切换图表：</span>
            <IconButtonGroup<ChartType>
              options={getVisibleChartIconMap(recommendChartTypes)}
              value={chartType}
              onChange={handleChartTypeChange}
            />
          </div>
        </AdminCard>
      )}
    </div>
  )
}

function EditChartTitle(props: { title: string; onChange: (i: string) => void }) {
  const { title, onChange } = props

  const [isEditingTitle, setIsEditingTitle] = React.useState(false)
  const inputRef = React.useRef<InputRef>(null)

  const handleBlur = async () => {
    setIsEditingTitle(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleBlur()
    }
  }

  // 在下一次渲染中 使input框获取到焦点
  React.useEffect(() => {
    if (isEditingTitle && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isEditingTitle])

  const handleEditTitle = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    event.stopPropagation()
    if (!isEditingTitle) {
      setIsEditingTitle(true)
    }
  }

  return (
    <div className="flex items-center gap-1">
      {isEditingTitle ? (
        <Input
          className="w-72 px-1 py-[2px] dark:border-slate-400 dark:shadow-sm dark:hover:border-slate-300"
          value={title}
          ref={inputRef}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
            onChange(event.target.value)
          }}
        />
      ) : (
        <span className="px-1 font-bold leading-[28px]">{title}</span>
      )}
      <div className="w-fit" onClick={handleEditTitle}>
        <SvgIcon icon={editIcon} className="h-5 w-5 cursor-pointer" />
      </div>
    </div>
  )
}

export default ChartEdit
