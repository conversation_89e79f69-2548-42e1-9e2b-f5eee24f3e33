/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
import EventEmitter from 'eventemitter3'
import { useState } from 'react'
import { BaseModel, NonFunctionKeys } from 'src/shared/types/base-model'

export class ReactBaseModel<
  EventTypes extends EventEmitter.ValidEventTypes = string | symbol,
  Context extends any = any,
> extends BaseModel<EventTypes, Context> {
  useState<K extends NonFunctionKeys<this>>(key: K, defaultValue?: this[K] | (() => this[K])) {
    const stateConfig = useState(defaultValue ?? this[key])
    this.set(key, stateConfig[0])
    return stateConfig
  }
}
