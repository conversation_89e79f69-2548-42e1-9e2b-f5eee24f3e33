/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
/**
 * @description client 侧的工具函数集合
 */
import React, { useEffect, useRef } from 'react'
import { debounce } from 'lodash-es'
import { theme as antdTheme, message } from 'antd'
import axios from 'axios'
import qs from 'query-string'
import { createStore } from 'jotai'
import { Dimension, Metric, MetricConfigForProject } from 'src/shared/metric-types'
import { getQueryState, getCryptoJsParseBase64, encryptUserId } from 'src/shared/common-utils'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { getBaoWuCodeValueList } from 'src/shared/baowu-share-utils'
import {
  SCATTER_CHART_CLASSIFIED_NUM,
  SCATTER_LEGEND_PRECISION_BOUND,
  SCATTER_CHART_CLASSIFIED_BUBBLE_COLOR,
  BASE_URL,
  codeValueSplitChar,
  X_LABEL_LENGTH_LIMIT,
  TOKEN_BI,
  TOKEN_RANGER,
  HUAXIA_APP_ID,
  SHANGHAI_AIRPORT_APP_ID,
} from '../../shared/constants'
import {
  APIResponse,
  BrandInfoType,
  FileMimeTypeInfo,
  MenuItem,
  OlapRow,
  RowsMetadata,
  ThemeType,
  isDimension,
  isMetric,
} from '../../shared/common-types'
import ChinaTelecomLogo from '/img/china-telecom-logo.webp'
import YuanjingLogo from '/img/yuanjingLogo.webp'
import YangtzeComputing from '/img/yangtze-computing-logo.webp'
import AskBot from '/img/askBot.webp'
import dipeakChatUserIcon from '/img/dipeakChatUserIcon.webp'
import baowuChatUserIcon from '/img/baowuChatUserIcon.webp'
import { docxIcon, excelIcon, fileUnknownIcon, htmlIcon, pdfIcon, pptxIcon } from '../components/SvgIcon'
import { brandInfoAtom, currentLoginUserAtom, queryStateAtom } from '../pages/AskBI/askBIAtoms'

/** 全局状态管理，用于在非组件函数中调用jotai */
export const stateStore = createStore()

export function typewriteInterval(
  str: string,
  callbackfn: React.Dispatch<React.SetStateAction<string>>,
  onComplete: () => void,
) {
  let gapTimeoutId
  const characters = str.split('')
  const interval = 1000 / characters.length
  const intervalId = setInterval(() => {
    if (characters.length < 1) {
      // 当前的循环结束，清空一下值，等待2秒，开始调用下一个
      clearInterval(intervalId)
      gapTimeoutId = setTimeout(() => {
        callbackfn('')
        onComplete()
      }, 2000)
    } else {
      const char = characters.shift()
      callbackfn((val) => val + char)
    }
  }, interval)
  return [intervalId, gapTimeoutId]
}

/** 获取某一列的最大值和最小值 */
export function getMaxAndMinValues(data: OlapRow[], columnName: string) {
  const salesFrequencies = data.map((row) => Number(row[columnName]))
  const maxValue = Math.max(...salesFrequencies)
  const minValue = Math.min(...salesFrequencies)
  return [maxValue, minValue]
}

/** 获取散点图的气泡大小 */
export function mapValueToRange(value: number, max: number, min: number) {
  // 设置映射范围
  const minValue = 10
  const maxValue = 30
  // 计算原始值与最小值的差值和最大值与最小值的差值
  const valueRange = value - min
  const maxRange = max - min
  return (valueRange / maxRange) * (maxValue - minValue) + minValue
}

/** 只用于 treemap */
export function transformArrayObject(
  rows: Array<{ [key: string]: any }>,
  rowsMetadata: RowsMetadata,
): Array<{ [key: string]: any }> {
  const dimensions = rowsMetadata.filter(isDimension).map((item) => item.value)
  const metrics = rowsMetadata.filter(isMetric).map((item) => item.value)
  // 取第一个维度属性的 value 数组
  const firstDimensionValues = dimensions[0] ? [...new Set(rows.map((item) => item[dimensions[0].name]))] : []

  let sourceData: Array<{ [key: string]: any }> = []

  // 一个维度
  if (dimensions.length === 1 && metrics.length > 0) {
    sourceData = rows.map((item: any) => {
      return {
        name: item[dimensions[0].name],
        value: item[metrics[0].name],
      }
    })
  } else if (dimensions.length >= 2 && metrics.length > 0) {
    // 二个以上维度
    sourceData = firstDimensionValues.map((item: any) => {
      return {
        name: item,
        children: rows.map((item: any) => {
          return {
            name: item[dimensions[1].name],
            value: Number(item[metrics[0].name]),
          }
        }),
      }
    })
  } else {
    sourceData = []
  }
  return sourceData
}

/** 获取string列的分类列 可能是空值 */
export function getColumnNamesForCategoricalColumns(
  // FIXME: 把 data 的 类型定义放到 common-types 中
  data: Array<{ [key: string]: string | number }>,
  columnIndex: string,
) {
  const uniqueValues = new Set(
    data.map((row) => {
      return row[columnIndex]
    }),
  )

  return [...uniqueValues].slice(0, SCATTER_CHART_CLASSIFIED_NUM)
}

/**
 * 四个通道的散点图 获取series配置
 * 数据被转化成了二维数组 可以对index作Number()处理
 */
export function getSeriesOptions(
  classifiedColumn: (string | number)[],
  data: (string | number)[][],
  xLabel: string,
  yLabel: string,
  classifiedColumnIndex: string,
  maxAndMinValue: number[],
  bubbleLabelIndex: string,
): Array<{ [key: string]: any }> {
  const [maxValue, minValue] = maxAndMinValue
  return classifiedColumn.map((column, index) => ({
    name: column,
    type: 'scatter',
    encode: {
      x: xLabel,
      y: yLabel,
    },
    data: data.filter((item) => item[Number(classifiedColumnIndex)] === column),
    itemStyle: {
      normal: {
        color: SCATTER_CHART_CLASSIFIED_BUBBLE_COLOR[index],
        opacity: 0.8,
      },
    },
    symbolSize: function (data: Array<any>) {
      const convertData = data[Number(bubbleLabelIndex)]
      if (!isNaN(convertData)) return mapValueToRange(convertData, maxValue, minValue)
      return 10
    },
  }))
}

/** 对数值进行向上或向下取整 */
function transformAndRoundValues(maxValue: number, minValue: number) {
  const processValue = (value: number, roundUp: boolean) => {
    // TODO: 对负数进行处理
    if (value <= 0) {
      return 0
    }
    const numberOfDigits = Math.floor(Math.log10(value)) + 1
    const powerOfTen = Math.pow(10, numberOfDigits - 1)

    const firstDigit = Math.floor(value / powerOfTen)
    const newValue = roundUp ? (firstDigit + 1) * powerOfTen : firstDigit * powerOfTen
    return newValue
  }

  const transformedMaxValue = processValue(maxValue, true)
  const transformedMinValue = processValue(minValue, false)
  return [transformedMaxValue, transformedMinValue]
}

/** 获取散点图的 visualMap 配置 */
export function getVisualMapOptions(maxValue: number, minValue: number, dimension: number | string) {
  const transformResult = transformAndRoundValues(maxValue, minValue)
  const isSmallValue = maxValue < SCATTER_LEGEND_PRECISION_BOUND

  const [max, min] = isSmallValue ? [maxValue, minValue] : transformResult
  let precision = 0
  if (isSmallValue) {
    const decimalPart = maxValue.toString().split('.')[1]
    precision = decimalPart ? decimalPart.length : 0
  }
  return [
    {
      left: 'right',
      top: 15,
      dimension: dimension, // 气泡图才有 与气泡大小属于一个通道
      min,
      max,
      itemWidth: 20,
      itemHeight: 80,
      calculable: true,
      precision,
      inRange: {
        symbolSize: [10, 50],
      },
      outOfRange: {
        symbolSize: [10, 50],
        color: ['rgba(255,255,255,0.4)'],
      },
    },
  ]
}

/**
 * 获取 url 上的参数信息
 * @param url 兼容 AskDoc 中链接格式："###askDocPdfLink###?folderId=Xxx&docId=Xxx&hrefPage=3&highlight=Xxx"
 * @returns
 */
export const getParamsFromURL = (url: string) => {
  const queryString = url.split('?')[1]

  if (!queryString) {
    return {}
  }

  const urlParams = new URLSearchParams(queryString)
  const paramsObject = Object.fromEntries(urlParams.entries())

  return paramsObject
}

const antdDeignToken = {
  components: {
    Table: {
      cellPaddingInlineSM: 6,
      cellPaddingBlockSM: 5,
    },
  },
}
/** 返回 Antd ConfigProvider theme 中的配置 */
export function getAntdConfigProviderTheme(theme: ThemeType) {
  const config =
    theme === 'dark'
      ? {
          token: {
            colorBgBase: 'rgb(51 65 85 1)',
            colorTextBase: '#fff',
          },
          ...antdDeignToken,
        }
      : {
          algorithm: antdTheme.defaultAlgorithm,
          ...antdDeignToken,
        }

  return config
}

/**
 * 根据不同的文档类型显示不同的icon图片
 * @param iconKey
 * @returns
 */
export const getIconComponent = (iconKey: FileMimeTypeInfo) => {
  // 根据 iconKey 返回对应的组件
  switch (iconKey) {
    case 'text/html':
      return htmlIcon
    case 'application/pdf':
      return pdfIcon
    case 'application/msword':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return docxIcon
    case 'application/vnd.ms-excel':
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return excelIcon
    case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      return pptxIcon
    case 'text/csv':
      return excelIcon

    default:
      return fileUnknownIcon
  }
}
/**
 * 格式化展示文本，超出展示maxShowNumber则进行阶段省略展示
 * @param text
 * @param maxShowNumber
 *
 */
export function formatShowText(text: string, maxShowNumber?: number) {
  const ellipsis = '...'
  if (typeof text !== 'string') {
    return ''
  }
  if (typeof maxShowNumber !== 'number') {
    return text
  }
  const length = text.length
  if (length > maxShowNumber) {
    return text.slice(0, maxShowNumber) + ellipsis
  }
  return text
}

/**
 * menu菜单 - 根据当前页面路径找到对应的菜单项的 key
 * @param path
 * @param items
 * @returns
 */
export const getKeyFromPath = (path: string, menuItems: MenuItem[]): string | null => {
  const menuItem = menuItems.find((item) => item.path === path)
  if (menuItem) {
    return menuItem.key
  }
  for (const item of menuItems) {
    if (item.children) {
      const foundKey = getKeyFromPath(path, item.children)
      if (foundKey) {
        return foundKey
      }
    }
  }
  return null
}

/**
 * menu菜单 - 根据path寻找上一级的key
 * @param path
 * @param menuItems
 * @returns
 */
export const findParentKey = (path: string, menuItems: MenuItem[]): string | null => {
  for (const item of menuItems) {
    if ((item.children || []).some((childItem) => childItem.path === path)) {
      return item.key // 找到了匹配的路径，返回父级的 key
    }
  }
  return null // 如果未找到匹配的菜单项，返回 null
}

/**
 * 遍历多层结构，并查找匹配的menuItem
 * @param menuItems
 * @param key
 * @returns
 */
export const findMenuItem = (menuItems: MenuItem[], key: string): MenuItem | null => {
  for (const item of menuItems) {
    if (item.key === key) {
      return item
    }
    if (item.children) {
      const found = findMenuItem(item.children, key)
      if (found) {
        return found
      }
    }
  }
  return null
}

export const chatHistoryBoxId = 'chat-history-box'
/** 会话历史页面 滚动到底部 */
export const scrollToBottom = () => {
  const event = new CustomEvent('scroll-to-bottom')
  document.dispatchEvent(event)
}

/**
 * 将path的路径分割，将匹配中的menuItems的key
 */

export function traversePathToGetKeys(path: string, menuItems: MenuItem[]) {
  const pathItems = (path || '').split('/')
  const keys: string[] = []
  pathItems.forEach((pathStr) => {
    const menuItem = menuItems.find((menu) => menu.key === pathStr)
    if (menuItem) {
      keys.push(pathStr)
      menuItems = menuItem.children || []
    }
  })
  return keys
}

// 文档状态
export const docStatusMap: { [key: string]: { label: string; colorClass: string } } = {
  Pending: { label: '异常', colorClass: 'warning' },
  Ready: { label: '解析中', colorClass: 'orange' },
  Done: { label: '解析完成', colorClass: 'green' },
  Fail: { label: '失败', colorClass: 'error' },
}

// 获取品牌信息
export const getBrandInfo = (groups: string[]): BrandInfoType => {
  const queryState = stateStore.get(queryStateAtom)
  // 如果 groups 为 null 或 undefined，则返回默认值
  // 默认品牌信息
  const defaultBrandInfo: BrandInfoType = {
    appId: '',
    logo: AskBot,
    chatUserIcon: dipeakChatUserIcon,
    companyName: '数巅科技',
    brandName: 'DIPEAK',
    header: {
      userInfo: true,
      themeToggle: true,
    },
  }

  // 如果 groups 为 null 或 undefined，则返回默认值
  if (!groups) {
    return defaultBrandInfo
  }

  if (groups.includes('china-telecom') || groups.includes('China Telecom')) {
    return {
      appId: 'china-telecom',
      logo: ChinaTelecomLogo,
      chatUserIcon: dipeakChatUserIcon,
      companyName: '中国电信',
      brandName: 'China Telecom',
      header:
        queryState.appid === 'china-telecom' && queryState.username
          ? {
              userInfo: false,
              themeToggle: false,
            }
          : undefined,
    }
  }

  if (groups.includes('yuanjing')) {
    return {
      appId: 'yuanjing',
      logo: YuanjingLogo,
      chatUserIcon: dipeakChatUserIcon,
      companyName: '元景大模型',
      brandName: 'yuanjing',
      header:
        queryState.appid === 'yuanjing' && queryState.username
          ? {
              userInfo: false,
              themeToggle: false,
            }
          : undefined,
    }
  }

  if (groups.includes('yangtze-computing')) {
    return {
      appId: 'yangtze-computing',
      logo: AskBot,
      chatUserIcon: dipeakChatUserIcon,
      companyName: '长江计算',
      brandName: 'yangtze-computing',
      jointLogo: YangtzeComputing,
      header:
        queryState.appid === 'yangtze-computing' && queryState.username
          ? {
              userInfo: false,
              themeToggle: false,
            }
          : undefined,
    }
  }

  // 可以继续添加更多的if-else语句来检查其他组 -- 例如宝武
  if (groups.includes('baowu')) {
    document.documentElement.classList.remove('dipeak-light')
    document.documentElement.classList.add('baowu-light')
    return {
      appId: 'baowu',
      logo: AskBot,
      chatUserIcon: baowuChatUserIcon,
      companyName: '数巅科技',
      brandName: 'DIPEAK',
    }
  }

  if (groups.includes('zyhl')) {
    return {
      appId: 'zyhltest',
      logo: AskBot,
      chatUserIcon: dipeakChatUserIcon,
      companyName: '致远互联',
      brandName: 'zyhl',
      header: false,
    }
  }

  if (groups.includes('zhuoxue')) {
    return {
      appId: 'bbwsytest',
      logo: AskBot,
      chatUserIcon: dipeakChatUserIcon,
      companyName: '琢学',
      brandName: 'bbwsy',
      llmName: 'GK',
      header: false,
    }
  }

  // 默认返回数巅科技
  return defaultBrandInfo
}
export const searchMetricConfigBySearchValue = <T extends Metric | Dimension>(
  configList: T[],
  searchValue: string,
): { list: T[]; searchValue: string } => {
  const result: T[] = []
  let searchWord = searchValue
  while (searchWord.length > 0) {
    configList.forEach((item) => {
      if (item.label.indexOf(searchWord) > -1) {
        result.push(item)
      }
    })
    if (result.length > 0 || searchWord.length === 1) {
      break
    }
    searchWord = searchWord.slice(1)
  }
  const hasResult = result.length > 0
  // 筛选后没数据时, searchWord应为''
  return { list: result, searchValue: hasResult ? searchWord : '' }
}

/** 判断是否是ios */

const ua = typeof window === 'object' ? window.navigator.userAgent : ''
export const isIOS = /iPhone|iPod|iPad/i.test(ua) ? 1 : 0
export const isAndroid = /(Android)/i.test(ua) ? 1 : 0

export async function login(args: Partial<{ username: string; password: string; proxy: string; hostname: string }>) {
  try {
    const searchParams = new URLSearchParams(window.location.search)
    // enc表示url上的账号密码是否加密了并且编码了，用于url上有账号密码免登操作时，必须有这个参数
    const isEncryption = searchParams.get('enc') === 'true'
    const hideHeader = searchParams.get('hideHeader') === 'true'

    const {
      hostname = window.location.hostname,
      username,
      password = '',
      // proxy = (await axios.get(askBIApiUrls.diProxyLogin.diDefaultProxy)).data.data,
    } = args
    const envResult = await axios.get<APIResponse<{ tag: string; code: string }>>(askBIApiUrls.env.getEnvTag)
    const envTag = envResult.data.data
    if (!envTag) throw new Error('envTag 获取失败')
    if (!username) throw new Error('用户名不能为空')
    const tag = getCryptoJsParseBase64(envTag.tag)
    const code = getCryptoJsParseBase64(envTag.code)
    const requestData = {
      username: username,
      password: isEncryption ? password : encryptUserId(password, tag, code),
      proxy: '',
      hostname,
      isEncryption,
    }

    const result = await axios.post(askBIApiUrls.auth.login, requestData)
    if (result.data.code === 0) {
      const data = result.data.data
      localStorage.setItem(TOKEN_BI, data.token)
      if (data.ranger?.token) {
        localStorage.setItem(TOKEN_RANGER, data.ranger.token)
      }
      if (data.ranger?.uToken) {
        localStorage.setItem('x-ranger-u-token', data.ranger.uToken)
      }
      const info = await axios.get(askBIApiUrls.auth.userInfo)
      if (info.data.code === 0) {
        const userInfo = info.data.data
        const brandInfo = {
          ...getBrandInfo(userInfo.groups.map((v: any) => v.groupName)),
          ...(hideHeader ? { header: false } : {}),
        }
        stateStore.set(currentLoginUserAtom, () => userInfo)
        stateStore.set(brandInfoAtom, () => brandInfo)
        return userInfo
      } else {
        message.error(info.data.message)
      }
    } else {
      throw new Error(result.data.message || result.data.msg || '登录失败')
    }
  } catch (error: any) {
    // APIError
    if (error.response && (error.response.msg || error.response.message)) {
      throw new Error(error.response.msg || error.response.message)
    }
    if (error instanceof Error) {
      throw error
    }
    throw new Error(error?.message || error?.toString() || '登录失败')
  }
}

/**
 * 通用登录函数，用于登录页面和自动登录
 */
export async function login2(args: Partial<{ username: string; password: string; proxy: string; hostname: string }>) {
  try {
    const searchParams = new URLSearchParams(window.location.search)
    // enc表示url上的账号密码是否加密了并且编码了，用于url上有账号密码免登操作时，必须有这个参数
    const isEncryption = searchParams.get('enc') === 'true'
    const {
      hostname = window.location.hostname,
      username,
      password = '',
      proxy = (await axios.get(askBIApiUrls.diProxyLogin.diDefaultProxy)).data.data,
    } = args
    const envResult = await axios.get<APIResponse<{ tag: string; code: string }>>(askBIApiUrls.env.getEnvTag)
    const authTokenResult = await axios.get(askBIApiUrls.login.getAuthToken)
    const envTag = envResult.data.data
    const authToken = authTokenResult.data.data
    if (!authToken) throw new Error('authToken 获取失败')
    if (!envTag) throw new Error('envTag 获取失败')
    if (!username) throw new Error('用户名不能为空')
    const tag = getCryptoJsParseBase64(envTag.tag)
    const code = getCryptoJsParseBase64(envTag.code)

    const requestData = {
      userId: isEncryption ? username : encryptUserId(username, tag, code),
      authCode: isEncryption ? password : encryptUserId(password, tag, code),
      proxy,
      hostname,
      isEncryption,
      authToken,
    }
    const result = await axios.post(askBIApiUrls.auth.login, requestData)
    const { data } = result.data
    if (data) {
      stateStore.set(currentLoginUserAtom, () => data)
      stateStore.set(brandInfoAtom, () => getBrandInfo(data.groups))
      return data
    } else {
      throw new Error('Login fail')
    }
  } catch (error: any) {
    console.error('login error', error)
    message.error(error.message || '登录失败')
    stateStore.set(currentLoginUserAtom, () => null)
    stateStore.set(brandInfoAtom, () => ({
      appId: '',
      logo: AskBot,
      chatUserIcon: dipeakChatUserIcon,
      companyName: '数巅科技',
      brandName: 'DIPEAK',
    }))
    return null
  }
}

/**
 * 自动登录函数，在特殊环境（enableAutoLogin）调用后会自动请求登录
 */
export async function autoLogin() {
  const queryState = getQueryState(true)
  try {
    await axios.get(askBIApiUrls.login.logout, { params: { hostname: window.location.hostname } })
    localStorage.clear()
    sessionStorage.clear()
    const requestData = {
      username: queryState.username,
      password: queryState.password,
      proxy: queryState.proxy,
    }
    // 特殊处理
    if (queryState.appid === 'china-telecom' && queryState.username) {
      requestData.username = 'test_public' // test_public
      // requestData.password = 'Qwer1234!'
      requestData.password = location.hostname === 'askbi-pre.dipeak.com' ? 'Qwer1234!' : 'Xf3sP6ed@4hj8q' // 经分环境密码为这个Xf3sP6ed@4hj8q ，其他环境为Qwer1234!
    } else if (requestData.username === 'zyhltest') {
      requestData.password = 'Zyhl@123'
    } else if (requestData.username?.startsWith('bbwsy') || requestData.username?.startsWith('bbwys')) {
      requestData.password = 'Bbwsy@123'
    }
    await login(requestData)
  } catch (error) {
    console.error('auto login error', error)
    return null
  }
}

export async function autoPufaLogin(token: string) {
  try {
    const response = await axios.get(askBIApiUrls.login.checkTokenFromUrl, {
      params: { urlToken: token },
    })
    const { userName, admin } = response.data

    const data = {
      username: userName,
      admin,
      token,
    }

    // 更新当前登录用户
    // @ts-expect-error 赵洋写的
    stateStore.set(currentLoginUserAtom, () => data)

    return response
  } catch (error) {
    console.error('Failed to auto-login:', error)
    throw new Error('Auto login failed') // 抛出错误以便外部捕获
  }
}

const BAO_WU_DIMENSIONS_LIST = ['中国宝武钢铁集团有限公司-合并', '宝武共享服务有限公司-合并', '宝武资源有限公司-合并']

export const getTempDimensionList = (dimensions: Dimension[] = [], isBaoWu: boolean) => {
  return isBaoWu
    ? getBaoWuCodeValueDimensionList(dimensions)
    : dimensions.filter((item) => {
        return item.type !== 'virtual-time'
      }) || []
}

function getBaoWuCodeValueDimensionList(dimensions: Dimension[]) {
  const replaceReg = new RegExp(codeValueSplitChar, 'g')
  const codeValueList = getBaoWuCodeValueList(dimensions)
  const sortedValueList =
    codeValueSort(codeValueList, BAO_WU_DIMENSIONS_LIST)?.map((item) => {
      const label = item?.split?.(replaceReg)?.[0]
      return {
        id: label,
        label: label,
        name: label,
      }
    }) || []

  return sortedValueList as Dimension[]
}

// 部分公司需要在前面显示, 所以需要将这部分提出来, 并且把原始的删掉
function codeValueSort(codeValues: string[], frontList: string[]) {
  const backList = codeValues.filter((item) => {
    return !frontList.some((frontItem) => item.startsWith(frontItem))
  })
  return [...frontList, ...backList]
}

export function isAutofocus() {
  return qs.parse(location.search).autofocus === '1'
}
/**
 * 全局的ac管理，便于在一些特殊的场景下（比如登出）终止所有的请求
 */
export const abortControllerManager = new Set<{ abort: () => void }>()

export const handleAbortAll = () => {
  abortControllerManager.forEach((ac) => ac.abort())
  abortControllerManager.clear()
}

/**
 * abortController 的hook
 * 用于在组件卸载的时候终止掉请求，防止阻塞页面
 */
export function useAbortController({ debounceTimeout = 200 }: { debounceTimeout?: number } = {}) {
  const abortControllerRef = useRef(new AbortController())
  // 防止react18调用两次useEffect
  // mount -> unmount(abort) -> mount，此时就会被终止
  // 增加一个字段判断触发abort时没有被再次挂载
  const needAbort = useRef(false)
  const abort = useRef(
    debounce((force = false) => {
      if (force || needAbort.current) {
        abortControllerRef.current.abort()
      }
    }, debounceTimeout),
  )
  useEffect(() => {
    const ac = abortControllerRef.current
    const abortFn = abort.current
    function beforeUnload() {
      abortFn(true)
    }
    needAbort.current = false
    window.addEventListener('beforeunload', beforeUnload)
    abortControllerManager.add(ac)
    return () => {
      needAbort.current = true
      window.removeEventListener('beforeunload', beforeUnload)
      abortFn()
      abortControllerManager.delete(ac)
    }
  }, [])
  return abortControllerRef.current
}

/**
 * 如果它没有BASE_URL前缀就加上
 */
export function formatPathWithBaseUrl<T>(path: T): T {
  if (typeof path === 'string' && path.startsWith('/') && !path.startsWith(BASE_URL)) {
    return (BASE_URL + path) as T
  }
  return path
}

/**
 * 对X轴的label长度进行format截断
 */
export function formatXLableLength(xValue: unknown) {
  if (typeof xValue === 'string' && xValue.length > X_LABEL_LENGTH_LIMIT) {
    return `${xValue.slice(0, X_LABEL_LENGTH_LIMIT)}...`
  }
  return xValue
}

export async function getEnv() {
  const envResult = await axios.get<APIResponse<{ tag: string; code: string }>>(askBIApiUrls.env.getEnvTag)
  const envTag = envResult.data.data
  if (!envTag) throw new Error('envTag 获取失败')
  const tag = getCryptoJsParseBase64(envTag.tag)
  const code = getCryptoJsParseBase64(envTag.code)
  return { tag, code }
}

export function getCookieValue(name: string) {
  const match = document.cookie.match(new RegExp('(^|;\\s*)(' + name + ')=([^;]*)'))
  return match ? decodeURIComponent(match[3]) : null
}

/**
 * 判断当前环境是否安全
 * 由于复制Clipboard API的安全限制，只有在HTTPS协议或localhost环境下才能使用
 * @returns 如果当前页面使用HTTPS协议或主机名为localhost，则返回true，否则返回false
 */
export const isSecureEnvironment = () => {
  return window.location.protocol === 'https:' || window.location.hostname === 'localhost'
}

export function getConfigFromProjectMetricConfig<ConfigType extends Metric | Dimension>(
  metricConfigDataOfProject: MetricConfigForProject | null,
  configName: 'allMetrics' | 'allDimensions',
): ConfigType[] | [] {
  if (!metricConfigDataOfProject) {
    return []
  }
  const temp: ConfigType[] = []
  if (configName === 'allMetrics') {
    metricConfigDataOfProject.forEach((metricConfig) => {
      temp.push(...(metricConfig.data.allMetrics as ConfigType[]))
    })
  }
  if (configName === 'allDimensions') {
    metricConfigDataOfProject.forEach((metricConfig) => {
      temp.push(...(metricConfig.data.allDimensions as ConfigType[]))
    })
  }
  const resultLabelSet = new Set()
  // 去重
  const result = temp.filter((item) => {
    if (resultLabelSet.has(item.label)) {
      return false
    }
    resultLabelSet.add(item.label)
    return true
  })
  return result
}

/**
 * 判断是否是首页
 * @returns boolean
 */
export function checkIsMiniMode() {
  return location.pathname === askBIPageUrls.home
}

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
export const ENV = import.meta.env as Record<string, string>

export const IS_HUAXIA = ENV.VITE_PROJECT_ENV === HUAXIA_APP_ID
export const IS_SHANGHAI_AIRPORT = ENV.VITE_PROJECT_ENV === SHANGHAI_AIRPORT_APP_ID
export * from './chat'
export * from './copyToClipboard'
export * from './generateBlobForCopy'
export * from './react-base-model'
