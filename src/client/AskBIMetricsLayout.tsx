/**
 * 指标详情界面页面的布局
 */
import React, { useCallback, useEffect, useState } from 'react'
import { IdentificationIcon } from '@heroicons/react/24/outline'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import { useAtomValue, useSetAtom } from 'jotai'
import { ConfigProvider, App } from 'antd'
import clsx from 'clsx'
import zhCN from 'antd/lib/locale/zh_CN'
import { BASE_URL, IS_CHROME_EXTENSION, IS_H5 } from '@shared/constants'
import { askBIPageUrls } from 'src/shared/url-map'
import { MenuItem } from 'src/shared/common-types'
import { ProjectType, SceneType } from 'src/shared/metric-types'
import {
  currentDatasetAtom,
  isDianxinAtom,
  isProjectChosenAtom,
  requestDefaultDatasetAtom,
  semanticProjectInfoAtom,
  themeAtom,
} from './pages/AskBI/askBIAtoms'
import Sidebar from './components/AdminSidebar'
import LayoutHeader from './LayoutHeader'
import { getAntdConfigProviderTheme } from './utils'
import {
  SvgIcon,
  dimensionOverviewIcon,
  documentManageIcon,
  metricOverviewIcon,
  metricTreeIcon,
  reportIcon,
  sideChatHistoryIcon,
  smartReportIcon,
} from './components/SvgIcon'
import ChatScenePopover from './components/ChatScenePopover'
import { authAtom } from './hooks/useAuth'

export function useMetricsSidebarMenuItems() {
  const isDianxin = useAtomValue(isDianxinAtom)
  const auth = useAtomValue(authAtom)
  const getMetricsSidebarMenuItems = (): MenuItem[] => {
    const items = [
      auth.metricStoreMetrics && {
        key: 'metrics',
        path: askBIPageUrls.metricStore.metrics.overview,
        label: '指标',
        icon: <SvgIcon icon={metricOverviewIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreDimensions && {
        key: 'dimensions',
        path: askBIPageUrls.metricStore.dimensions.overview,
        label: '维度',
        icon: <SvgIcon icon={dimensionOverviewIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreMetricTree && {
        key: 'metric-tree',
        path: askBIPageUrls.metricStore.metricTree.overview,
        label: '指标树',
        icon: <SvgIcon icon={metricTreeIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreCharts && {
        key: 'charts',
        path: askBIPageUrls.metricStore.charts.list,
        label: '报表',
        icon: <SvgIcon icon={reportIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreDocument && {
        key: 'document',
        path: askBIPageUrls.metricStore.document.list,
        label: '文档数据',
        icon: <SvgIcon icon={documentManageIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreAskHistory && {
        key: 'ask-history',
        path: askBIPageUrls.metricStore.askHistory.overview,
        label: '提问历史',
        icon: <SvgIcon icon={sideChatHistoryIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreHint && {
        key: 'hint',
        path: askBIPageUrls.metricStore.hint.overview,
        label: '业务术语',
        icon: <SvgIcon icon={sideChatHistoryIcon} className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreExternalReport && {
        key: 'external-report',
        path: askBIPageUrls.metricStore.externalReport.management,
        label: '外部报表',
        icon: <IdentificationIcon className="h-6 w-6 dark:text-white" />,
      },
      auth.metricStoreSmartReport &&
        isDianxin && {
          key: 'smart-report',
          label: '智能报告引擎',
          icon: <SvgIcon icon={smartReportIcon} className="h-6 w-6 dark:text-black" />,
          children: [
            { label: '模板管理', key: 'template', path: askBIPageUrls.metricStore.smartReport.templateManagement },
            {
              label: '报告管理',
              key: 'report',
              path: askBIPageUrls.metricStore.smartReport.reportManagement,
            },
            {
              label: '打标任务管理',
              key: 'marking-task',
              path: askBIPageUrls.metricStore.smartReport.markingTaskManagement,
            },
          ],
        },
    ].filter(Boolean)
    return items as MenuItem[]
  }
  return getMetricsSidebarMenuItems()
}

export function AskBIMetricsLayout() {
  const theme = useAtomValue(themeAtom)
  const requestDefaultDataset = useSetAtom(requestDefaultDatasetAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const isProjectChosen = useAtomValue(isProjectChosenAtom)
  const [isShowDocMenu, setIsShowDocMenu] = useState<boolean>(false)
  const navigate = useNavigate()
  const location = useLocation()

  const MetricsSidebarMenuItems = useMetricsSidebarMenuItems()

  // 判断是否有 doc 场景的agent，如果是的话，则显示文档菜单
  const checkAndSetDocMenu = useCallback(
    (project: ProjectType) => {
      if (isProjectChosen) {
        const isHaveDoc = project.scenes.some((scene: SceneType) => scene.agent?.includes('Doc'))
        setIsShowDocMenu(isHaveDoc)
      } else {
        const currentScene = project.scenes.find((scene: SceneType) => scene.id === currentDataset?.sceneId)
        if (currentScene) {
          setIsShowDocMenu(currentScene.agent?.includes('Doc') || false)
        }
      }
    },
    [currentDataset, isProjectChosen],
  )

  // 加载默认的 dataset
  useEffect(() => {
    // 先判断 currentDatasetAtom 是否为空，当为空的时候才去请求
    if (currentDataset) {
      const projectId = currentDataset.projectId
      const project = semanticProjectInfo.find((item: ProjectType) => item.id === projectId)
      project && checkAndSetDocMenu(project)
    }
  }, [checkAndSetDocMenu, currentDataset, requestDefaultDataset, semanticProjectInfo])

  useEffect(() => {
    // 当用户访问 '/metrics' 时重定向到 '/metrics/overview'
    if (location.pathname === BASE_URL + '/metrics' || location.pathname === BASE_URL + '/metrics/') {
      navigate('/metrics/overview')
    }
  }, [navigate, location])

  // 隐藏项目树的页面
  const hideProjectTreeUrls = [
    '/metrics/detail',
    askBIPageUrls.metricStore.charts.list,
    askBIPageUrls.manage.project,
    askBIPageUrls.manage.sync,
    askBIPageUrls.metricStore.measures.create,
    '/metric-tree/detail',
    askBIPageUrls.metricStore.document.list,
    askBIPageUrls.metricStore.smartReport.templateManagement,
    askBIPageUrls.metricStore.smartReport.reportManagement,
    askBIPageUrls.metricStore.smartReport.markingTaskManagement,
  ]

  const hideProjectTree = !hideProjectTreeUrls.some((url) => location.pathname.includes(url))

  return (
    <ConfigProvider locale={zhCN} theme={getAntdConfigProviderTheme(theme)}>
      <App>
        <div
          className={clsx(
            'metrics-layout-root relative flex h-screen flex-col overflow-hidden text-black dark:bg-slate-900 dark:text-slate-100 md:overflow-visible',
            IS_CHROME_EXTENSION && 'chrome-extension',
          )}
          style={IS_H5 ? { height: window.innerHeight + 'px' } : {}}
        >
          <LayoutHeader isFullWidth={true} />
          <section className="flex flex-grow flex-row items-stretch overflow-auto border-t border-slate-200 pb-0 dark:border-slate-700">
            <Sidebar
              className="pt-6"
              menuItems={
                isShowDocMenu
                  ? MetricsSidebarMenuItems
                  : MetricsSidebarMenuItems.filter((item) => item.key !== 'document')
              }
            />
            <div className="relative flex-1 overflow-auto bg-white p-[28px] dark:bg-slate-800">
              <div className="relative mx-auto flex w-full md:max-w-screen-2xl">
                <div className="absolute right-4">
                  {/* 当页面是 metricDetail 的时候不显示 */}
                  {hideProjectTree && <div>{currentDataset ? <ChatScenePopover /> : <p>暂无场景</p>}</div>}
                </div>
              </div>
              <Outlet />
            </div>
          </section>
        </div>
      </App>
    </ConfigProvider>
  )
}
