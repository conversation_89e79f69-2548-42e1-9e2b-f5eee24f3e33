/* eslint-disable @typescript-eslint/ban-ts-comment */
/**
 * @description 存放所有共享的常量
 */

import { DefaultOptionType } from 'antd/es/cascader'
import {
  DeleteOutlined,
  FilterOutlined,
  CodeOutlined,
  CopyOutlined,
  FileAddOutlined,
  EyeOutlined,
  FieldTimeOutlined,
} from '@ant-design/icons'
import { getBaseUrl } from './utils/utils'

/** Input输入框的默认长度 */
export const DEFAULT_INPUT_MESSAGE_LENGTH = 2500

/** 优先展示表格数据 */
export const SHOW_TABLE_FIRST = false

/** 饼图最多显示的饼块数量，超过的合并为"其他" */
export const PIE_CHART_DATA_LIMIT = 15

/** 判断数值类型的这一列是否属于维度（年份）[数值类型的值去重后 与 原始数据数量的比例]  */
export const IS_DIMENSION_PROPORTION = 0

/** 散点图最多支持的分类个数  */
export const SCATTER_CHART_CLASSIFIED_NUM = 5

/** 散点图图例显示精度的界限，低于这个数值，才会显示精度 */
export const SCATTER_LEGEND_PRECISION_BOUND = 1

/** 柱状图最多展示条数 */
export const BAR_CHART_NUM = 20

/** echarts x轴最多展示label长度 */
export const X_LABEL_LENGTH_LIMIT = 6

/** echarts 缩放条出现x轴数组最大长度 */
export const DATAZOOM_SHOW_LIMIT = 20

/** 散点气泡图不同类型的气泡颜色 */
export const SCATTER_CHART_CLASSIFIED_BUBBLE_COLOR = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']

/** 报告生成 超时 20 分钟 */
export const DOC_REPORT_TIMEOUT = 20 * 60 * 1000

/** 默认接口超时 10 分钟 */
export const DEFAULT_TIMEOUT = 10 * 60 * 1000 * 10

/** Xengine query 超时200s */
export const XENGINE_QUERY_TIMEOUT = 20 * 1000 * 10

/** Ask Doc 默认知识库名字 */
export const DEFAULT_ASK_DOC_FOLDER_ID = 'DEFAULT_FOLDER'

/** mock的多场景 scene Id */
export const MULTI_SCENE_CHAT_MOCK_SCENE_ID = 'MULTI_SCENE_CHAT_MOCK_SCENE_ID'

export const ABORT_IGNORED = 'ABORT_IGNORED'

/** 如果 select 没有 limit，则添加默认的 limit 数量 */
export const DEFAULT_SELECT_LIMIT = 100

export const ECHARTS_CHART_TYPES = [
  'LineChart',
  'ColumnChart',
  'PieChart',
  'ScatterChart',
  'TreemapChart',
  'GroupColumnChart',
  'StackedColumnChart',
  'MultiLineChart',
  'RankBarChart',
] as const
export const LOCAL_CHART_TYPES = ['Kpi', 'SimpleTable', 'AttrAnalysis', 'AttrMetricAnalysis'] as const
export const CHART_TYPES = [...ECHARTS_CHART_TYPES, ...LOCAL_CHART_TYPES] as const

/** 最多上传10个文件 */
export const MAX_COUNT_UPLOAD_LIMIT = 20
/** 文件大小10M */
export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10M

/** PDf常见的缩放级别 */
export const PDF_SCALE_LIST = [
  { label: '自动缩放', value: 'auto' },
  { label: '实际大小', value: 'page-actual' },
  { label: '适合页面', value: 'page-fit' },
  { label: '适合页宽', value: 'page-width' },
  { label: '50%', value: '50' },
  { label: '75%', value: '75' },
  { label: '100%', value: '100' },
  { label: '125%', value: '125' },
  { label: '150%', value: '150' },
  { label: '200%', value: '200' },
  { label: '300%', value: '300' },
  { label: '400%', value: '400' },
]
/** 最大缩放级别为 400% */
export const PDF_MAX_SCALE = 4
/** 最大缩放级别为 25% */
export const PDF_MIN_SCALE = 0.25

// 根据 url 中的 chrome=true 来判断是否在 chrome 插件中
export const IS_CHROME_EXTENSION =
  typeof window === 'object' ? window?.location?.search?.includes('chrome=true') : false

/** chrome 插件支持的 action type */
export const CHROME_ACTIONS = {
  closeDisplay: 'closeDisplay',
  toggleDisplay: 'toggleDisplay',
  changeTheme: 'changeTheme',
} as const

/** askbi 中 文档问答使用的知识库 id */
export const ASKBI_INTEGRATED_ASKDOC_FOLDER_ID: string = 'rzQaALgXZYpxXHPQjWTzM'

/** chrome 插件内 iframe 使用的 URL */
// 发布使用
export const CHROME_IFRAME_URL = 'https://askbi-pre.dipeak.com/chat/new?chrome=true'
// 本地开发使用
// export const CHROME_IFRAME_URL = 'http://localhost:3000/chat/new?chrome=true'

/** 判断是否浏览器环境 */
export const IS_BROWSER = typeof window === 'object'
/** 判断是否是H5移动端 */
export const IS_H5 = typeof window === 'object' ? /Mobi|Android|iPhone/i.test(window.navigator.userAgent) : false

/** 展示前端[数据解读] */
export const DISPLAY_INSIGHT = true
export const DISABLE_INSIGHT_PROJECT_NAME_LIST = ['baowu', '宝武']

// 先不用切换主题功能
// export const IS_DARK =
//   typeof window === 'object'
//     ? !IS_H5 && // 移动端禁止dark
//       (localStorage.theme === 'dark' ||
//         (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches))
//     : false

export const IS_DARK = false

/** 图表色系列表 */
export const CHART_THEMES = [
  {
    type: 'default',
    name: '默认',
    colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'],
  },
  {
    type: 'purple',
    name: '紫色',
    colors: ['#b6a2de', '#2ec7c9', '#5ab1ef', '#ffb980', '#d87a80', '#8d98b3', '#e5cf0d', '#97b552'],
  },
  {
    type: 'yellow',
    name: '蓝色',
    colors: ['#001852', '#0098d9', '#2b821d', '#005eaa', '#339ca8', '#f7f494', '#e6b600', '#fcce10'],
  },
  {
    type: 'grey',
    name: '灰色',
    colors: ['#6e7074', '#d87c7f', '#919e8b', '#d7ab82', '#61a0a8', '#efa18d', '#fe8463', '#c92a1f'],
  },
  {
    type: 'red',
    name: '红色',
    colors: ['#c92a1f', '#e01f54', '#e87c25', '#f5bf44', '#f58db2', '#d0648a', '#fe8463', '#d87c7c'],
  },
] as const

/** 图表色系类型 */
export type ChartThemeType = (typeof CHART_THEMES)[number]['type']

export const CompanyName: string = `©2022-${new Date().getFullYear()} 北京数巅科技有限公司`
export const RecordNumber: string = '京ICP备2022019047号-1'

/** 用于 mysql2 查询元数据库，设置一个唯一的 datasourceId */
export const PRISMA_DATASOURCE_ID = Symbol.for('@@PRISMA_DATASOURCE_ID')

/** 显示文档类型 */
export const mimeTypeList = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '.html',
    value: 'text/html',
  },
  {
    label: '.pdf',
    value: 'application/pdf',
  },
  {
    label: '.doc',
    value: 'application/msword',
  },
  {
    label: '.xls',
    value: 'application/vnd.ms-excel',
  },
  {
    label: '.csv',
    value: 'text/csv',
  },
  {
    label: '.xlsx',
    value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  },
  {
    label: '.docx',
    value: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  },
  {
    label: '.pptx',
    value: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  },
]

export const messageSafeAreaClass = 'message-input-click-safe-area'
export const dimensionsSafeAreaClass = 'dimension-click-safe-area'
export const recommendedSafeAreaClass = 'recommended-click-safe-area'
export const selectedDimensionClass = 'border border-[#8BECD4] bg-[#8BECD4]'
export const unselectedDimensionClass = 'border border-[#A8DED1] bg-[#F7FFFD]'

export const selectedMetricClass = 'border border-[#C8C3F0] bg-[#C8C3F0]'
export const unselectedMetricClass = 'border border-[#C8C3F0] bg-[#F3F2FB]'

/** 特殊标记符，用于在prompt-studio中测试时，会携带该信息访问 */
export const TAG_PROMPT_TEST = 'prompt-test'

/** 全局通用前缀 */
export const BASE_URL = getBaseUrl()

export const chatPath = BASE_URL + '/chat/home'
export const XEngineHomePath = BASE_URL + '/manage/external-datasource/catalog-list'

export const codeValueSplitChar = '\x01\x01\x01'

/**
 * 外部实体类型枚举
 */
export const ExternalEntityType = [
  {
    value: 'baowu_report',
    label: '宝武外部报表',
  },
]

export const HintType = [
  {
    value: 'fixed',
    label: '固定',
    desc: '每次提问模型都会学习该业务知识',
  },
  {
    value: 'dynamic',
    label: '动态',
    desc: '只有问题有相关性，模型才会学习该业务知识',
  },
]

/** 数字格式化正则 */
export const FormatNumberReg = /^(?<prefix>[^,.\d]*)(?<integer>,)?(?<decimals>\.\df)?(?<suffix>[^,.\d]*)$/
export const IntNotZeroReg = /^[1-9]\d*$/
export const IntReg = /0|^[1-9]\d*$/
// export const getRegWithFix = (n) => new RegExp(`0|^[1-9]*(.[0-9]${n})$`)

// type IValueElement = Record<string, string | ReactElement> & { key: string }

export const Customer: Record<string, Record<string, boolean | (<T>(value: T) => T)>> = {
  PU_FA: {
    // customerIsSupportType('formDefaultTimeColumnNameAndGranularityAndFormatRequire')
    formDefaultTimeColumnNameAndGranularityAndFormatRequire: false,
    agentHide: true,
    CSVCreateTableHide: true,
    dataSceneHide: true,
    calculateFormDisableDimensions: true,
    metricModelMeaDimRecommendBtnHide: true,
    calculateFormCheckCfgHide: true,
    createMetricCumulativeFormatTemplateHide: true,
    createMetricSynonymsHide: true,
    createMetricFilterHide: false, // 浦发一期隐藏 filter 二期释放
    scenarioDetailCodeValuesBtnsHide: true,
    pufaLogin: true,
    mvDiskHide: true,
    mvWidthExpand: true,
    catalogIsEncrypt: true,
    mvSearchHide: true,
    mvOperationButtonHide: true,
    mvDetailDiskHide: true,
    mvDetailDropDrownHide: true,
    isCsvMetricmodelUploadSimple: true,
    isCSVToLikeVTRemoveDistributedCfg: true,
    calculateFormCreateMetricAlwaysFalse: true,
    metricModelTableAutoFillNameAndNameZh: true,
    metricModelTableTitleTimeHide: true,
    xlsxUploadMetricWithoutBI: true,
    vTableOperateButtonsFilter: (value) => {
      const hideActions = [VTableOperateActions.DATA_MASKING]
      if (Array.isArray(value)) {
        return value.filter((btn) => !hideActions.includes(btn.action)) as typeof value
      }
      return value
    },
    scenarioTableColumnsFilter: (columns) => {
      const hideDataIndexes = ['agent']
      if (Array.isArray(columns)) {
        return columns.filter((col) => !hideDataIndexes.includes(col.dataIndex)) as typeof columns
      }
      return columns
    },
    metricModelTableColumnsFilter: (columns) => {
      const hideDataIndexes = ['synonyms', 'formatTemplate']
      if (Array.isArray(columns)) {
        return columns.filter((col) => !hideDataIndexes.includes(col.dataIndex)) as typeof columns
      }
      return columns
    },
    metricModelCreateTypeFilter: (value) => {
      // 浦发一期隐藏掉了 创建嵌套指标模型 二期放开 为了增加以后的控制灵活性，这个变量控制不移除
      return value
    },
    createMetricTypesFilter: (value) => {
      const supportValues = ['simple']
      if (Array.isArray(value)) {
        return value.filter((val) => supportValues.includes(val.value)) as typeof value
      }
      return value
    },
    scenarioDetailMeaTableColumnsFilter: (value) => {
      const hideDataIndexes = ['synonyms']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    scenarioDetailDimTableColumnsFilter: (value) => {
      const hideDataIndexes = ['synonyms', 'filterSwitch', 'values', 'granularity']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    metricListTableColumnsFilter: (value) => {
      const hideDataIndexes = ['synonyms']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    mvListTableColumnsFilter: (value) => {
      const hideDataIndexes = [
        'relatedMetricsCount',
        'compressedSize',
        'uncompressedSize',
        'compressionRatio',
        'scheduleFrequency',
        'hitCount',
      ]
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    mvDetailTabFilter: (value) => {
      const hideDataIndexes = ['overview', 'list', 'partition', 'dependencies']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.key)) as typeof value
      }
      return value
    },
    isUseSimpleMetricmodel: true,
    xlsxMetricBatchUploadTemplate: (value) => '/files/metric-upload-pufa.xlsx' as typeof value,
  },
  TIAN_HONG: {
    agentHide: true,
    etlSceneHide: true,
    createVTableHide: true,
    mvFilterFactTableHide: false,
    timeLimitModalButtonHide: true,
    filterLimitModalButtonHide: true,
    downloadMvStatisticsHide: true,
    toolBoxDiagnosisClick: true,
    metricModelTableAutoFillNameAndNameZh: true,
    metricModelMeaDimRecommendBtnHide: true,
    calculateFormCheckCfgHide: true,
    vTableOperateButtonsFilter: (value) => {
      const hideActions: string[] = [
        VTableOperateActions.DATA_MASKING,
        VTableOperateActions.CREATE_CEP_TABLE,
        VTableOperateActions.SQL_CLEANING,
        VTableOperateActions.VISUAL_FILTER,
        VTableOperateActions.CREATE_VIRTUAL_TABLE,
      ]
      if (Array.isArray(value)) {
        return value.filter((v) => !hideActions.includes(v.action)) as typeof value
      }
      return value
    },
  },
  CHANG_AN: {
    formDefaultTimeColumnNameAndGranularityAndFormatRequire: false,
    agentHide: true,
    CSVCreateTableHide: false,
    dataSceneHide: true,
    calculateFormDisableDimensions: true,
    metricModelMeaDimRecommendBtnHide: true,
    calculateFormCheckCfgHide: true,
    createMetricCumulativeFormatTemplateHide: true,
    createMetricSynonymsHide: true,
    createMetricFilterHide: false, // 浦发一期隐藏 filter 二期释放
    scenarioDetailCodeValuesBtnsHide: true,
    mvDiskHide: true,
    mvWidthExpand: true,
    mvSearchHide: true,
    mvOperationButtonHide: true,
    mvDetailDiskHide: true,
    mvDetailDropDrownHide: true,
    isCsvMetricmodelUploadSimple: true,
    isCSVToLikeVTRemoveDistributedCfg: true,
    calculateFormCreateMetricAlwaysFalse: true,
    metricModelTableAutoFillNameAndNameZh: true,
    metricModelTableTitleTimeHide: true,
    xlsxUploadMetricWithoutBI: true,
    vTableOperateButtonsFilter: (value) => {
      return value
    },
    scenarioTableColumnsFilter: (columns) => {
      const hideDataIndexes = ['agent']
      if (Array.isArray(columns)) {
        return columns.filter((col) => !hideDataIndexes.includes(col.dataIndex)) as typeof columns
      }
      return columns
    },
    metricModelTableColumnsFilter: (columns) => {
      const hideDataIndexes = ['synonyms', 'formatTemplate']
      if (Array.isArray(columns)) {
        return columns.filter((col) => !hideDataIndexes.includes(col.dataIndex)) as typeof columns
      }
      return columns
    },
    metricModelCreateTypeFilter: (value) => {
      // 浦发一期隐藏掉了 创建嵌套指标模型 二期放开 为了增加以后的控制灵活性，这个变量控制不移除
      return value
    },
    createMetricTypesFilter: (value) => {
      const supportValues = ['simple']
      if (Array.isArray(value)) {
        return value.filter((val) => supportValues.includes(val.value)) as typeof value
      }
      return value
    },
    scenarioDetailMeaTableColumnsFilter: (value) => {
      const hideDataIndexes = ['synonyms']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    scenarioDetailDimTableColumnsFilter: (value) => {
      const hideDataIndexes = ['synonyms', 'filterSwitch', 'values', 'granularity']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    metricListTableColumnsFilter: (value) => {
      const hideDataIndexes = ['synonyms']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    mvListTableColumnsFilter: (value) => {
      const hideDataIndexes = [
        'relatedMetricsCount',
        'compressedSize',
        'uncompressedSize',
        'compressionRatio',
        'scheduleFrequency',
        'hitCount',
      ]
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.dataIndex)) as typeof value
      }
      return value
    },
    mvDetailTabFilter: (value) => {
      const hideDataIndexes = ['overview', 'list', 'partition', 'dependencies']
      if (Array.isArray(value)) {
        return value.filter((val) => !hideDataIndexes.includes(val.key)) as typeof value
      }
      return value
    },
    isUseSimpleMetricmodel: true,
    xlsxMetricBatchUploadTemplate: (value) => '/files/metric-upload-pufa.xlsx' as typeof value,
  },
  DEFAULT: {
    vTableOperateButtonsFilter(value) {
      const hideActions: string[] = [
        VTableOperateActions.CREATE_CEP_TABLE,
        VTableOperateActions.SQL_CLEANING,
        VTableOperateActions.VISUAL_FILTER,
        VTableOperateActions.CREATE_VIRTUAL_TABLE,
      ]
      if (Array.isArray(value)) {
        return value.filter((v) => !hideActions.includes(v.action)) as typeof value
      }
      return value
    },
  },
  PING_AN: {
    agentHide: true,
    metricModelMeaDimRecommendBtnHide: true,
    materialViewListColumnsFilter: (value) => {
      const hideDataIndexes = ['settings']
      if (Array.isArray(value)) {
        return value.filter((btn) => !hideDataIndexes.includes(btn.dataIndex)) as typeof value
      }
      return value
    },
  },
}

// 定义操作按钮类型
export const VTableOperateActions = {
  DELETE: 'DELETE',
  VISUAL_FILTER: 'VISUAL_FILTER',
  SQL_CLEANING: 'SQL_CLEANING',
  CREATE_VIRTUAL_TABLE: 'CREATE_VIRTUAL_TABLE',
  CREATE_CEP_TABLE: 'CREATE_CEP_TABLE',
  DATA_MASKING: 'DATA_MASKING',
  TIME_COLUMN_SETTING: 'TIME_COLUMN_SETTING',
} as const
export const VTableOperateButtons = [
  {
    action: VTableOperateActions.CREATE_VIRTUAL_TABLE,
    label: '复制虚拟表',
    danger: false,
    icon: CopyOutlined,
  },
  {
    action: VTableOperateActions.VISUAL_FILTER,
    label: '可视化 Filter 清洗',
    danger: false,
    icon: FilterOutlined,
  },
  {
    action: VTableOperateActions.SQL_CLEANING,
    label: '自定义 SQL 清洗',
    danger: false,
    icon: CodeOutlined,
  },
  {
    action: VTableOperateActions.CREATE_CEP_TABLE,
    label: '用 SQL 创建虚拟表',
    danger: false,
    icon: FileAddOutlined,
  },
  {
    action: VTableOperateActions.DATA_MASKING,
    label: '数据脱敏',
    danger: false,
    icon: EyeOutlined,
  },
  {
    action: VTableOperateActions.TIME_COLUMN_SETTING,
    label: '时间字段设置',
    danger: false,
    icon: FieldTimeOutlined,
  },
  {
    action: VTableOperateActions.DELETE,
    label: '删除',
    danger: true,
    icon: DeleteOutlined,
  },
]
export const TimeLimitTimeOffsetTimeOptions: DefaultOptionType[] = [
  { value: 'HOURS', label: '小时' },
  { value: 'MINUTES', label: '分钟' },
  { value: 'SECONDS', label: '秒' },
]
export const getLastDotStr = (str: string) => {
  const l = str.split('.')
  return l[l.length - 1]
}
// 定义AES的密钥和初始化向量（IV）
export const XENGINE_AES_KEY = '__secret_^)@*%(_dipeak__' // 密钥
export const XENGINE_AES_IV = '1234567890123456' // 初始化向量
export const TASK_STATUS_LIST = [
  { label: 'CREATED', value: 'CREATED' },
  { label: 'RUNNING', value: 'RUNNING' },
  { label: 'SUSPENDED', value: 'SUSPENDED' },
  { label: 'FAILED', value: 'FAILED' },
  { label: 'COMPLETED', value: 'COMPLETED' },
]

export const TOKEN_BI = 'x-token'
export const TOKEN_RANGER = 'x-ranger-token'
export const U_TOKEN_RANGER = 'x-ranger-u-token'

export const SensitiveWordList = [
  '军队',
  '军营',
  '武警',
  '部队',
  '战区',
  '军委',
  '军网',
  '火箭军',
  '海军',
  '陆军',
  '空军',
  '国防部',
  '保密局',
  '安全部',
  '安全局',
  '机要局',
  '解放军',
  '武装警察',
  '军分区',
  '人武部',
  '军事',
  '军民融合',
  '司令部',
  '警备区',
  '卫戍区',
  '省军区',
  '参谋部',
  '战略支援',
  '联勤保障',
  '战支',
  '军区',
  '装备部',
  '装备发展部',
  '后勤部',
  '后勤保障部',
  '战备建设',
  '国防动员',
  '海警',
  '政治部',
  '政治工作部',
]

export const HUAXIA_APP_ID = 'huaxia'
export const SHANGHAI_AIRPORT_APP_ID = 'shanghai_airport'

// 科学计数法
export const ScientificNotationWithNumbersReg = /[-+]?\d*\.?\d+([eE][-+]?\d+)/g
