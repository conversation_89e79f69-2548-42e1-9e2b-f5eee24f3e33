import MetricConfig from 'src/server/MetricStore/metric-config'
import { OlapRow } from '../common-types'

export function getUsefulData(data: OlapRow[], metricNames: string[] = []) {
  // 如果没提出指标,直接返回原数据
  if (metricNames?.length === 0) {
    return data
  }
  const result = data.filter((item) => {
    const hasData = Object.keys(item).some((key) => {
      if (metricNames.includes(key)) {
        // 值存在或者为零,都算是有可用的数据
        return item[key] === 0 || !!item[key]
      }
      return false
    })
    return hasData
  })
  return result
}

export function getQueriedMetricNames(metricNames: string[], metricConfig?: MetricConfig | null) {
  if (!metricConfig) {
    return metricNames
  }
  const allMetricNames = metricConfig.allMetrics.flatMap((metric) => {
    if (metricNames.includes(metric.name)) {
      return metric.type === 'list' ? metric?.typeParams?.metrics.map((i) => i.name) : metric.name
    }
    return []
  })

  return allMetricNames
}
