const configTypes = ['scene', 'project'] as const
export type ConfigType = (typeof configTypes)[number]

// config_key type@type_id:typeConfigName
export const TYPE_SEPARATOR = '@'
export const CONFIG_KEY_SEPARATOR = ':'

export abstract class AbstractConfig<T = ConfigValueType> {
  constructor(
    public label: string,
    public desc: string,
    public defaultValue: T,
  ) {}
  abstract parse(v: string): T
  abstract stringify(v: T): string
  abstract type: string
}

export class BooleanConfig extends AbstractConfig<boolean> {
  static assert(v: AbstractConfig<boolean>): v is BooleanConfig {
    return v.type === 'boolean'
  }
  type = 'boolean'
  parse(v: string): boolean {
    if (v === 'true') return true
    if (v === 'false') return false
    return this.defaultValue
  }
  stringify(v: boolean): string {
    return v.toString()
  }
}

export class StringConfig extends AbstractConfig<string> {
  static assert(v: AbstractConfig<string>): v is StringConfig {
    return v.type === 'string'
  }
  type = 'string'
  parse(v: string): string {
    return v
  }
  stringify(v: string): string {
    return v
  }
}

export class NumberConfig extends AbstractConfig<number> {
  static assert(v: AbstractConfig<number>): v is NumberConfig {
    return v.type === 'number'
  }
  type = 'number'
  constructor(
    label: AbstractConfig<number>['label'],
    desc: AbstractConfig<number>['desc'],
    defaultValue: AbstractConfig<number>['defaultValue'],
    public min: number | undefined,
    public max: number | undefined,
    public decimals: number | undefined,
  ) {
    super(label, desc, defaultValue)
  }
  parse(v: string): number {
    const num = Number(v)
    if (Number.isNaN(num)) return this.defaultValue
    return num
  }
  stringify(v: number): string {
    return v.toString()
  }
}

export class StringListConfig extends AbstractConfig<string[]> {
  static assert(v: AbstractConfig<string[]>): v is StringListConfig {
    return v.type === 'string-list'
  }
  constructor(
    label: AbstractConfig<number>['label'],
    desc: AbstractConfig<number>['desc'],
    defaultValue: AbstractConfig<string[]>['defaultValue'],
    public separator: string,
  ) {
    super(label, desc, defaultValue)
  }
  type = 'string-list'
  parse(v: string): string[] {
    return v.split(this.separator).filter(Boolean)
  }
  stringify(v: string[]): string {
    return v.join(this.separator)
  }
}

export type ExtractConfigType<C extends AbstractConfig<any>> = C extends AbstractConfig<infer R> ? R : never

export type ConfigValueType =
  | ExtractConfigType<StringConfig>
  | ExtractConfigType<BooleanConfig>
  | ExtractConfigType<NumberConfig>
  | ExtractConfigType<StringListConfig>

export const commonFactory = {
  StringConfig: StringConfig,
  BooleanConfig: BooleanConfig,
  NumberConfig: NumberConfig,
  StringListConfig: StringListConfig,
} as const

export type ConfigFactory = typeof commonFactory

export const getConfigDefinitionMap = (factory: typeof commonFactory) =>
  ({
    scene: {
      enableMetricExactMatch: new factory.BooleanConfig('置信度', '是否启用置信度匹配', false),
      enableFollowUpQuestion: new factory.BooleanConfig('默认追问', '是否启用默认追问', false),
      enableTryQueryUp: new factory.BooleanConfig('无数据追溯查询', '是否启用无数据时追溯查询', false),
      enableAccMetricToastWhenEmptyData: new factory.BooleanConfig(
        '可累加指标无数据区间提示',
        '可累加指标无数据区间提示',
        false,
      ),
      enableAutoHideThink: new factory.BooleanConfig('结果产出之后收起think', '是否在结果产出后自动收起think框', false),
      hintNum: new factory.NumberConfig('Hint数量', '设置Hint的数量', 5, 0, 100, 0),
      hintDenseWeight: new factory.NumberConfig('Hint密集提示权重', 'Hint密集提示权重', 0.5, 0, 1, 1),
      hintSparseWeight: new factory.NumberConfig('Hint稀疏提示权重', 'Hint稀疏提示权重', 0.5, 0, 1, 1),
      recommendQuestions: new factory.StringListConfig('场景推荐问题', '场景推荐问题', [], '@'),
    },
    project: {},
  }) as const

export type ConfigDefinitionMap = ReturnType<typeof getConfigDefinitionMap>

export type ConfigMapKeys = keyof ConfigDefinitionMap

export type GetConfigType<O extends Record<string, AbstractConfig<any>>> = {
  [K in keyof O]: ExtractConfigType<O[K]>
}

export type ConfigManagementMap = {
  [K in ConfigMapKeys]: GetConfigType<ConfigDefinitionMap[K]>
}

export const serverConfigFactory: ConfigFactory = commonFactory

export const ServerConfigManagementMap = getConfigDefinitionMap(serverConfigFactory)

export type SceneConfigValueTypeMap = ConfigManagementMap['scene']
