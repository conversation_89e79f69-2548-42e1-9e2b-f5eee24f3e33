import { Metric, MetricConfig } from 'src/shared/metric-types'
import { ParsedCondition } from './types/match-types'

/**
 * 将python返回的where条件通过 AND 进行分割
 * @param wherePart
 * @returns {string[]}
 */
function splitOriginWhere(wherePart: string): string[] {
  return wherePart.trim().split(' AND ').filter(Boolean)
}

type NameAndValues = {
  name: string
  codeValues: string[]
}

function extractNameAndValues(whereClause: string): NameAndValues | null {
  // 1. 处理 IN 语句
  const inRegex = /(\w+)\s+IN\s+\(((?:'[^']*'(?:,\s*)?)*)\)/
  const inMatch = whereClause.match(inRegex)
  if (inMatch) {
    const name = inMatch[1]
    const codeValuesString = inMatch[2]
    const codeValues = codeValuesString
      .split(',')
      .map((value) => value.replace(/'/g, '').trim())
      .filter((value) => value !== '')
    return { name, codeValues }
  }

  // 处理 `=` 和 `OR`
  const eqOrRegex = /(\w+)\s*=\s*'([^']*)'/g
  const eqOrMatches = [...whereClause.matchAll(eqOrRegex)]

  if (eqOrMatches.length > 0) {
    const dimensionNames = new Set(eqOrMatches.map((match) => match[1])) // 收集所有出现的字段名
    if (dimensionNames.size > 1) {
      return null
    }
    const name = eqOrMatches[0][1]
    const codeValues = eqOrMatches.map((match) => match[2])
    return { name, codeValues }
  }

  return null
}

/**
 * 解析 WHERE 条件并匹配维度或指标
 * @param wherePart WHERE 条件字符串
 * @param metricConfig 维度和指标的配置信息
 */
export function parseWhereConditions(
  wherePart: string | undefined,
  metricConfig?: MetricConfig | null,
): ParsedCondition[] {
  if (!wherePart) return []

  const conditions = splitOriginWhere(wherePart)
    .map((part) => extractNameAndValues(part))
    .filter((x): x is NameAndValues => x !== null)

  return conditions.map(({ name, codeValues }) => {
    let type: 'dimension' | 'metric' | 'unknown' = 'unknown'
    let label = name

    const dimension = metricConfig?.allDimensions.find((d) => d.name === name)
    const metric = metricConfig?.allMetrics.find((m) => m.name === name)

    if (dimension) {
      type = 'dimension'
      label = dimension.label
    } else if (metric) {
      type = 'metric'
      label = metric.label
    }

    return {
      type,
      value: { name, label, codeValues },
    }
  })
}

export function conditionsToWherePart(conditions: ParsedCondition[]): string {
  return conditions
    .filter((c) => c.value.codeValues.length > 0)
    .map((c) => `${c.value.name} IN (${c.value.codeValues.map((v) => `'${v}'`).join(', ')})`)
    .join(' AND ')
}

export function isDocMetric(metric?: Metric) {
  return !!metric?.config?.specific_for_doc
}
