import { Message } from '../common-types'
import { ChartThemeType, ECHARTS_CHART_TYPES, LOCAL_CHART_TYPES } from '../constants'
import {
  AttrTimeDiffParams,
  Dimension,
  MetricWithPeriodOverPeriod,
  QueryParams,
  QueryParamsVerified,
  TimeQueryParams,
} from '../metric-types'

/* eslint-disable @typescript-eslint/naming-convention */
export interface ChatRequest {
  conversationId: string
  message: string
  taskId: string
  traceId: string
  parentId?: string
  sceneId?: string
  projectId: string
  enableFollowUpQuestion: boolean
  enableMetricExactMatch: boolean
  enableTryQueryUp: boolean
  enableAccMetricToastWhenEmptyData: boolean
  currentParamsExtractApi: string
  enableInternetSearch: boolean
  enableDocSearch: boolean
  enableBi: boolean
  additionalInfo: {
    hintDynamicLimit: number
    hintDenseWeight: number
    hintSparseWeight: number
  }
  messages: Message[]
  fileIds?: string[]
  dirIds?: number[]
  extractedParams?: any
}

export interface BaseChatResponse {
  ready: true
  conversationId: string
  projectId: string
  sceneId: string
  traceId?: string
  isPartialRow?: boolean
  partialRowMsg?: string
  taskId?: string
}

/** 支持的图表类型枚举 */
export type EChartsChartType = (typeof ECHARTS_CHART_TYPES)[number]

export type LocalChartType = (typeof LOCAL_CHART_TYPES)[number]

export type ChartType = EChartsChartType | LocalChartType

// TODO: 是否可以去掉，直接使用 Dimension 和 Metric？
export type RowsMetadataDimension = {
  type: 'dimension'
  value: Dimension
}
export type RowsMetadataMetric = {
  type: 'metric'
  value: MetricWithPeriodOverPeriod
}

export type RowsMetadata = (RowsMetadataDimension | RowsMetadataMetric)[]

/** 后端返回的一行数据，和 RowDataPacket 区别是，OlapRow 的 key 只能为 string，不支持 number */
export interface OlapRow {
  constructor: {
    name: 'OlapRow'
  }
  [column: string]: any
}

export interface AttrAnalysisItem {
  label: string
  name: string
  expr_calc: string
  expr_relation: string
  node_type: 'summable' | 'non_summable' // 可加和 不可加和
  attribution_analysis_result: {
    values: [number, number]
    abs_change: number
    rate_change: number
    contribution: number
    rank: number // 排名
  }
  children_names: Array<AttrAnalysisItem>
}

export interface AttrAnalysisDimension {
  metric: string //指标名
  dim: string //维度
  js: number //js散度
  element_pos: string[] // 正向贡献的码值 最多有三个
  element_neg: string[] // 反向贡献的码值 最多有三个
  contribution_pos: number[] // 正向贡献度
  contribution_neg: number[]
  change_pos: number[] // 波动 码值的值的波动
  change_neg: number[]
}

export interface AttrAnalysisResult {
  tree: AttrAnalysisItem
  dimension: AttrAnalysisDimension[]
  base_compare: AttrTimeDiffParams
  attr_params: {
    metric: string[]
    filter: string
  }
  metric_sql: {
    sqlBase: string
    sqlCompare: string
  }
  mockChart?: boolean
}

export interface ChatResponseQueryMetric extends BaseChatResponse {
  taskType: 'query-metric'
  sql?: string
  sqlColumnName?: string[]
  chartType: ChartType
  chartTitle: string
  queryParamsVerified: QueryParamsVerified
  recommendChartTypes: ChartType[]
  rowsMetadata: RowsMetadata
  /** 警告性文案 */
  infoTexts: string[]
  /** 置信度选择原始数据 */
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  rows: OlapRow[]
  isPartialRow: boolean
  partialRowMsg: string
}

export interface ChatResponsePercentage extends BaseChatResponse {
  taskType: 'percentage'
  calculator: any[]
}

export interface ChatResponsePeriodOnPeriod extends BaseChatResponse {
  taskType: 'period-on-period'
  calculator: any[]
}
// export interface ChatResponseMultiAgent extends BaseChatResponse {
//   taskType: 'multi-agent'
//   data: MultiAgentData
// }

export interface ChatResponseMetricExactMatch extends BaseChatResponse {
  taskType: 'metric-exact-match'
  metricNames: string[]
  queryParamsVerified: QueryParamsVerified
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  infoTexts: string[]
}

export interface ChatResponseExternalReport extends BaseChatResponse {
  taskType: 'query-external-report'
  externalReports: string[]
  where: string
  queryParamsVerified: QueryParamsVerified
  timeQueryParams?: TimeQueryParams
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
}

export interface ChatResponseAttributionAnalysis extends BaseChatResponse {
  taskType: 'attribution-analysis'
  chartType: 'AttrAnalysis'
  chartTitle: string
  sql: string
  rows: [AttrAnalysisResult]
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AttrMetricAnalysisResult {
  reportPrompt: string
  metricName: string
  metricValue: { [key: string]: number }
  metricExpr?: string
  dimensionDetail: {
    name: string
    data: { [key: string]: string | number }[]
  }[]
}

export interface ChatResponseAttributionMetricAnalysis extends BaseChatResponse {
  taskType: 'attribution-metric-analysis'
  chartType: 'AttrMetricAnalysis'
  chartTitle: string
  rows: [AttrMetricAnalysisResult]
  isPartialRow: boolean
  partialRowMsg: string
}

export interface ChatResponseDataOverview extends BaseChatResponse {
  taskType: 'data-overview'
  rows: string
}

export interface ChatResponseTableList extends BaseChatResponse {
  taskType: 'table-list'
  rows: string
}

export interface ChatResponseDimensionList extends BaseChatResponse {
  taskType: 'dimension-list'
  rows: string
}

export interface ChatResponseDimensionDetail extends BaseChatResponse {
  taskType: 'dimension-detail'
  rows: string
}

export interface ChatResponseMetricList extends BaseChatResponse {
  taskType: 'metric-list'
  rows: string
}

export interface ChatResponseMetricDetail extends BaseChatResponse {
  taskType: 'metric-detail'
  rows: string
}

export interface ChatResponseMetricTree extends BaseChatResponse {
  taskType: 'metric-tree'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface ChatResponseChitchat extends BaseChatResponse {
  taskType: 'chitchat'
}

export interface ChatResponseLlmError extends BaseChatResponse {
  taskType: 'llm-error'
  content: string
}
export interface ChatResponseError extends Omit<BaseChatResponse, 'ready'> {
  taskType: 'chat-error'
  ready: false
  errType: ChatResponseErrorStatus
  // 部分错误原因由前端写死的, 所以unreadyReason可以改为可选
  unreadyReason?: string
  subUnreadyReason?: string
  metricNames?: string[]
  whereCompany?: string[]
  queryParamsVerified?: QueryParamsVerified
  sql?: string
  sqlColumnName?: string[]
  originResponse?: any
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  infoTexts?: string[]
  llmResponse?: any
  originLlmResponse?: any
}

// 错误类型(包括后端返回的及前端处理的)
export const ChatResponseErrorTypes = {
  E_UNKNOWN: 'E_UNKNOWN', // 其他错误
  LLM_ERROR: 'LLM_ERROR', // 大模型出错；大模型超时
  METRICS_NOT_EXIST: 'METRICS_NOT_EXIST', // 给定项目/场景中不存在指标-数据尚未产生
  DIMENSIONS_TREE_NOT_EXIST: 'DIMENSIONS_TREE_NOT_EXIST', // 给定项目/场景中不存在维度-数据尚未产生
  METRIC_TREE_NOT_EXIST: 'METRIC_TREE_NOT_EXIST', // 归因无法找到指标树
  ATTR_ANALYSIS_NOT_SUPPORT: 'ATTR_ANALYSIS_NOT_SUPPORT', // 不支持的归因类型
  ATTR_ANALYSIS_DATA_NOT_EXIST: 'ATTR_ANALYSIS_DATA_NOT_EXIST', // 归因无法找到相关数据
  CHIT_CHAT: 'CHIT_CHAT', // 意图识别出错
  PROJECT_NOT_EXIST: 'PROJECT_NOT_EXIST', // 项目不存在
  MODEL_NOT_EXIST: 'MODEL_NOT_EXIST', // 场景不存在
  REFRESHING_CACHE: 'REFRESHING_CACHE', // 场景创建中
  PARAM_EXTRACT_EMPTY_RESULT: 'PARAM_EXTRACT_EMPTY_RESULT', // 提参结果为空(指标未录入/不存在)
  LATEST_DATA_NOT_EXIST: 'LATEST_DATA_NOT_EXIST', // 数据正在处理中-前端使用, 本月问上月，但是还差几天没有更新
  FUTURE_DATA_NOT_EXIST: 'FUTURE_DATA_NOT_EXIST', // 数据尚未产生-前端使用, 本月问未来的数据
  NO_DATA_AUTHORITY: 'NO_DATA_AUTHORITY', // 没有数据访问权限 -- 前端判断并使用
  QUESTION_NOT_SUPPORTED: 'QUESTION_NOT_SUPPORTED', // 问题不支持
  LOOKUP_FAILED: 'LOOKUP_FAILED', // 查询指标的值失败
  CALCULATE_FAILED: 'CALCULATE_FAILED', // 查询指标的值失败
  COST_SCENE_ERROR: 'COST_SCENE_ERROR', // 成本场景错误
  AGENT_BRAIN_NO_SUCCEED_TOOL_CALL: 'AGENT_BRAIN_NO_SUCCEED_TOOL_CALL', // brain的所有工具调用都失败了
  AGENT_JUDGE_NO_SUCCEED_TOOL_CALL: 'AGENT_JUDGE_NO_SUCCEED_TOOL_CALL', // judge的所有工具调用都失败了
  AGENT_BRAIN_ILLEGAL_TOOL_CALL: 'AGENT_BRAIN_ILLEGAL_TOOL_CALL', // brain调用了非法工具
  AGENT_JUDGE_ILLEGAL_TOOL_CALL: 'AGENT_JUDGE_ILLEGAL_TOOL_CALL', // judge调用了非法工具
  SENSITIVE_QUESTION: 'SENSITIVE_QUESTION', // 政治正确敏感词
  NEED_MANUAL_SELECT: 'NEED_MANUAL_SELECT', // 需要人工选择
  MANUAL_SELECT_NOT_EXIST: 'MANUAL_SELECT_NOT_EXIST', // 人工选择的缓存不存在，通常是因为过期被删除了
  MANUAL_SELECT_NOT_ENOUGH: 'MANUAL_SELECT_NOT_ENOUGH', // 前端传回来的人工选择少了
  NOT_NEED_BI_RESULT: 'NOT_NEED_BI_RESULT', // 前端传回来的人工选择少了
  E_MATCH: 'E_MATCH', // 置信度匹配出错
  E_MAKE_AGENT_REQUEST_CANCELLED: 'E_MAKE_AGENT_REQUEST_CANCELLED', // 置信度匹配出错
} as const

// 后端返回的code及错误映射
export const ChatResponseErrorMap = {
  '-1': ChatResponseErrorTypes.E_UNKNOWN,
  '-2': ChatResponseErrorTypes.LLM_ERROR,
  '-3': ChatResponseErrorTypes.METRICS_NOT_EXIST,
  '-4': ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST,
  '-5': ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST,
  '-6': ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT,
  '-7': ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST,
  '-8': ChatResponseErrorTypes.PROJECT_NOT_EXIST,
  '-9': ChatResponseErrorTypes.MODEL_NOT_EXIST,
  '-10': ChatResponseErrorTypes.REFRESHING_CACHE,
  '-11': ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT,
  '-12': ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED,
  '-13': ChatResponseErrorTypes.LOOKUP_FAILED,
  '-14': ChatResponseErrorTypes.CALCULATE_FAILED,
  '-15': ChatResponseErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL,
  '-16': ChatResponseErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL,
  '-17': ChatResponseErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL,
  '-18': ChatResponseErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL,
  '-19': ChatResponseErrorTypes.SENSITIVE_QUESTION,
  '-20': ChatResponseErrorTypes.NEED_MANUAL_SELECT,
  '-21': ChatResponseErrorTypes.MANUAL_SELECT_NOT_EXIST,
  '-22': ChatResponseErrorTypes.MANUAL_SELECT_NOT_ENOUGH,
  '-23': ChatResponseErrorTypes.NOT_NEED_BI_RESULT,
  '-100': ChatResponseErrorTypes.E_MATCH,
  '-101': ChatResponseErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED,
}

export type ChatResponseErrorStatus = (typeof ChatResponseErrorTypes)[keyof typeof ChatResponseErrorTypes]

export const ConverChatErrorTypes = {
  NO_METRICS: 'NO_METRICS',
  NO_DATA_AUTHORITY: 'NO_DATA_AUTHORITY',
  OTHER_ERROR: 'OTHER_ERROR',
  CHIT_CHAT: 'CHIT_CHAT',
  LLM_ERROR: 'LLM_ERROR',
  // NOT_ANALYSIS: 'NOT_ANALYSIS',
  LATEST_DATA_NOT_EXIST: 'LATEST_DATA_NOT_EXIST',
  FUTURE_DATA_NOT_EXIST: 'FUTURE_DATA_NOT_EXIST',
  DATA_ERROR_FEEDBACK: 'DATA_ERROR_FEEDBACK',
  QUESTION_NOT_SUPPORTED: 'QUESTION_NOT_SUPPORTED',
  LOOKUP_FAILED: 'LOOKUP_FAILED', // 查询指标的值失败
  CALCULATE_FAILED: 'CALCULATE_FAILED', // 查询指标的值失败
  AGENT_BRAIN_NO_SUCCEED_TOOL_CALL: 'AGENT_BRAIN_NO_SUCCEED_TOOL_CALL',
  AGENT_JUDGE_NO_SUCCEED_TOOL_CALL: 'AGENT_JUDGE_NO_SUCCEED_TOOL_CALL',
  AGENT_BRAIN_ILLEGAL_TOOL_CALL: 'AGENT_BRAIN_ILLEGAL_TOOL_CALL',
  AGENT_JUDGE_ILLEGAL_TOOL_CALL: 'AGENT_JUDGE_ILLEGAL_TOOL_CALL',
  SENSITIVE_QUESTION: 'SENSITIVE_QUESTION',
  NEED_MANUAL_SELECT: 'NEED_MANUAL_SELECT',
  MANUAL_SELECT_NOT_EXIST: 'MANUAL_SELECT_NOT_EXIST',
  MANUAL_SELECT_NOT_ENOUGH: 'MANUAL_SELECT_NOT_ENOUGH',
  NOT_NEED_BI_RESULT: 'NOT_NEED_BI_RESULT',
  E_MATCH: 'E_MATCH',
  E_MAKE_AGENT_REQUEST_CANCELLED: 'E_MAKE_AGENT_REQUEST_CANCELLED',
} as const

export const ConverChatErrorTypesMap = {
  [ConverChatErrorTypes.NO_METRICS]: '指标未录入',
  [ConverChatErrorTypes.NO_DATA_AUTHORITY]: '没有数据权限',
  [ConverChatErrorTypes.OTHER_ERROR]: '其他错误',
  [ConverChatErrorTypes.CHIT_CHAT]: '非数据分析问题',
  [ConverChatErrorTypes.LLM_ERROR]: '大模型网络断开',
  // NOT_ANALYSIS: '非数据分析类问题',
  [ConverChatErrorTypes.LATEST_DATA_NOT_EXIST]: '时间段下无数据-暂未更新',
  [ConverChatErrorTypes.FUTURE_DATA_NOT_EXIST]: '时间段下无数据-未来时间',
  [ConverChatErrorTypes.DATA_ERROR_FEEDBACK]: '反馈数据问题',
  [ConverChatErrorTypes.QUESTION_NOT_SUPPORTED]: '问题类型不支持',
} as const

export interface DragTaskItem {
  code: string
  name: string
}

export interface DragTableColumn {
  name: string
  items: DragTaskItem[]
}
// 生成报告模块中的字段修正拖拽
export interface FieldCorrectionColumns {
  timeDimensionsColumn: DragTableColumn
  dimensionsColumn: DragTableColumn
  metricsColumn: DragTableColumn
}

// 生成报告中的参数
export interface DocReportParamsType {
  fileId: string
  chatId: string
  userIntent: string
  focusDimensions: string[]
  focusMetrics: string[]
  timeDimension: string
  timeRangeEnd: string
  timeRangeStart: string
  timeRange: number | null
  isRegenerateReportAgainTag: boolean
}
export interface ChatResponseDocReport extends BaseChatResponse {
  taskType: 'doc-report'
  rows: any
  isReportGenerated?: boolean
}

/** 为了避免 common-types 和 askdoc-types 循环引用，把这3个类型定义到 common-types */
export interface AskDocTextNode {
  fileId: string
  content: string
  fileName: string
  folderId: string
  nodeId: string
  partName: string
  page: number
  columnIndex?: boolean
}

export interface AskDocImageNode {
  fileId: string
  content: string
  url: string
  page: number
}

export interface AskDocSourceNodes {
  textNodes: AskDocTextNode[]
  imageNodes: AskDocImageNode[]
}

export interface DocResultType {
  indexType: 'Document' | 'Folder'
  content: string
  sourceNodes?: AskDocSourceNodes
}

export interface ChatResponseDocResult extends BaseChatResponse {
  taskType: 'doc-result'
  content: DocResultType
}

export type ChatResponse =
  | ChatResponseQueryMetric
  | ChatResponseMetricExactMatch
  | ChatResponseExternalReport
  | ChatResponseAttributionAnalysis
  | ChatResponseAttributionMetricAnalysis
  | ChatResponseDataOverview
  | ChatResponseTableList
  | ChatResponseDimensionList
  | ChatResponseDimensionDetail
  | ChatResponseMetricList
  | ChatResponseMetricDetail
  | ChatResponseMetricTree
  | ChatResponseChitchat
  | ChatResponseLlmError
  | ChatResponseDocReport
  | ChatResponseError
  | ChatResponsePercentage
  | ChatResponsePeriodOnPeriod
// | ChatResponseMultiAgent

// Type guard to check if a RowsMetadata item is a RowsMetadataDimension
export function isDimension(item: RowsMetadataDimension | RowsMetadataMetric): item is RowsMetadataDimension {
  return item.type === 'dimension'
}
// Type guard to check if a RowsMetadata item is a RowsMetadataMetric
export function isMetric(item: RowsMetadataDimension | RowsMetadataMetric): item is RowsMetadataMetric {
  return item.type === 'metric'
}

/** 保存图表的响应格式 */
export interface ReadyChartResponse extends ChatResponseQueryMetric {
  id: string
  username: string
  ask: string
  queryParams: QueryParams
  originRows: OlapRow[] | [AttrAnalysisResult]
  chartThemeType: ChartThemeType
  //   llmType: LlmType
  createdAt: Date
  updatedAt: Date
  errorMessage?: string
}
