/* eslint-disable no-underscore-dangle */
import { difference } from 'lodash'
import {
  DerivedMetric,
  Dimension,
  ExternalReport,
  Measure,
  Metric,
  NoListMetric,
  RatioMetric,
  TimeDimensionDatum,
} from 'src/shared/metric-types'
import { Hookable } from 'src/shared/types/hook'
import { assertExhaustive } from '../common-utils'

export function throwError(msg: string): never {
  throw new Error(msg)
}

export function groupBy<T>(arr: T[], getKey: (item: T) => string): Record<string, T> {
  return arr.reduce(
    (obj, item) => {
      obj[getKey(item)] = item
      return obj
    },
    {} as ReturnType<typeof groupBy<T>>,
  )
}

export type NormalizeNameParamOptions =
  | string
  | {
      name: string // metric name
    }

export function normalizeNameParam(name: NormalizeNameParamOptions) {
  return typeof name === 'string' ? name : name.name
}

export function formatName(name: string) {
  return name.toUpperCase().trim()
}

/**
 * 把 measure 转成 sql 片段
 */
export function measure2Sql(measure: Measure) {
  if (measure.expr == null) {
    throw new Error(`The expression is null`)
  }
  const aggKeywords = ['sum', 'sum_boolean', 'count', 'avg', 'max', 'min']
  if (!aggKeywords.some((keyword) => measure.expr.toLocaleLowerCase().includes(keyword))) {
    throw new Error(`${measure.expr} expression is illegal`)
  }
  return measure.expr
}

export class MetricConfig extends Hookable {
  static from(o: any) {
    try {
      if (typeof o === 'string') o = JSON.parse(o)
      if (!o) throw new Error('初始化失败')
      return new MetricConfig(
        o.id,
        o.name,
        o.allDimensions,
        o.allMeasures,
        o.allMetrics,
        o.hotMetrics,
        o.allExternalReports,
        o.updateTime,
        o.timeDimensionDatum,
        o.description,
      )
    } catch (e) {
      console.info('MetricConfigFromError')
      console.error(e)
      return null
    }
  }

  hooks = Object.freeze({})

  dimensionRecord: Record<string, Dimension>
  measureRecord: Record<string, Measure>
  metricRecord: Record<string, Metric>
  hotMetricRecord: Record<string, Metric>
  externalReportRecord: Record<string, ExternalReport>

  // compat
  get metricTableName() {
    return this.name
  }

  constructor(
    public id: string,
    public name: string,
    public allDimensions: Dimension[],
    public allMeasures: Measure[],
    public allMetrics: Metric[],
    public hotMetrics: Metric[],
    public allExternalReports: ExternalReport[],
    public updateTime: number,
    public timeDimensionDatum?: TimeDimensionDatum,
    public description?: string,
  ) {
    super()
    this.dimensionRecord = groupBy(this.allDimensions, (item) => formatName(item.name))
    this.measureRecord = groupBy(this.allMeasures, (item) => formatName(item.name))
    this.metricRecord = groupBy(this.allMetrics, (item) => formatName(item.name))
    this.hotMetricRecord = groupBy(this.hotMetrics, (item) => formatName(item.name))
    this.externalReportRecord = groupBy(this.allExternalReports, (item) => formatName(item.name))
    //  兼容
    this.allMetrics.forEach((metric) => (metric.displayExpr = this.getMetricDisplayExpr(metric)))
    this.hotMetrics.forEach((metric) => (metric.displayExpr = this.getMetricDisplayExpr(metric)))
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      // compact
      metricTableName: this.metricTableName,
      allDimensions: this.allDimensions,
      allMeasures: this.allMeasures,
      allMetrics: this.allMetrics,
      hotMetrics: this.hotMetrics,
      allExternalReports: this.allExternalReports,
      updateTime: this.updateTime,
      timeDimensionDatum: this.timeDimensionDatum,
      description: this.description,
    }
  }

  private _has<T>(record: Record<string, T>, name: NormalizeNameParamOptions): boolean {
    return Boolean(record[formatName(normalizeNameParam(name))])
  }
  hasDimension(name: NormalizeNameParamOptions) {
    return this._has(this.dimensionRecord, name)
  }
  hasMeasure(name: NormalizeNameParamOptions) {
    return this._has(this.measureRecord, name)
  }
  hasMetric(name: NormalizeNameParamOptions) {
    return this._has(this.metricRecord, name)
  }
  hasHotMetric(name: NormalizeNameParamOptions) {
    return this._has(this.hotMetricRecord, name)
  }
  hasExternalReport(name: NormalizeNameParamOptions) {
    return this._has(this.externalReportRecord, name)
  }

  private _findByName<T, R>(record: Record<string, T>, name: NormalizeNameParamOptions, onEmpty: () => R) {
    return record[formatName(normalizeNameParam(name))] ?? onEmpty()
  }
  private _findByNameWithThrow<T>(record: Record<string, T>, name: NormalizeNameParamOptions, type: string) {
    return this._findByName<T, never>(record, name, () => throwError(`${type}: [${name}]不能为空`))
  }
  findDimensionByName(name: NormalizeNameParamOptions) {
    return this._findByNameWithThrow(this.dimensionRecord, name, 'dimension')
  }
  findMeasureByName(name: NormalizeNameParamOptions) {
    return this._findByNameWithThrow(this.measureRecord, name, 'measure')
  }
  findMetricByName(name: NormalizeNameParamOptions) {
    return this._findByNameWithThrow(this.metricRecord, name, 'metric')
  }
  findHotMetricByName(name: NormalizeNameParamOptions) {
    return this._findByNameWithThrow(this.hotMetricRecord, name, 'hotMetric')
  }
  findExternalReportByName(name: NormalizeNameParamOptions) {
    return this._findByNameWithThrow(this.externalReportRecord, name, 'externalReport')
  }

  private _findByNameWithoutErr<T>(record: Record<string, T>, name: NormalizeNameParamOptions) {
    return this._findByName<T, null>(record, name, () => null)
  }
  findDimensionByNameWithoutErr(name: NormalizeNameParamOptions) {
    return this._findByNameWithoutErr(this.dimensionRecord, name)
  }
  findMeasureByNameWithoutErr(name: NormalizeNameParamOptions) {
    return this._findByNameWithoutErr(this.measureRecord, name)
  }
  findMetricByNameWithoutErr(name: NormalizeNameParamOptions) {
    return this._findByNameWithoutErr(this.metricRecord, name)
  }
  findHotMetricByNameWithoutErr(name: NormalizeNameParamOptions) {
    return this._findByNameWithoutErr(this.hotMetricRecord, name)
  }
  findExternalReportByNameWithoutErr(name: NormalizeNameParamOptions) {
    return this._findByNameWithoutErr(this.externalReportRecord, name)
  }

  getAllNoListMetrics(): NoListMetric[] {
    return this.allMetrics.filter(
      (m) => m.type === 'simple' || m.type === 'derived' || m.type === 'ratio',
    ) as NoListMetric[]
  }

  /**
   * 把 metric 转换成 sql 片段
   * @param metric 指标
   */
  metric2SqlByType(metric: Metric): string {
    const metricType = metric.type
    switch (metricType) {
      case 'simple': {
        const measure = this.findMeasureByName(metric.typeParams.measure)
        return `${measure2Sql(measure)}`
      }
      case 'derived': {
        // 正则表达式匹配字母或数字组成的词，并确保这些词是独立的（通过边界断言）
        const derivedExpr = metric.typeParams.expr.replace(/\b[a-zA-Z0-9_]+\b/g, (match) => {
          // 只替换指标名
          if (this.allMetrics.find((m) => m.name.toLowerCase().trim() === match.toLowerCase().trim())) {
            return `${match}_TBL.${match}`
          } else {
            return match
          }
        })
        return `(${derivedExpr})`
      }
      case 'ratio': {
        return `Unavailable, create in generateSql`
      }
      case 'list': {
        throw Error('sql片段不支持list metric')
      }
      default:
        return assertExhaustive(metricType)
    }
  }

  /**
   * metric 的 displayExpr。以后可能修改
   */
  getMetricDisplayExpr(metric: Metric) {
    const metricType = metric.type

    switch (metricType) {
      case 'simple':
        return this.metric2SqlByType(metric)
      case 'derived': {
        return (metric as DerivedMetric).typeParams.expr
      }
      case 'ratio': {
        return (metric as RatioMetric).typeParams.numerator + ' / ' + (metric as RatioMetric).typeParams.denominator
      }
      case 'list':
        return '引擎生成，包括：' + metric.typeParams.metrics.map((metric) => metric.name).join(', ')
      default:
        return assertExhaustive(metricType)
    }
  }

  formatName(name: string) {
    return formatName(name)
  }

  /**
   * 对指标数组进行拓扑排序
   */
  getOrderedMetrics(metricNames: string[]) {
    // 记录指标依赖了哪些指标
    const parentsRecord: Record<string, string[]> = {}
    // 记录指标被哪些指标依赖
    const childrenRecord: Record<string, string[]> = {}
    const queue: string[] = Array.from(metricNames)
    // 记录所有遍历过的指标
    const allMetrics = new Set<string>()
    // 记录所有的非根指标，即有依赖的指标
    const nonRootMetrics = new Set<string>()
    // 整理
    while (queue.length) {
      const metricName = queue.shift()!
      allMetrics.add(metricName)
      // 没找到就直接报错
      const metric = this.findMetricByName(metricName)
      const depList: string[] = []
      if (metric.type === 'ratio') {
        // 比值类指标 上下比值的依赖指标一样的，否则无意义
        depList.push(metric.typeParams.numerator)
      } else if (metric.type === 'derived') {
        // 派生指标 获取所有的依赖指标
        depList.push(...metric.typeParams.metrics.map((v) => v.name))
      }
      // 如果存在依赖，就收集
      if (depList.length) {
        parentsRecord[metricName] ??= []
        nonRootMetrics.add(metricName)
        depList.forEach((child) => {
          childrenRecord[child] ??= []
          childrenRecord[child].push(metricName)
          parentsRecord[metricName].push(child)
        })
        queue.push(...depList)
      }
    }
    // 排序
    const parentsSetRecord: Record<string, Set<string>> = Object.fromEntries(
      Object.entries(parentsRecord).map(([k, arr]) => [k, new Set(arr)]),
    )
    queue.length = 0
    queue.push(...difference<string>(Array.from(allMetrics), Array.from(nonRootMetrics)))
    const levelRecord: Record<string, number> = {}
    const levels: string[][] = []
    const level: string[] = []
    let size = queue.length
    while (queue.length) {
      const metricName = queue.shift()!
      level.push(metricName)
      levelRecord[metricName] = levels.length
      for (const child of childrenRecord[metricName] ?? []) {
        parentsSetRecord[child]?.delete(metricName)
        // 如果当前依赖都被处理，那他就可以被处理
        if (parentsSetRecord[child].size === 0) {
          queue.push(child)
        }
      }
      size -= 1
      if (size === 0) {
        levels.push(level.slice())
        level.length = 0
        size = queue.length
      }
    }
    return { levels, levelRecord, parentsRecord, childrenRecord }
  }
}
