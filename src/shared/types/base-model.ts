/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
import EventEmitter from 'eventemitter3'

export type NonFunctionKeys<T> = Exclude<keyof T, { [P in keyof T]: T[P] extends Function ? P : never }[keyof T]>

export class BaseModel<
  EventTypes extends EventEmitter.ValidEventTypes = string | symbol,
  Context extends any = any,
> extends EventEmitter<EventTypes, Context> {
  set<K extends NonFunctionKeys<this>, V extends this[K]>(key: K, val: V) {
    this[key] = val
    return this
  }
  get<K extends NonFunctionKeys<this>>(key: K) {
    return this[key]
  }
}
