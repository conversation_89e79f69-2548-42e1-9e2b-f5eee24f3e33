import express, { Router } from 'express'
import chalk from 'chalk'
import { nanoid } from 'nanoid'
import axios from 'axios'
import { DEFAULT_TIMEOUT, MULTI_SCENE_CHAT_MOCK_SCENE_ID } from 'src/shared/constants'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { DATE_ALIAS, MetricTrendResponse } from 'src/shared/metric-types'
import { defaultResponseHeaders, sseStringify, ResponseEntity } from 'src/shared'
import {
  LlmType,
  ChatResponseErrorTypes,
  ChatResponse,
  APIResponse,
  OlapRow,
  ChatResponseErrorMap,
  ChatResponseError,
  ChatRequest,
} from 'src/shared/common-types'
import { setAxiosDefaults } from 'src/shared/config'
import { reportLogs } from 'src/server/winston-log'
import { fetchProjectInfoBySceneId } from 'src/server/AskBI/datasets/dao'
import { asyncResponseWrapper } from 'src/server/asyncResponseWrapper'
import { recordMetricFrequencyFromResult } from '../AskBI/metrics/dao'
import { PROCESS_ENV } from '../server-constants'
import { paramsExtractChat } from '../ai/askbi-client'
import { getFollowUpQuestionParams, updateConverChatResponse } from '../AskBI/conver_chats/utils'
import MetricConfig from '../MetricStore/metric-config'
import { processMultiScenesChatMetricParams } from '../AskBI/chats/utils'
import { buildChatMetricParams, processExtractedParams } from '../AskBI/chats/chat-pipelines'
import { Chat } from './chat'

function createRouter() {
  const router: Router = express.Router()

  router.post(
    '/chat',
    asyncResponseWrapper(async (req, res) => {
      const url = '/agent/chat'
      const chatRequest: ChatRequest = req.body
      console.info(chalk.cyan(`POST ${url} with request body: `), req.body)
      const chat = new Chat({ req })
      return res.json({ code: 0, data: await chat.invoke(chatRequest) })
    }),
  )

  router.post(
    '/metric-query',
    asyncResponseWrapper(async (req, res) => {
      const startTime = Date.now()
      const url = '/agent/metric-query'
      console.info(chalk.cyan(`POST ${url} with request body: `), req.body)

      // 获取到header中的traceId并设置到axios请求头中
      const traceId = req.header('traceId') as string
      setAxiosDefaults(traceId)
      const username = req.user?.username || ''
      const userIp = req.header('X-Real-IP') || req.socket.remoteAddress
      const host = req.header('Host') || ''

      console.info(`${url} Request and user info: username = ${username}, user ip = ${userIp}, server host = ${host}.`)

      const message: string = req.body.message
      const chatId = req.body.chatId
      // 后续启用默认追问功能
      const enableFollowUpQuestion = req.body.enableFollowUpQuestion
      const enableMetricExactMatch = req.body.enableMetricExactMatch
      // 提参接口api地址
      const currentParamsExtractApi = req.body.currentParamsExtractApi

      // 宝武权限相关数据
      const authData = { userId: username }

      const sceneId = req.body.sceneId
      const projectId = req.body.projectId
      const taskId = req.body.taskId
      // 对于新的对话没有 conversationId，在服务端返回一个 nanoid 作为 conversationId。
      let conversationId = req.body.conversationId
      if (conversationId == null) {
        conversationId = nanoid()
      }

      const llmType: LlmType = req.body.llmType

      try {
        // [1]默认追问功能，会自动携带上一轮python提参结果 history_params_extract_data
        const followUpQuestionParams = await getFollowUpQuestionParams(conversationId, chatId, enableFollowUpQuestion)

        console.info(chalk.bgCyanBright('/metric-query 截止默认追问功能结束耗时: ', Date.now() - startTime))
        // [2]用户前端点击按钮追问 会带上历史的会话 和SQL查数结果
        const messages = [{ role: 'user' as const, content: message }]

        const projectParamsExtractResult = await paramsExtractChat(
          messages,
          sceneId,
          projectId,
          llmType,
          enableMetricExactMatch,
          followUpQuestionParams,
          currentParamsExtractApi,
          { traceId, username, host, conversationId, chatId, req },
          req.body.taskId,
          req.body.paramsExtractResponse,
        )

        console.info(
          chalk.bgCyanBright('/metric-query 截止paramsExtractChat提参功能结束耗时: ', Date.now() - startTime),
        )
        if (projectParamsExtractResult.code !== 0) {
          return res.json({
            data: [
              {
                ready: false,
                taskType: 'chat-error',
                errType:
                  ChatResponseErrorMap[projectParamsExtractResult.code as unknown as keyof typeof ChatResponseErrorMap],
                conversationId: conversationId,
                llmResponse: projectParamsExtractResult,
                sceneId: MULTI_SCENE_CHAT_MOCK_SCENE_ID,
              } as ChatResponseError,
            ],
          })
        }

        const multiScenesResult = projectParamsExtractResult.data
        const sceneIds = Object.keys(multiScenesResult)

        if (sceneIds.length === 0) {
          throw new Error('没有匹配到场景来应答此问题')
        }

        const multiScenesChatMetricParams = await Promise.all(
          sceneIds.map(async (id) => {
            try {
              const startTime2 = Date.now()
              const metricConfig = await MetricConfig.createBySceneId(id)
              console.info(
                chalk.bgCyanBright(
                  'multiScenesResultForFrontend 截止MetricConfig.createBySceneId处理结束耗时: ',
                  Date.now() - startTime2,
                ),
              )
              const result = multiScenesResult[id]
              const chatMetricParams = await buildChatMetricParams({
                conversationId,
                chatId,
                sceneId: id,
                projectId,
                message,
                llmType,
                metricConfig,
                enableMetricExactMatch,
                username,
                host,
                traceId,
                paramsExactResponse: { code: 0, data: result },
                authData,
              })
              console.info(
                chalk.bgCyanBright(
                  'multiScenesResultForFrontend 截止buildChatMetricParams处理结束耗时: ',
                  Date.now() - startTime2,
                ),
              )
              return chatMetricParams
            } catch (error) {
              console.error(error)
              return buildChatError({ error, conversationId, sceneId: id, traceId, projectId: req.body.projectId })
            }
          }),
        )

        const params = processMultiScenesChatMetricParams(multiScenesChatMetricParams)

        console.info(
          chalk.bgCyanBright(
            '/metric-query 截止processMultiScenesChatMetricParams处理结束耗时: ',
            Date.now() - startTime,
          ),
        )

        const multiScenesResultForFrontend: ChatResponse[] = await Promise.all(
          params.map(async (param) => {
            try {
              const chatResponse = await processExtractedParams(param)
              // 记录热门指标
              void recordMetricFrequencyFromResult({ chatResponse, sceneId: chatResponse.sceneId, projectId })
              return { ...chatResponse, traceId, taskId }
            } catch (error) {
              console.error(error)
              return buildChatError({
                error,
                conversationId,
                sceneId: param.sceneId,
                traceId,
                projectId: projectId,
              })
            }
          }),
        )

        console.info(
          chalk.bgCyanBright('/metric-query 截止multiScenesResultForFrontend处理结束耗时: ', Date.now() - startTime),
        )
        // MultiAgent-提问历史在前端存, 保证其他思考过程也存下来
        // updateConverChatResponse(chatId, traceId, result)

        res.json({ code: 0, ready: true, data: multiScenesResultForFrontend })
      } catch (e) {
        console.error(`POST ${url} encountered an error`, e)

        const chatResponse: ChatResponse = {
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.E_UNKNOWN,
          ready: false,
          unreadyReason: (e as Error)?.message || '超纲了，大模型正在努力学习中...',
          conversationId,
          sceneId,
          traceId,
          taskId: req.body.taskId,
          projectId: req.body.projectId,
        }

        void updateConverChatResponse(chatId, traceId, [chatResponse])
        return res.json(chatResponse)
      }
    }),
  )

  router.get(
    '/download-file',
    asyncResponseWrapper(async (req, res) => {
      console.info('/api/agent/download_file', req.query)
      const currentParamsExtractApi = req.body.currentParamsExtractApi
      const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
      const url = `${baseUrl}/api/v1/download_file`
      const resp = await axios.post(url, { file_path: req.query.p })
      resp.data.pipe(res)
    }),
  )

  router.get(
    '/read-image',
    asyncResponseWrapper(async (req, res) => {
      console.info('/api/agent/read-image', req.url)
      const currentParamsExtractApi = req.query.currentParamsExtractApi
      const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
      const url = `${baseUrl}/api/v1/download_file`
      const resp = await axios.post(
        url,
        {
          file_path: req.query.p,
        },
        { responseType: 'stream' },
      )
      resp.data.pipe(res)
    }),
  )

  router.get(
    '/read-csv',
    asyncResponseWrapper(async (req, res) => {
      console.info('/api/agent/read-csv', req.query)
      // const currentParamsExtractApi = req.body.currentParamsExtractApi
      // const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
      // const url = `${baseUrl}/api/v1/read_file`
      // const resp = await axios.post(url, { file_path: req.query.p })
      // return res.json({ code: 0, data: resp.data })

      const currentParamsExtractApi = req.query.currentParamsExtractApi
      const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
      const url = `${baseUrl}/api/v1/download_file`
      const resp = await axios.post(url, {
        file_path: req.query.p,
      })
      return res.json({ code: 0, data: resp.data })

      const tempCsv = `
      姓名,年龄,城市,姓名,年龄,城市,姓名,年龄,城市,姓名,年龄,城市,姓名,年龄,城市
      张三,25,北京,张三,25,北京,张三,25,北京,张三,25,北京,张三,25,北京
      李四,30,上海,李四,30,上海,李四,30,上海,李四,30,上海,李四,30,上海
      王五,35,广州,王五,35,广州,王五,35,广州,王五,35,广州,王五,35,广州
      `.trim()
      return res.json({ code: 0, data: tempCsv.split('\n').map((v) => v.split(',')) })
    }),
  )

  router.post(
    '/cot-stream',
    asyncResponseWrapper(async (req, res) => {
      for (const [k, v] of Object.entries(defaultResponseHeaders)) {
        res.setHeader(k, v)
      }
      res.flushHeaders()
      const currentParamsExtractApi = req.body.currentParamsExtractApi
      const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
      const url = `${baseUrl}/api/v1/get_cot`
      // const mock = JSON.parse(
      //   '"<think>好的，我现在需要帮助用户优化最近一个月的网络性能。首先<A/>，我得理解用户的问题是什么。用户希望优化网络性能，这意味着我们需要找出影响网络性能的关键问题，并采取措施进行改进。\\n\\n<MultiAgentTable p=\\"xx1\\"/>\\n\\n首先，我应该分析影响网络性能的几个关键指标。根据环境信息，我有networkThroughput（网络吞吐量）、signalStrength（信号强度）、latency（延迟）、packetLoss（数据包丢失率）、networkAvailability（网络可用性）、userDensity（用户密度）和bandwidthUsage（带宽使用率）这些指标。这些都是网络性能的重要因素。\\n\\n接下来，我需要拆解用户的问题。优化网络性能通常涉及到找出哪些指标表现不佳，以及这些指标在哪些区域或时间段内存在问题。因此，我需要分别分析每个关键指标的表现情况，找出瓶颈所在。\\n\\n首先，我想查看最近一个月的网络吞吐量。这可以帮助我了解整体网络的传输效率。使用fast_lookup工具，我可以查询networkThroughput在过去一个月的趋势，看看是否有下降的趋势，或者是否有特定的时段出现了问题。\\n\\n然后，信号强度也是一个关键因素。弱信号会导致连接不稳定和性能下降。同样，使用fast_lookup工具，我可以查看signalStrength在过去一个月的趋势，找出是否有基站覆盖不足的区域，或者设备类型的问题。\\n\\n接下来是延迟问题。延迟高会影响用户体验，特别是在需要实时响应的应用中。通过fast_lookup工具，我可以查看latency的趋势，找出延迟最高的时段或区域，分析是否有网络拥塞或其他技术问题。\\n\\n数据包丢失也是一个严重的问题，会导致网络不稳定。使用fast_lookup工具，我可以查看packetLoss的趋势，找出数据包丢失率较高的时段或区域，分析是否有硬件故障或网络配置问题。\\n\\n网络可用性是衡量网络稳定性的关键指标。使用fast_lookup工具，我可以查看networkAvailability的趋势，找出网络中断的次数和持续时间，分析是否有维护窗口或设备故障导致的问题。\\n\\n用户密度也是一个重要因素，高密度区域可能导致网络拥塞。使用fast_lookup工具，我可以查看userDensity的趋势，找出用户密集的区域，分析是否有需要增加基站或调整网络容量的地方。\\n\\n另外，带宽使用率也是一个关键指标。高带宽使用率可能导致网络拥塞，影响整体性能。使用fast_lookup工具，我可以查看bandwidthUsage的趋势，找出带宽使用高峰期和使用率高的区域，分析是否有需要升级网络设备或优化流量管理的情况。\\n\\n通过以上分析，我可以找出影响网络性能的主要问题，例如特定时间段的高延迟，某个区域的信号强度不足，或者某些时段的高数据包丢失率。然后，我可以根据这些问题采取相应的优化措施，比如调整基站的位置，优化网络配置，增加带宽，或者进行设备维护。\\n\\n如果在分析过程中发现某些问题超出了我的分析能力，或者需要更详细的数据，我可以使用search工具查找相关的技术文档或案例，或者使用doc工具查找内部的指导文档，以获取更多的解决方案和建议。\\n\\n最后，综合以上分析，我可以为用户提供一个全面的网络性能优化方案，包括问题诊断、原因分析和改进建议，帮助他们提升网络性能，改善用户体验。\\n</think>\\n\\n{\\n \\"\\"name\\"\\": \\"\\"data_tools\\"\\",\\n \\"\\"arguments\\"\\": [\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的网络吞吐量（networkThroughput）趋势\\"\\"\\n },\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的信号强度（signalStrength）趋势\\"\\"\\n },\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的延迟（latency）趋势\\"\\"\\n },\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的数据包丢失率（packetLoss）趋势\\"\\"\\n },\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的网络可用性（networkAvailability）趋势\\"\\"\\n },\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的用户密度（userDensity）趋势\\"\\"\\n },\\n {\\n \\"\\"type\\"\\": \\"\\"fast_lookup\\"\\",\\n \\"\\"question\\"\\": \\"\\"查询过去一个月的带宽使用率（bandwidthUsage）趋势\\"\\"\\n }\\n ]\\n}"',
      // )
      // for (let i = 0; i < mock.length; i++) {
      //   SSE.writeData(res, new SSE().set('data', mock[i]))
      //   await sleep(50)
      // }
      // res.end()
      // return
      await axios
        .post(
          url,
          { id: req.body.uuid },
          {
            timeout: DEFAULT_TIMEOUT * 3,
            headers: { 'Content-Type': 'application/json' },
            responseType: 'stream',
          },
        )
        .then((response) => {
          let result = ''
          response.data.on('data', (chunk: Buffer) => {
            const chunkStr = chunk.toString('utf8')
            result += chunkStr
            // process.stdout.write(chunkStr)
            res.write(
              sseStringify({
                id: Date.now().toString(),
                data: JSON.stringify(ResponseEntity.from({ code: 0, data: chunkStr })),
              }),
            )
            res.flush()
          })

          response.data.on('end', () => {
            console.info('\n\n闲聊完成：', result)
            res.end()
          })
        })
        .catch((error) => {
          console.error('chitchat failed in generate insight report: ', error)
          res.write(JSON.stringify({ code: 500, data: error?.toString() ?? '闲聊失败' }))
          res.end()
        })
    }),
  )

  router.get(
    '/metric-detail',
    asyncResponseWrapper(async (req, res) => {
      console.info(`GET ${req.url}`)
      const sceneId = req.query.sceneId as string
      const metricName = req.query.metricName as string
      if (!sceneId) throw new Error('场景不存在')
      if (!metricName) throw new Error('指标名称不存在')
      const metricConfig = await MetricConfig.createBySceneId(sceneId)
      const metricDetail = metricConfig.allMetrics.find((v) => v.name === metricName)
      if (!metricDetail) throw new Error(`指标${metricName}在场景${sceneId}中不存在`)
      const resultData: any = { ...metricDetail }
      {
        const timeRes = await axios.get<APIResponse<{ list: OlapRow[] }>>(
          PROCESS_ENV.ASK_BI_HOST + askBIApiUrlsWithoutBaseUrl.metrics.detailWithTime(sceneId, metricName),
        )
        if (timeRes.data.code !== 0) throw new Error('获取指标区间错误')
        const metricTime = timeRes.data.data
        resultData.startTime = metricTime?.list && metricTime?.list?.length > 0 ? metricTime.list[0][DATE_ALIAS] : '-'
        resultData.endTime =
          metricTime?.list && metricTime?.list?.length > 0
            ? metricTime.list[metricTime.list.length - 1][DATE_ALIAS]
            : '-'
      }
      {
        const trendRes = await axios.get<APIResponse<MetricTrendResponse>>(
          PROCESS_ENV.ASK_BI_HOST + askBIApiUrlsWithoutBaseUrl.metrics.trendInScene(sceneId, metricName),
        )
        if (trendRes.data.code !== 0) throw new Error('获取指标趋势错误')
        resultData.trend = trendRes.data.data
      }
      return res.json({ code: 0, data: resultData })
    }),
  )

  router.post(
    '/after-match',
    asyncResponseWrapper(async (req, res) => {
      console.info('/api/agent/after-match', req.body)
      const host = req.header('Host') || ''
      const startTime = Date.now()
      const username = req.user?.username || ''
      const currentParamsExtractApi = req.body.currentParamsExtractApi
      const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
      const url = `${baseUrl}/api/v1/agent_handler_after_manual_select`
      const resp = await axios.post(
        url,
        { manual_selects_result: req.body.matchedData, task_id: req.body.taskId },
        {
          headers: {
            Authorization: req.headers.authorization,
            traceId: req.body.traceId,
          },
        },
      )
      console.info('===> /api/agent/after-match  AfterMatch from PY', resp.data)
      reportLogs({
        moduleType: 'MULTI_AGENT_AFTER_MATCH',
        host,
        username,
        traceId: req.body.traceId,
        startTime,
        resultCode: 0,
        input: { matchedData: req.body.matchedData },
        output: { result: resp.data.data },
        semanticSceneId: req.body.sceneId,
        semanticProjectId: (await fetchProjectInfoBySceneId(req.body.sceneId)).projectId || '',
        debug: {
          matchedData: req.body.matchedData,
          taskId: req.body.taskId,
          respData: resp.data,
        },
      })
      return res.json({ code: 0, data: resp.data.data })
    }),
  )
  return router
}

export function buildChatError({
  error,
  conversationId,
  sceneId,
  traceId,
  projectId,
}: {
  error: unknown
  conversationId: string
  sceneId: string
  traceId: string
  projectId: string
}): ChatResponseError {
  return {
    taskType: 'chat-error',
    errType: ChatResponseErrorTypes.E_UNKNOWN,
    ready: false,
    unreadyReason: (error as Error)?.message || '超纲了，大模型正在努力学习中...',
    conversationId,
    sceneId,
    traceId,
    projectId,
  }
}

export const router = createRouter()
