/**
 * 数据库在 pre 的 mysql 上面的 mytest database。可以运行生成的 SQL
 * 因为 x-engine 不支持 SIGNED/UNSIGNED 类型，改成了 INTEGER。如果在 mysql 里执行，需要把 INTEGER 改成 SIGNED/UNSIGNED。
 */
import { describe, it, expect } from 'vitest'
import { nanoid } from 'nanoid'
import {
  Dimension,
  ListMetric,
  Measure,
  QueryParams,
  SemanticModel,
  NoListMetric,
  TimeDimensionDatum,
  TimeQueryParams,
  ExternalReport,
} from 'src/shared/metric-types'
// import { Metric2Sql } from 'src/server/sdk/metric-to-sql'
import { DEFAULT_FORMAT_DECIMAL, DEFAULT_FORMAT_RATIO } from 'src/shared/common-utils'
import MetricConfig from '../metric-config'
import Metric2Sql from './metric2sql'
// import Metric2Sql, { generateWhereDeriveMeta, handleOrderByAbsent } from './metric2sql'

const allDimensions: Dimension[] = [
  {
    id: nanoid(),
    name: 'area_id',
    label: 'area id',
    synonyms: [],
    filterSwitch: true,
    description: 'area id description',
    typeParams: {},
    type: 'categorical',
    expr: 'area_id_column',
  },
  {
    id: nanoid(),
    name: 'shop_id',
    label: 'shop id',
    synonyms: [],
    filterSwitch: true,
    description: 'shop id description',
    typeParams: {},
    type: 'categorical',
    expr: 'shop_id_column',
  },
  {
    id: nanoid(),
    name: 'login_dt',
    label: 'login time',
    synonyms: [],
    filterSwitch: true,
    description: 'fake login time',
    typeParams: {
      timeGranularity: 'day',
      timeType: 'string',
      timeFormat: 'yyyy_MM_DD',
    },
    type: 'time',
    expr: 'time_column',
  },
  {
    id: nanoid(),
    name: 'pay_dt',
    label: 'pay time',
    synonyms: [],
    filterSwitch: true,
    description: 'fake pay time',
    typeParams: {
      timeGranularity: 'day',
      timeType: 'string',
      timeFormat: 'yyyy/MM/DD',
    },
    type: 'time',
    expr: 'pay_dt',
  },
]
const allMeasures: Measure[] = [
  {
    id: nanoid(),
    name: 'cost_sum',
    label: 'cost label',
    synonyms: [],
    expr: 'SUM(cost_column)',
    agg: 'sum',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'revenue_sum',
    label: 'revenue label',
    synonyms: [],
    expr: 'SUM(revenue_column)',
    agg: 'sum',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'balance_sum',
    label: 'balance label',
    synonyms: [],
    expr: 'SUM(balance_column)',
    agg: 'sum',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'revenue_avg',
    label: 'revenue avg label',
    synonyms: [],
    expr: 'AVG(revenue_column)',
    agg: 'avg',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'revenue_max',
    label: 'revenue max label',
    synonyms: [],
    expr: 'MAX(revenue_column)',
    agg: 'max',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
]
const semanticModel: SemanticModel = {
  name: 'sales_data',
  tableName: 'vt_test_sales_table',
  sourceCreateTime: 1719563575847,
}

const allNoListMetrics: NoListMetric[] = [
  {
    id: nanoid(),
    name: 'cost_sum',
    label: 'cost label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'cost_sum',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'shop_40_cost_sum',
    label: 'cost label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'cost_sum',
    },
    filter: "shop_id_column = 'shop_40'",
    rank: -1,
    keypoint: false,
    category: '类目1%1',
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'shop_30_cost_sum',
    label: 'cost label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'cost_sum',
    },
    filter: "shop_id_column = 'shop_30'",
    rank: -1,
    keypoint: false,
    category: '类目2%2',
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'revenue_sum',
    label: 'revenue label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_sum',
    },
    rank: -1,
    keypoint: false,
    category: '类目3%3',
    isCumulative: true,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'balance_sum',
    label: 'balance label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'balance_sum',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'revenue_avg',
    label: 'revenue avg label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_avg',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'revenue_max',
    label: 'revenue max label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_max',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'profit',
    label: 'profit metric',
    synonyms: [],
    description: 'cost metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      expr: 'revenue_sum - cost_sum',
      metrics: [
        {
          name: 'revenue_sum',
        },
        {
          name: 'cost_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'profit_increment',
    label: 'profit increment',
    synonyms: [],
    description: 'profit increment metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      expr: 'revenue_sum / cost_sum - 1',
      metrics: [
        {
          name: 'revenue_sum',
        },
        {
          name: 'cost_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'shop_30_profit',
    label: 'shop_30 profit metric',
    synonyms: [],
    description: 'shop_30 profit metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    updatedAt: new Date(),
    typeParams: {
      expr: 'revenue_sum - shop_30_cost_sum',
      metrics: [
        {
          name: 'revenue_sum',
        },
        {
          name: 'shop_30_cost_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
  },
  {
    id: nanoid(),
    name: 'balance_ratio',
    label: 'balance ratio metric',
    synonyms: [],
    description: 'balance ratio metric description',
    type: 'ratio',
    formatTemplate: DEFAULT_FORMAT_RATIO,
    typeParams: {
      numerator: 'balance_sum',
      denominator: 'balance_sum',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
]

const listMetrics: ListMetric[] = [
  {
    id: nanoid(),
    name: 'money_list',
    label: 'money list',
    synonyms: [],
    type: 'list',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      metrics: [
        {
          name: 'cost_sum',
        },
        {
          name: 'revenue_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    updatedAt: new Date(),
  },
]

const allMetrics = [...allNoListMetrics, ...listMetrics]

const allExternalReports: ExternalReport[] = [
  {
    id: nanoid(),
    name: 'external_report1',
    label: 'external_report1',
    synonyms: [],
    type: 'baowu_report',
    createdAt: new Date(),
    updatedAt: new Date(),
    semanticSceneId: '',
    semanticProjectId: '',
  },
]

const timeDimensionDatum: TimeDimensionDatum = {
  timeDimensionName: 'order_dt',
  timeDimensionType: 'string',
  timeDimensionFormat: 'yyyyMM',
  timeGranularityMin: 'month',
}

const timeDimensionDatumYyyyMMDD: TimeDimensionDatum = {
  timeDimensionName: 'order_dt',
  timeDimensionType: 'string',
  timeDimensionFormat: 'yyyy-MM-DD',
  timeGranularityMin: 'day',
}

const timeDimensionDatumYyyyMMDDSlash: TimeDimensionDatum = {
  timeDimensionName: 'order_dt',
  timeDimensionType: 'string',
  timeDimensionFormat: 'yyyy/MM/DD',
  timeGranularityMin: 'day',
}

describe('#generateWhereDeriveMeta 用于解析 WHERE 中的 metric 和 dimension', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('最基本的 where', async () => {
    const verifiedMeta = {
      queryParams: {
        metricNames: ['revenue'],
        groupBys: ['shop_id'],
        orderBys: ['cost desc'],
        where: '1 = 1',
      },
    }
    const builder = await metric2Sql.createDerivedMetaBuilder(verifiedMeta)
    const whereDerived = await builder.invoke(verifiedMeta)
    expect(whereDerived.whereDerived).toEqual({
      dimensionNames: [],
      metricNames: [],
      lastWhere: '',
      sharedWhere: '(1 = 1)',
    })
  })
  it('WHERE 中只有 metric', async () => {
    const verifiedMeta = {
      queryParams: {
        metricNames: ['revenue_sum'],
        groupBys: ['shop_id'],
        orderBys: ['cost_sum desc'],
        where: 'revenue_sum > 100',
      },
    }

    const builder = await metric2Sql.createDerivedMetaBuilder(verifiedMeta)
    const whereDerived = await builder.invoke(verifiedMeta)
    expect(whereDerived.whereDerived).toEqual({
      dimensionNames: [],
      metricNames: ['revenue_sum'],
      lastWhere: 'revenue_sum > 100',
      sharedWhere: '',
    })
  })
  it('WHERE 中混合有 metric', async () => {
    const verifiedMeta = {
      queryParams: {
        metricNames: ['revenue_sum'],
        groupBys: ['shop_id'],
        orderBys: ['cost_sum desc'],
        where: '1 = 1 and revenue_sum > 100',
      },
    }

    const builder = await metric2Sql.createDerivedMetaBuilder(verifiedMeta)
    const whereDerived = await builder.invoke(verifiedMeta)
    expect(whereDerived.whereDerived).toEqual({
      dimensionNames: [],
      metricNames: ['revenue_sum'],
      lastWhere: 'revenue_sum > 100',
      sharedWhere: '(1 = 1)',
    })
  })
  it('WHERE 中混合有 dimension', async () => {
    const verifiedMeta = {
      queryParams: {
        metricNames: ['revenue'],
        groupBys: ['shop_id'],
        orderBys: ['cost desc'],
        where: '1 = 1 and shop_id = 12345 and area_id = "hz"',
      },
    }

    const builder = await metric2Sql.createDerivedMetaBuilder(verifiedMeta)
    const whereDerived = await builder.invoke(verifiedMeta)
    expect(whereDerived.whereDerived).toEqual({
      dimensionNames: ['shop_id', 'area_id'],
      metricNames: [],
      lastWhere: '',
      sharedWhere: '(1 = 1 AND shop_id_column = 12345 AND area_id_column = "hz")',
    })
  })
  it('WHERE 中混合有 dimension，并且是OR的关系', async () => {
    const verifiedMeta = {
      queryParams: {
        metricNames: ['revenue'],
        groupBys: ['shop_id'],
        orderBys: ['cost desc'],
        where: 'shop_id = 12345 or area_id = "hz"',
      },
    }

    const builder = await metric2Sql.createDerivedMetaBuilder(verifiedMeta)
    const whereDerived = await builder.invoke(verifiedMeta)
    expect(whereDerived.whereDerived).toEqual({
      dimensionNames: ['shop_id', 'area_id'],
      metricNames: [],
      lastWhere: '',
      sharedWhere: '(shop_id_column = 12345 OR area_id_column = "hz")',
    })
  })
  it('WHERE 中混合有 多个 dimension、多个 metric', async () => {
    const verifiedMeta = {
      queryParams: {
        metricNames: ['revenue_sum'],
        groupBys: ['shop_id'],
        orderBys: ['cost desc'],
        where: 'revenue_avg = 123 and 1 = 1 and shop_id in (100, 101, 102) and cost_sum > 1000 and area_id = "hz"',
      },
    }

    const builder = await metric2Sql.createDerivedMetaBuilder(verifiedMeta)
    const whereDerived = await builder.invoke(verifiedMeta)
    expect(whereDerived.whereDerived).toEqual({
      dimensionNames: ['shop_id', 'area_id'],
      metricNames: ['revenue_avg', 'cost_sum'],
      lastWhere: 'revenue_avg = 123 AND cost_sum > 1000',
      sharedWhere: '(1 = 1 AND shop_id_column IN (100, 101, 102) AND area_id_column = "hz")',
    })
  })
})

describe('#toSql，没有任何 GROUP BY 的情况，使用 cross join', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )
  it('只有1个指标', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['shop_30_cost_sum'],
    })
    expect(sql).toEqual(`WITH shop_30_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_30_cost_sum FROM sales_data WHERE shop_id_column = 'shop_30'
)
SELECT shop_30_cost_sum_TBL.shop_30_cost_sum FROM shop_30_cost_sum_TBL`)
  })

  it('只有3个指标', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['shop_30_cost_sum', 'shop_40_cost_sum', 'revenue_sum'],
    })
    expect(sql).toEqual(`WITH shop_30_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_30_cost_sum FROM sales_data WHERE shop_id_column = 'shop_30'
), shop_40_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_40_cost_sum FROM sales_data WHERE shop_id_column = 'shop_40'
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data
)
SELECT shop_30_cost_sum_TBL.shop_30_cost_sum, shop_40_cost_sum_TBL.shop_40_cost_sum, revenue_sum_TBL.revenue_sum FROM shop_30_cost_sum_TBL cross join shop_40_cost_sum_TBL cross join revenue_sum_TBL`)
  })

  it('有3个指标，还有 where，limit', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['shop_30_cost_sum', 'shop_40_cost_sum', 'revenue_sum'],
      where: 'area_id = "area_10"',
      limit: 100,
      orderBys: ['revenue_sum desc'],
    })
    expect(sql).toEqual(`WITH shop_30_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_30_cost_sum FROM sales_data WHERE (area_id_column = "area_10") AND shop_id_column = 'shop_30'
), shop_40_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_40_cost_sum FROM sales_data WHERE (area_id_column = "area_10") AND shop_id_column = 'shop_40'
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data WHERE (area_id_column = "area_10")
)
SELECT shop_30_cost_sum_TBL.shop_30_cost_sum, shop_40_cost_sum_TBL.shop_40_cost_sum, revenue_sum_TBL.revenue_sum FROM shop_30_cost_sum_TBL cross join shop_40_cost_sum_TBL cross join revenue_sum_TBL ORDER BY revenue_sum desc LIMIT 100`)
  })
})

describe('#toSql', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('查询2个指标，带1个group by，支持年聚合', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'cost_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(
      `SELECT SUM(revenue_column) AS revenue_sum, SUM(cost_column) AS cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id ORDER BY V_DATE_ ASC`,
    )
    // 走了 SQL 旁路优化，改写了SQL，原来的单侧不通过了
    /*
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, cost_sum_TBL.cost_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
*/
  })

  it('查询2个指标，带1个group by，支持季度聚合', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'cost_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'quarter',
      } as TimeQueryParams,
    })

    expect(sql).toEqual(
      `SELECT SUM(revenue_column) AS revenue_sum, SUM(cost_column) AS cost_sum, CONCAT(YEAR(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d'))) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202001' AND order_dt <= '202412' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id ORDER BY V_DATE_ ASC`,
    )
  })

  it('查询2个指标，带日期聚合，带2个group by，带1个 having', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'cost_sum'],
      groupBys: ['shop_id', 'area_id'],
      where: 'balance_sum > revenue_avg',
      orderBys: ['revenue_sum desc'],
      limit: 100,
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2023 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'month',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT order_dt AS V_DATE_, area_id_column AS area_id, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202301' AND order_dt <= '202412'
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, order_dt AS V_DATE_, area_id_column AS area_id, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202301' AND order_dt <= '202412' GROUP BY V_DATE_, shop_id, area_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, order_dt AS V_DATE_, area_id_column AS area_id, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202301' AND order_dt <= '202412' GROUP BY V_DATE_, shop_id, area_id
), balance_sum_TBL AS (
  SELECT SUM(balance_column) as balance_sum, order_dt AS V_DATE_, area_id_column AS area_id, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202301' AND order_dt <= '202412' GROUP BY V_DATE_, shop_id, area_id
), revenue_avg_TBL AS (
  SELECT AVG(revenue_column) as revenue_avg, order_dt AS V_DATE_, area_id_column AS area_id, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202301' AND order_dt <= '202412' GROUP BY V_DATE_, shop_id, area_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, DIM_TBL.area_id, revenue_sum_TBL.revenue_sum, cost_sum_TBL.cost_sum, balance_sum_TBL.balance_sum, revenue_avg_TBL.revenue_avg FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.area_id = revenue_sum_TBL.area_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.area_id = cost_sum_TBL.area_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_ LEFT JOIN balance_sum_TBL ON DIM_TBL.shop_id = balance_sum_TBL.shop_id AND DIM_TBL.area_id = balance_sum_TBL.area_id AND DIM_TBL.V_DATE_ = balance_sum_TBL.V_DATE_ LEFT JOIN revenue_avg_TBL ON DIM_TBL.shop_id = revenue_avg_TBL.shop_id AND DIM_TBL.area_id = revenue_avg_TBL.area_id AND DIM_TBL.V_DATE_ = revenue_avg_TBL.V_DATE_ WHERE balance_sum > revenue_avg ORDER BY revenue_sum desc, V_DATE_ ASC LIMIT 100`)
  })
})

describe('#generateSql 派生指标', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('1个派生指标，没有聚合', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['profit'],
      where: 'shop_id = "shop_45"',
    })
    expect(sql).toEqual(`WITH revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45")
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum FROM sales_data WHERE (shop_id_column = "shop_45")
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit FROM revenue_sum_TBL, cost_sum_TBL
)
SELECT profit_TBL.profit FROM profit_TBL`)
  })

  it('1个派生指标+1个普通指标，没有聚合', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['profit', 'revenue_sum'],
      where: 'shop_id = "shop_45"',
    })
    expect(sql).toEqual(`WITH revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45")
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum FROM sales_data WHERE (shop_id_column = "shop_45")
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit FROM revenue_sum_TBL, cost_sum_TBL
)
SELECT profit_TBL.profit, revenue_sum_TBL.revenue_sum FROM profit_TBL cross join revenue_sum_TBL`)
  })

  it('2个派生指标+1个普通指标，没有聚合', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['profit', 'revenue_sum', 'shop_30_profit'],
      where: 'shop_id = "shop_30"',
    })
    expect(sql).toEqual(`WITH revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_30")
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum FROM sales_data WHERE (shop_id_column = "shop_30")
), shop_30_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_30_cost_sum FROM sales_data WHERE (shop_id_column = "shop_30") AND shop_id_column = 'shop_30'
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit FROM revenue_sum_TBL, cost_sum_TBL
), shop_30_profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - shop_30_cost_sum_TBL.shop_30_cost_sum) as shop_30_profit FROM revenue_sum_TBL, shop_30_cost_sum_TBL
)
SELECT profit_TBL.profit, revenue_sum_TBL.revenue_sum, shop_30_profit_TBL.shop_30_profit FROM profit_TBL cross join revenue_sum_TBL cross join shop_30_profit_TBL`)
  })

  it('1个派生指标，带日期聚合，带1个group by', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['profit'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit, DIM_TBL.V_DATE_, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, profit_TBL.profit FROM DIM_TBL LEFT JOIN profit_TBL ON DIM_TBL.shop_id = profit_TBL.shop_id AND DIM_TBL.V_DATE_ = profit_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })

  it('1个普通指标，1个派生指标，带日期聚合，带1个group by', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'profit'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit, DIM_TBL.V_DATE_, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, profit_TBL.profit FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN profit_TBL ON DIM_TBL.shop_id = profit_TBL.shop_id AND DIM_TBL.V_DATE_ = profit_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })

  it('1个普通指标，2个派生指标，带日期聚合，带1个group by', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'profit', 'shop_30_profit'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), shop_30_cost_sum_TBL AS (
  SELECT SUM(cost_column) as shop_30_cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") AND shop_id_column = 'shop_30' GROUP BY V_DATE_, shop_id
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit, DIM_TBL.V_DATE_, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_
), shop_30_profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - shop_30_cost_sum_TBL.shop_30_cost_sum) as shop_30_profit, DIM_TBL.V_DATE_, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN shop_30_cost_sum_TBL ON DIM_TBL.shop_id = shop_30_cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = shop_30_cost_sum_TBL.V_DATE_
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, profit_TBL.profit, shop_30_profit_TBL.shop_30_profit FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN profit_TBL ON DIM_TBL.shop_id = profit_TBL.shop_id AND DIM_TBL.V_DATE_ = profit_TBL.V_DATE_ LEFT JOIN shop_30_profit_TBL ON DIM_TBL.shop_id = shop_30_profit_TBL.shop_id AND DIM_TBL.V_DATE_ = shop_30_profit_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })

  it('派生指标中有表达式', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'profit_increment'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), profit_increment_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum / cost_sum_TBL.cost_sum - 1) as profit_increment, DIM_TBL.V_DATE_, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, profit_increment_TBL.profit_increment FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN profit_increment_TBL ON DIM_TBL.shop_id = profit_increment_TBL.shop_id AND DIM_TBL.V_DATE_ = profit_increment_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('#generateSql, 支持 list metrics', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('查询 list 指标，带日期聚合，带1个group by', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['money_list', 'profit'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45")
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, SUBSTRING(order_dt, 1, 4) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 4) >= '2020' AND SUBSTRING(order_dt, 1, 4) <= '2024' AND (shop_id_column = "shop_45") GROUP BY V_DATE_, shop_id
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit, DIM_TBL.V_DATE_, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, cost_sum_TBL.cost_sum, revenue_sum_TBL.revenue_sum, profit_TBL.profit FROM DIM_TBL LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_ LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN profit_TBL ON DIM_TBL.shop_id = profit_TBL.shop_id AND DIM_TBL.V_DATE_ = profit_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('提取到 list 指标指定按照第一个指标排序', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('大模型提参中提出来列表指标 orderBys', async () => {
    const { sql } = await metric2Sql.toSql({
      // 列表指标 money_list = ['cost_sum', 'revenue_sum']
      metricNames: ['money_list', 'profit'],
      orderBys: ['money_list desc'],
      where: 'shop_id = "shop_45"',
    })

    expect(sql).toEqual(`WITH cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum FROM sales_data WHERE (shop_id_column = "shop_45")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45")
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit FROM revenue_sum_TBL, cost_sum_TBL
)
SELECT cost_sum_TBL.cost_sum, revenue_sum_TBL.revenue_sum, profit_TBL.profit FROM cost_sum_TBL cross join revenue_sum_TBL cross join profit_TBL ORDER BY cost_sum desc`)
  })
})

describe('#toSql 月份的同环比的配置', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('查询指标的月份同环比，没有 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      // groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2022, month: 12 },
        timeEndFunction: { type: 'specificMonth', year: 2022, month: 12 },
        timeGranularity: 'total', // month
      } as TimeQueryParams,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth'],
    })

    expect(sql).toEqual(`WITH revenue_sum_MONTHLY_TBL AS (
  SELECT LEFT(order_dt, 6) AS V_DATE_, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(order_dt, 5, 2) AS INTEGER) AS month_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, month_num ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT V_DATE_, year_num, month_num, revenue_sum FROM revenue_sum_MONTHLY_TBL WHERE V_DATE_ = '202212'
), revenue_sum_TBL AS (
  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.revenue_sum, previous_month.revenue_sum as pre_month_revenue_sum, previous_year_month.revenue_sum as pre_year_month_revenue_sum, (current_month.revenue_sum - previous_month.revenue_sum) as mom_growth_revenue_sum, (current_month.revenue_sum - previous_year_month.revenue_sum) as yoy_month_growth_revenue_sum, ((current_month.revenue_sum - previous_month.revenue_sum) / previous_month.revenue_sum) as mom_growth_rate_revenue_sum, ((current_month.revenue_sum - previous_year_month.revenue_sum) / previous_year_month.revenue_sum) as yoy_month_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_month LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num
)
SELECT revenue_sum_TBL.revenue_sum, revenue_sum_TBL.mom_growth_revenue_sum, revenue_sum_TBL.yoy_month_growth_revenue_sum FROM revenue_sum_TBL`)
  })

  it('查询指标的月份同环比，带 groupBy，带多日期区间', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2024, month: 1 },
        timeEndFunction: { type: 'specificMonth', year: 2024, month: 4 },
        timeGranularity: 'month',
      } as TimeQueryParams,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth', 'momGrowthRate', 'yoyMonthGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202401' AND order_dt <= '202404' AND (shop_id_column = "shop_45")
), revenue_sum_MONTHLY_TBL AS (
  SELECT LEFT(order_dt, 6) AS V_DATE_, shop_id_column AS shop_id, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(order_dt, 5, 2) AS INTEGER) AS month_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, month_num, shop_id ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT V_DATE_, year_num, month_num, shop_id, revenue_sum FROM revenue_sum_MONTHLY_TBL WHERE V_DATE_ >= '202401' AND V_DATE_ <= '202404'
), revenue_sum_TBL AS (
  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.shop_id, current_month.revenue_sum, previous_month.revenue_sum as pre_month_revenue_sum, previous_year_month.revenue_sum as pre_year_month_revenue_sum, (current_month.revenue_sum - previous_month.revenue_sum) as mom_growth_revenue_sum, (current_month.revenue_sum - previous_year_month.revenue_sum) as yoy_month_growth_revenue_sum, ((current_month.revenue_sum - previous_month.revenue_sum) / previous_month.revenue_sum) as mom_growth_rate_revenue_sum, ((current_month.revenue_sum - previous_year_month.revenue_sum) / previous_year_month.revenue_sum) as yoy_month_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_month LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) AND current_month.shop_id = previous_month.shop_id LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num AND current_month.shop_id = previous_year_month.shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.mom_growth_revenue_sum, revenue_sum_TBL.yoy_month_growth_revenue_sum, revenue_sum_TBL.mom_growth_rate_revenue_sum, revenue_sum_TBL.yoy_month_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('#toSql 季度的同环比的配置', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('查询指标的季度同环比，没有 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      // groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificQuarter', year: 2024, quarter: 1 },
        timeEndFunction: { type: 'specificQuarter', year: 2024, quarter: 2 },
        timeGranularity: 'quarter',
      } as TimeQueryParams,
      periodOverPeriods: ['qoqGrowth', 'yoyQuarterGrowth', 'yoyQuarterGrowthRate', 'qoqGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT CONCAT(YEAR(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d'))) AS V_DATE_ FROM sales_data WHERE order_dt >= '202401' AND order_dt <= '202406' AND (shop_id_column = "shop_45")
), revenue_sum_QUARTERLY_TBL AS (
  SELECT CONCAT(YEAR(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d'))) AS V_DATE_, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')) AS quarter_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, quarter_num ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT revenue_sum_QUARTERLY_TBL.V_DATE_, revenue_sum_QUARTERLY_TBL.year_num, revenue_sum_QUARTERLY_TBL.quarter_num, revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_QUARTERLY_TBL ON DIM_TBL.V_DATE_ = revenue_sum_QUARTERLY_TBL.V_DATE_
), revenue_sum_TBL AS (
  SELECT current_quarter.V_DATE_, current_quarter.revenue_sum, previous_quarter.revenue_sum as pre_quarter_revenue_sum, previous_year_quarter.revenue_sum as pre_year_quarter_revenue_sum, (current_quarter.revenue_sum - previous_quarter.revenue_sum) as qoq_growth_revenue_sum, (current_quarter.revenue_sum - previous_year_quarter.revenue_sum) as yoy_quarter_growth_revenue_sum, ((current_quarter.revenue_sum - previous_quarter.revenue_sum) / previous_quarter.revenue_sum) as qoq_growth_rate_revenue_sum, ((current_quarter.revenue_sum - previous_year_quarter.revenue_sum) / previous_year_quarter.revenue_sum) as yoy_quarter_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_quarter LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_quarter ON (current_quarter.year_num * 4 + current_quarter.quarter_num - 1) = (previous_quarter.year_num * 4 + previous_quarter.quarter_num) LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_year_quarter ON (current_quarter.year_num - 1) = previous_year_quarter.year_num AND current_quarter.quarter_num = previous_year_quarter.quarter_num
)
SELECT DIM_TBL.V_DATE_, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.qoq_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_rate_revenue_sum, revenue_sum_TBL.qoq_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })

  it('查询指标的季度同环比，带 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id in ("shop_45", "shop_5", "shop_22")',
      timeQueryParams: {
        timeStartFunction: { type: 'specificQuarter', year: 2024, quarter: 1 },
        timeEndFunction: { type: 'specificQuarter', year: 2024, quarter: 2 },
        timeGranularity: 'quarter',
      } as TimeQueryParams,
      periodOverPeriods: ['qoqGrowth', 'yoyQuarterGrowth', 'yoyQuarterGrowthRate', 'qoqGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT CONCAT(YEAR(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d'))) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202401' AND order_dt <= '202406' AND (shop_id_column IN ("shop_45", "shop_5", "shop_22"))
), revenue_sum_QUARTERLY_TBL AS (
  SELECT CONCAT(YEAR(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d'))) AS V_DATE_, shop_id_column AS shop_id, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, QUARTER(STR_TO_DATE(CONCAT(order_dt, '01'), '%Y%m%d')) AS quarter_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column IN ("shop_45", "shop_5", "shop_22")) GROUP BY V_DATE_, year_num, quarter_num, shop_id ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT revenue_sum_QUARTERLY_TBL.V_DATE_, revenue_sum_QUARTERLY_TBL.year_num, revenue_sum_QUARTERLY_TBL.quarter_num, revenue_sum_QUARTERLY_TBL.shop_id, revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_QUARTERLY_TBL ON DIM_TBL.V_DATE_ = revenue_sum_QUARTERLY_TBL.V_DATE_ AND DIM_TBL.shop_id = revenue_sum_QUARTERLY_TBL.shop_id
), revenue_sum_TBL AS (
  SELECT current_quarter.V_DATE_, current_quarter.shop_id, current_quarter.revenue_sum, previous_quarter.revenue_sum as pre_quarter_revenue_sum, previous_year_quarter.revenue_sum as pre_year_quarter_revenue_sum, (current_quarter.revenue_sum - previous_quarter.revenue_sum) as qoq_growth_revenue_sum, (current_quarter.revenue_sum - previous_year_quarter.revenue_sum) as yoy_quarter_growth_revenue_sum, ((current_quarter.revenue_sum - previous_quarter.revenue_sum) / previous_quarter.revenue_sum) as qoq_growth_rate_revenue_sum, ((current_quarter.revenue_sum - previous_year_quarter.revenue_sum) / previous_year_quarter.revenue_sum) as yoy_quarter_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_quarter LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_quarter ON (current_quarter.year_num * 4 + current_quarter.quarter_num - 1) = (previous_quarter.year_num * 4 + previous_quarter.quarter_num) AND current_quarter.shop_id = previous_quarter.shop_id LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_year_quarter ON (current_quarter.year_num - 1) = previous_year_quarter.year_num AND current_quarter.quarter_num = previous_year_quarter.quarter_num AND current_quarter.shop_id = previous_year_quarter.shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.qoq_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_rate_revenue_sum, revenue_sum_TBL.qoq_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('#toSql 月份的同环比的配置，支持 yyyy-MM-DD 的格式', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatumYyyyMMDD,
    ),
  )

  it('查询指标的月份同环比，没有 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      // groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2022, month: 12 },
        timeEndFunction: { type: 'specificMonth', year: 2022, month: 12 },
        timeGranularity: 'total', // month
      } as TimeQueryParams,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth'],
    })

    expect(sql).toEqual(`WITH revenue_sum_MONTHLY_TBL AS (
  SELECT LEFT(order_dt, 7) AS V_DATE_, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(order_dt, 6, 2) AS INTEGER) AS month_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, month_num ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT V_DATE_, year_num, month_num, revenue_sum FROM revenue_sum_MONTHLY_TBL WHERE V_DATE_ = '2022-12'
), revenue_sum_TBL AS (
  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.revenue_sum, previous_month.revenue_sum as pre_month_revenue_sum, previous_year_month.revenue_sum as pre_year_month_revenue_sum, (current_month.revenue_sum - previous_month.revenue_sum) as mom_growth_revenue_sum, (current_month.revenue_sum - previous_year_month.revenue_sum) as yoy_month_growth_revenue_sum, ((current_month.revenue_sum - previous_month.revenue_sum) / previous_month.revenue_sum) as mom_growth_rate_revenue_sum, ((current_month.revenue_sum - previous_year_month.revenue_sum) / previous_year_month.revenue_sum) as yoy_month_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_month LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num
)
SELECT revenue_sum_TBL.revenue_sum, revenue_sum_TBL.mom_growth_revenue_sum, revenue_sum_TBL.yoy_month_growth_revenue_sum FROM revenue_sum_TBL`)
  })

  it('查询指标的月份同环比，带 groupBy，带多日期区间', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2024, month: 1 },
        timeEndFunction: { type: 'specificMonth', year: 2024, month: 4 },
        timeGranularity: 'month',
      } as TimeQueryParams,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth', 'momGrowthRate', 'yoyMonthGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 7) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 7) >= '2024-01' AND SUBSTRING(order_dt, 1, 7) <= '2024-04' AND (shop_id_column = "shop_45")
), revenue_sum_MONTHLY_TBL AS (
  SELECT LEFT(order_dt, 7) AS V_DATE_, shop_id_column AS shop_id, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(order_dt, 6, 2) AS INTEGER) AS month_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, month_num, shop_id ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT V_DATE_, year_num, month_num, shop_id, revenue_sum FROM revenue_sum_MONTHLY_TBL WHERE V_DATE_ >= '2024-01' AND V_DATE_ <= '2024-04'
), revenue_sum_TBL AS (
  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.shop_id, current_month.revenue_sum, previous_month.revenue_sum as pre_month_revenue_sum, previous_year_month.revenue_sum as pre_year_month_revenue_sum, (current_month.revenue_sum - previous_month.revenue_sum) as mom_growth_revenue_sum, (current_month.revenue_sum - previous_year_month.revenue_sum) as yoy_month_growth_revenue_sum, ((current_month.revenue_sum - previous_month.revenue_sum) / previous_month.revenue_sum) as mom_growth_rate_revenue_sum, ((current_month.revenue_sum - previous_year_month.revenue_sum) / previous_year_month.revenue_sum) as yoy_month_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_month LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) AND current_month.shop_id = previous_month.shop_id LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num AND current_month.shop_id = previous_year_month.shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.mom_growth_revenue_sum, revenue_sum_TBL.yoy_month_growth_revenue_sum, revenue_sum_TBL.mom_growth_rate_revenue_sum, revenue_sum_TBL.yoy_month_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('#toSql 季度的同环比的配置，支持 yyyy-MM-DD 的格式', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatumYyyyMMDD,
    ),
  )
  it('查询指标的季度同环比，带 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id in ("shop_45", "shop_5", "shop_22")',
      timeQueryParams: {
        timeStartFunction: { type: 'specificQuarter', year: 2024, quarter: 1 },
        timeEndFunction: { type: 'specificQuarter', year: 2024, quarter: 2 },
        timeGranularity: 'quarter',
      } as TimeQueryParams,
      periodOverPeriods: ['qoqGrowth', 'yoyQuarterGrowth', 'yoyQuarterGrowthRate', 'qoqGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT CONCAT(YEAR(STR_TO_DATE(order_dt, '%Y-%m-%d')), '-Q', QUARTER(STR_TO_DATE(order_dt, '%Y-%m-%d'))) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 7) >= '2024-01' AND SUBSTRING(order_dt, 1, 7) <= '2024-06' AND (shop_id_column IN ("shop_45", "shop_5", "shop_22"))
), revenue_sum_QUARTERLY_TBL AS (
  SELECT CONCAT(YEAR(STR_TO_DATE(order_dt, '%Y-%m-%d')), '-Q', QUARTER(STR_TO_DATE(order_dt, '%Y-%m-%d'))) AS V_DATE_, shop_id_column AS shop_id, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, QUARTER(STR_TO_DATE(order_dt, '%Y-%m-%d')) AS quarter_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column IN ("shop_45", "shop_5", "shop_22")) GROUP BY V_DATE_, year_num, quarter_num, shop_id ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT revenue_sum_QUARTERLY_TBL.V_DATE_, revenue_sum_QUARTERLY_TBL.year_num, revenue_sum_QUARTERLY_TBL.quarter_num, revenue_sum_QUARTERLY_TBL.shop_id, revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_QUARTERLY_TBL ON DIM_TBL.V_DATE_ = revenue_sum_QUARTERLY_TBL.V_DATE_ AND DIM_TBL.shop_id = revenue_sum_QUARTERLY_TBL.shop_id
), revenue_sum_TBL AS (
  SELECT current_quarter.V_DATE_, current_quarter.shop_id, current_quarter.revenue_sum, previous_quarter.revenue_sum as pre_quarter_revenue_sum, previous_year_quarter.revenue_sum as pre_year_quarter_revenue_sum, (current_quarter.revenue_sum - previous_quarter.revenue_sum) as qoq_growth_revenue_sum, (current_quarter.revenue_sum - previous_year_quarter.revenue_sum) as yoy_quarter_growth_revenue_sum, ((current_quarter.revenue_sum - previous_quarter.revenue_sum) / previous_quarter.revenue_sum) as qoq_growth_rate_revenue_sum, ((current_quarter.revenue_sum - previous_year_quarter.revenue_sum) / previous_year_quarter.revenue_sum) as yoy_quarter_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_quarter LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_quarter ON (current_quarter.year_num * 4 + current_quarter.quarter_num - 1) = (previous_quarter.year_num * 4 + previous_quarter.quarter_num) AND current_quarter.shop_id = previous_quarter.shop_id LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_year_quarter ON (current_quarter.year_num - 1) = previous_year_quarter.year_num AND current_quarter.quarter_num = previous_year_quarter.quarter_num AND current_quarter.shop_id = previous_year_quarter.shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.qoq_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_rate_revenue_sum, revenue_sum_TBL.qoq_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('#toSql 月份的同环比的配置，支持 yyyy/MM/DD 的格式', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatumYyyyMMDDSlash,
    ),
  )

  it('查询指标的月份同环比，没有 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      // groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2022, month: 12 },
        timeEndFunction: { type: 'specificMonth', year: 2022, month: 12 },
        timeGranularity: 'total', // month
      } as TimeQueryParams,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth'],
    })

    expect(sql).toEqual(`WITH revenue_sum_MONTHLY_TBL AS (
  SELECT LEFT(order_dt, 7) AS V_DATE_, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(order_dt, 6, 2) AS INTEGER) AS month_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, month_num ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT V_DATE_, year_num, month_num, revenue_sum FROM revenue_sum_MONTHLY_TBL WHERE V_DATE_ = '2022/12'
), revenue_sum_TBL AS (
  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.revenue_sum, previous_month.revenue_sum as pre_month_revenue_sum, previous_year_month.revenue_sum as pre_year_month_revenue_sum, (current_month.revenue_sum - previous_month.revenue_sum) as mom_growth_revenue_sum, (current_month.revenue_sum - previous_year_month.revenue_sum) as yoy_month_growth_revenue_sum, ((current_month.revenue_sum - previous_month.revenue_sum) / previous_month.revenue_sum) as mom_growth_rate_revenue_sum, ((current_month.revenue_sum - previous_year_month.revenue_sum) / previous_year_month.revenue_sum) as yoy_month_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_month LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num
)
SELECT revenue_sum_TBL.revenue_sum, revenue_sum_TBL.mom_growth_revenue_sum, revenue_sum_TBL.yoy_month_growth_revenue_sum FROM revenue_sum_TBL`)
  })

  it('查询指标的月份同环比，带 groupBy，带多日期区间', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id = "shop_45"',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2024, month: 1 },
        timeEndFunction: { type: 'specificMonth', year: 2024, month: 4 },
        timeGranularity: 'month',
      } as TimeQueryParams,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth', 'momGrowthRate', 'yoyMonthGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT SUBSTRING(order_dt, 1, 7) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 7) >= '2024/01' AND SUBSTRING(order_dt, 1, 7) <= '2024/04' AND (shop_id_column = "shop_45")
), revenue_sum_MONTHLY_TBL AS (
  SELECT LEFT(order_dt, 7) AS V_DATE_, shop_id_column AS shop_id, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(order_dt, 6, 2) AS INTEGER) AS month_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column = "shop_45") GROUP BY V_DATE_, year_num, month_num, shop_id ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT V_DATE_, year_num, month_num, shop_id, revenue_sum FROM revenue_sum_MONTHLY_TBL WHERE V_DATE_ >= '2024/01' AND V_DATE_ <= '2024/04'
), revenue_sum_TBL AS (
  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.shop_id, current_month.revenue_sum, previous_month.revenue_sum as pre_month_revenue_sum, previous_year_month.revenue_sum as pre_year_month_revenue_sum, (current_month.revenue_sum - previous_month.revenue_sum) as mom_growth_revenue_sum, (current_month.revenue_sum - previous_year_month.revenue_sum) as yoy_month_growth_revenue_sum, ((current_month.revenue_sum - previous_month.revenue_sum) / previous_month.revenue_sum) as mom_growth_rate_revenue_sum, ((current_month.revenue_sum - previous_year_month.revenue_sum) / previous_year_month.revenue_sum) as yoy_month_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_month LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) AND current_month.shop_id = previous_month.shop_id LEFT JOIN revenue_sum_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num AND current_month.shop_id = previous_year_month.shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.mom_growth_revenue_sum, revenue_sum_TBL.yoy_month_growth_revenue_sum, revenue_sum_TBL.mom_growth_rate_revenue_sum, revenue_sum_TBL.yoy_month_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('#toSql 季度的同环比的配置，支持 yyyy/MM/DD 的格式', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allMetrics,
      allMetrics,
      allExternalReports,
      0,
      timeDimensionDatumYyyyMMDDSlash,
    ),
  )
  it('查询指标的季度同环比，带 groupBy', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'shop_id in ("shop_45", "shop_5", "shop_22")',
      timeQueryParams: {
        timeStartFunction: { type: 'specificQuarter', year: 2024, quarter: 1 },
        timeEndFunction: { type: 'specificQuarter', year: 2024, quarter: 2 },
        timeGranularity: 'quarter',
      } as TimeQueryParams,
      periodOverPeriods: ['qoqGrowth', 'yoyQuarterGrowth', 'yoyQuarterGrowthRate', 'qoqGrowthRate'],
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT CONCAT(YEAR(STR_TO_DATE(order_dt, '%Y/%m/%d')), '-Q', QUARTER(STR_TO_DATE(order_dt, '%Y/%m/%d'))) AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE SUBSTRING(order_dt, 1, 7) >= '2024/01' AND SUBSTRING(order_dt, 1, 7) <= '2024/06' AND (shop_id_column IN ("shop_45", "shop_5", "shop_22"))
), revenue_sum_QUARTERLY_TBL AS (
  SELECT CONCAT(YEAR(STR_TO_DATE(order_dt, '%Y/%m/%d')), '-Q', QUARTER(STR_TO_DATE(order_dt, '%Y/%m/%d'))) AS V_DATE_, shop_id_column AS shop_id, CAST(SUBSTRING(order_dt, 1, 4) AS INTEGER) AS year_num, QUARTER(STR_TO_DATE(order_dt, '%Y/%m/%d')) AS quarter_num, SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column IN ("shop_45", "shop_5", "shop_22")) GROUP BY V_DATE_, year_num, quarter_num, shop_id ORDER BY V_DATE_ desc
), revenue_sum_RECENT_TBL AS (
  SELECT revenue_sum_QUARTERLY_TBL.V_DATE_, revenue_sum_QUARTERLY_TBL.year_num, revenue_sum_QUARTERLY_TBL.quarter_num, revenue_sum_QUARTERLY_TBL.shop_id, revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_QUARTERLY_TBL ON DIM_TBL.V_DATE_ = revenue_sum_QUARTERLY_TBL.V_DATE_ AND DIM_TBL.shop_id = revenue_sum_QUARTERLY_TBL.shop_id
), revenue_sum_TBL AS (
  SELECT current_quarter.V_DATE_, current_quarter.shop_id, current_quarter.revenue_sum, previous_quarter.revenue_sum as pre_quarter_revenue_sum, previous_year_quarter.revenue_sum as pre_year_quarter_revenue_sum, (current_quarter.revenue_sum - previous_quarter.revenue_sum) as qoq_growth_revenue_sum, (current_quarter.revenue_sum - previous_year_quarter.revenue_sum) as yoy_quarter_growth_revenue_sum, ((current_quarter.revenue_sum - previous_quarter.revenue_sum) / previous_quarter.revenue_sum) as qoq_growth_rate_revenue_sum, ((current_quarter.revenue_sum - previous_year_quarter.revenue_sum) / previous_year_quarter.revenue_sum) as yoy_quarter_growth_rate_revenue_sum FROM revenue_sum_RECENT_TBL AS current_quarter LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_quarter ON (current_quarter.year_num * 4 + current_quarter.quarter_num - 1) = (previous_quarter.year_num * 4 + previous_quarter.quarter_num) AND current_quarter.shop_id = previous_quarter.shop_id LEFT JOIN revenue_sum_QUARTERLY_TBL AS previous_year_quarter ON (current_quarter.year_num - 1) = previous_year_quarter.year_num AND current_quarter.quarter_num = previous_year_quarter.quarter_num AND current_quarter.shop_id = previous_year_quarter.shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, revenue_sum_TBL.qoq_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_revenue_sum, revenue_sum_TBL.yoy_quarter_growth_rate_revenue_sum, revenue_sum_TBL.qoq_growth_rate_revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
  })
})

describe('metric2sql 中方法的单元测试', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )
  // TODO: kongsa UT 问题
  // it('handleOrderByAbsent 当 orderBy 中有一些列不在 groupBy 或者 metricNames 中的时候，需要把这些列加入到 groupBy 或者 metricNames 中', async () => {
  //   const queryParams = {
  //     metricNames: ['revenue'],
  //     groupBys: ['shop_id'],
  //     orderBys: ['cost desc'],
  //     where: '1 = 1',
  //   }
  //   const queryParamsBuilder = await metric2Sql.createQueryParamsBuilder()
  //   const queryParams2 = await queryParamsBuilder.invoke(queryParams)
  //   expect(queryParams2).toEqual({
  //     metricNames: ['revenue', 'cost'],
  //     groupBys: ['shop_id'],
  //     orderBys: ['cost desc'],
  //     where: '1 = 1',
  //   })
  // })

  it('WHERE 右值如果是 metric，就使用子查询', async () => {
    // 交易额大于平均值的店铺列表
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'cost_sum'],
      groupBys: ['shop_id'],
      where: 'revenue_sum > revenue_avg AND cost_sum > 100',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2024, month: 6 },
        timeEndFunction: { type: 'specificMonth', year: 2024, month: 7 },
        timeGranularity: 'day',
      } as TimeQueryParams,
    })
    console.info('sql is \n', sql)
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407'
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' GROUP BY V_DATE_, shop_id
), revenue_avg_TBL AS (
  SELECT AVG(revenue_column) as revenue_avg, order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' GROUP BY V_DATE_, shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, cost_sum_TBL.cost_sum, revenue_avg_TBL.revenue_avg FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_ LEFT JOIN revenue_avg_TBL ON DIM_TBL.shop_id = revenue_avg_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_avg_TBL.V_DATE_ WHERE revenue_sum > revenue_avg AND cost_sum > 100 ORDER BY V_DATE_ ASC`)
  })

  it('metricNames 和 groupBys 不能同时为空', async () => {
    expect(async () => {
      await expect(
        metric2Sql.toSql({
          metricNames: [],
          groupBys: [],
          timeQueryParams: {
            timeStartFunction: { type: 'recentDays', days: 30 },
            timeEndFunction: { type: 'recentDays', days: 0 },
            timeGranularity: 'day',
          } as TimeQueryParams,
        }),
      ).rejects.toThrowError('metricNames 和 groupBys 不能同时为空')
    })
  })

  it('metricNames 为空，groupBys 非空的时候使用 SELECT DISTINCT', async () => {
    // 交易额大于平均值的店铺列表
    const { sql } = await metric2Sql.toSql({
      metricNames: [],
      groupBys: ['area_id', 'shop_id'],
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT area_id_column AS area_id, shop_id_column AS shop_id FROM sales_data
)
SELECT DIM_TBL.area_id, DIM_TBL.shop_id FROM DIM_TBL`)
  })

  it('WHERE 左值有 1 个 metric 过滤，会创建子查询，只把 metric 过滤的条件放到子查询外面，其他的条件放到子查询where上', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: 'revenue_sum > 100',
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, shop_id_column AS shop_id FROM sales_data GROUP BY shop_id
)
SELECT DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id WHERE revenue_sum > 100`)
  })

  it('WHERE support is not null', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: [],
      where: 'shop_id is not null',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2024, month: 6 },
        timeEndFunction: { type: 'specificMonth', year: 2024, month: 7 },
        timeGranularity: 'day',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(
      `SELECT SUM(revenue_column) AS revenue_sum, order_dt AS V_DATE_ FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' AND (shop_id_column IS NOT NULL) GROUP BY V_DATE_ ORDER BY V_DATE_ ASC`,
    )

    /*
         expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT order_dt AS V_DATE_ FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' AND (shop_id_column IS NOT NULL)
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, order_dt AS V_DATE_ FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' AND (shop_id_column IS NOT NULL) GROUP BY V_DATE_
)
SELECT DIM_TBL.V_DATE_, revenue_sum_TBL.revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ ORDER BY V_DATE_ ASC`)
     */

    const { sql: sql2 } = await metric2Sql.toSql({
      metricNames: ['revenue_sum'],
      groupBys: [],
      where: 'shop_id not like "示例商店"',
    })
    expect(sql2).toEqual(
      `SELECT SUM(revenue_column) AS revenue_sum FROM sales_data WHERE (shop_id_column NOT LIKE "示例商店")`,
    )

    /*
     expect(sql2).toEqual(`WITH revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum FROM sales_data WHERE (shop_id_column NOT LIKE "示例商店")
)
SELECT revenue_sum_TBL.revenue_sum FROM revenue_sum_TBL`)
 */
  })

  it('WHERE 左值有 2 个 metrics 过滤', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['revenue_sum', 'cost_sum'],
      groupBys: ['shop_id'],
      where: 'revenue_sum > 100 AND shop_id = "hz001" AND cost_sum > 100',
      timeQueryParams: {
        timeStartFunction: { type: 'specificMonth', year: 2024, month: 6 },
        timeEndFunction: { type: 'specificMonth', year: 2024, month: 7 },
        timeGranularity: 'day',
      } as TimeQueryParams,
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' AND (shop_id_column = "hz001")
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' AND (shop_id_column = "hz001") GROUP BY V_DATE_, shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, order_dt AS V_DATE_, shop_id_column AS shop_id FROM sales_data WHERE order_dt >= '202406' AND order_dt <= '202407' AND (shop_id_column = "hz001") GROUP BY V_DATE_, shop_id
)
SELECT DIM_TBL.V_DATE_, DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum, cost_sum_TBL.cost_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = revenue_sum_TBL.V_DATE_ LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id AND DIM_TBL.V_DATE_ = cost_sum_TBL.V_DATE_ WHERE revenue_sum > 100 AND cost_sum > 100 ORDER BY V_DATE_ ASC`)
  })

  it('WHERE 中包含 metric_sum 和 metric_avg 的比较', async () => {
    // 交易额大于平均值的店铺列表
    const { sql } = await metric2Sql.toSql({
      groupBys: ['shop_id'],
      metricNames: ['revenue_avg', 'revenue_sum'],
      orderBys: [],
      where: 'revenue_sum > revenue_avg',
    })
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data
), revenue_avg_TBL AS (
  SELECT AVG(revenue_column) as revenue_avg, shop_id_column AS shop_id FROM sales_data GROUP BY shop_id
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, shop_id_column AS shop_id FROM sales_data GROUP BY shop_id
)
SELECT DIM_TBL.shop_id, revenue_avg_TBL.revenue_avg, revenue_sum_TBL.revenue_sum FROM DIM_TBL LEFT JOIN revenue_avg_TBL ON DIM_TBL.shop_id = revenue_avg_TBL.shop_id LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id WHERE revenue_sum > revenue_avg`)
  })

  it('有 groupBy 和 WHERE 中包含 avg 指标', async () => {
    // 查询21年3月的平均存量,注意按照产品名称进行分组
    const { sql } = await metric2Sql.toSql({
      groupBys: ['shop_id'],
      metricNames: ['revenue_avg'],
      orderBys: [],
      where: '',
      timeQueryParams: {
        timeEndFunction: {
          day: 31,
          month: 3,
          type: 'specificDate',
          year: 2021,
        },
        timeGranularity: 'total',
        timeStartFunction: {
          day: 1,
          month: 3,
          type: 'specificDate',
          year: 2021,
        },
      },
    })
    expect(sql).toEqual(
      `SELECT AVG(revenue_column) AS revenue_avg, shop_id_column AS shop_id FROM sales_data WHERE order_dt = '202103' GROUP BY shop_id`,
    )

    /*
    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data WHERE order_dt = '202103'
), revenue_avg_TBL AS (
  SELECT AVG(revenue_column) as revenue_avg, shop_id_column AS shop_id FROM sales_data WHERE order_dt = '202103' GROUP BY shop_id
)
SELECT DIM_TBL.shop_id, revenue_avg_TBL.revenue_avg FROM DIM_TBL LEFT JOIN revenue_avg_TBL ON DIM_TBL.shop_id = revenue_avg_TBL.shop_id`)
*/
  })
})

describe('metric2sql toSql 方法支持 ratio 指标', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )

  it('占比指标无 GROUP BY 和 where', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['balance_ratio'],
    })

    expect(sql).toEqual(`WITH balance_sum_TBL AS (
  SELECT SUM(balance_column) as balance_sum FROM sales_data
), balance_ratio_TBL AS (
  SELECT balance_sum_TBL.balance_sum / (SELECT SUM(balance_column) FROM sales_data) as balance_ratio FROM balance_sum_TBL
)
SELECT balance_ratio_TBL.balance_ratio FROM balance_ratio_TBL`)
  })

  it('占比指标，有 GROUP BY 和 WHERE 对分子生效，分母直接使用子查询解决', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['balance_ratio'],
      groupBys: ['shop_id'],
      where: '1 = 1 and 2 = 2',
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data WHERE (1 = 1 AND 2 = 2)
), balance_sum_TBL AS (
  SELECT SUM(balance_column) as balance_sum, shop_id_column AS shop_id FROM sales_data WHERE (1 = 1 AND 2 = 2) GROUP BY shop_id
), balance_ratio_TBL AS (
  SELECT balance_sum_TBL.balance_sum / (SELECT SUM(balance_column) FROM sales_data) as balance_ratio, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN balance_sum_TBL ON DIM_TBL.shop_id = balance_sum_TBL.shop_id
)
SELECT DIM_TBL.shop_id, balance_ratio_TBL.balance_ratio FROM DIM_TBL LEFT JOIN balance_ratio_TBL ON DIM_TBL.shop_id = balance_ratio_TBL.shop_id`)
  })

  it('占比指标和其他指标混用，且有 GROUP BY 和 where', async () => {
    const { sql } = await metric2Sql.toSql({
      metricNames: ['balance_ratio', 'balance_sum', 'profit'],
      groupBys: ['shop_id'],
      where: '1 = 1 or 2 = 2',
    })

    expect(sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data WHERE (1 = 1 OR 2 = 2)
), balance_sum_TBL AS (
  SELECT SUM(balance_column) as balance_sum, shop_id_column AS shop_id FROM sales_data WHERE (1 = 1 OR 2 = 2) GROUP BY shop_id
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, shop_id_column AS shop_id FROM sales_data WHERE (1 = 1 OR 2 = 2) GROUP BY shop_id
), cost_sum_TBL AS (
  SELECT SUM(cost_column) as cost_sum, shop_id_column AS shop_id FROM sales_data WHERE (1 = 1 OR 2 = 2) GROUP BY shop_id
), balance_ratio_TBL AS (
  SELECT balance_sum_TBL.balance_sum / (SELECT SUM(balance_column) FROM sales_data) as balance_ratio, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN balance_sum_TBL ON DIM_TBL.shop_id = balance_sum_TBL.shop_id
), profit_TBL AS (
  SELECT (revenue_sum_TBL.revenue_sum - cost_sum_TBL.cost_sum) as profit, DIM_TBL.shop_id FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id LEFT JOIN cost_sum_TBL ON DIM_TBL.shop_id = cost_sum_TBL.shop_id
)
SELECT DIM_TBL.shop_id, balance_ratio_TBL.balance_ratio, balance_sum_TBL.balance_sum, profit_TBL.profit FROM DIM_TBL LEFT JOIN balance_ratio_TBL ON DIM_TBL.shop_id = balance_ratio_TBL.shop_id LEFT JOIN balance_sum_TBL ON DIM_TBL.shop_id = balance_sum_TBL.shop_id LEFT JOIN profit_TBL ON DIM_TBL.shop_id = profit_TBL.shop_id`)
  })
})

describe('verifyQueryParams 当对日期进行分组，但是没有按照日期排序的时候，默认加日期asc排序', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )
  it('如果对日期分组，则返回 orderBys 日期', async () => {
    const queryParams: QueryParams = {
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: '1 = 1',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'year',
      } as TimeQueryParams,
    }
    const builder = await metric2Sql.createQueryParamsBuilder()
    const result = await builder.invoke(queryParams)
    expect(result.orderBys).toEqual(['V_DATE_ ASC'])
  })

  it('如果对日期分组，则返回 orderBys 日期', async () => {
    const queryParams: QueryParams = {
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: '1 = 1',
      timeQueryParams: {
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'total',
      } as TimeQueryParams,
    }
    const builder = await metric2Sql.createQueryParamsBuilder()
    const result = await builder.invoke(queryParams)
    expect(result.orderBys).toEqual([])
  })
})

describe('多个时间维度，取其中一个时间维度', async () => {
  const metric2Sql = new Metric2Sql(
    new MetricConfig(
      nanoid(),
      semanticModel.name,
      allDimensions,
      allMeasures,
      allNoListMetrics,
      allNoListMetrics,
      allExternalReports,
      0,
      timeDimensionDatum,
    ),
  )
  it('使用非 default 日期维度 yyyy_MM_DD', async () => {
    const queryParams: QueryParams = {
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: '1 = 1',
      timeQueryParams: {
        timeDimensionName: 'login_dt',
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'total',
      } as TimeQueryParams,
    }
    expect((await metric2Sql.toSql(queryParams)).sql).toEqual(
      `SELECT SUM(revenue_column) AS revenue_sum, shop_id_column AS shop_id FROM sales_data WHERE time_column >= '2020_01_01' AND time_column <= '2024_12_31' AND (1 = 1) GROUP BY shop_id`,
    )

    /*
    expect(metric2Sql.toSql(queryParams).sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data WHERE time_column >= '2020_01_01' AND time_column <= '2024_12_31' AND (1 = 1)
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, shop_id_column AS shop_id FROM sales_data WHERE time_column >= '2020_01_01' AND time_column <= '2024_12_31' AND (1 = 1) GROUP BY shop_id
)
SELECT DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id`)
    */
  })

  it('使用非 default 日期维度 yyyy/MM/DD ', async () => {
    const queryParams: QueryParams = {
      metricNames: ['revenue_sum'],
      groupBys: ['shop_id'],
      where: '1 = 1 or 2 = 2',
      timeQueryParams: {
        timeDimensionName: 'pay_dt',
        timeStartFunction: { type: 'specificYear', year: 2020 },
        timeEndFunction: { type: 'specificYear', year: 2024 },
        timeGranularity: 'total',
      } as TimeQueryParams,
    }
    expect((await metric2Sql.toSql(queryParams)).sql).toEqual(
      `SELECT SUM(revenue_column) AS revenue_sum, shop_id_column AS shop_id FROM sales_data WHERE pay_dt >= '2020/01/01' AND pay_dt <= '2024/12/31' AND (1 = 1 OR 2 = 2) GROUP BY shop_id`,
    )

    /*
    expect(metric2Sql.toSql(queryParams).sql).toEqual(`WITH DIM_TBL AS (
  SELECT DISTINCT shop_id_column AS shop_id FROM sales_data WHERE pay_dt >= '2020/01/01' AND pay_dt <= '2024/12/31' AND (1 = 1 OR 2 = 2)
), revenue_sum_TBL AS (
  SELECT SUM(revenue_column) as revenue_sum, shop_id_column AS shop_id FROM sales_data WHERE pay_dt >= '2020/01/01' AND pay_dt <= '2024/12/31' AND (1 = 1 OR 2 = 2) GROUP BY shop_id
)
SELECT DIM_TBL.shop_id, revenue_sum_TBL.revenue_sum FROM DIM_TBL LEFT JOIN revenue_sum_TBL ON DIM_TBL.shop_id = revenue_sum_TBL.shop_id`)
    */
  })
})
// 查询成本大于1000的各区域列表
// 查询各区域成本大于1000的店铺列表
// 如果是维度，就不存在是否 sum 的问题。
