/**
 * @description server 侧的工具函数集合
 */
import path from 'path'
import fs from 'fs'
import axios from 'axios'
import { <PERSON><PERSON>, Request, RequestHandler } from 'express'
import CryptoJS from 'crypto-js'
import {
  ChatResponseErrorStatus,
  ChatResponseErrorTypes,
  ConverChatErrorTypes,
  Llm,
  LlmType,
  UserPermissionDatasource,
  UserPermissionLlmType,
  UserPermissionProject,
} from '@shared/common-types'
import { HUAXIA_APP_ID } from 'src/shared/constants'
import { assertExhaustive, getCryptoJsBase64 } from 'src/shared/common-utils'
import { encodeResourceRule } from 'src/shared/auth'
import { prisma } from './dao/db'
import { PROCESS_ENV } from './server-constants'
import { enforcer } from './auth'

export const generateLocalUrl = (path: string) => {
  return `http://localhost:${PROCESS_ENV.PORT}${path}`
}

export const generateXengineUrl = (path: string, baseUrl?: string) => {
  if (baseUrl?.startsWith(PROCESS_ENV.BASE_URL)) {
    baseUrl = baseUrl.replace(PROCESS_ENV.BASE_URL, '')
  }
  if (baseUrl) {
    return `${PROCESS_ENV.XENGINE_PROXY_URL}${baseUrl}${path}`
  }
  return `${PROCESS_ENV.XENGINE_PROXY_URL}${path}`
}

export const generateClusterUrl = (path: string, baseUrl?: string) => {
  if (baseUrl?.startsWith(PROCESS_ENV.CLUSTER_PROXY_URL)) {
    baseUrl = baseUrl.replace(PROCESS_ENV.BASE_URL, '')
  }
  if (baseUrl) {
    return `${PROCESS_ENV.CLUSTER_PROXY_URL}${baseUrl}${path}`
  }
  return `${PROCESS_ENV.CLUSTER_PROXY_URL}${path}`
}

export const generateAIUrl = (path: string, paramsExtractApi?: string) => {
  const baseUrl = paramsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
  return `${baseUrl}/api${path}`
}
export function safeJSONParse(JSONStr: string) {
  try {
    return JSON.parse(JSONStr)
  } catch (err) {
    return null
  }
}

/**
 * 去除掉开头和末尾的 引号
 */
function removeQuotesFromStartAndEnd(text: string): string {
  if (text.startsWith('"') || text.startsWith("'")) {
    text = text.slice(1)
  }
  if (text.endsWith('"') || text.endsWith("'")) {
    text = text.slice(0, -1)
  }
  return text
}

/**
 * 去除 sql 中的换行符号，合并多个空格为1个空格
 */
function removeNewlinesAndMergeSpaces(sql: string): string {
  return sql
    .replace(/\n|\\n|\\/g, ' ') // 去掉换行符
    .replace(/ +/g, ' ') // 多个空格合并成一个
    .trim()
}

/**
 * 抽取sql语句 并去掉换行符
 */
export function extractFormattedSQL(text: string): string | null {
  // 匹配到 markdown 格式的 sql 语句，sql 中必须有 select 才算
  const sqlMarkDownRegex = /```(sql)?\s*?select([\s\S]*?)```/im

  const markdownMatch = text.match(sqlMarkDownRegex)
  if (markdownMatch && markdownMatch.length >= 3) {
    return removeNewlinesAndMergeSpaces('SELECT ' + markdownMatch[2])
  }

  // 如果返回的是纯sql，根据正则进行匹配，从【select】开始 到【;】结束
  const sqlRegex = /^((?:\s*)?(SELECT|select)[\s\S]*?)(?=(?:;|$))/g
  const textWithoutQuotes = removeQuotesFromStartAndEnd(text)
  const commonSqlMatch = textWithoutQuotes.match(sqlRegex)
  if (commonSqlMatch) {
    return removeNewlinesAndMergeSpaces(commonSqlMatch[0])
  }

  // 如果返回 JSON 格式，里面有 sql，也做支持
  try {
    const data = JSON.parse(text)
    if (data.sql) {
      return removeNewlinesAndMergeSpaces(data.sql)
    }
  } catch (e) {
    return null // 非 json，不做处理
  }
  return null
}

/** 除了GPT之外的多种大模型 */
export const NOT_GPT_LLM_TYPE: LlmType[] = ['vllm-mixtral-8x7b-chat']
export const GPT_LLM_TYPE: LlmType[] = ['gpt-3.5-turbo', 'gpt-4-turbo-preview']

/**
 * 所有可用的大模型列表，为了数据安全，只有 server 端可以访问
 * @type 传递到后端的 llm-type
 * @name dropdown中每一项的label
 * @abbrName 选中模型后，展示在Input框中的label
 */
const ORIGINAL_ALL_LLMS: Llm[] = [
  {
    type: 'gpt-3.5-turbo',
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5',
    abbrName: 'GPT-3.5',
    tokenLimit: 16000,
    logo: 'gpt-3.5-turbo',
    disable: false,
  },
  {
    type: 'gpt-4-turbo-preview',
    id: 'gpt-4-turbo-preview',
    name: 'GPT-4',
    abbrName: 'GPT-4',
    tokenLimit: 8192,
    logo: 'gpt-4-turbo-preview',
    disable: false,
  },
  {
    type: 'azure-gpt',
    id: 'azure-gpt',
    name: 'Azure-GPT',
    abbrName: 'Azure-GPT',
    tokenLimit: 16000,
    logo: 'Azure-GPT',
    disable: false,
  },
  {
    type: 'zhipu-glm-4',
    id: 'zhipu-glm-4',
    name: 'GLM-4',
    abbrName: 'GLM-4',
    tokenLimit: 16000,
    logo: 'GLM-4',
    disable: false,
  },
  {
    type: 'zhipu-chatglm3_32b',
    id: 'zhipu-chatglm3_32b',
    name: 'GLM3-32B',
    abbrName: 'GLM3-32B',
    tokenLimit: 16000,
    logo: 'GLM3-32B',
    disable: false,
  },
  {
    type: 'vllm-mixtral-8x7b-chat',
    id: 'vllm-mixtral-8x7b-chat',
    name: 'DIPeak Mixtral',
    abbrName: 'DIPeak Mixtral',
    tokenLimit: 4096,
    logo: 'Mixtral',
    disable: false,
  },
  {
    type: 'baichuan2',
    id: 'baichuan2',
    name: '百川',
    abbrName: '百川',
    tokenLimit: 16000,
    logo: '百川',
    disable: false,
  },
  {
    type: 'yi_1.5_34b',
    id: 'yi_1.5_34b',
    name: 'Dipeak LLM1-4k',
    abbrName: 'Dipeak LLM1-4k',
    tokenLimit: 4096,
    logo: 'Dipeak LLM1-4k',
    disable: false,
  },
  {
    type: 'yi_1.5_34b_16k',
    id: 'yi_1.5_34b_16k',
    name: 'Dipeak LLM1-16k',
    abbrName: 'Dipeak LLM1-16k',
    tokenLimit: 16384,
    logo: 'Dipeak LLM1-16k',
    disable: false,
  },
  {
    type: 'chat_law_7b',
    id: 'chat_law_7b',
    name: 'ChatLaw-7B',
    abbrName: 'ChatLaw-7B',
    tokenLimit: 32768,
    logo: 'ChatLaw-7B',
    disable: false,
  },
  {
    type: 'pre-glm-4-9b',
    id: 'pre-glm-4-9b',
    name: 'Dipeak GLM',
    abbrName: 'Dipeak GLM',
    tokenLimit: 4096,
    logo: 'Dipeak GLM',
    disable: false,
  },
  {
    type: 'deepseek-14b',
    id: 'deepseek-14b',
    name: 'DeepSeek 14B',
    abbrName: 'DeepSeek 14B',
    tokenLimit: 16000,
    logo: 'DeepSeek 14B',
    disable: false,
  },
]

/**
 * 检查 PROCESS_ENV.ENABLED_LLM_MODELS 的格式：
 * 1. 可不设置，不设置则不过滤
 * 2. 如果设置，用逗号分隔，每个元素必须是 ALL_LLMS 中的 type
 */
if (PROCESS_ENV.ENABLED_LLM_MODELS.length > 0) {
  PROCESS_ENV.ENABLED_LLM_MODELS.split(',').forEach((llmType: string) => {
    if (!ORIGINAL_ALL_LLMS.map((llm) => llm.type).includes(llmType as LlmType)) {
      throw new Error(`ENABLED_LLM_MODELS 中包含了不合法的模型类型：${llmType}`)
    }
  })
}

/**
 * 检查 DEFAULT_LLM_MODEL 的格式：必须配置，且必须是 ALL_LLMS 中的 type
 */
const isDefaultLlmValid = ORIGINAL_ALL_LLMS.some((llm) => {
  if (llm.type === PROCESS_ENV.DEFAULT_LLM_MODEL) {
    return true
  }
  return false
})

const onlyXEngine = PROCESS_ENV.VITE_PRODUCTS === 'X-Engine'
// 如果只部署 xengine 那不配置模型不报错
if (!isDefaultLlmValid && !onlyXEngine) {
  throw new Error(`DEFAULT_LLM_MODEL 配置错误，暂不支持这个模型：${PROCESS_ENV.DEFAULT_LLM_MODEL}`)
}

export const ALL_LLMS = ORIGINAL_ALL_LLMS.filter((llm) => {
  if (PROCESS_ENV.ENABLED_LLM_MODELS.length > 0) {
    return PROCESS_ENV.ENABLED_LLM_MODELS.split(',').includes(llm.type)
  }
  return true
})

// 从node中req中拿到username后设置到axios上,设置之前先获取username，判断，避免重复设置
export function setAxiosUserId(username: string) {
  axios.defaults.headers.common['userId'] = username
}

/**
 * 从字符串中提取出 JSON，用于从 LLM 结果中提取 JSON，是否返回 null。
 * 注意，只能提取出 {}，不能提取出 []
 * 搜索字符串，找到第一个 { 符号和对应的闭合 } 符号，以此来提取 JSON 对象。
 */
export function extractJsonFromString<T = any>(str: string) {
  // 首先匹配 ```json {} ``` 格式的 JSON
  const jsonRegex = /```json\s*([\s\S]*?)```/i
  const match = jsonRegex.exec(str)

  if (match) {
    try {
      return JSON.parse(match[1])
    } catch (error) {
      console.error(`Unexpected parsing JSON: ${str}, error: ${(error as Error).message}`)
      return null
    }
  }

  // 如果没有匹配到 ```json {} ``` 格式的 JSON，再尝试匹配普通的 JSON
  const stack: string[] = []
  let inString = false
  let escape = false
  let jsonStr = ''
  let start = -1

  for (let i = 0; i < str.length; i++) {
    const char = str[i]

    if (inString) {
      if (escape) {
        escape = false
      } else if (char === '\\') {
        escape = true
      } else if (char === '"') {
        inString = false
      }
    } else {
      if (char === '"') {
        inString = true
      } else if (char === '{') {
        stack.push('{')
        if (start === -1) start = i
      } else if (char === '}') {
        stack.pop()
        if (stack.length === 0) {
          jsonStr = str.substring(start, i + 1)
          break
        }
      }
    }
  }

  if (jsonStr) {
    // 尝试解析 JSON，如果失败则返回 null
    try {
      return JSON.parse(jsonStr) as T
    } catch (error) {
      console.error('解析 JSON 时出错：', error)
      return null
    }
  } else {
    return null
  }
}

const rolePermissionSelectMap = {
  Datasource: {
    rolePermissionDatasources: {
      select: {
        id: true,
        roleId: true,
        datasourceId: true,
      },
    },
  },
  Llm: {
    rolePermissionLlms: {
      select: {
        id: true,
        roleId: true,
        llmType: true,
      },
    },
  },
  Project: {
    rolePermissionProjects: {
      select: {
        id: true,
        roleId: true,
        semanticProjectId: true,
      },
    },
  },
}
interface RoleBase {
  id: string
  name: string
}

interface RoleWithDatasource extends RoleBase {
  rolePermissionDatasources: {
    id: string
    roleId: string
    datasourceId: string
  }[]
}

interface RoleWithLlm extends RoleBase {
  rolePermissionLlms: {
    id: string
    roleId: string
    llmType: string
  }[]
}

interface RoleWithProject extends RoleBase {
  rolePermissionProjects: {
    id: string
    roleId: string
    semanticProjectId: string
  }[]
}

interface UserRoleBase {
  role: RoleWithDatasource | RoleWithLlm | RoleWithProject
  username: string
  roleId: string
}

type PermissionMap = {
  Datasource: UserPermissionDatasource[]
  Llm: UserPermissionLlmType[]
  Project: UserPermissionProject[]
}

const permissionModelMap = {
  Datasource: 'permissionDatasource',
  Llm: 'permissionLlm',
  Project: 'permissionProject',
}

/**
 * 根据用户和角色获取有权限的[模型列表, 数据源列表, Project列表]
 * 获取[用户权限] [角色权限]
 * 融合两个权限列表
 */
export async function getPermissionListUniqueArray<T extends keyof PermissionMap>(
  username: string,
  permissionType: T,
): Promise<PermissionMap[T]> {
  // 确定是否是管理员
  const isAdmin = await prisma.userRole.findMany({
    where: { username, role: { id: 'admin' } },
  })

  if (isAdmin.length > 0) {
    // 如果是管理员，根据permissionType返回所有数据
    const list = await getAllResourceListByAdmin(username, permissionType)
    console.info(`管理员用户，返回所有的${permissionType}数据`, list.length)
    return list
  }

  const userPermissionList = await (prisma as any)[permissionModelMap[permissionType]].findMany({
    where: { username },
  })
  const selectConfig = rolePermissionSelectMap[permissionType]

  const rolePermissionRawList = (await prisma.userRole.findMany({
    where: { username },
    include: {
      role: {
        select: {
          id: true,
          name: true,
          ...selectConfig,
        },
      },
    },
  })) as UserRoleBase[]

  let rolePermissionList
  if (permissionType === 'Datasource') {
    // 合并rolePermissionRawList中的所有权限列表并将内容统一成userPermissionList
    rolePermissionList = rolePermissionRawList.reduce((acc: any[], currentValue) => {
      const permissionsWithExtraInfo = (currentValue.role as RoleWithDatasource).rolePermissionDatasources.map(
        (permission) => ({
          id: permission.id,
          username,
          datasourceId: permission.datasourceId,
        }),
      )
      return acc.concat(permissionsWithExtraInfo)
    }, [])
    // 合并两个数组 并去重
    return userPermissionList
      .concat(rolePermissionList)
      .reduce((acc: UserPermissionDatasource[], current: UserPermissionDatasource) => {
        if (!acc.some((item) => item.datasourceId === current.datasourceId)) {
          acc.push(current)
        }
        return acc
      }, [])
  } else if (permissionType === 'Llm') {
    rolePermissionList = rolePermissionRawList.reduce((acc: any[], currentValue) => {
      const permissionsWithExtraInfo = (currentValue.role as RoleWithLlm).rolePermissionLlms.map((permission) => ({
        id: permission.id,
        username,
        llmType: permission.llmType,
      }))
      return acc.concat(permissionsWithExtraInfo)
    }, [])
    return userPermissionList
      .concat(rolePermissionList)
      .reduce((acc: UserPermissionLlmType[], current: UserPermissionLlmType) => {
        if (!acc.some((item) => item.llmType === current.llmType)) {
          acc.push(current)
        }
        return acc
      }, [])
  } else if (permissionType === 'Project') {
    rolePermissionList = rolePermissionRawList.reduce((acc: any[], currentValue) => {
      const permissionsWithExtraInfo = (currentValue.role as RoleWithProject).rolePermissionProjects.map(
        (permission) => ({
          id: permission.id,
          username,
          semanticProjectId: permission.semanticProjectId,
        }),
      )
      return acc.concat(permissionsWithExtraInfo)
    }, [])
    return userPermissionList.concat(rolePermissionList).reduce((acc: UserPermissionProject[], current: any) => {
      if (!acc.some((item) => item.semanticProjectId === current.semanticProjectId)) {
        acc.push(current)
      }
      return acc
    }, [])
  }
  return [] as PermissionMap[T]
}

/**
 * 管理员用户获取所有的[模型列表, 数据源列表, Project列表]
 * @param permissionType
 */
export async function getAllResourceListByAdmin<T extends keyof PermissionMap>(
  username: string,
  permissionType: T,
): Promise<PermissionMap[T]> {
  if (permissionType === 'Datasource') {
    const datasource = await prisma.datasource.findMany({})
    const datasourceList = datasource.map((e) => ({
      id: e.id,
      datasourceId: e.id,
      username,
    }))
    return datasourceList as PermissionMap[T]
  } else if (permissionType === 'Llm') {
    const allLlms = ALL_LLMS.filter((llm) => !llm.disable)
    const llmList = allLlms.map((llm) => ({
      id: llm.type,
      llmType: llm.type,
      username,
    }))
    return llmList as PermissionMap[T]
  } else if (permissionType === 'Project') {
    const semanticProjectList = await prisma.semanticProject.findMany({})
    const projectList = semanticProjectList.map((project) => ({
      id: project.id,
      semanticProjectId: project.id,
      username,
    }))
    return projectList as PermissionMap[T]
  }
  return [] as PermissionMap[T]
}

/**
 * 去掉url中的ip和端口，用于在后端代理
 * @param url 文件url
 * @returns
 */
export function removeDomainFromUrl(url: string) {
  const pathMatch = url.match(/:\/\/[^/]*(\/.*)/)
  return pathMatch ? pathMatch[1] : ''
}

export function getConverChatErrorTypeByResErrType(errType: ChatResponseErrorStatus) {
  switch (errType) {
    case ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST:
    case ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT:
    case ChatResponseErrorTypes.PROJECT_NOT_EXIST:
    case ChatResponseErrorTypes.MODEL_NOT_EXIST:
    case ChatResponseErrorTypes.REFRESHING_CACHE:
    case ChatResponseErrorTypes.COST_SCENE_ERROR:
    case ChatResponseErrorTypes.E_UNKNOWN: {
      return ConverChatErrorTypes.OTHER_ERROR
    }
    case ChatResponseErrorTypes.CHIT_CHAT: {
      return ConverChatErrorTypes.CHIT_CHAT
    }
    case ChatResponseErrorTypes.LLM_ERROR: {
      return ConverChatErrorTypes.LLM_ERROR
    }
    case ChatResponseErrorTypes.NO_DATA_AUTHORITY: {
      return ConverChatErrorTypes.NO_DATA_AUTHORITY
    }
    case ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST: {
      return ConverChatErrorTypes.LATEST_DATA_NOT_EXIST
    }
    case ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST: {
      return ConverChatErrorTypes.FUTURE_DATA_NOT_EXIST
    }
    case ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED: {
      return ConverChatErrorTypes.QUESTION_NOT_SUPPORTED
    }
    case ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT:
    case ChatResponseErrorTypes.METRICS_NOT_EXIST:
    case ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST:
    case ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST: {
      return ConverChatErrorTypes.NO_METRICS
    }
    case ChatResponseErrorTypes.LOOKUP_FAILED: {
      return ConverChatErrorTypes.LOOKUP_FAILED
    }
    case ChatResponseErrorTypes.CALCULATE_FAILED: {
      return ConverChatErrorTypes.CALCULATE_FAILED
    }
    case ChatResponseErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL: {
      return ConverChatErrorTypes.AGENT_BRAIN_NO_SUCCEED_TOOL_CALL
    }
    case ChatResponseErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL: {
      return ConverChatErrorTypes.AGENT_JUDGE_NO_SUCCEED_TOOL_CALL
    }
    case ChatResponseErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL: {
      return ConverChatErrorTypes.AGENT_BRAIN_ILLEGAL_TOOL_CALL
    }
    case ChatResponseErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL: {
      return ConverChatErrorTypes.AGENT_JUDGE_ILLEGAL_TOOL_CALL
    }
    case ChatResponseErrorTypes.SENSITIVE_QUESTION: {
      return ConverChatErrorTypes.SENSITIVE_QUESTION
    }
    case ChatResponseErrorTypes.NEED_MANUAL_SELECT: {
      return ConverChatErrorTypes.NEED_MANUAL_SELECT
    }
    case ChatResponseErrorTypes.MANUAL_SELECT_NOT_EXIST: {
      return ConverChatErrorTypes.MANUAL_SELECT_NOT_EXIST
    }
    case ChatResponseErrorTypes.MANUAL_SELECT_NOT_ENOUGH: {
      return ConverChatErrorTypes.MANUAL_SELECT_NOT_ENOUGH
    }
    case ChatResponseErrorTypes.NOT_NEED_BI_RESULT: {
      return ConverChatErrorTypes.NOT_NEED_BI_RESULT
    }
    case ChatResponseErrorTypes.E_MATCH: {
      return ConverChatErrorTypes.E_MATCH
    }
    case ChatResponseErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED: {
      return ConverChatErrorTypes.E_MAKE_AGENT_REQUEST_CANCELLED
    }
    default: {
      return assertExhaustive(errType)
    }
  }
}

/**
 * 通用路径处理函数，从项目根路径开始引入
 * @param p 路径集合
 */
export function resolve(...p: string[]) {
  return path.resolve(__dirname, '..', '..', ...p)
}

/**
 * client打包产物路径，方便后续的路径拼接
 */
export const PATH_DIST_CLIENT = resolve('dist-client')

/**
 * 静态压缩中间件
 */
export function createStaticCompression() {
  const needStaticCompressionSet = new Set(['.js', '.css'])
  // 只对js和css进行压缩文件返回
  function needStaticCompression(filePath: string) {
    return needStaticCompressionSet.has(path.extname(filePath))
  }
  // 通过后缀获得content-type
  function getContentType(filePath: string) {
    switch (path.extname(filePath)) {
      case '.js':
        return 'application/javascript'
      case '.css':
        return 'text/css'
      default:
        return null
    }
  }
  const requestHandler: RequestHandler = (req, res, next) => {
    const filePath = resolve(PATH_DIST_CLIENT, '.' + req.url)
    // 用来判断chrome是否支持某个压缩
    const acceptEncoding = req.headers['accept-encoding']
    const contentType = getContentType(filePath)
    // 提前校验基础信息是否符合
    if (needStaticCompression(filePath) && contentType && fs.existsSync(filePath)) {
      if (acceptEncoding?.includes('br') && fs.existsSync(filePath + '.br')) {
        // 检测是否可以br压缩
        res.setHeader('Content-Encoding', 'br')
        res.setHeader('Content-Type', contentType)
        fs.createReadStream(filePath + '.br').pipe(res)
      } else if (acceptEncoding?.includes('gzip') && fs.existsSync(filePath + '.gz')) {
        // 检测是否可以gzip压缩
        res.setHeader('Content-Encoding', 'gzip')
        res.setHeader('Content-Type', contentType)
        fs.createReadStream(filePath + '.gz').pipe(res)
      } else {
        next()
      }
    } else {
      next()
    }
  }
  return requestHandler
}

/**
 * 获取提参URL中的第一个
 * 因为会有多个测试提参URL，所以需要获取第一个
 * @returns 第一个url
 */
export function getParamsExtractUrl() {
  const urls = PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
  // 使用split方法将字符串转换为数组，以逗号为分隔符
  const urlArray = urls && urls.split(',')
  // 返回JSON响应，包含转换后的数组
  return urlArray[0]
}

/**
 * 解密用户ID
 *
 * @param k 密钥，使用UTF-8编码
 * @param i 初始化向量，使用UTF-8编码
 * @param base64 待解密的密文，可以是Base64字符串或CipherParams对象
 * @returns 解密后的用户ID字符串
 */
export function decryptUserId(base64: string | CryptoJS.lib.CipherParams) {
  const key = CryptoJS.enc.Utf8.parse(PROCESS_ENV.BI_AES_KEY)
  const iv = CryptoJS.enc.Utf8.parse(PROCESS_ENV.BI_AES_IV)
  const encrypted = CryptoJS.AES.decrypt(base64, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  return encrypted.toString(CryptoJS.enc.Utf8)
}

export function getEnvTag() {
  // 从环境变量获取值，如果不存在则返回空字符串
  const key = PROCESS_ENV.BI_AES_KEY || ''
  const code = PROCESS_ENV.BI_AES_IV || ''
  return { tag: getCryptoJsBase64(key), code: getCryptoJsBase64(code) }
}

export async function createResource(name: any, type: any, typeData: any, rules: any) {
  const existResource = await prisma.xResource.findFirst({
    where: { name },
  })
  if (existResource) throw new Error('存在相同名称的资源')
  const resource = await prisma.xResource.create({
    data: {
      name,
      type,
      typeData,
      rules: {
        createMany: {
          data: rules
            .map(({ type: ownerType, id: ownerId, action }: Record<string, string>) => {
              return encodeResourceRule(type, typeData).map((v1) => {
                return {
                  ptype: 'p',
                  v0: `${ownerType}:${ownerId}`,
                  v1,
                  v2: action,
                }
              })
            })
            .flat(),
        },
      },
    },
  })
  return resource
}
export async function createUser(arg: {
  username: string
  password: string
  roles?: string[]
  groups?: string[]
  rangerUsername?: string
  rangerPassword?: string
  roleNames?: string[]
  groupNames?: string[]
}) {
  const { username, password, roles, groups, rangerUsername, rangerPassword, roleNames, groupNames } = arg
  const isExist = await prisma.xUser.findFirst({ where: { username } })
  if (isExist) throw new Error('用户名不能重复')
  const user = await prisma.xUser.create({
    data: {
      username,
      password,
      rangerUsername,
      rangerPassword,
    },
  })
  if (Array.isArray(roles)) {
    await enforcer.unionUserRole({
      v0: { id: user.id, type: 'user' },
      v1: { type: 'roles', ids: roles },
    })
  }
  if (Array.isArray(roleNames)) {
    const idListFromNames = (
      await prisma.xRole.findMany({ where: { roleName: { in: roleNames } }, select: { id: true } })
    ).map((v) => v.id)
    await enforcer.unionUserRole({
      v0: { id: user.id, type: 'user' },
      v1: { type: 'roles', ids: idListFromNames },
    })
  }
  if (Array.isArray(groups)) {
    await enforcer.unionUserRole({
      v0: { id: user.id, type: 'user' },
      v1: { type: 'groups', ids: groups },
    })
  }
  if (Array.isArray(groupNames)) {
    const idListFromNames = (
      await prisma.xGroup.findMany({ where: { groupName: { in: groupNames } }, select: { id: true } })
    ).map((v) => v.id)
    await enforcer.unionUserRole({
      v0: { id: user.id, type: 'user' },
      v1: { type: 'groups', ids: idListFromNames },
    })
  }
  return user
}

export const ENV = process.env as Record<string, string>

export const IS_HUAXIA = ENV.VITE_PROJECT_ENV === HUAXIA_APP_ID

export function getRequestInfo(req: Request) {
  const username = req.user?.username || ''
  const userIp = req.header('X-Real-IP') || req.socket.remoteAddress
  const host = req.header('Host') || ''
  const traceId = req.header('traceId')
  const user = req.user
  return {
    username,
    userIp,
    host,
    traceId,
    user,
  }
}

export function asyncResponseWrapper(fn: Handler) {
  return async (...args: Parameters<Handler>) => {
    const [, res] = args
    try {
      await fn(...args)
    } catch (err: any) {
      console.info('===> ERROR', err?.message ?? err?.toString() ?? 'Server Error')
      console.error(err)
      return res.json({ code: 1, msg: err?.message ?? err?.toString() ?? 'Server Error' })
    }
  }
}
