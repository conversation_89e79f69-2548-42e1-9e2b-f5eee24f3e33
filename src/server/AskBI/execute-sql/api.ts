/**
 * @description: 对元数据库执行SQL
 */

import express, { Router, Request, Response } from 'express'
import { prisma } from 'src/server/dao/prisma-init'

const router: Router = express.Router()

const dangerousKeywords = [/delete\s+/i, /truncate\s+/i, /drop\s+/i, /alter\s+/i]

const isDangerousSQL = (sql: string) => dangerousKeywords.some((reg) => reg.test(sql))

router.post('/', async (req: Request, res: Response) => {
  try {
    const sql = req.body.sql
    console.info('Execute Raw SQL:', sql)

    if (isDangerousSQL(sql)) {
      return res.json({ code: 403, msg: '禁止执行敏感/危险SQL语句！' })
    }

    const sqlResult = await prisma.$queryRawUnsafe(sql)
    return res.json({ code: 0, data: sqlResult })
  } catch (error) {
    return res.json({ code: 500, data: {}, msg: '执行SQL失败, Error: ' + (error as Error)?.message })
  }
})

export default router
