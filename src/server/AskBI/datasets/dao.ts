import { SemanticScene } from '@prisma/client'
import { enforcer } from 'src/server/auth'
import { semanticToDataset } from 'src/shared/common-utils'
import { SceneConfigValueTypeMap } from 'src/shared/config-management-constants'
import { prisma } from 'src/server/dao/prisma-init'
import { DatasetDatum } from 'src/shared/common-types'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { ColumnsTitleMapItem } from 'src/shared/metric-types'

/**
 * 获取当前用户默认的 dataset
 */
export async function getDefaultDataset(id: string): Promise<DatasetDatum | null> {
  const projects = await enforcer.getProjectsFromUserId(id)
  if (!projects.length || !projects[0].semanticScenes.length) return null
  const project = projects[0]
  const scene = project.semanticScenes[0] as SemanticScene & SceneConfigValueTypeMap
  return semanticToDataset({ project, scene })
}

/** 返回一个表的所有字段英文名和字段中文名信息 */
export async function getAllColumnCommentOfTable(tableName: string): Promise<ColumnsTitleMapItem[]> {
  const columnMetaSql = `SHOW FULL COLUMNS FROM ${tableName}`

  try {
    const columnMeta = (await executeAllXengineSql(columnMetaSql)).data
    return columnMeta.map((item) => {
      return {
        field: item.Field,
        title: item.Comment,
      }
    })
  } catch {
    return []
  }
}
export async function fetchProjectInfoBySceneId(sceneId: string | undefined): Promise<{
  sceneId?: string
  sceneLabel?: string
  projectId?: string
  projectLabel?: string
}> {
  const fallbackResult = {
    sceneId,
    sceneLabel: undefined,
    projectId: undefined,
    projectLabel: undefined,
  }

  try {
    if (!sceneId) {
      // console.error(`[fetchProjectInfoBySceneId] SceneId not found: ${sceneId}`)
      return fallbackResult
    }
    const scene = await prisma.semanticScene.findUnique({
      where: { id: sceneId },
      select: { semanticProjectId: true, label: true },
    })
    if (!scene) {
      console.error(`[fetchProjectInfoBySceneId] Scene not found: ${sceneId}`)
      return fallbackResult
    }
    const project = await prisma.semanticProject.findUnique({
      where: { id: scene.semanticProjectId },
      select: { id: true, name: true },
    })
    if (!project) {
      console.error(`[fetchProjectInfoBySceneId] Project not found: ${scene.semanticProjectId}`)
      return fallbackResult
    }
    return {
      sceneId,
      sceneLabel: scene.label,
      projectId: project.id,
      projectLabel: project.name,
    }
  } catch (error) {
    console.error('[fetchProjectInfoBySceneId] Unexpected error:', error)
    return fallbackResult
  }
}

export async function fetchProjectInfoByProjectId(projectId: string | undefined): Promise<{
  projectId?: string
  projectLabel?: string
}> {
  const fallbackResult = {
    projectId,
    projectLabel: undefined,
  }

  if (!projectId) {
    // console.error(`[fetchProjectInfoByProjectId] ProjectId not found: ${projectId}`)
    return fallbackResult
  }

  try {
    const project = await prisma.semanticProject.findUnique({
      where: { id: projectId },
      select: { id: true, name: true },
    })
    if (!project) {
      console.error(`[fetchProjectInfoByProjectId] Project not found: ${projectId}`)
      return fallbackResult
    }
    return {
      projectId: project.id,
      projectLabel: project.name,
    }
  } catch (error) {
    console.error('[fetchProjectInfoByProjectId] Unexpected error:', error)
    return fallbackResult
  }
}
