import { ConfigManage } from '@prisma/client'
import {
  ConfigType,
  TYPE_SEPARATOR,
  CONFIG_KEY_SEPARATOR,
  ServerConfigManagementMap,
  ConfigManagementMap,
} from '@shared/config-management-constants'
import { prisma } from 'src/server/dao/prisma-init'

export async function getConfigManagement<K extends ConfigType>(
  type: K,
  typeId: string,
): Promise<ConfigManagementMap[K]> {
  try {
    const prefix = `${type}${TYPE_SEPARATOR}${typeId}`
    const configResult: ConfigManage[] = await prisma.configManage.findMany({
      where: { configKey: { startsWith: prefix } },
    })

    // console.info(`[ConfigFetch] Type: ${type}, TypeId: ${typeId}, Keys: 'ALL'}`)
    // console.info(`[ConfigFetch] Result Count: ${configResult.length}`, configResult)

    return processConfigResult(type, configResult)
  } catch (error) {
    console.error(`[getConfigManagement] Failed to get configs for ${type}:${typeId}`, error)
    return {} as ConfigManagementMap[K]
  }
}

function processConfigResult<K extends ConfigType>(type: K, configResult: ConfigManage[]): ConfigManagementMap[K] {
  const configTypeMap = ServerConfigManagementMap[type as ConfigType]

  const rawMap = new Map<string, string>()
  for (const item of configResult) {
    const [, configName] = item.configKey.split(CONFIG_KEY_SEPARATOR)
    rawMap.set(configName, item.configValue)
  }

  const result = {} as any
  for (const [key, config] of Object.entries(configTypeMap)) {
    const rawValue = rawMap.get(key)
    result[key] = rawValue !== undefined ? config.parse(rawValue) : config.defaultValue
  }

  return result
}
