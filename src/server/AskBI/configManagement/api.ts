import express, { Router, Request, Response } from 'express'
import {
  CONFIG_KEY_SEPARATOR,
  ConfigType,
  ServerConfigManagementMap,
  TYPE_SEPARATOR,
} from '@shared/config-management-constants'
import { prisma } from 'src/server/dao/prisma-init'
import { getConfigManagement } from 'src/server/AskBI/configManagement/dao'

const router: Router = express.Router()

router.get('/list', async (req: Request, res: Response) => {
  try {
    const { type, typeId } = req.query
    if (typeof type !== 'string' || typeof typeId !== 'string') {
      return res.json({ code: 400, data: null, msg: 'type 或 typeId 参数缺失或格式不正确' })
    }
    const result = await getConfigManagement(type as ConfigType, typeId)
    return res.json({ code: 0, data: result, msg: '' })
  } catch (error) {
    console.error('[GET /list] Error:', error)
    return res.json({ code: 500, data: null, msg: '获取配置列表失败' })
  }
})

router.post('/upsert', async (req: Request, res: Response) => {
  try {
    const { type, typeId, configName, value } = req.body

    if (!type || !typeId || !configName || value === undefined) {
      return res.json({ code: 400, data: null, msg: '参数缺失' })
    }

    const configKey = `${type}${TYPE_SEPARATOR}${typeId}${CONFIG_KEY_SEPARATOR}${configName}`
    const configValue = value.toString()

    console.info('[POST /upsert]', { type, typeId, configName, value, configKey, configValue })

    const result = await prisma.configManage.upsert({
      where: { configKey },
      create: {
        configKey,
        configValue,
        description: `${configKey}#${configValue}`,
      },
      update: {
        configValue,
        description: `${configKey}#${configValue}`,
      },
    })

    return res.json({ code: 0, data: result, msg: '' })
  } catch (error) {
    console.error('[POST /upsert] Error:', error)
    return res.json({ code: 500, data: null, msg: '更新配置失败' })
  }
})

router.post('/updateByType', async (req: Request, res: Response) => {
  try {
    const { type, typeId, config } = req.body
    if (!type || !typeId || !config === undefined) {
      return res.json({ code: 400, data: null, msg: '参数缺失' })
    }
    console.info('[POST /updateByType]', { type, typeId, config })

    const configDeps = Object.entries(ServerConfigManagementMap[type as ConfigType]).map(([key, configFn]) => {
      return {
        configName: key,
        configValue: (configFn as any).stringify(config[key]),
      }
    })

    const deleteResult = await prisma.configManage.deleteMany({
      where: {
        configKey: {
          startsWith: `${type}${TYPE_SEPARATOR}${typeId}`,
        },
      },
    })

    const createData = configDeps.map(({ configName, configValue }) => {
      const configKey = `${type}${TYPE_SEPARATOR}${typeId}${CONFIG_KEY_SEPARATOR}${configName}`
      return {
        configKey,
        configValue,
        description: `${configKey}#${configValue}`,
      }
    })

    const createResult = await prisma.configManage.createMany({
      data: createData,
    })

    return res.json({ code: 0, data: { configDeps, createData, deleteResult, createResult }, msg: '' })
  } catch (error) {
    console.error('[POST /upsert] Error:', error)
    return res.json({ code: 500, data: null, msg: '更新配置失败' })
  }
})

router.delete('/', async (req: Request, res: Response) => {
  try {
    const { type, typeId, configName } = req.body

    if (!type || !typeId || !configName) {
      return res.json({ code: 400, data: null, msg: 'type、typeId 或 configName 参数缺失' })
    }

    const configKey = `${type}${TYPE_SEPARATOR}${typeId}${CONFIG_KEY_SEPARATOR}${configName}`

    const existing = await prisma.configManage.findUnique({ where: { configKey } })

    if (!existing) {
      return res.json({ code: 404, data: null, msg: '配置不存在' })
    }

    const deleted = await prisma.configManage.delete({ where: { configKey } })

    return res.json({ code: 0, data: deleted, msg: '删除成功' })
  } catch (error) {
    console.error('[POST /delete] Error:', error)
    return res.json({ code: 500, data: null, msg: '删除配置失败' })
  }
})

export default router
