/* eslint-disable no-extra-semi */
/* eslint-disable @typescript-eslint/no-extra-semi */
import chalk from 'chalk'
import dayjs from 'dayjs'
import axios from 'axios'
import { uniq, omit } from 'lodash'
import {
  ChartGroup,
  LlmType,
  AttrAnalysisResult,
  ChatResponse,
  ChatResponseQueryMetric,
  ChatResponseError,
  ChatResponseErrorTypes,
  AuthData,
} from '@shared/common-types'
import { askBIApiUrlsWithoutBaseUrl } from '@shared/url-map'
import { generateXengineUrl } from '@server/utils'
import { conditionsToWherePart, parseWhereConditions } from '@shared/match-utils'
import { QueryParams, ResultOfParamsExtract } from '@shared/metric-types'
import Metric2Sql from 'src/server/MetricStore/metric2sql/metric2sql'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { assertExhaustive, filterSensitiveWord, getTimeQuery } from 'src/shared/common-utils'
import { reportLogs } from 'src/server/winston-log'
import { convertTimeToSpecificMonth } from 'src/shared/time-utils'
import { isBaoWu, isBaoWuAmt, isBaoWuFinancial } from 'src/shared/baowu-share-utils'
import { processAmtSceneChartType } from 'src/server/custom/baowu/baowu-amt-scene'
import { addLimitToSql } from '../../dao/db'
import { parseParamsExtractResponse } from '../../ai/askbi-client'
import { getRecommendChartTypes } from '../charts/recommend-chart-types'
import { getBaoWuAuth, getBaoWuInfoTexts, getCompaniesFromOriginWhere } from '../../custom/baowu/baowu-utils'

export interface BuildChatMetricParams {
  ready: true
  resultOfParamsExtract: ResultOfParamsExtract
  message: string
  llmResponse: any
  conversationId: string
  chatId: string
  traceId: string
  sceneId: string
  projectId: string
  host: string
  username: string
  llmType: LlmType
  metricConfig: MetricConfig
}
import { handleTransformRows } from './utils'

/**
 * 处理参数 [用于单场景问答]
 * @param {Object} props - 函数参数
 * @param {string} props.conversationId - 会话ID
 * @param {string} props.chatId - 聊天ID
 * @param {string} props.sceneId - 场景ID
 * @param {string} props.projectId - 项目ID
 * @param {string} props.message - 消息
 * @param {LlmType} props.llmType - 大模型类型
 * @param {MetricConfig} props.metricConfig - 场景详情配置
 * @param {boolean} props.enableMetricExactMatch - 场景开启精确匹配 置信度功能
 * @param {boolean} props.paramsExactResponse - 场景提参结果
 * @returns {Promise<BuildChatMetricParams | ChatResponseError>} - 处理后的ChatMetricParams
 */
export async function buildChatMetricParams(props: {
  conversationId: string
  chatId: string
  projectId: string
  sceneId: string
  message: string
  llmType: LlmType
  metricConfig: MetricConfig
  enableMetricExactMatch: boolean
  host: string
  username: string
  traceId: string
  paramsExactResponse: any
  authData?: AuthData
}): Promise<BuildChatMetricParams | ChatResponseError> {
  const {
    conversationId,
    chatId,
    projectId,
    sceneId,
    message,
    llmType,
    metricConfig,
    enableMetricExactMatch,
    host,
    username,
    traceId,
    paramsExactResponse: response,
    authData,
  } = props
  console.info('Call chatMetrics authData->>>>>: ', authData)
  try {
    const startDate = Date.now()
    if (response.code === 0 && isBaoWu(metricConfig?.name) && response.data?.query_metric) {
      // 宝武的查询要进行数据权限处理, 修改where, 查询有权限的数据, 如果没有任何公司权限,就返回没数据的错误
      const queryMetric = response.data.query_metric
      const error = await getBaoWuAuth({
        queryParams: queryMetric,
        username,
        metricConfig,
        conversationId,
        sceneId,
      })
      if (error !== null) {
        return error
      }
    }

    console.info(chalk.bgCyanBright('chatMetrics 截止宝武权限处理结束耗时: ', Date.now() - startDate))

    const result: ResultOfParamsExtract = await parseParamsExtractResponse({
      resp: { ...response, message },
      config: { enableMetricExactMatch, metricConfig },
    })

    console.info(
      chalk.bgCyanBright('chatMetrics 截止 parseParamsExtractResponse 处理结束耗时: ', Date.now() - startDate),
    )

    return {
      ready: true,
      message,
      resultOfParamsExtract: result,
      llmResponse: response,
      conversationId,
      chatId,
      traceId,
      sceneId,
      projectId,
      host,
      username,
      llmType,
      metricConfig,
    }
  } catch (e) {
    console.error('chatMetrics 链路出现错误', e)
    let unreadyReason = '处理出错，请联系管理员排查'
    if (e instanceof Error) {
      unreadyReason += ' ' + e.message
    } else {
      unreadyReason += `: ${String(e)}`
    }

    return {
      ready: false,
      errType: ChatResponseErrorTypes.E_UNKNOWN,
      taskType: 'chat-error',
      conversationId,
      unreadyReason,
      sceneId,
      llmResponse: unreadyReason,
      projectId,
    }
  }
}

/**
 * 处理提取参数的结果 [不处理异常]
 * @param {Object} props - 函数参数
 * @param {ResultOfParamsExtract} props.resultOfParamsExtract - 大模型提取的参数结果
 * @param {MetricConfig} props.metricConfig - 指标配置
 * @param {string} props.message - 消息
 * @param {string} props.sceneId - 场景ID
 * @param {string} props.conversationId - 会话ID
 * @param {string} props.chatId - 聊天ID
 * @param {LlmType} props.llmType - 大模型类型
 * @returns {Promise<ChatResponse>} - 处理后的聊天响应 返回给前端
 */
export async function processExtractedParams(props: BuildChatMetricParams | ChatResponseError): Promise<ChatResponse> {
  if (!props.ready) return props
  const {
    resultOfParamsExtract,
    metricConfig,
    message,
    sceneId,
    projectId,
    conversationId,
    username,
    traceId,
    host,
    llmResponse,
  } = props
  const resultType = resultOfParamsExtract.type
  switch (resultType) {
    case 'query-metric': {
      const infoTexts = []
      const metric2Sql = new Metric2Sql(metricConfig)
      const queryParamsBuilder = await metric2Sql.createQueryParamsBuilder()
      const queryParams = await queryParamsBuilder.invoke({
        metricNames: resultOfParamsExtract.metricNames,
        groupBys: resultOfParamsExtract.groupBys,
        originGroupBys: resultOfParamsExtract.originGroupBys,
        where: resultOfParamsExtract.where,
        originWhere: resultOfParamsExtract.originWhere,
        orderBys: resultOfParamsExtract.orderBys,
        limit: resultOfParamsExtract.limit,
        timeQueryParams: metricConfig.timeDimensionDatum == null ? undefined : resultOfParamsExtract.timeQueryParams,
        isMetricNamesExactMatch: resultOfParamsExtract.isMetricNamesExactMatch,
        isWhereExactMatch: resultOfParamsExtract.isWhereExactMatch,
        extraInfo: resultOfParamsExtract.extraInfo,
        notExistMetricNames: resultOfParamsExtract.notExistMetricNames,
        notExistGroupBys: resultOfParamsExtract.notExistGroupBys,
        notExistOrderBys: resultOfParamsExtract.notExistOrderBys,
      })

      infoTexts.push(...resultOfParamsExtract.infoTexts)
      console.info('chatMetrics queryParams: ' + JSON.stringify(queryParams))
      const queryParamsWithPop = queryParams

      if (metricConfig.timeDimensionDatum == null && resultOfParamsExtract.timeQueryParams != null) {
        infoTexts.push('没有配置时间维度，当前返回的为最新的数据。')
      }

      if (message.includes('同比') || message.includes('环比') || message.includes('同环比')) {
        queryParamsWithPop.hasPop = true
        if (metricConfig.timeDimensionDatum == null) {
          // 如果没有时间维度，不支持同环比
          infoTexts.push('当前没有配置时间维度，无法自动计算同环比。请添加同环比指标或者配置时间维度。')
        } else {
          // 如果没有指定时间，默认上个月
          if (queryParamsWithPop.timeQueryParams == null) {
            queryParamsWithPop.timeQueryParams = {
              timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
              timeStartFunction: { type: 'recentMonths', months: 1 },
              timeEndFunction: { type: 'recentMonths', months: 1 },
              timeGranularity: 'month',
            }
          }
          if (queryParamsWithPop.timeQueryParams.timeGranularity === 'total') {
            // 但开启了同环比，没有分组的时候改为月分组
            queryParamsWithPop.timeQueryParams.timeGranularity = 'month'
          }
          if (queryParamsWithPop.timeQueryParams.timeGranularity !== 'month') {
            // 年、季度、日暂时不支持同环比
            infoTexts.push('目前只支持月份的同环比，暂不支持其他时间周期。')
          } else {
            if (message.includes('同环比')) {
              queryParamsWithPop.periodOverPeriods = [
                'momGrowth',
                'momGrowthRate',
                'yoyMonthGrowth',
                'yoyMonthGrowthRate',
              ]
            } else if (message.includes('环比增长额')) {
              queryParamsWithPop.periodOverPeriods = ['momGrowth']
            } else if (message.includes('环比增长率')) {
              queryParamsWithPop.periodOverPeriods = ['momGrowthRate']
            } else if (message.includes('环比')) {
              queryParamsWithPop.periodOverPeriods = ['momGrowth', 'momGrowthRate']
            } else if (message.includes('同比增长额')) {
              queryParamsWithPop.periodOverPeriods = ['yoyMonthGrowth']
            } else if (message.includes('同比增长率')) {
              queryParamsWithPop.periodOverPeriods = ['yoyMonthGrowthRate']
            } else if (message.includes('同比')) {
              queryParamsWithPop.periodOverPeriods = ['yoyMonthGrowth', 'yoyMonthGrowthRate']
            }
          }
        }
      }

      return await generateResponseOfQueryMetric({
        sceneId,
        projectId,
        metricConfig,
        queryParams: queryParamsWithPop,
        conversationId,
        message,
        infoTexts,
        username,
        traceId,
        host,
        needLog: true,
        confidenceOriginData: resultOfParamsExtract.confidenceOriginData,
      })
    }
    case 'metric-exact-match': {
      const infoTexts: string[] = []
      const metric2Sql = new Metric2Sql(metricConfig)

      const queryParamsBuilder = await metric2Sql.createQueryParamsBuilder()
      const queryParams = await queryParamsBuilder.invoke({
        metricNames: resultOfParamsExtract.metricNames,
        externalReportNames: resultOfParamsExtract.externalReportNames,
        groupBys: resultOfParamsExtract.groupBys,
        originGroupBys: resultOfParamsExtract.originGroupBys,
        where: resultOfParamsExtract.where,
        originWhere: resultOfParamsExtract.originWhere,
        orderBys: resultOfParamsExtract.orderBys,
        limit: resultOfParamsExtract.limit,
        timeQueryParams: metricConfig.timeDimensionDatum == null ? undefined : resultOfParamsExtract.timeQueryParams,
        isMetricNamesExactMatch: resultOfParamsExtract.isMetricNamesExactMatch,
        isWhereExactMatch: resultOfParamsExtract.isWhereExactMatch,
        extraInfo: resultOfParamsExtract.extraInfo,
        notExistMetricNames: resultOfParamsExtract.notExistMetricNames,
        notExistGroupBys: resultOfParamsExtract.notExistGroupBys,
        notExistOrderBys: resultOfParamsExtract.notExistOrderBys,
      })
      const verifiedQueryParamsBuilder = await metric2Sql.createVerifiedQueryParamsBuilder(queryParams)
      const verifiedMetricParams = await verifiedQueryParamsBuilder.invoke(queryParams)

      infoTexts.push(...resultOfParamsExtract.infoTexts)
      const confidenceOriginData = resultOfParamsExtract.confidenceOriginData
      const queryParamsVerified = confidenceOriginData
        ? {
            queryParams: {
              ...verifiedMetricParams.queryParams,
              where: confidenceOriginData.where,
              isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
            },
            originalQueryParams: {
              ...verifiedMetricParams.originalQueryParams,
              where: confidenceOriginData.originWhere,
              isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
            },
            extraParams: verifiedMetricParams.extraParams,
          }
        : verifiedMetricParams
      return {
        ready: true,
        conversationId: conversationId,
        sceneId: sceneId,
        taskType: 'metric-exact-match',
        metricNames: resultOfParamsExtract.metricNames,
        queryParamsVerified,
        confidenceOriginData,
        infoTexts,
        projectId,
      }
    }
    case 'query-external-report': {
      const confidenceOriginData =
        resultOfParamsExtract.externalReports.includes('BAOWU_FEIYONGZHICHU_REPORT') ||
        resultOfParamsExtract.externalReports.includes('BAOWU_ZICHANFUZHAILV_REPORT')
          ? resultOfParamsExtract.confidenceOriginData
          : undefined
      const timeQueryParams = resultOfParamsExtract.timeQueryParams
        ? resultOfParamsExtract.timeQueryParams
        : {
            timeStartFunction: { type: 'recentMonths', months: 1 } as const,
            timeEndFunction: { type: 'recentMonths', months: 1 } as const,
            timeGranularity: 'total' as const,
          }
      const queryParams = {
        metricNames: [],
        where: resultOfParamsExtract.where,
        isWhereExactMatch: false,
        isMetricNamesExactMatch: true,
        externalReportNames: resultOfParamsExtract.externalReports,
        timeQueryParams,
      }
      return {
        projectId,
        ready: true,
        conversationId: conversationId,
        sceneId: sceneId,
        taskType: resultOfParamsExtract.type,
        externalReports: resultOfParamsExtract.externalReports,
        where: resultOfParamsExtract.where,
        timeQueryParams,
        queryParamsVerified: {
          originalQueryParams: {
            ...queryParams,
            where: confidenceOriginData?.originWhere || resultOfParamsExtract.where,
          },
          queryParams,
          extraParams: {
            extraMetricNames: [],
            extraGroupBys: [],
            extraOrderBys: [],
          },
        },
        confidenceOriginData,
      }
    }
    case 'attribution-analysis': {
      // 如果 timeDimensionDatum 为空，那么就不支持归因分析
      if (metricConfig.timeDimensionDatum == null) {
        return {
          projectId,
          ready: false,
          errType: ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT,
          taskType: 'chat-error',
          sceneId,
          conversationId: conversationId,
          unreadyReason: '没有配置虚拟时间维度，无法进行归因分析',
        }
      }
      const sql = resultOfParamsExtract.metric_sql
      const attrAnalysisSql = `${sql.sqlBase ? '-- Base Metric SQL \n' + sql.sqlBase.trim() + ';' : ''}${sql.sqlCompare ? '-- Compare Metric SQL \n' + sql.sqlCompare.trim() + ';' : ''}`
      return {
        ready: true,
        projectId,
        conversationId: conversationId,
        sceneId: sceneId,
        sql: attrAnalysisSql,
        chartType: 'AttrAnalysis',
        taskType: 'attribution-analysis',
        chartTitle: '归因分析',
        rows: [resultOfParamsExtract as AttrAnalysisResult],
        isPartialRow: false,
        partialRowMsg: '',
      }
    }
    case 'attribution-metric-analysis': {
      return {
        ready: true,
        projectId,
        sceneId,
        conversationId,
        taskType: 'attribution-metric-analysis',
        chartTitle: '',
        chartType: 'AttrMetricAnalysis',
        rows: [resultOfParamsExtract],
        isPartialRow: false,
        partialRowMsg: '',
      }
    }
    case 'data-overview':
    case 'table-list':
    case 'dimension-list':
    case 'dimension-detail':
    case 'metric-list':
    case 'metric-detail':
    case 'metric-tree': {
      return {
        ready: true,
        projectId,
        conversationId: conversationId,
        sceneId,
        rows: resultOfParamsExtract.content,
        taskType: isBaoWu(metricConfig.name) ? 'data-overview' : resultType,
        isPartialRow: false,
        partialRowMsg: '',
      }
    }
    case 'chitchat': {
      return {
        ready: false,
        projectId,
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.CHIT_CHAT,
        sceneId,
        conversationId: conversationId,
        unreadyReason: resultOfParamsExtract.content,
      }
    }
    case 'llm-error': {
      return {
        ready: false,
        projectId,
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.LLM_ERROR,
        sceneId,
        conversationId: conversationId,
        unreadyReason: resultOfParamsExtract.content,
      }
    }
    case 'chat-error': {
      return {
        ready: false,
        projectId,
        taskType: 'chat-error',
        errType: resultOfParamsExtract.errType || ChatResponseErrorTypes.E_UNKNOWN,
        sceneId,
        conversationId: conversationId,
        unreadyReason: resultOfParamsExtract.content,
      }
    }
    case 'percentage': {
      return {
        ready: true,
        projectId,
        taskType: 'percentage',
        conversationId: conversationId,
        sceneId: sceneId,
        calculator: llmResponse.data.calculator,
      }
    }
    case 'period_on_period': {
      return {
        ready: true,
        projectId,
        taskType: 'period-on-period',
        conversationId: conversationId,
        sceneId: sceneId,
        calculator: llmResponse.data.calculator,
      }
    }
    default: {
      assertExhaustive(resultType, `Not supported type ${resultType}`)
    }
  }
}

/** 根据 queryParams 来做 metric2sql 和查询数据拼装结果返回 */
export const generateResponseOfQueryMetric = async ({
  sceneId,
  projectId,
  metricConfig,
  queryParams,
  conversationId,
  message,
  infoTexts,
  username,
  traceId,
  host,
  needLog,
  disableMoMYoY,
  confidenceOriginData,
}: {
  sceneId: string
  projectId: string
  metricConfig: MetricConfig
  queryParams: QueryParams
  conversationId: string
  message: string
  infoTexts: string[]
  username: string
  traceId: string
  host: string
  needLog?: boolean
  disableMoMYoY?: boolean
  confidenceOriginData?: {
    where: string
    isWhereExactMatch: boolean
    originWhere: string
  }
}): Promise<ChatResponseQueryMetric | ChatResponseError> => {
  console.info('Generate Response Of QueryMetric with queryParams.')
  const startTime = Date.now()
  const hasOrderBy = queryParams.orderBys?.length

  if (isBaoWu(metricConfig?.name) && queryParams) {
    // 宝武的查询要进行数据权限处理, 修改where, 查询有权限的数据, 如果没有任何公司权限,就返回没数据的错误
    const error = await getBaoWuAuth({
      queryParams,
      username,
      metricConfig,
      conversationId,
      sceneId,
    })
    if (error !== null) {
      return error
    }
  }

  if (
    isBaoWuFinancial(metricConfig.name) &&
    queryParams.metricNames
      .map((v) => metricConfig.allMetrics.find((item) => item.name === v))
      .every((v) => v?.type !== 'derived' && v?.type !== 'list') &&
    !hasOrderBy
  ) {
    if (queryParams.timeQueryParams != null) {
      const startMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeStartFunction)
      const endMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeEndFunction)
      const timeSpan = Math.abs(
        startMonthFunction.year * 12 + startMonthFunction.month - (endMonthFunction.year * 12 + endMonthFunction.month),
      )
      if (timeSpan === 0) {
        // 宝武场景如果时间跨度为0，支持同环比
        const arr = queryParams.periodOverPeriods ?? []
        queryParams.periodOverPeriods = uniq(arr.concat('momGrowth', 'yoyMonthGrowth'))
      }
    }
  }

  if (queryParams.metricNames.length === 0 && (queryParams.groupBys || []).length === 0) {
    return {
      projectId,
      sceneId,
      ready: false,
      taskType: 'chat-error',
      errType: ChatResponseErrorTypes.METRICS_NOT_EXIST,
      conversationId,
      unreadyReason: '找不到任何维度和指标，请换个问题',
      infoTexts,
    }
  }
  console.info('Starting Metric To SQL...')
  // TODO: 这里做个拆分，单独做 title、chartType 的推断
  const metric2Sql = new Metric2Sql(metricConfig)
  let { sql, rowsMetadata, verifiedQueryParams } = await metric2Sql.toSql({
    ...queryParams,
    periodOverPeriods: disableMoMYoY ? [] : queryParams?.periodOverPeriods,
  })
  sql = addLimitToSql(sql, 100) // 给 SQL 添加 limit，如果原来没有就添加 100

  const metric2SqlDone = Date.now()
  const { data: rows, columnName: sqlColumnName } = await executeAllXengineSql(sql, {
    username,
    traceId,
    host,
    conversationId,
    sceneId,
    projectId,
  })
  const sql2DataDone = Date.now()

  console.info(
    `使用 SQL 查询数据，一共 ${rows.length} 条数据。${rows.length >= 5 ? '前 5 条为' : '数据为'}`,
    rows.slice(0, 5),
  )

  console.info(
    '查询耗时--',
    '拼sql-metric2sqlTime:',
    metric2SqlDone - startTime,
    '查数据-sql2DataTime:',
    sql2DataDone - metric2SqlDone,
  )

  const queryParamsVerified = confidenceOriginData
    ? {
        queryParams: {
          ...verifiedQueryParams.queryParams,
          where: confidenceOriginData.where,
          isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
        },
        originalQueryParams: {
          ...verifiedQueryParams.originalQueryParams,
          where: confidenceOriginData.originWhere,
          isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
        },
        extraParams: verifiedQueryParams.extraParams,
      }
    : verifiedQueryParams

  const isPartialRow = false
  const partialRowMsg = ''
  if (rows.length === 0 || isRowsNoData(rows, verifiedQueryParams.queryParams.metricNames, metricConfig)) {
    if (verifiedQueryParams.queryParams.timeQueryParams?.timeEndFunction) {
      const originEndMonth = convertTimeToSpecificMonth(verifiedQueryParams.queryParams.timeQueryParams.timeEndFunction)
      const endMonthDay = dayjs()
        .year(originEndMonth.year)
        .month(originEndMonth.month - 1)
        .startOf('month')
      const lastMonth = dayjs().subtract(1, 'month').startOf('month')

      const whereCompany = isBaoWu(metricConfig.name)
        ? getCompaniesFromOriginWhere(verifiedQueryParams.queryParams.where)
        : undefined

      const { timeQueryParams } = verifiedQueryParams.queryParams
      let subUnreadyReason = ''
      let date = ''
      if (timeQueryParams) {
        date = getTimeQuery(timeQueryParams)
        subUnreadyReason = `当前查询时间：${date}`
      }
      if (endMonthDay.isSame(lastMonth, 'month')) {
        // 上月没数据
        return {
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: `${date ? date + '的' : ''}报表数据还没有出来哦，请等数据库更新后再尝试`,
          subUnreadyReason,
          sql,
          sqlColumnName,
          queryParamsVerified: queryParamsVerified,
          metricNames: queryParamsVerified.queryParams.metricNames,
          whereCompany,
          confidenceOriginData,
          infoTexts,
          projectId,
        }
      }
      if (endMonthDay.isAfter(lastMonth, 'month')) {
        // 本月及以后没数据
        return {
          projectId,
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: '数据尚未产生，请耐心等待...',
          subUnreadyReason,
          sql,
          sqlColumnName,
          queryParamsVerified: queryParamsVerified,
          metricNames: queryParamsVerified.queryParams.metricNames,
          whereCompany,
          confidenceOriginData,
          infoTexts,
        }
      } else {
        // 上月以前没数据
        return {
          projectId,
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: `该时间段下无数值`,
          subUnreadyReason,
          sql,
          sqlColumnName,
          queryParamsVerified: queryParamsVerified,
          metricNames: queryParamsVerified.queryParams.metricNames,
          whereCompany,
          confidenceOriginData,
          infoTexts,
        }
      }
    }
  }
  //  else {
  // 暂时不去判断展示是否个别公司没数据, 因为排名类问数没有基准的数量可供判断
  // const rowsLength = getBaoWuRowsCompanyLength(rows)
  // isPartialRow = await checkIsPartialRow({
  //   rowsLength,
  //   metricConfig,
  //   where: verifiedMetricParams.queryParams.where || '',
  //   limit: verifiedMetricParams.queryParams.limit,
  // })
  // if (isPartialRow) {
  //   const timeStr = getTimeQuery(verifiedMetricParams.queryParams.timeQueryParams)
  //   const metricName = getMetricNamesByMetricCodes(
  //     metricConfig.allMetrics,
  //     verifiedMetricParams.queryParams.metricNames,
  //   )
  //   partialRowMsg = `很抱歉，有个别子公司报表还未出来，现为您提供已有报表的部分子公司${timeStr}${metricName}。`
  // }
  // }

  // 图表推荐：获取推荐的图表类型数组，第一个为首选推荐的图表，其他的为可以切换的图表
  // 规则来判断类型：如果有 排名，排行 就使用
  let chartGroup: ChartGroup = 'Others'
  if (hasOrderBy) {
    chartGroup = 'Rank'
  }

  const recommendChartTypes = getRecommendChartTypes({
    chartGroup,
    rowsMetadata,
    rows,
    verifiedMetricParams: verifiedQueryParams,
  })

  let chartType = recommendChartTypes[0]

  if (isBaoWuAmt(metricConfig.name)) {
    const newChartType = processAmtSceneChartType(
      verifiedQueryParams.queryParams,
      recommendChartTypes,
      message.includes('趋势'),
    )
    if (newChartType) {
      chartType = newChartType
    }
  }

  if (isBaoWu(metricConfig.name)) {
    const baowuInfoTexts = getBaoWuInfoTexts({
      rows,
      queryParams: verifiedQueryParams.queryParams,
      metricConfig,
    })
    infoTexts.push(...baowuInfoTexts)
  }

  const transformedRows = handleTransformRows({ rows, metricConfig, verifiedMetricParams: verifiedQueryParams })

  // TODO: 把图标标题放到前端异步生成 @Sa Kong
  const chartTitle = '图表标题'

  /**
   * 获取底层物理表SQL
   * const physicalTableSQL = await executeUnwrapSql(datasource, sql)
   */

  needLog &&
    reportLogs({
      moduleType: 'METRIC_TO_SQL_TO_DATA',
      host,
      username,
      traceId,
      startTime,
      resultCode: 0,
      input: { queryParams: verifiedQueryParams.queryParams },
      output: { data: rows.slice(0, 5), rowsMetadata, recommendChartTypes, infoTexts, isPartialRow, partialRowMsg },
      semanticProjectId: projectId,
      semanticSceneId: sceneId,
      debug: {
        conversationId,
        sceneId,
        taskType: 'query-metric',
        queryParams: verifiedQueryParams.queryParams,
        sql,
        data: rows.slice(0, 5),
        duration: {
          metric2sqlTime: metric2SqlDone - startTime,
          sql2DataTime: sql2DataDone - metric2SqlDone,
          data2ResultTime: Date.now() - sql2DataDone,
        },
      },
    })

  return await afterResultsProcessing({
    projectId,
    ready: true,
    taskType: 'query-metric',
    rows: transformedRows,
    sqlColumnName,
    rowsMetadata: rowsMetadata,
    recommendChartTypes: recommendChartTypes,
    sql,
    sceneId: sceneId,
    chartType: chartType,
    chartTitle,
    infoTexts,
    conversationId,
    queryParamsVerified,
    isPartialRow,
    partialRowMsg,
    confidenceOriginData,
    metricConfig,
  })
}

/** 后置处理结果 */
async function afterResultsProcessing(
  result: ChatResponseQueryMetric & { metricConfig: MetricConfig },
): Promise<ChatResponseQueryMetric> {
  const { rows, rowsMetadata, metricConfig } = result
  // FIXME: REMOVE 宝武定制逻辑
  let newRows = rows
  let newRowsMetadata = rowsMetadata
  if (result.queryParamsVerified?.queryParams.metricNames.includes('OTOTAL_CASH_OUTFLOW')) {
    newRows = rows.map((row) => {
      const values = Object.entries(row)
        .filter(([key]) => key !== 'COMPANY_INNER_CODE_DES')
        .map(([key, value]) => ({ key, value: value !== null ? parseFloat(value) : null }))
      values.sort((a, b) => {
        if (a.value === null) return 1
        if (b.value === null) return -1
        return b.value - a.value
      })
      const topThreeValues = values.slice(0, 3)
      const newObject: any = {
        COMPANY_INNER_CODE_DES: row.COMPANY_INNER_CODE_DES,
      }
      topThreeValues.forEach((item) => {
        if (item.key) {
          newObject[item.key] = item.value
        }
      })
      return newObject
    })
    const rowsKeyMap = Object.keys(newRows[0])
    newRowsMetadata = rowsMetadata
      .map((item) => {
        if (rowsKeyMap.includes(item?.value?.name)) {
          return item
        }
        return null
      })
      .filter((item) => item !== null)
  }

  const maskRules = await axios
    .get(generateXengineUrl(askBIApiUrlsWithoutBaseUrl.xengine.dataMasking.getDataMasking), {
      params: {
        catalog: metricConfig.name.split('.')[0],
        database: metricConfig.name.split('.')[1],
        table: metricConfig.name.split('.')[2],
      },
      headers: { Authorization: 'init' },
    })
    .then((res) => res.data.data)
    .catch((error) => {
      console.error('获取脱敏规则失败:', error)
      return []
    })
  if (maskRules.length > 0) {
    const allMaskColumns: string[] = maskRules.flatMap((i: any) => i.columns)
    const where = result.queryParamsVerified.queryParams.where
    const whereConditions = parseWhereConditions(where)
    const untouchedConditions = []
    const touchedConditions = []

    for (const condition of whereConditions) {
      if (allMaskColumns.includes(condition.value.name)) {
        touchedConditions.push(condition)
      } else {
        untouchedConditions.push(condition)
      }
    }
    const maskExpr = touchedConditions.map((i) => `${i.value.name} = '数据已脱敏'`).join(' AND ')
    const untouchedWhere = conditionsToWherePart(untouchedConditions)
    const newWhere = untouchedWhere && maskExpr ? `${untouchedWhere} AND ${maskExpr}` : untouchedWhere || maskExpr
    result.queryParamsVerified.queryParams = {
      ...result.queryParamsVerified.queryParams,
      where: newWhere,
    }
    console.info(
      '[脱敏规则]',
      maskRules,
      '[原始WHERE]',
      where,
      '[命中脱敏SQL]',
      maskExpr,
      '[未命中WHERE]',
      untouchedWhere,
      '[最终WHERE]',
      newWhere,
    )
  }

  return {
    ...omit(result, ['metricConfig']),
    rows: filterSensitiveWord(newRows),
    rowsMetadata: filterSensitiveWord(newRowsMetadata),
  }
}

function isRowsNoData(
  rows: { [key: string]: string | number | null }[],
  metricNames: string[],
  metricConfig: MetricConfig,
) {
  if (metricNames.length === 0) {
    return false
  }

  const names = metricNames.flatMap((m) =>
    metricConfig.allMetrics
      .filter((metric) => metric.name === m)
      .flatMap((metric) => (metric.type === 'list' ? metric.typeParams.metrics.map((i) => i.name) : [m])),
  )

  // 存在,没数据,但是rows length不等于0, 数组中的指标对应的值是null的情况,所以需要判断下, 数据结构是{码值:xxx, 指标:xxx}, 所以得专门针对指标的值来判断
  // 如果item[key] 不存在,且item[key]!==0, 则认为是空数据
  return rows.every((item) => {
    return Object.keys(item).every((key) => {
      if (names.includes(key)) {
        return !item[key] && item[key] !== 0
      }
      return true
    })
  })
}
