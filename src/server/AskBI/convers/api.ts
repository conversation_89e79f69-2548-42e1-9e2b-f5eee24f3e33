import express, { Router, Request, Response } from 'express'
import dayjs from 'dayjs'
import { Prisma } from '@prisma/client'
import { prisma } from 'src/server/dao/prisma-init'
import { asyncResponseWrapper } from 'src/server/asyncResponseWrapper'
import { getConfigManagement } from 'src/server/AskBI/configManagement/dao'
import { ConverWithDataset, DatasetDatum } from 'src/shared/common-types'
import { validateUserConversationOwnership } from './dao'

const router: Router = express.Router()

/**
 * convers表使用is_draft列来判断是否展示历史记录
 * is_draft: true 属于草稿 不展示
 * is_draft: false 默认不属于草稿 展示
 */

/** 获取日期范围内的conversations 带有[数据库/表]中文名 */
const fetchConversations = async ({
  username,
  startDate,
  endDate,
  projectId,
  sceneId,
}: {
  username: string
  projectId: string
  sceneId: string
  startDate: Date
  endDate: Date
}): Promise<ConverWithDataset[]> => {
  const filter = {
    username,
    isDraft: false,
    createdAt: {
      gte: startDate,
      lt: endDate,
    },
  } as Prisma.ConverWhereInput
  if (projectId) {
    filter.semanticProjectId = projectId
  }
  if (sceneId) {
    filter.semanticSceneId = sceneId
  }
  const conversations = await prisma.conver.findMany({
    where: filter,
    include: {
      semanticScene: {
        include: {
          semanticProject: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  })
  const sceneConfig = await getConfigManagement('scene', sceneId)

  return Promise.all(
    conversations.map(async (conversation) => {
      const { semanticScene, ...conversationWithoutSemanticModel } = conversation
      // 多场景下查不到单独的semanticScene, 所以默认为传入的projectId
      const dataset: DatasetDatum = {
        projectId: semanticScene?.semanticProject.id || projectId || '',
        projectName: semanticScene?.semanticProject.name || '',
        sceneId: semanticScene?.id || sceneId || '',
        tableName: semanticScene?.tableName || '',
        sceneLabel: semanticScene?.label || '',
        ...sceneConfig,
      }

      return {
        ...conversationWithoutSemanticModel,
        dataset: dataset,
      }
    }),
  )
}

router.get('/list', async (req: Request, res: Response) => {
  const today = dayjs()
  const startOfToday = today.startOf('day')
  const endOfToday = today.endOf('day')
  const projectId = req.query.projectId as string
  const sceneId = req.query.sceneId as string
  const thirtyDaysAgo = today.subtract(30, 'days')
  const startOfThirtyDaysAgo = thirtyDaysAgo.startOf('day')

  const username = req.user?.username || ''
  try {
    const todayConversationListWithDataset = await fetchConversations({
      username: username,
      startDate: startOfToday.toDate(),
      endDate: endOfToday.toDate(),
      projectId,
      sceneId,
    })

    const thirtyDaysAgoConversationListWithDataset = await fetchConversations({
      username: username,
      startDate: startOfThirtyDaysAgo.toDate(),
      endDate: startOfToday.toDate(),
      projectId,
      sceneId,
    })

    return res.json({
      code: 0,
      data: { todayConversationListWithDataset, thirtyDaysAgoConversationListWithDataset },
      msg: '',
    })
  } catch (error: any) {
    return res.json({ code: 500, data: {}, msg: '获取会话历史列表失败，error:' + error?.message })
  }
})

router.get('/ask-history', async (req: Request, res: Response) => {
  const username = req.user?.username || ''

  console.info('get /conver_chat/history with params: ', req.query, username)
  const sceneId = req.query.sceneId as string | undefined
  const isProjectChosen = req.query.isProjectChosen as 'true' | 'false'
  const projectId = req.query.projectId as string | undefined

  const filter: { [key: string]: string | boolean | undefined } = { username: username, isDraft: false }
  if (isProjectChosen === 'true') {
    if (!projectId) {
      return res.json({ code: 400, data: {}, msg: 'Project ID is required !' })
    }

    filter.semanticProjectId = projectId
  } else {
    if (!sceneId) {
      return res.json({ code: 400, data: {}, msg: ' Scene ID is required !' })
    }
    filter.semanticSceneId = sceneId
  }

  try {
    const allAsks = await prisma.conver.findMany({
      select: { asks: true },
      where: filter,
      orderBy: { createdAt: 'desc' },
    })
    // 去重
    const askList = [...new Set(allAsks.map((e) => e.asks).flat())]
    return res.json({ code: 0, data: askList, msg: '' })
  } catch (error) {
    return res.json({ code: 500, data: {}, msg: 'Get ask history failed with error' + (error as Error).message })
  }
})

router.post(
  '/upsert-next',
  asyncResponseWrapper(async (req, res) => {
    console.info('POST /api/convers/upsert-next', JSON.stringify(req.body))
    const username = req.user!.username
    const { isNewConversation, requestData } = req.body
    const { conversationId } = requestData
    if (!conversationId) throw 'ConversationID不存在'
    const conversation = await prisma.conver.findUnique({ where: { id: conversationId } })
    if (conversation && !isNewConversation) {
      const updatedConversation = await prisma.conver.update({
        data: {
          text2SqlMessages: [...(conversation.text2SqlMessages as any[]), requestData.messages],
          updatedAt: new Date(),
        },
        where: { id: conversationId },
      })
      return res.json({ code: 0, data: updatedConversation, msg: 'Conversation updated successfully' })
    }
    if (!conversation && isNewConversation) {
      const newConversation = await prisma.conver.create({
        data: {
          id: conversationId,
          title: '新聊天_未命名',
          username,
          asks: [],
          version: 'v1',
          semanticProjectId: requestData.projectId,
          semanticSceneId: requestData.sceneId,
          text2SqlMessages: requestData.messages,
          llmType: 'IGNORE',
          isDraft: false,
        },
      })
      return res.json({ code: 0, data: newConversation, msg: 'Conversation created successfully' })
    }
    throw '当前工作簿已删除，请创建新工作簿后提问'
  }),
)

router.post('/upsert', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const { isNewConversation, conversationId, chatId, parentId, llmType, message, sceneId, projectId, traceId } =
    req.body

  if (!conversationId) {
    return res.json({ code: 400, data: {}, msg: 'Conversation ID is required!' })
  }
  try {
    const conversation = await prisma.conver.findUnique({
      where: { id: conversationId },
    })
    if (conversation && !isNewConversation) {
      const updatedConversation = await prisma.conver.update({
        data: {
          asks: [...(conversation.asks as string[]), message],
          text2SqlMessages: [...(conversation.text2SqlMessages as any[]), { role: 'user', content: message }],
          updatedAt: new Date(),
        },
        where: { id: conversationId },
      })
      const currentChat = await prisma.converChat.findUnique({
        where: { id: chatId },
      })
      if (currentChat == null) {
        await prisma.converChat.create({
          data: {
            id: chatId,
            converId: conversationId,
            parentId,
            ask: message,
            response: [],
            llmResponse: [],
            docResponse: [],
          },
        })
      }
      return res.json({ code: 0, data: updatedConversation, msg: 'Conversation updated successfully' })
    }
    if (!conversation && isNewConversation) {
      console.info('===> newConversation', {
        data: {
          id: conversationId,
          title: '新聊天_未命名',
          username,
          asks: [message],
          version: 'v1',
          semanticProjectId: projectId,
          semanticSceneId: sceneId,
          text2SqlMessages: [{ role: 'user', content: message }],
          llmType,
          isDraft: false,
        },
      })
      const newConversation = await prisma.conver.create({
        data: {
          id: conversationId,
          title: '新聊天_未命名',
          username,
          asks: [message],
          version: 'v1',
          semanticProjectId: projectId,
          semanticSceneId: sceneId,
          text2SqlMessages: [{ role: 'user', content: message }],
          llmType: 'IGNORE',
          isDraft: false,
        },
      })

      const currentChat = await prisma.converChat.findUnique({
        where: { id: chatId },
      })
      if (currentChat == null) {
        await prisma.converChat.create({
          data: {
            id: chatId,
            converId: conversationId,
            parentId,
            ask: message,
            response: [],
            llmResponse: [],
            docResponse: [],
            traceId,
          },
        })
      }
      return res.json({ code: 0, data: newConversation, msg: 'Conversation created successfully' })
    }
    return res.json({ code: 400, data: {}, msg: '当前工作簿已删除，请创建新工作簿后提问' })
  } catch (error: any) {
    return res.json({ code: 500, data: {}, msg: 'Error updating or creating conversation: ' + error.message })
  }
})

/**
 * 删除会话
 */
router.delete('/:id', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const conversationId = req.params.id
  if (conversationId == null) {
    return res.json({ code: 404, msg: '缺少 conversationId，无法删除此会话' })
  }
  const verifyResult = await validateUserConversationOwnership(username, conversationId)
  if (!verifyResult) {
    return res.json({ code: 403, data: [], msg: '暂无删除此会话的权限！' })
  }
  try {
    console.info(`Delete ${conversationId} conversation...`, conversationId, username)
    const conversation = await prisma.conver.delete({
      where: {
        id: conversationId,
        username,
      },
    })

    if (conversation == null) {
      return res.json({ code: 404, msg: `找不到conversation${conversationId}, 删除失败!` })
    }

    return res.json({ code: 0, data: { id: conversationId } })
  } catch (error) {
    console.error('Delete conversation with error :', error)
    return res.json({ code: 500, data: {}, msg: '删除会话失败，请联系管理员处理' })
  }
})

/**
 * 更新会话
 */
router.put('/:id', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const conversationId = req.params.id
  const updatePayload = req.body

  if (conversationId == null) {
    return res.json({ code: 404, msg: '缺少 conversationId，无法更新此会话' })
  }
  const verifyResult = await validateUserConversationOwnership(username, conversationId)
  if (!verifyResult) {
    return res.json({ code: 403, data: [], msg: '暂无修改此会话的权限！' })
  }
  try {
    console.info(`Update ${conversationId} conversation...`, conversationId, username)
    const conversation = await prisma.conver.update({
      where: {
        id: conversationId,
        username,
      },
      data: { ...updatePayload, updatedAt: new Date() },
    })

    if (conversation == null) {
      return res.json({ code: 404, msg: `找不到conversation${conversationId}, 更新失败!` })
    }

    return res.json({ code: 0, data: { id: conversationId } })
  } catch (error) {
    console.error('Update conversation with error :', error)
    return res.json({ code: 500, data: {}, msg: '更新会话失败，请联系管理员处理' })
  }
})

/**
 * 更新一个用户下面所有的conversations
 */
router.put('/update/all-conversations/:sceneId', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const updatePayload = req.body
  const sceneId = req.params.sceneId

  console.info('Update all conversations.', username, updatePayload, sceneId)

  try {
    const conversations = await prisma.conver.updateMany({
      where: {
        username,
        semanticSceneId: sceneId,
      },
      data: { ...updatePayload, updatedAt: new Date() },
    })
    return res.json({ code: 0, data: conversations })
  } catch (error) {
    console.error('Update conversations with error :', error)
    return res.json({ code: 500, data: {}, msg: '更新会话失败，请联系管理员处理' })
  }
})

// 获取用户列表
router.get('/users', async function getUserList(req: Request, res: Response) {
  try {
    const { projectId, sceneId, isProjectChosen } = req.query as {
      projectId: string
      sceneId?: string
      isProjectChosen: string
    }

    if (!projectId) {
      return res.json({
        message: '项目ID不能为空',
        code: 400,
        data: [],
      })
    }

    // 构建查询条件
    const whereCondition: any = {
      semanticProjectId: projectId,
    }

    // 根据 isProjectChosen 参数决定是否过滤场景
    if (isProjectChosen === 'false' && sceneId) {
      whereCondition.semanticSceneId = sceneId
    }

    // 查询所有符合条件的对话记录，只选择 username 字段
    const usernames = await prisma.conver.findMany({
      where: whereCondition,
      select: {
        username: true,
      },
      distinct: ['username'], // 使用 Prisma 的 distinct 功能去重
    })

    // 提取用户名列表
    const users = usernames.map((item) => item.username).filter(Boolean)

    res.json({
      data: users,
      code: 0,
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    res.json({
      code: 500,
      message: '获取用户列表失败',
      data: [],
    })
  }
})

export default router
