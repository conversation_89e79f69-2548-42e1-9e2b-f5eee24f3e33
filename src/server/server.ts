/**
 * @description server entry
 * Setup dotenv at the beginning, so that you can use it outside of function
 */
import http from 'http'
import moduleAlias from 'module-alias'

// 注册别名，这里只需要考虑 server 使用的别名，完整的参考 vite.config.ts
moduleAlias.addAliases({
  src: __dirname + '/../',
  '@shared': __dirname + '/../shared',
  '@server/utils': __dirname + '/../server/utils',
})

import express, { Express } from 'express'
import { PROCESS_ENV } from 'src/server/server-constants'
import { askBIPageUrls } from 'src/shared/url-map'
import '../shared/config'
import router from './express-app'
import { validateDBConnection } from './dao/db'
import semanticModelBuilder from './MetricStore/buildSematicModel/chain'
import { enforcer } from './auth'
const port = PROCESS_ENV.PORT

// 在外部包裹一层express，可以在内部逻辑基本不需要修改的情况下，快速增加BASE_URL
const app: Express = express()
// 需要给服务增加websocket接口, 所以需要改为通过http.create(app)启动服务
const server = http.createServer(app)

// 创建websocket服务

app.use(PROCESS_ENV.BASE_URL, router)

app.use('/*', (_, res) => {
  return res.redirect(askBIPageUrls.login)
})

server.listen(port, '0.0.0.0', () => {
  validateDBConnection()
    .then(async () => {
      await enforcer.init()
      semanticModelBuilder.start()
    })
    .then(async () => {
      console.info(`✅[ask-bi]: Server is running at http://0.0.0.0:${port}`)
    })
    .catch((e: Error) => {
      console.info('❌[ask-bi]: Server start failed.', e.message)
      console.error(e)
      process.exit(1)
    })
})
