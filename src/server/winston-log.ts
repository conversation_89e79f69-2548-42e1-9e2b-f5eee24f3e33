/* eslint-disable @typescript-eslint/naming-convention */
import * as tapable from 'tapable'
import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'
import { ReportModuleType, ReportLogResponse } from 'src/shared/common-types'
import { PluginClass, IHookable, Hookable } from 'src/shared/types'
import { fetchProjectInfoByProjectId, fetchProjectInfoBySceneId } from 'src/server/AskBI/datasets/dao'
import { PROCESS_ENV } from './server-constants'

const { combine, timestamp } = winston.format
const defaultOptions = {
  datePattern: 'YYYY-MM-DD',
  zippedArchive: false, // 禁用压缩归档。
  maxSize: '500m',
  maxFiles: '30d', // 表示保留最近 30 天的日志文件，自动删除更早的日志文件。
}

export interface ReportLogParams {
  moduleType: ReportModuleType
  host: string
  username: string
  traceId: string
  startTime: number
  resultCode: number
  input: Record<string, any>
  output: Record<string, any> | string
  debug: Record<string, any>
  semanticProjectId?: string
  semanticSceneId?: string
}

export class Reporter extends Hookable implements IHookable<Reporter> {
  hooks = Object.freeze({
    beforeReport: new tapable.AsyncParallelHook<[ReportLogParams]>(['params']),
    onGenParams: new tapable.AsyncSeriesWaterfallHook<[ReportLogParams]>(['params']),
    afterReport: new tapable.AsyncSeriesWaterfallHook<[ReportLogParams, ReportLogResponse]>([
      'params',
      'ReportLogResponse',
    ]),
    onGenReportLogResponse: new tapable.AsyncSeriesWaterfallHook<[ReportLogResponse, ReportLogParams]>([
      'ReportLogResponse',
      'params',
    ]),
    onReport: new tapable.AsyncParallelHook<[ReportLogResponse]>(['ReportLogResponse']),
  })

  constructor() {
    super()
    this.hooks.onGenReportLogResponse.tapPromise('AttachSceneProjectInfo', async (res, params) => {
      if (params.semanticSceneId) {
        const sceneInfo = await fetchProjectInfoBySceneId(params.semanticSceneId)
        res.semantic_scene_id = sceneInfo.sceneId
        res.semantic_scene_name = sceneInfo.sceneLabel
      }
      if (params.semanticProjectId) {
        const projectInfo = await fetchProjectInfoByProjectId(params.semanticProjectId)
        res.semantic_project_id = projectInfo.projectId
        res.semantic_project_name = projectInfo.projectLabel
      }
      return res
    })
  }

  async report(params: ReportLogParams) {
    await this.hooks.beforeReport.promise(params)
    params = await this.hooks.onGenParams.promise(params)
    const { startTime, username, traceId, host, resultCode, moduleType, input, output, debug } = params
    const ReportLogResponse: ReportLogResponse = await this.hooks.onGenReportLogResponse.promise(
      {
        timestamp: new Date().toISOString(),
        user_id: username,
        request_id: traceId,
        host,
        service_type: 'web_service',
        start_time: new Date(startTime).toISOString(),
        end_time: new Date(Date.now()).toISOString(),
        duration: Date.now() - startTime,
        result_code: resultCode,
        module_type: moduleType,
        input,
        output,
        debug,
        url: debug?.url ?? 'empty_need_to_handle',
        cluster_id: PROCESS_ENV.CLUSTER_ID,
      },
      params,
    )
    await this.hooks.onReport.promise(ReportLogResponse)
    await this.hooks.afterReport.promise(params, ReportLogResponse)
  }
}

export class NodeLogger implements PluginClass<Reporter> {
  logger = winston.createLogger({
    format: combine(timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.json()),
    defaultMeta: { service: 'user-service' },
    transports: [
      new DailyRotateFile({
        filename: 'logs/%DATE%.log',
        level: 'info',
        ...defaultOptions,
      }),
    ],
  })
  apply(reporter: Reporter) {
    reporter.hooks.onReport.tap(NodeLogger.name, (elk) => {
      this.logger.info(elk)
    })
  }
}

export class PrometheusLogger implements PluginClass<Reporter> {
  apply(reporter: Reporter) {
    reporter.hooks.onReport.tap(PrometheusLogger.name, (_elk) => {
      // guohao 监听到
    })
  }
}

export const reporter = new Reporter().use(new NodeLogger()).use(new PrometheusLogger())

export async function reportLogs(params: ReportLogParams) {
  return reporter.report(params)
}
