import { join } from 'path'
import * as dotenv from 'dotenv'

// 只在测试环境中加载 .env.test 文件
if (process.env.NODE_ENV === 'test') {
  dotenv.config({ path: join(__dirname, '../../.env.test'), override: true })
} else {
  dotenv.config({
    // 有些环境会自动加载.env文件，比如启动pyenv的时候，vscode的终端会加载.env
    // 此时如果没有override，就会不会覆盖process.env上的变量，导致变量缓存
    // 增加override后，强制每次都读取.env文件上的变量对process.env做覆盖
    override: true,
  })
}

import { EnvError, cleanEnv, makeValidator, port, str, bool, num } from 'envalid'

const strNoEmpty = makeValidator<string>((input: string) => {
  if (typeof input === 'string' && input.length > 0) return input
  throw new EnvError(`Not a string: "${input}"`)
})
const onlyXEngine = process.env.VITE_PRODUCTS === 'X-Engine'
// 为下面校验提供默认值， 如果是只有 xengine 的环境，就返回默认值，否则返回 undefined 走校验逻辑
const getDefaultValueBasedOnWhetherOnlyXEngine = (defaultValue?: string) =>
  onlyXEngine ? { default: defaultValue ?? '' } : undefined

export const PROCESS_ENV = cleanEnv(process.env, {
  PORT: port(),
  DATABASE_URL: strNoEmpty(),
  METRIC_TO_SQL_DEFAULT_VERSION: str({ default: '2024' }),
  AUTH_LOGIN_HOST: str({ default: '' }),
  XENGINE_PROXY_URL: str({ default: '' }),
  VITE_XENGINE_ORIGIN: str({ default: '' }),
  VITE_OPEN_META_DATA_URL: str({ default: '' }),
  VITE_REMOVE_ROUTER_KEYS: str({ default: '' }),
  RANGER_LOGIN_ENABLE: bool({ default: false }),
  AUTH_LOGIN_SYSTEM: str({ default: '' }),
  // 拉取ELK日志的URL
  ELK_LOG_URL: str({ default: '' }),
  ELK_LOG_SIZE: num({ default: 10 }),

  PARAMS_EXTRACT_URL_HOST: strNoEmpty(getDefaultValueBasedOnWhetherOnlyXEngine()),
  DEFAULT_LLM_MODEL: strNoEmpty(getDefaultValueBasedOnWhetherOnlyXEngine()),
  ENABLED_LLM_MODELS: str({ default: '' }),
  VITE_XENGINE_KEEP_ROUTER_KEYS: str({ default: '' }),
  DEFAULT_SELECT_PROJECT: bool({ default: false }),
  VITE_PRODUCTS: str({ default: 'X-Engine,Ask-BI' }),
  BASE_URL: str({ default: '' }),
  ASKDOC_API_FILE_URL: strNoEmpty(getDefaultValueBasedOnWhetherOnlyXEngine()),
  ASKDOC_FILE_PREVIEW_URL: strNoEmpty(getDefaultValueBasedOnWhetherOnlyXEngine()),
  RATE_LIMIT_GLOBAL: str({ default: '100/1' }),
  // 分级限速配置，后面放到DB里面管理
  LEVEL1_USER_RATE_LIMIT: str({ default: 'bbwsy1,bbwsy2,bbwsy3@@5/10' }),
  LEVEL2_USER_RATE_LIMIT: str({ default: 'userX@@10/10' }),
  LEVEL3_USER_RATE_LIMIT: str({ default: 'bbwys,dipeak@@20/10' }),
  // 拉取 Xengine Backend 数据建模周期, 单位秒
  SEMANTIC_MODEL_BUILD_CYCLE: num({ default: 480 }),
  DISABLE_SEMANTIC_MODEL_BUILD: bool({ default: false }),
  //关闭 sql 生成 bypass（用于优化纯原子指标查询SQL优化）
  DISABLE_SQL_GENERATE_BYPASS: bool({ default: true }),
  CLUSTER_ID: str({ default: '' }),
  ASK_BI_HOST: str({ default: '' }),
  BI_AES_KEY: str({ default: '' }),
  BI_AES_IV: str({ default: 'PtORdzqOOd5H1LU1' }),
  CHECK_CONTAINERS: str({ default: 'http://*************:10019' }),
  PRIVATE_KEY: str({ default: '' }),
  ALIPAY_PUBLIC_KEY: str({ default: '' }),
  VITE_CUSTOMER: str({ default: '' }),
  CLUSTER_PROXY_URL: str({ default: '' }),
  CONSOLE_LOG: str({ default: '' }),
  VITE_AUTH_SPDB_LOGIN: str({ default: '' }),
  DEFAULT_SCENE: str({ default: '' }),
  DEFAULT_PROJECT: str({ default: '' }),
  DEEPSEEK_MODEL_TYPE: str({ default: 'deepseek-14b' }),
  CHINA_TELECOM_MARKING: str({ default: '' }),
  CHINA_TELECOM_MARKING_DOWNLOAD_URL: str({ default: '' }),
  CHINA_TELECOM_EXAMPLE_CSV_URL: str({ default: '' }),
})

/** 记录系统的启动时间 */
export const ServerStartTime = new Date()
export const rangerLoginUrl = PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/login'
export const rangerGetAuthToken = PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/getToken'
export const registerUserUrl = PROCESS_ENV.AUTH_LOGIN_HOST + '/service/xusers/secure/users'
export const getAccessResourceUrl = PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/service/'
export const logoutUrl = PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/logout'
export const checkToken = PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/checkToken'
export const scenesListUrl = PROCESS_ENV.PARAMS_EXTRACT_URL_HOST + '/api/v1/retrieve_multi_scene'
export const generateChartTitleUrl = PROCESS_ENV.PARAMS_EXTRACT_URL_HOST + '/api/v1/generate_chart_title'
