import * as tapable from 'tapable'
import { Hookable, ChatRequest, ChatResponse } from 'src/shared/types'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { MetricQueryParamsExtractor, ExtractedParams } from './params-extractor'
import { MetricQueryMetricParamsBuilder, MetricParams } from './metric-params-builder'
import { MetricQueryTaskRunner } from './task-runner'

export interface MetricQueryConfig {}

export class MetricQuery extends Hookable {
  hooks = Object.freeze({
    beforeInvoke: new tapable.AsyncParallelHook<[ChatRequest]>(['chatRequest']),
    afterInvoke: new tapable.AsyncParallelHook<[ChatResponse]>(['chatResponse']),

    onGenChatRequest: new tapable.AsyncSeriesWaterfallHook<[ChatRequest]>(['chatRequest']),
    afterGenChatRequest: new tapable.AsyncParallelHook<[ChatRequest]>(['chatRequest']),

    onCreateParamsExtractor: new tapable.AsyncSeriesWaterfallHook<[MetricQueryParamsExtractor, ChatRequest]>([
      'paramsExtract',
      'chatRequest',
    ]),
    afterCreateParamsExtractor: new tapable.AsyncParallelHook<[MetricQueryParamsExtractor]>(['paramsExtract']),

    onCreateMetricParamsBuilder: new tapable.AsyncSeriesWaterfallHook<
      [MetricQueryMetricParamsBuilder, ExtractedParams]
    >(['metricQueryMetricParamsBuilder', 'extractedParams']),
    afterCreateMetricParamsBuilder: new tapable.AsyncParallelHook<[MetricQueryMetricParamsBuilder]>([
      'metricQueryMetricParamsBuilder',
    ]),

    onCreateTaskRunner: new tapable.AsyncSeriesWaterfallHook<[MetricQueryTaskRunner, MetricParams]>([
      'metricQueryTaskRunner',
      'buildChatMetricParams',
    ]),
    afterCreateTaskRunner: new tapable.AsyncParallelHook<[MetricQueryTaskRunner]>(['metricQueryTaskRunner']),

    onCreateMetricConfig: new tapable.AsyncSeriesWaterfallHook<[MetricConfig, string]>(['metricConfig', 'sceneId']),
    afterCreateMetricConfig: new tapable.AsyncParallelHook<[MetricConfig]>(['metricConfig']),
  })

  constructor(public config: MetricQueryConfig) {
    super()
  }

  async createParamsExtractor(chatRequest: ChatRequest) {
    const paramsExtractor = await this.hooks.onCreateParamsExtractor.promise(
      new MetricQueryParamsExtractor(),
      chatRequest,
    )
    await this.hooks.afterCreateParamsExtractor.promise(paramsExtractor)
    return paramsExtractor
  }

  async createMetricParamsBuilder(extractedParams: ExtractedParams) {
    const metricParamsBuilder = await this.hooks.onCreateMetricParamsBuilder.promise(
      new MetricQueryMetricParamsBuilder(),
      extractedParams,
    )
    await this.hooks.afterCreateMetricParamsBuilder.promise(metricParamsBuilder)
    return metricParamsBuilder
  }

  async createTaskRunner(buildChatMetricParams: MetricParams) {
    const taskRunner = await this.hooks.onCreateTaskRunner.promise(new MetricQueryTaskRunner(), buildChatMetricParams)
    await this.hooks.afterCreateTaskRunner.promise(taskRunner)
    return taskRunner
  }

  async createMetricConfig(sceneId: string) {
    const metricConfig = await this.hooks.onCreateMetricConfig.promise(
      await MetricConfig.createBySceneId(sceneId),
      sceneId,
    )
    await this.hooks.afterCreateMetricConfig.promise(metricConfig)
    return metricConfig
  }

  async invoke({ chatRequest, sceneId }: { sceneId: string; chatRequest: ChatRequest }) {
    let extractedParams!: ExtractedParams
    let metricParams!: MetricParams
    let chatResponse!: ChatResponse
    let metricConfig!: MetricConfig
    try {
      metricConfig = await this.createMetricConfig(sceneId)
      await this.hooks.beforeInvoke.promise(chatRequest)

      chatRequest = await this.hooks.onGenChatRequest.promise(chatRequest)
      await this.hooks.afterGenChatRequest.promise(chatRequest)

      const paramsExtractor = await this.createParamsExtractor(chatRequest)
      extractedParams = await paramsExtractor.invoke(chatRequest)

      const metricParamsBuilder = await this.createMetricParamsBuilder(extractedParams)
      metricParams = await metricParamsBuilder.invoke(extractedParams)

      const taskRunner = await this.createTaskRunner(metricParams)
      chatResponse = await taskRunner.invoke(metricParams)

      await this.hooks.afterInvoke.promise(chatResponse)
      return chatResponse
    } catch (err) {
      console.info(extractedParams, metricParams, chatResponse, metricConfig)
      return {}
    }
  }
}
