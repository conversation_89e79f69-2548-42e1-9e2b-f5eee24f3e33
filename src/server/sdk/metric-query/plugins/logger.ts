/* eslint-disable @typescript-eslint/naming-convention */
import winston from 'winston'
import { PluginClass } from 'src/shared/types'
import { MetricQuery } from '../metric-query'

export class LoggerPlugin implements PluginClass<MetricQuery> {
  logger = winston.createLogger({
    format: winston.format.combine(winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.json()),
    transports: [],
  })

  apply(instance: MetricQuery): void {
    instance.hooks.beforeInvoke.tapPromise(LoggerPlugin.name, async (chatRequest) => {
      this.logger.info('beforeInvoke: ' + JSON.stringify(chatRequest))
    })
  }
}
