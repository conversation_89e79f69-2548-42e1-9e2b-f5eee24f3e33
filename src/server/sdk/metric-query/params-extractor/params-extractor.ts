import * as tapable from 'tapable'
import { Hookable, ChatRequest } from 'src/shared/types'

export type ExtractedParams = any

export class MetricQueryParamsExtractor extends Hookable {
  hooks = Object.freeze({
    onParamsExtract: new tapable.AsyncSeriesBailHook<[ChatRequest], ExtractedParams | undefined>(['chatRequest']),
    afterParamsExtract: new tapable.AsyncParallelHook<[ExtractedParams]>(['queryParams']),

    onGenExtractedParams: new tapable.AsyncSeriesWaterfallHook<[ExtractedParams]>(['queryParams']),
    afterGenExtractedParams: new tapable.AsyncSeriesWaterfallHook<[ExtractedParams]>(['queryParams']),
  })

  async invoke(chatRequest: ChatRequest) {
    let queryParam: ExtractedParams
    queryParam = (await this.hooks.onParamsExtract.promise(chatRequest)) as ExtractedParams
    await this.hooks.afterParamsExtract.promise(queryParam)

    queryParam = await this.hooks.onGenExtractedParams.promise(queryParam)
    await this.hooks.afterGenExtractedParams.promise(queryParam)

    return queryParam
  }
}
