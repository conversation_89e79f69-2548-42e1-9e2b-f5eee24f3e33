import { PluginClass } from 'src/shared/types'
import { MetricQueryParamsExtractor } from '../params-extractor'

// 从Chat接口中获取到提参返回值后直接返回
export class ExtractBailParamsPlugin implements PluginClass<MetricQueryParamsExtractor> {
  apply(instance: MetricQueryParamsExtractor): void {
    instance.hooks.onParamsExtract.tapPromise(ExtractBailParamsPlugin.name, async (chatRequest) => {
      if (chatRequest.extractedParams) {
        return chatRequest.extractedParams
      }
    })
  }
}
