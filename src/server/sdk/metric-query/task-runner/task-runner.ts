import * as tapable from 'tapable'
import { ChatResponse } from 'src/shared/common-types'
import { Hookable } from 'src/shared/types'
import { MetricParams } from '../metric-params-builder'
import { QueryMetricRunner, MetricListRunner } from './plugins'

export class MetricQueryTaskRunner extends Hookable {
  hooks = Object.freeze({
    onTaskRun: new tapable.AsyncSeriesBailHook<[MetricParams], ChatResponse | undefined>(['chatRequest']),
    afterTaskRun: new tapable.AsyncParallelHook<[ChatResponse]>(['chatResponse']),

    onGenChatResponse: new tapable.AsyncSeriesWaterfallHook<[ChatResponse]>(['chatResponse']),
    afterGenChatResponse: new tapable.AsyncSeriesWaterfallHook<[ChatResponse]>(['chatResponse']),
  })

  constructor() {
    super()
    this.use(new QueryMetricRunner())
    this.use(new MetricListRunner())
  }

  async invoke(metricParams: MetricParams) {
    let res: ChatResponse
    res = (await this.hooks.onTaskRun.promise(metricParams)) as ChatResponse
    await this.hooks.afterTaskRun.promise(res)

    res = await this.hooks.onGenChatResponse.promise(res)
    await this.hooks.afterGenChatResponse.promise(res)

    return res
  }
}
