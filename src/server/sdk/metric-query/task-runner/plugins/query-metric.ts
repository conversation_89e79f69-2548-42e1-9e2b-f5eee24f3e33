import { ChatResponse, PluginClass } from 'src/shared/types'
import { MetricQueryTaskRunner } from '../task-runner'

export class QueryMetricRunner implements PluginClass<MetricQueryTaskRunner> {
  apply(instance: MetricQueryTaskRunner): void {
    instance.hooks.onTaskRun.tapPromise(QueryMetricRunner.name, async (params) => {
      if (params.resultOfParamsExtract.type !== 'query-metric') return undefined as any as ChatResponse
      // a
      // b
      // c
      return {} as ChatResponse
    })
  }
}
