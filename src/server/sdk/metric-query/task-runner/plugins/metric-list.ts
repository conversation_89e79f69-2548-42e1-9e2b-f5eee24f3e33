import { ChatResponse, PluginClass } from 'src/shared/types'
import { MetricQueryTaskRunner } from '../task-runner'

export class MetricListRunner implements PluginClass<MetricQueryTaskRunner> {
  apply(instance: MetricQueryTaskRunner): void {
    instance.hooks.onTaskRun.tapPromise(MetricListRunner.name, async (params) => {
      if (params.resultOfParamsExtract.type !== 'metric-list') return
      // a
      // b
      // c
      return {} as ChatResponse
    })
  }
}
