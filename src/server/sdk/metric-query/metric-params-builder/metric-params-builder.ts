import * as tapable from 'tapable'
import { Hookable } from 'src/shared/types'
import { BuildChatMetricParams } from 'src/server/AskBI/chats/chat-pipelines'
import { ExtractedParams } from '../params-extractor'

export type MetricParams = BuildChatMetricParams

export class MetricQueryMetricParamsBuilder extends Hookable {
  hooks = Object.freeze({
    onMetricParamsBuild: new tapable.AsyncSeriesBailHook<[ExtractedParams], MetricParams | undefined>(['chatRequest']),
    afterMetricParamsBuild: new tapable.AsyncParallelHook<[MetricParams]>(['metricParams']),

    onGenBuildMetricParams: new tapable.AsyncSeriesWaterfallHook<[MetricParams]>(['metricParams']),
    afterGenExtractParams: new tapable.AsyncSeriesWaterfallHook<[MetricParams]>(['metricParams']),
  })

  async invoke(queryParam: ExtractedParams) {
    let metricParams: BuildChatMetricParams

    metricParams = (await this.hooks.onMetricParamsBuild.promise(queryParam)) as MetricParams
    await this.hooks.afterMetricParamsBuild.promise(metricParams)

    metricParams = await this.hooks.onGenBuildMetricParams.promise(metricParams)
    await this.hooks.afterGenExtractParams.promise(queryParam)

    return queryParam
  }
}
