/* eslint-disable no-console */
import chalk from 'chalk'
// import { format as baseSqlFormatter } from 'sql-formatter'
import { assertExhaustive } from 'src/shared/common-utils'
import { PROCESS_ENV } from 'src/server/server-constants'
import { PluginClass } from 'src/shared'
import {
  NoListMetric,
  Dimension,
  TimeDimensionDatum,
  SimpleMetric,
  DATE_ALIAS,
  SqlStatement,
  PeriodOverPeriodMonthNames,
  PeriodOverPeriodType,
  PeriodOverPeriodQuarterNames,
  DerivedMetric,
  MetricConfig,
} from 'src/shared/metric-types'
import { SqlMeta } from '../../sql-meta'
import { MetricToSql } from '../../metric-to-sql'

// const sqlFormatter = (sql: string) => baseSqlFormatter(sql, { indentStyle: 'tabularLeft' })

function composeLastTableJoinPart(metricNames: string[], groupBys: string[]): string {
  // 以 DIM_TBL 为主表，依次 left join 每一个 metric，使用 groupBys 作为 on 进行 join
  const joinPart = metricNames.reduce((acc, cur, _) => {
    const onPart: string[] = []

    groupBys.forEach((groupBy) => {
      onPart.push(`DIM_TBL.${groupBy} = ${cur}_TBL.${groupBy}`)
    })

    acc = acc + ` LEFT JOIN ${cur}_TBL ON ${onPart.join(' AND ')}`
    return acc
  }, '')
  return 'DIM_TBL' + joinPart
}

/** 是否启用了月份周期的同环比 */
function hasMonthPeriodOverPeriod(periodOverPeriods?: PeriodOverPeriodType[]) {
  return (
    periodOverPeriods &&
    periodOverPeriods.length > 0 &&
    PeriodOverPeriodMonthNames.some((name) => periodOverPeriods.includes(name as PeriodOverPeriodType))
  )
}
/** 是否启用了季度周期的同环比 */
function hasQuarterPeriodOverPeriod(periodOverPeriods?: PeriodOverPeriodType[]) {
  return (
    periodOverPeriods &&
    periodOverPeriods.length > 0 &&
    PeriodOverPeriodQuarterNames.some((name) => periodOverPeriods.includes(name as PeriodOverPeriodType))
  )
}

function enableBypass(sqlMeta: SqlMeta, metrics: NoListMetric[]): boolean {
  // 同环比使用 MultiAgent支持，不在 Bypass 这里支持
  if (
    PROCESS_ENV.DISABLE_SQL_GENERATE_BYPASS ||
    sqlMeta.isMetricInWhere ||
    sqlMeta.tableMetricNames.length === 0 ||
    hasMonthPeriodOverPeriod(sqlMeta.periodOverPeriods) ||
    hasQuarterPeriodOverPeriod(sqlMeta.periodOverPeriods)
  ) {
    return false
  }
  // 都是简单指标 不带单独过滤条件
  for (const item of sqlMeta.tableMetricNames) {
    const metric = metrics.find((m) => m.name === item)
    if (metric?.type === 'simple') {
      const simpleMetric = metric as SimpleMetric
      if (simpleMetric.filter && simpleMetric.filter.length > 0) {
        return false
      }
    } else {
      return false
    }
  }
  return true
}

/**
 * 把 dimension 转换成 sql 片段
 */
export function dimension2Sql(dimension: Dimension) {
  // 如果没有 expr，直接返回 name
  if (!dimension.expr) {
    return dimension.name
  }
  // 如果 expr 和 name 不一样，添加 AS
  if (dimension.expr !== dimension.name) {
    return `${dimension.expr} AS ${dimension.name}`
  }
  // 否则直接返回 expr
  return dimension.expr
}

/**
 * 把 sqlStatement 拼成 SQL
 */
function sqlStatementToSql(statement: SqlStatement) {
  return [
    `SELECT`,
    Array.isArray(statement.select) ? statement.select.join(', ') : statement.select,
    `FROM`,
    statement.from,
    statement.join && statement.join.length > 0 ? statement.join : '',
    statement.where && statement.where.length > 0 ? 'WHERE ' + statement.where : '',
    statement.groupBys && statement.groupBys?.length > 0 ? `GROUP BY ${statement.groupBys.join(', ')}` : '',
    statement.orderBys && statement.orderBys.length > 0 ? `ORDER BY ${statement.orderBys.join(', ')}` : '',
    statement.limit ? `LIMIT ${statement.limit}` : '',
  ]
    .filter((x) => x.length > 0)
    .join(' ')
}

function generateDimensionVirtualTable(
  sqlMeta: SqlMeta,
  dimensions: Dimension[],
  tableName: string,
  sharedWhereWithTime: string,
) {
  const dimensionVirtualTableSelectElements: string[] = []
  if (sqlMeta.timeGroupBy) {
    // 加上日期提参的 group by
    dimensionVirtualTableSelectElements.push(sqlMeta.timeGroupBy + ` AS ${DATE_ALIAS}`)
  }
  if (sqlMeta.sharedGroupBys.length > 0) {
    const groupByDimensions = dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name))
    dimensionVirtualTableSelectElements.push(...groupByDimensions.map((d) => dimension2Sql(d)))
  }
  let dimensionVirtualTable: string | null = null
  if (dimensionVirtualTableSelectElements.length > 0) {
    dimensionVirtualTable = sqlStatementToSql({
      select: 'DISTINCT ' + dimensionVirtualTableSelectElements.join(', '),
      from: tableName,
      where: sharedWhereWithTime,
    })
  }
  return dimensionVirtualTable
}

function getDateSqlPartOfTime(timeDimensionDatum: TimeDimensionDatum): {
  yearAndMonth: string
  year: string
  month: string
  yearAndQuarter: string
  quarter: string
} {
  const timeDimensionType = timeDimensionDatum.timeDimensionType
  const emptyResult = {
    yearAndMonth: '',
    year: '',
    month: '',
    yearAndQuarter: '',
    quarter: '',
  }
  switch (timeDimensionType) {
    case 'date':
    case 'datetime':
      console.error('尚不支持')
      return emptyResult
    case 'string': {
      const timeDimensionFormat = timeDimensionDatum.timeDimensionFormat
      const yyyyMmYearAndMonth = `LEFT(${timeDimensionDatum.timeDimensionName}, 6)`
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const yyMmYearAndMonth = `LEFT(${timeDimensionDatum.timeDimensionName}, 4)`
      const yyyyYear = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 1, 4) AS INTEGER)`
      const yyyyMmMonth = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 5, 2) AS INTEGER)`
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const yyyy_mmYearAndMonth = `LEFT(${timeDimensionDatum.timeDimensionName}, 7)`
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const yyyy_mmMonth = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 6, 2) AS INTEGER)`
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const yyYear = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 1, 2) AS INTEGER)`
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const yyMmMonth = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 3, 2) AS INTEGER)`
      switch (timeDimensionFormat) {
        case undefined:
          console.error('timeDimensionFormat 不能为空')
          return emptyResult
        case 'yyyy':
          console.error('数据为年周期，无法支持月的同环比')
          return emptyResult
        case 'yyyyMMDD':
        case 'yyyyMMdd':
          return {
            yearAndMonth: yyyyMmYearAndMonth,
            year: yyyyYear,
            month: yyyyMmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y%m%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y%m%d'))`,
          }
        case 'yyyyMM':
          return {
            yearAndMonth: yyyyMmYearAndMonth,
            year: yyyyYear,
            month: yyyyMmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '01'), '%Y%m%d')))`,
            quarter: `QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '01'), '%Y%m%d'))`,
          }
        case 'yyyy-MM':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '-01'), '%Y-%m-%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '-01'), '%Y-%m-%d')))`,
            quarter: `QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '-01'), '%Y-%m-%d'))`,
          }
        case 'yyyy/MM':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '/01'), '%Y/%m/%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '/01'), '%Y/%m/%d')))`,
            quarter: `QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '/01'), '%Y/%m/%d'))`,
          }
        case 'yyyy-MM-DD':
        case 'yyyy-MM-dd':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y-%m-%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y-%m-%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y-%m-%d'))`,
          }
        case 'yyyy_MM_DD':
        case 'yyyy_MM_dd':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y_%m_%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y_%m_%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y_%m_%d'))`,
          }
        case 'yyyy/MM/DD':
        case 'yyyy/MM/dd':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y/%m/%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y/%m/%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y/%m/%d'))`,
          }
        default:
          return assertExhaustive(timeDimensionFormat)
      }
    }
    default:
      assertExhaustive(timeDimensionType)
  }
}

function generateSqlOfSimpleMetric(
  metric: SimpleMetric,
  sqlMeta: SqlMeta,
  metricNameWithSql: { name: string; sql: string }[],
  tableName: string,
  dimensions: Dimension[],
  timeDimensionDatum?: TimeDimensionDatum,
) {
  // 如果开启了同环比，就需要生成多个子查询语句
  if (hasMonthPeriodOverPeriod(sqlMeta.periodOverPeriods) && timeDimensionDatum != null) {
    const groupByDimensions =
      sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
    const groupByWithAs = groupByDimensions.map((d) => dimension2Sql(d))
    const groupBys = [DATE_ALIAS, 'year_num', 'month_num', ...groupByDimensions.map((d) => d.name)]
    const dateSqlPartOfTime = getDateSqlPartOfTime(timeDimensionDatum)
    const selectPart = [
      `${dateSqlPartOfTime.yearAndMonth} AS ${DATE_ALIAS}`,
      ...groupByWithAs,
      `${dateSqlPartOfTime.year} AS year_num`,
      `${dateSqlPartOfTime.month} AS month_num`,
      `${metricNameWithSql.find((m) => m.name === metric.name)!.sql} as ${metric.name}`,
    ]
      .filter((x) => x.length > 0)
      .join(', ')
    // 返回多个子查询语句数组
    const monthlyMetricTable = {
      cteName: metric.name + '_MONTHLY_TBL',
      sql: sqlStatementToSql({
        select: selectPart,
        from: tableName,
        where: [sqlMeta.sharedWhereWithoutTime, metric.filter].filter((w) => w).join(' AND '),
        groupBys: groupBys,
        orderBys: [DATE_ALIAS + ' desc'],
      }),
    }
    const recentMetricTable = {
      cteName: metric.name + '_RECENT_TBL',
      // 把 _MONTHLY_TBL 和 DIM_TBL join，因为 DIM_TBL 已经过滤了时间，join 之后就是过滤时间后的数据
      sql: sqlStatementToSql({
        select: `${groupBys.join(', ')}, ${metric.name}`,
        from: `${metric.name}_MONTHLY_TBL`,
        where: sqlMeta.timeWhereForPeriodOverPeriod,
      }),
    }
    const groupByToSelect = groupBys.map((groupBy) => `current_month.${groupBy}`).join(', ')
    const finalMetricTable = {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: [
          groupByToSelect,
          `current_month.${metric.name}`,
          `previous_month.${metric.name} as pre_month_${metric.name}`,
          `previous_year_month.${metric.name} as pre_year_month_${metric.name}`,
          `(current_month.${metric.name} - previous_month.${metric.name}) as mom_growth_${metric.name}`,
          `(current_month.${metric.name} - previous_year_month.${metric.name}) as yoy_month_growth_${metric.name}`,
          `((current_month.${metric.name} - previous_month.${metric.name}) / previous_month.${metric.name}) as mom_growth_rate_${metric.name}`,
          `((current_month.${metric.name} - previous_year_month.${metric.name}) / previous_year_month.${metric.name}) as yoy_month_growth_rate_${metric.name}`,
        ],
        from: `${metric.name}_RECENT_TBL AS current_month`,
        join: [
          `LEFT JOIN`,
          `${metric.name}_MONTHLY_TBL AS previous_month`,
          `ON`,
          `(current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num)`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_month.${d.name} = previous_month.${d.name}`).join(' ')
            : '',
          `LEFT JOIN`,
          `${metric.name}_MONTHLY_TBL AS previous_year_month`,
          `ON`,
          `(current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_month.${d.name} = previous_year_month.${d.name}`).join(' ')
            : '',
        ]
          .filter((x) => x.length > 0)
          .join(' '),
      }),
    }
    return [monthlyMetricTable, recentMetricTable, finalMetricTable]
  } else if (hasQuarterPeriodOverPeriod(sqlMeta.periodOverPeriods) && timeDimensionDatum != null) {
    const groupByDimensions =
      sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
    const groupByWithAs = groupByDimensions.map((d) => dimension2Sql(d))
    const groupBys = [DATE_ALIAS, ...groupByDimensions.map((d) => d.name)]
    const groupBysWithYearQuarter = [DATE_ALIAS, 'year_num', 'quarter_num', ...groupByDimensions.map((d) => d.name)]
    const dateSqlPartOfTime = getDateSqlPartOfTime(timeDimensionDatum)
    const selectPart = [
      `${dateSqlPartOfTime.yearAndQuarter} AS ${DATE_ALIAS}`,
      ...groupByWithAs,
      `${dateSqlPartOfTime.year} AS year_num`,
      // TODO: 目前只有 yyyyMM 的日期格式，未来要支持其他的格式
      `${dateSqlPartOfTime.quarter} AS quarter_num`,
      `${metricNameWithSql.find((m) => m.name === metric.name)!.sql} as ${metric.name}`,
    ]
      .filter((x) => x.length > 0)
      .join(', ')
    // 返回多个子查询语句数组
    const quarterlyMetricTable = {
      cteName: metric.name + '_QUARTERLY_TBL',
      sql: sqlStatementToSql({
        select: selectPart,
        from: tableName,
        where: [sqlMeta.sharedWhereWithoutTime, metric.filter].filter((w) => w).join(' AND '),
        groupBys: groupBysWithYearQuarter,
        orderBys: [DATE_ALIAS + ' desc'],
      }),
    }
    const recentMetricTable = {
      cteName: metric.name + '_RECENT_TBL',
      sql: sqlStatementToSql({
        select: `${groupBysWithYearQuarter.map((v) => `${metric.name}_QUARTERLY_TBL.${v}`).join(', ')}, ${metric.name}`,
        from: composeLastTableJoinPart([`${metric.name}_QUARTERLY`], groupBys),
      }),
    }
    const groupByToSelect = groupBys.map((groupBy) => `current_quarter.${groupBy}`).join(', ')
    const finalMetricTable = {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: [
          groupByToSelect,
          `current_quarter.${metric.name}`,
          `previous_quarter.${metric.name} as pre_quarter_${metric.name}`,
          `previous_year_quarter.${metric.name} as pre_year_quarter_${metric.name}`,
          `(current_quarter.${metric.name} - previous_quarter.${metric.name}) as qoq_growth_${metric.name}`,
          `(current_quarter.${metric.name} - previous_year_quarter.${metric.name}) as yoy_quarter_growth_${metric.name}`,
          `((current_quarter.${metric.name} - previous_quarter.${metric.name}) / previous_quarter.${metric.name}) as qoq_growth_rate_${metric.name}`,
          `((current_quarter.${metric.name} - previous_year_quarter.${metric.name}) / previous_year_quarter.${metric.name}) as yoy_quarter_growth_rate_${metric.name}`,
        ],

        from: `${metric.name}_RECENT_TBL AS current_quarter`,
        join: [
          `LEFT JOIN`,
          `${metric.name}_QUARTERLY_TBL AS previous_quarter`,
          `ON`,
          `(current_quarter.year_num * 4 + current_quarter.quarter_num - 1) = (previous_quarter.year_num * 4 + previous_quarter.quarter_num)`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_quarter.${d.name} = previous_quarter.${d.name}`).join(' ')
            : '',
          `LEFT JOIN`,
          `${metric.name}_QUARTERLY_TBL AS previous_year_quarter`,
          `ON`,
          `(current_quarter.year_num - 1) = previous_year_quarter.year_num AND current_quarter.quarter_num = previous_year_quarter.quarter_num`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_quarter.${d.name} = previous_year_quarter.${d.name}`).join(' ')
            : '',
        ]
          .filter((x) => x.length > 0)
          .join(' '),
      }),
    }
    return [quarterlyMetricTable, recentMetricTable, finalMetricTable]
  } else {
    const metricSqlPart = metricNameWithSql.find((m) => m.name === metric.name)!.sql
    // 生成这个指标单独的查询语句。和 dimensionVirtualTableSql 关联，如果有
    const dimensionVirtualTableSelectElements: string[] = []
    if (sqlMeta.timeGroupBy) {
      // 加上日期提参的 group by
      dimensionVirtualTableSelectElements.push(sqlMeta.timeGroupBy + ` AS ${DATE_ALIAS}`)
    }
    const groupByDimensions =
      sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
    dimensionVirtualTableSelectElements.push(...groupByDimensions.map((d) => dimension2Sql(d)))
    const selectElements = [`${metricSqlPart} as ${metric.name}`, ...dimensionVirtualTableSelectElements]
    const wherePart = [sqlMeta.sharedWhereWithTime, metric.filter].filter((w) => w).join(' AND ')
    const groupByPart = sqlMeta.timeGroupBy ? [DATE_ALIAS, ...sqlMeta.sharedGroupBys] : sqlMeta.sharedGroupBys
    return {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: selectElements.join(', '),
        from: tableName,
        where: wherePart,
        groupBys: groupByPart,
      }),
    }
  }
}

function generateSqlOfDerivedMetric(
  metric: DerivedMetric,
  metricSqlPart: string,
  hasDimensionVirtualTable: boolean,
  sqlMeta: SqlMeta,
) {
  // 派生指标没有 filter
  const requiredMetricNames = [...new Set(metric.typeParams.metrics.map((m) => m.name))]
  if (!hasDimensionVirtualTable) {
    // 当没有 groupBys 的时候，只有一行，可以直接 from 多个 table 即可
    return {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: `${metricSqlPart} as ${metric.name}`,
        from: requiredMetricNames.map((m) => `${m}_TBL`).join(', '),
      }),
    }
  } else {
    // 生成这个指标单独的查询语句。和 dimensionVirtualTableSql 关联，如果有
    const dimensionVirtualTableSelectElements: string[] = []
    if (sqlMeta.timeGroupBy) {
      // 加上日期提参的 group by
      dimensionVirtualTableSelectElements.push(`DIM_TBL.${DATE_ALIAS}`)
    }
    dimensionVirtualTableSelectElements.push(...sqlMeta.sharedGroupBys.map((d) => `DIM_TBL.${d}`))
    const selectElements = [`${metricSqlPart} as ${metric.name}`, ...dimensionVirtualTableSelectElements]
    // 派生指标的逻辑是从其他指标虚拟表中查询数据并计算，不能直接 from raw table。需要从 DIM_TBL 开始关联起来
    return {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: selectElements,
        from: composeLastTableJoinPart(
          requiredMetricNames,
          sqlMeta.sharedGroupBys.concat(sqlMeta.timeGroupBy ? [DATE_ALIAS] : []),
        ),
      }),
    }
  }
}

// 旁路：全部原子指标（直接度量自动创建），并且单个指标模板配置不带 filter 条件
function generateSqlOfSimpleWithoutFilter(
  sqlMeta: SqlMeta,
  metricNameWithSql: { name: string; sql: string }[],
  tableName: string,
  dimensions: Dimension[],
  //timeDimensionDatum?: TimeDimensionDatum,
): string {
  // 1.Trick，尝试基于现在单个原子指标 CTE 先生成第一个指标，然后剩下的指标直接塞到 select 子句中
  // 2.虚拟维表DIM_TBL中 select 子句需要挪过来（维表字段来自于Group），塞到 select 子句中
  // 3.Order By 和 Limit 子句需要从 lastSql中拿过来
  // 4.需要适配sql结果信息，和原来多个 CTE 方式能够接上，不能改动前端代码，需要看如何适配？？？

  // TODO:
  const allMetricSqlPart = sqlMeta.tableMetricNames.map((metricName) => {
    return metricNameWithSql.find((m) => m.name === metricName)!.sql + ' AS ' + metricName
  })

  // 生成这个指标单独的查询语句和 dimensionVirtualTableSql 关联，如果有
  const dimensionVirtualTableSelectElements: string[] = []
  if (sqlMeta.timeGroupBy) {
    // 加上日期提参的 group by
    dimensionVirtualTableSelectElements.push(sqlMeta.timeGroupBy + ` AS ${DATE_ALIAS}`)
  }
  const groupByDimensions =
    sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
  dimensionVirtualTableSelectElements.push(...groupByDimensions.map((d) => dimension2Sql(d)))
  const selectElements = [...allMetricSqlPart, ...dimensionVirtualTableSelectElements]
  const wherePart = [sqlMeta.sharedWhereWithTime].filter((w) => w).join(' AND ')
  const groupByPart = sqlMeta.timeGroupBy ? [DATE_ALIAS, ...sqlMeta.sharedGroupBys] : sqlMeta.sharedGroupBys
  return sqlStatementToSql({
    select: selectElements.join(', '),
    from: tableName,
    where: wherePart,
    groupBys: groupByPart,
    // order by 从 lastSql 中拿过来
    orderBys: sqlMeta.lastSqlPart.orderBys,
    // limit 从 lastSql 中拿过来
    limit: sqlMeta.lastSqlPart.limit,
  })
}

/** 生成一个 simple 指标的直接查询语句，目前用于 ratio 指标的分母部分 */
function getPureSqlOfSimpleMetric(metric: SimpleMetric, tableName: string, metricNameSql: string) {
  const denominatorCTE = sqlStatementToSql({
    select: metricNameSql,
    from: tableName,
    where: metric.filter,
  })
  return denominatorCTE
}

function generateSql(
  sqlMeta: SqlMeta,
  metricNameWithSql: { name: string; sql: string }[],
  tableName: string,
  metrics: NoListMetric[],
  dimensions: Dimension[],
  timeDimensionDatum?: TimeDimensionDatum,
): string {
  // 如果有 timeGroupBy sharedGroupBys 都不为空，这个时候创建维度虚拟表
  const hasDimensionVirtualTable =
    (sqlMeta.timeGroupBy && sqlMeta.timeGroupBy.length > 0) || sqlMeta.sharedGroupBys.length > 0
  const dimensionVirtualTableSql = generateDimensionVirtualTable(
    sqlMeta,
    dimensions,
    tableName,
    sqlMeta.sharedWhereWithTime,
  )

  // 旁路优化：全部原子指标且无过滤条件，并且无同环比（同环比走 MultiAgent 复杂问题拆解）
  if (enableBypass(sqlMeta, metrics)) {
    console.warn(chalk.red('generate sql use bypass >>>'))
    return generateSqlOfSimpleWithoutFilter(sqlMeta, metricNameWithSql, tableName, dimensions)
  }

  // 生成 metric 虚拟表
  const metricTableSqlArr: { cteName: string; sql: string }[] = sqlMeta.tableMetricNames.flatMap((metricName) => {
    const metric = metrics.find((m) => m.name === metricName)
    if (metric?.type === 'simple') {
      return generateSqlOfSimpleMetric(metric, sqlMeta, metricNameWithSql, tableName, dimensions, timeDimensionDatum)
    } else if (metric?.type === 'derived') {
      const metricSqlPart = metricNameWithSql.find((m) => m.name === metricName)!.sql
      return generateSqlOfDerivedMetric(metric, metricSqlPart, hasDimensionVirtualTable, sqlMeta)
    } else if (metric?.type === 'ratio') {
      // TODO: ratio 指标可以拆解为依赖一个 不带 where&groupBy 的 simple metric 的 derived metric。然后就复用 derived 的逻辑
      // 占比指标的分母只支持普通指标，分子支持普通指标和派生指标，占比指标没有 filter
      const requiredMetricNames = [metric.typeParams.numerator]
      const denominatorMetric = metrics.find((m) => m.name === metric.typeParams.denominator)! as SimpleMetric
      if (denominatorMetric.type !== 'simple') {
        console.error(
          `denominator metric ${metric.typeParams.denominator} type not supported, only support simple metric`,
        )
        throw new Error(
          `denominator metric ${metric.typeParams.denominator} type not supported, only support simple metric`,
        )
      }

      const metricNameSql = metricNameWithSql.find((m) => m.name === denominatorMetric.name)!.sql
      const denominatorSql = getPureSqlOfSimpleMetric(denominatorMetric, tableName, metricNameSql)
      const ratioSelectPart = `${metric.typeParams.numerator}_TBL.${metric.typeParams.numerator} / (${denominatorSql})`
      if (!hasDimensionVirtualTable) {
        // 当没有 groupBys 的时候，只有一行，可以直接 from 多个 table 即可
        return {
          cteName: metricName + '_TBL',
          sql: sqlStatementToSql({
            select: `${ratioSelectPart} as ${metricName}`,
            from: requiredMetricNames.map((m) => `${m}_TBL`).join(', '),
          }),
        }
      } else {
        const dimensionVirtualTableSelectElements: string[] = []
        if (sqlMeta.timeGroupBy) {
          // 加上日期提参的 group by
          dimensionVirtualTableSelectElements.push(`DIM_TBL.${DATE_ALIAS}`)
        }
        dimensionVirtualTableSelectElements.push(...sqlMeta.sharedGroupBys.map((d) => `DIM_TBL.${d}`))
        const selectElements = [`${ratioSelectPart} as ${metricName}`, ...dimensionVirtualTableSelectElements]
        return {
          cteName: metricName + '_TBL',
          sql: sqlStatementToSql({
            select: selectElements,
            from: composeLastTableJoinPart(
              requiredMetricNames,
              sqlMeta.sharedGroupBys.concat(sqlMeta.timeGroupBy ? [DATE_ALIAS] : []),
            ),
          }),
        }
      }
    } else {
      throw new Error(`metric ${metricName} type not supported`)
    }
  })

  if (!hasDimensionVirtualTable) {
    // 最后的查询语句使用第一个 metric 和其他 metric 依次做 cross join，也就是列合并
    const firstMetricName = sqlMeta.lastSqlPart.metricNames[0]
    const lastSql = [
      'SELECT',
      sqlMeta.lastSqlPart.select,
      'FROM',
      firstMetricName + '_TBL',
      ...sqlMeta.lastSqlPart.metricNames.slice(1).map((metricName) => `cross join ${metricName}_TBL`),
      sqlMeta.lastSqlPart.where ? `WHERE ${sqlMeta.lastSqlPart.where}` : '',
      sqlMeta.lastSqlPart.orderBys?.length > 0 ? `ORDER BY ${sqlMeta.lastSqlPart.orderBys.join(', ')}` : '',
      sqlMeta.lastSqlPart.limit ? `LIMIT ${sqlMeta.lastSqlPart.limit}` : '',
    ]
      .filter((x) => x.length > 0)
      .join(' ')
    const withPart = metricTableSqlArr
      .reduce((acc, cur) => {
        const sqlLines = cur.sql
          .split('\n')
          .map((line) => `  ${line}`)
          .join('\n')
        return acc + `, ${cur.cteName} AS (\n${sqlLines}\n)`
      }, '')
      .slice(2) // 去掉第一个逗号
    return 'WITH ' + withPart + '\n' + lastSql.trim()
  } else {
    // 最后的查询语句使用 DIM_TBL 依次 left join 每一个 metric
    const lastSql = sqlStatementToSql({
      select: sqlMeta.lastSqlPart.select,
      from: composeLastTableJoinPart(
        sqlMeta.lastSqlPart.metricNames,
        sqlMeta.sharedGroupBys.concat(sqlMeta.timeGroupBy ? [DATE_ALIAS] : []),
      ),
      where: sqlMeta.lastSqlPart.where,
      orderBys: sqlMeta.lastSqlPart.orderBys,
      limit: sqlMeta.lastSqlPart.limit,
    })

    // 把前 2 部分拼接起来做 with
    const allWith =
      dimensionVirtualTableSql == null
        ? metricTableSqlArr
        : [{ cteName: 'DIM_TBL', sql: dimensionVirtualTableSql }, ...metricTableSqlArr]
    const withPart = allWith
      .reduce((acc, cur) => {
        const sqlLines = cur.sql
          .split('\n')
          .map((line) => `  ${line}`)
          .join('\n')
        return acc + `, ${cur.cteName} AS (\n${sqlLines}\n)`
      }, '')
      .slice(2) // 去掉第一个逗号

    return 'WITH ' + withPart + '\n' + lastSql
  }
}

export class GenerateSql2024Plugin implements PluginClass<MetricToSql> {
  metricConfig!: MetricConfig
  sqlMeta!: SqlMeta

  apply(instance: MetricToSql): void {
    this.metricConfig = instance.config.metricConfig
    instance.hooks.afterCreateSqlBuilder.tapPromise(GenerateSql2024Plugin.name, async (builder) => {
      builder.hooks.onSqlBuild.tapPromise(
        { name: GenerateSql2024Plugin.name, stage: Number.MAX_SAFE_INTEGER },
        async (sqlMeta) => {
          this.sqlMeta = sqlMeta
          return this.toSql()
        },
      )
    })
  }

  toSql() {
    const metricNameWithSql = this.metricConfig.getAllNoListMetrics().map((m) => {
      return {
        name: m.name,
        sql: this.metricConfig.metric2SqlByType(m),
      }
    })

    const finalSql = generateSql(
      this.sqlMeta,
      metricNameWithSql,
      this.metricConfig.name,
      this.metricConfig.getAllNoListMetrics(),
      this.metricConfig.allDimensions,
      this.metricConfig.timeDimensionDatum,
    )
    return finalSql
  }
}
