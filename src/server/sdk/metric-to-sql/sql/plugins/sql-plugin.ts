import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../../metric-to-sql'
import { GenerateSql2024Plugin } from './generate-sql-2024-plugin'
import { GenerateSql2025Plugin } from './generate-sql-2025-plugin'

export class SqlPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.use(new GenerateSql2025Plugin()).use(new GenerateSql2024Plugin())
  }
}
