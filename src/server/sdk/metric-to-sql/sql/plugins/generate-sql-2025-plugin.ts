/* eslint-disable no-case-declarations */
/* eslint-disable no-console */
import knex from 'knex'
import { format as baseSqlFormatter } from 'sql-formatter'
import { PluginClass } from 'src/shared'
import { DATE_ALIAS, Measure, MetricConfig } from 'src/shared/metric-types'
import { MetricToSql } from '../../metric-to-sql'
import { DerivedMeta } from '../../derived-meta'
import { dimension2Sql } from './generate-sql-2024-plugin'

const sqlFormatter = (sql: string) =>
  baseSqlFormatter(sql, {
    indentStyle: 'tabularLeft',
    keywordCase: 'upper',
    identifierCase: 'upper',
    dataTypeCase: 'upper',
    functionCase: 'upper',
  })

const SUFFIX_TBL = '_TBL'
const wrapperTable = (name: string) => name + SUFFIX_TBL
const DIM_TBL = wrapperTable('DIM')
const FLATTED_TBL = wrapperTable('FLATTED')
const concatTableName = (...args: string[]) => args.join('.')

/**
 * 把 measure 转成 sql 片段
 */
export function measure2Sql(measure: Measure) {
  if (measure.expr == null) {
    throw new Error(`The expression is null`)
  }
  const aggKeywords = ['sum', 'sum_boolean', 'count', 'avg', 'max', 'min', 'count_distinct']
  if (!aggKeywords.some((keyword) => measure.expr.toLocaleLowerCase().includes(keyword))) {
    throw new Error(`${measure.expr} expression is illegal`)
  }
  return measure.expr
}

export class GenerateSql2025Plugin implements PluginClass<MetricToSql> {
  metricConfig!: MetricConfig
  derivedMeta!: DerivedMeta
  knex = knex({
    client: 'mysql',
    wrapIdentifier: (value, _origImpl) => {
      return value
    },
  })

  apply(instance: MetricToSql): void {
    this.metricConfig = instance.config.metricConfig

    instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(GenerateSql2025Plugin.name, async (builder) => {
      builder.hooks.afterGenDerivedMeta.tapPromise(GenerateSql2025Plugin.name, async (derivedMeta) => {
        this.derivedMeta = derivedMeta
      })
    })

    instance.hooks.afterCreateSqlBuilder.tapPromise(GenerateSql2025Plugin.name, async (builder) => {
      builder.hooks.onSqlBuild.tapPromise(GenerateSql2025Plugin.name, async () => {
        // return undefined
        try {
          if (instance.config.version !== '2025') throw new Error('非2025版，跳过')
          return sqlFormatter(this.toSql())
        } catch (err: any) {
          console.log('Metric2Sql优化失败，退回老链路: ' + (err?.message ?? err?.toString()))
        }
      })
    })
  }

  buildDimLayer({ builder, metricNames }: { builder: knex.Knex.QueryBuilder; metricNames: string[] }) {
    // select distinct
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select({ [DATE_ALIAS]: this.derivedMeta.timeSqlPart.groupBy })
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      const dim = this.metricConfig.findDimensionByName(groupBy)
      builder.select(dimension2Sql(dim))
    })

    for (const metricName of metricNames) {
      const metric = this.metricConfig.findMetricByName(metricName)
      let sql = ''
      if (metric.type === 'simple') {
        const measure = this.metricConfig.findMeasureByName(metric.typeParams.measure)
        const columnName = concatTableName(this.metricConfig.name.split('.').at(-1)!, measure.name)
        sql = measure2Sql(measure)
        if (metric.filter) {
          sql = sql.replace(
            new RegExp(columnName, 'gi'),
            (val) => `(CASE WHEN ${metric.filter} then ${val} ELSE NULL END)`,
          )
        }
      } else {
        throw new Error(`暂时不支持${metricName}指标`)
      }
      builder.select({ [metric.name]: sql })
    }
    // from
    builder.from(this.metricConfig.name)
    this.buildWhere(builder)
    this.buildGroupBy(builder)
  }

  flat(metricName: string) {
    const metric = this.metricConfig.findMetricByName(metricName)
    let sql = ''
    if (metric.type === 'derived') {
      sql = metric.typeParams.expr
      for (const { name } of metric.typeParams.metrics) {
        sql = sql.replace(new RegExp(name, 'g'), this.flat(name))
      }
    } else if (metric.type === 'ratio') {
      // TODO: 这里分母是错的
      sql = `${this.flat(metric.typeParams.numerator)} / ${this.flat(metric.typeParams.denominator)}`
    } else if (metric.type === 'simple') {
      return concatTableName(DIM_TBL, metric.name)
    }
    return `(${sql})`
  }

  buildFlattedLayer({ builder, metricNames }: { builder: knex.Knex.QueryBuilder; metricNames: string[] }) {
    this.appendCommonSelect(builder)
    builder.from(DIM_TBL)
    for (const metricName of metricNames) {
      const metric = this.metricConfig.findMetricByName(metricName)
      const sql = this.flat(metricName)
      builder.select({ [metric.name]: sql })
    }
  }

  buildFinalLayer({
    builder,
    metricNames,
    from,
  }: {
    from: string
    builder: knex.Knex.QueryBuilder
    metricNames: string[]
  }) {
    this.appendCommonSelect(builder)
    metricNames.forEach((metricName) => {
      builder.select(metricName)
    })
    // from
    builder.from(from)
    // last where
    builder.whereRaw(this.derivedMeta.whereDerived.lastWhere)
    // last orderby
    this.derivedMeta.queryParams.orderBys?.forEach((orderBy) => {
      builder.orderByRaw(orderBy)
    })
    // last limit
    if (this.derivedMeta.queryParams.limit) builder.limit(this.derivedMeta.queryParams.limit)
  }

  buildWhere(builder: knex.Knex.QueryBuilder) {
    // where
    if (this.derivedMeta.timeSqlPart?.where) builder.whereRaw(this.derivedMeta.timeSqlPart.where)
    builder.whereRaw(this.derivedMeta.whereDerived.sharedWhere)
  }

  buildGroupBy(builder: knex.Knex.QueryBuilder) {
    // group by
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      builder.groupBy(this.derivedMeta.timeSqlPart.groupBy)
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      builder.groupBy(groupBy)
    })
  }

  appendCommonSelect(builder: knex.Knex.QueryBuilder) {
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select(concatTableName(DATE_ALIAS))
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      builder.select(concatTableName(groupBy))
    })
  }

  toSql(): string {
    const hasDimensionVirtualTable =
      (this.derivedMeta.timeSqlPart?.groupBy && this.derivedMeta.timeSqlPart?.groupBy.length > 0) ||
      (this.derivedMeta.queryParams.groupBys && this.derivedMeta.queryParams.groupBys.length > 0)
    if (this.derivedMeta.queryParams.periodOverPeriods && this.derivedMeta.queryParams.periodOverPeriods.length)
      throw new Error('不支持同环比')
    if (!hasDimensionVirtualTable) throw new Error('不支持无虚拟维度')
    const builder = this.knex.queryBuilder()
    const metricNames = Array.from(
      new Set(this.derivedMeta.queryParams.metricNames.concat(this.derivedMeta.whereDerived.metricNames)),
    )
    const orderedMetricInfo = this.metricConfig.getOrderedMetrics(metricNames)
    const flattedMetricNames = orderedMetricInfo.levels.flat()
    const simpleMetrics = flattedMetricNames.filter(
      (name) => this.metricConfig.findMetricByName(name).type === 'simple',
    )
    const othersMetrics = flattedMetricNames.filter(
      (name) => this.metricConfig.findMetricByName(name).type !== 'simple',
    )
    if (othersMetrics.some((name) => this.metricConfig.findMetricByName(name).type === 'ratio'))
      throw new Error('不支持比值指标')
    builder.with(DIM_TBL, (builder) => this.buildDimLayer({ builder, metricNames: simpleMetrics }))
    if (othersMetrics.length) {
      builder.with(FLATTED_TBL, (builder) => this.buildFlattedLayer({ builder, metricNames: othersMetrics }))
    }
    this.buildFinalLayer({
      builder,
      metricNames,
      from: othersMetrics.length ? FLATTED_TBL : DIM_TBL,
    })
    return builder.toQuery()
  }
}
