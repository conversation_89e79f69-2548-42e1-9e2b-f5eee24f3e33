import * as tapable from 'tapable'
import { Hookable } from 'src/shared'
import { SqlMeta } from '../sql-meta'

export class SqlBuilder extends Hookable {
  hooks = Object.freeze({
    onSqlBuild: new tapable.AsyncSeriesBailHook<[SqlMeta], string | undefined>(['sqlMeta']),
    afterSqlBuild: new tapable.AsyncParallelHook<[string]>(['sqlMeta']),

    onGenSql: new tapable.AsyncSeriesWaterfallHook<[string]>(['sqlMeta']),
    afterGenSql: new tapable.AsyncParallelHook<[string]>(['sqlMeta']),
  })

  async invoke(meta: SqlMeta) {
    let res: string
    res = (await this.hooks.onSqlBuild.promise(meta)) as string
    await this.hooks.afterSqlBuild.promise(res)
    res = await this.hooks.onGenSql.promise(res)
    await this.hooks.afterGenSql.promise(res)
    return res
  }
}
