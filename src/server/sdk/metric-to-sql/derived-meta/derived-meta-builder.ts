import * as tapable from 'tapable'
import { Hookable } from 'src/shared'
import { VerifiedMeta } from '../verified-meta'

/**
 * 第二步：从上面的参数中，判断出需要 where 派生出来的部分。
 * 同时，对 where 做处理。需要把 where 中的 dimension 转换成对应的 expr
 * Derived 字面意思有点歧义，其中这里包含了所有的参数，这是一个 union 结构
 */
export type DerivedMeta = VerifiedMeta & {
  whereDerived: {
    metricNames: string[] // 当 where 中子条件的左值或者右值为 metric 的时候，需要存储到这里
    dimensionNames: string[] // 当 where 中子条件的左值或者右值为 dimension 的时候，需要存储到这里
    sharedWhere: string // 除 lastSqlPart 之外都复用的 where。需要替换掉 where 中的维度表达式。包含时间的 where
    lastWhere: string // lastSql 单独使用的 where，就是 left 或者 right 值有 metric 的 where。需要替换掉 where 中的维度表达式
  }
}

export class DerivedMetaBuilder extends Hookable {
  hooks = Object.freeze({
    onDerivedMetaBuild: new tapable.AsyncSeriesBailHook<[VerifiedMeta], DerivedMeta | undefined>(['derivedMeta']),
    afterDerivedMetaBuild: new tapable.AsyncParallelHook<[DerivedMeta]>(['derivedMeta']),

    onGenDerivedMeta: new tapable.AsyncSeriesWaterfallHook<[DerivedMeta]>(['derivedMeta']),
    afterGenDerivedMeta: new tapable.AsyncParallelHook<[DerivedMeta]>(['derivedMeta']),
  })

  async invoke(verifiedMeta: VerifiedMeta) {
    let res: DerivedMeta
    res = (await this.hooks.onDerivedMetaBuild.promise(verifiedMeta)) as DerivedMeta
    await this.hooks.afterDerivedMetaBuild.promise(res)
    res = await this.hooks.onGenDerivedMeta.promise(res)
    await this.hooks.afterGenDerivedMeta.promise(res)
    return res
  }
}
