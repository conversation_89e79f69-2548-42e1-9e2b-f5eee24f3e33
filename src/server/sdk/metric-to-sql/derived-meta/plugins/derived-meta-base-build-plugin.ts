import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

export class DerivedMetaBaseBuildPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(DerivedMetaBaseBuildPlugin.name, async (builder) => {
      builder.hooks.onDerivedMetaBuild.tapPromise(
        {
          name: DerivedMetaBaseBuildPlugin.name,
          stage: Number.MAX_SAFE_INTEGER,
        },
        async (verifiedMeta) => {
          return {
            ...verifiedMeta,
            whereDerived: {
              metricNames: [],
              dimensionNames: [],
              sharedWhere: '',
              lastWhere: '',
            },
          }
        },
      )
    })
  }
}
