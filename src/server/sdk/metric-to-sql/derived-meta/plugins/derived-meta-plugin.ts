import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { DerivedMetaBaseBuildPlugin } from './derived-meta-base-build-plugin'
import { DerivedMetaWhereBuilderPlugin } from './derived-meta-where-builder-plugin'

export class DerivedMetaPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.use(new DerivedMetaBaseBuildPlugin()).use(new DerivedMetaWhereBuilderPlugin())
  }
}
