import { ColumnRef, Expr, Param, Function, Parser, Value } from 'node-sql-parser'
import { PluginClass } from 'src/shared'
import { NoListMetric, Dimension } from 'src/shared/metric-types'
import { VIRTUAL_TIME_DIM_NAMES } from 'src/server/MetricStore/metric2sql/time2sql'
import { assertExhaustive } from 'src/shared/common-utils'
import { MetricToSql } from '../../metric-to-sql'
import { VerifiedMeta } from '../../verified-meta'
import { DerivedMeta } from '../derived-meta-builder'

/** node-sql-parser 当中 where 表达式的 type 取值类型 */
type WhereNodeType =
  | 'binary_expr'
  | 'column_ref'
  | 'param'
  | 'null'
  | 'expr_list'
  | 'number' // Value 的 type 为 string，Value 的一种
  | 'double_quote_string' // Value 的 type 为 string，Value 的一种
  | 'single_quote_string' // Value 的 type 为 string，Value 的一种
  | 'unary_expr' // NOT
  | 'function'
  | 'interval'

/**
 * 从大模型提参的 where 中找出 WhereDerived
 * 1. 解析出 where 中包含 metric 的部分与不包含 metric 的部分
 * 2. 替换调 where 中维度的表达式
 * 注:
 * 1. lastWhere 用在最后一个SQL中 包含指标相关的数据
 * 2. sharedWhere 作用在所有SQL中 不能包含指标相关的数据 因为维度的SQL statement中没有指标相关数据
 */
export function generateWhereDeriveMeta(
  verifiedMeta: VerifiedMeta,
  metrics: NoListMetric[],
  dimensions: Dimension[],
): DerivedMeta {
  const where = verifiedMeta.queryParams.where
  if (where == null || where.length === 0) {
    return {
      ...verifiedMeta,
      whereDerived: {
        metricNames: [],
        dimensionNames: [],
        sharedWhere: '',
        lastWhere: '',
      },
    }
  }
  const metricNamesInWhere: string[] = []
  const dimensionNamesInWhere: string[] = []
  const lastWherePredicts: string[] = []

  const sqlParser = new Parser()
  const selectPrefix = 'SELECT * FROM `table1` WHERE '
  const ast = sqlParser.astify(selectPrefix + where)
  if (Array.isArray(ast)) {
    throw new Error('暂不支持 where 中包含多个语句，不应该出现: ' + where)
  }
  if (ast.type !== 'select' || ast.where == null) {
    throw new Error('where 不能为空')
  }
  const whereClause = ast.where
  if (whereClause.type === 'function') {
    throw new Error('暂不支持 where 中只有函数: ' + where)
  }

  // 当 left 为 metric 的时候，需要新建子查询，并拆分 where，这里做个记录
  let isMetricInBinaryExpr = false

  /**
   * 更新 where node 的 AST，递归函数
   * 返回更新后的 AST
   */
  const updateWhereNode = (
    node: Expr | ColumnRef | Param | Value | Function,
    leftOrRight: 'left' | 'right' | 'root',
  ): Expr | ColumnRef | Param | Value | Function => {
    if (leftOrRight === 'root' && node.type !== 'binary_expr') {
      // root 的只支持 'binary_expr' 类型。如果是其他类型，直接返回
      return node
    }
    const nodeType = node.type as WhereNodeType
    switch (nodeType) {
      case 'param':
      case 'unary_expr': // NOT
      case 'interval':
      case 'single_quote_string':
      case 'number':
      case 'null':
      case 'double_quote_string':
      case 'expr_list': // IN (1, 2, 3) 的右值
      case 'function':
        return node
      case 'column_ref': {
        // where 的左值或为 dimension 时，需要把 dimension 替换成 expr，因为 where 在 select 后执行，别名还没有生效
        // where 的右值为 dimension 时，需要把 dimension 替换成 expr。（场景如2个不相关的维度要做比较）
        // where 的左值或右值为 metric 需要创建 metric 子查询
        const theNode = node as ColumnRef
        const dimensionFound = dimensions.find((d) => d.name === theNode.column)
        if (VIRTUAL_TIME_DIM_NAMES.includes(theNode.column)) {
          console.error('大模型提参where的时候不应该包含时间维度', theNode.column)
        }
        if (dimensionFound) {
          theNode.column = dimensionFound.expr // 无论左右，dimension 都替换成 expr
          dimensionNamesInWhere.push(dimensionFound.name)
        }
        const metricFound = metrics.find((m) => m.name === theNode.column)
        if (metricFound) {
          if (leftOrRight === 'left') {
            // 当 where 的左值中用到 metric 的时候，且没有在 metric 当中，需要添加到 select 当中，不然会报错
            metricNamesInWhere.push(metricFound.name)
            isMetricInBinaryExpr = true
          }
          if (leftOrRight === 'right') {
            metricNamesInWhere.push(metricFound.name)
            isMetricInBinaryExpr = true
            // theNode.column = `(SELECT ${this.metric2SqlByType(metricFound)} AS ${metricFound.name} FROM ${this.metricConfig.name})`
          }
        }
        return node
      }
      case 'binary_expr': {
        const theNode = node as Expr
        theNode.left = updateWhereNode(theNode.left, 'left') as Expr
        theNode.right = updateWhereNode(theNode.right, 'right') as Expr
        if (isMetricInBinaryExpr) {
          lastWherePredicts.push(sqlParser.exprToSQL(theNode).replace(/`/g, '')) // 要去掉额外加的 ``
          isMetricInBinaryExpr = false
        }
        return theNode
      }
      default:
        console.error('未知的 nodeType，请添加处理：', nodeType)
        return assertExhaustive(nodeType)
    }
  }

  const updatedNode = updateWhereNode(whereClause, 'root')
  ast.where = updatedNode as Expr
  // 目前先去掉所有的 ``
  const allWhere = sqlParser.sqlify(ast).replace(selectPrefix, '').replace(/`/g, '')
  // 从 allWhere 中剥离出 lastWherePredicts，剩下的就是 sharedWhere
  const allWhereArr = allWhere.split(' AND ')
  const sharedWhere = allWhereArr.filter((w) => !lastWherePredicts.includes(w)).join(' AND ')
  const lastWhere = lastWherePredicts.join(' AND ')
  return {
    ...verifiedMeta,
    whereDerived: {
      metricNames: metricNamesInWhere,
      dimensionNames: dimensionNamesInWhere,
      sharedWhere: sharedWhere.length > 0 ? '(' + sharedWhere + ')' : sharedWhere,
      lastWhere,
    },
  }
}
export class DerivedMetaWhereBuilderPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config

    instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(DerivedMetaWhereBuilderPlugin.name, async (builder) => {
      builder.hooks.onGenDerivedMeta.tapPromise(DerivedMetaWhereBuilderPlugin.name, async (derivedMeta) => {
        return generateWhereDeriveMeta(derivedMeta, metricConfig.getAllNoListMetrics(), metricConfig.allDimensions)
      })
    })
  }
}
