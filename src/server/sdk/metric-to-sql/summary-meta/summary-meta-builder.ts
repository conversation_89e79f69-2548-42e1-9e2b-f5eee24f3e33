import * as tapable from 'tapable'
import { Hookable } from 'src/shared'
import { PeriodOverPeriodType } from 'src/shared/metric-types'
import { DerivedMeta } from '../derived-meta'

/** 第三步：汇总前面提出的 verifiedMetricParams、time、whereDerived 得到最终需要查询的汇总  */
export type SummaryMeta = {
  // 时间的要单独处理，因为时间的 groupBy 要用别名
  time: {
    timeDimensionName: string
    groupBy?: string // 如果是 total，那么不需要 group by 语句
    where: string
    whereForPeriodOverPeriod: string
  }
  groupBys: string[]
  // 集合所有 metric 包括：1.QueryParams 里的 metric; 2.where 中的 metric
  metricNames: string[]
  sharedWhere: string
  lastWhere: string
  limit?: number
  orderBys?: string[] // 格式为 ["name desc", "age asc"], dbt 的格式为 metrics or group bys to order by ("-" prefix for DESC). For example: --order -ds or --order ds,-revenue
  periodOverPeriods?: PeriodOverPeriodType[]
}

export class SummaryMetaBuilder extends Hookable {
  hooks = Object.freeze({
    onSummaryMetaBuild: new tapable.AsyncSeriesBailHook<[DerivedMeta], SummaryMeta | undefined>(['derivedMeta']),
    afterSummaryMetaBuild: new tapable.AsyncParallelHook<[SummaryMeta]>(['derivedMeta']),

    onGenSummaryMeta: new tapable.AsyncSeriesWaterfallHook<[SummaryMeta]>(['derivedMeta']),
    afterGenSummaryMeta: new tapable.AsyncParallelHook<[SummaryMeta]>(['derivedMeta']),
  })

  async invoke(meta: DerivedMeta) {
    let res: SummaryMeta
    res = (await this.hooks.onSummaryMetaBuild.promise(meta)) as SummaryMeta
    await this.hooks.afterSummaryMetaBuild.promise(res)
    res = await this.hooks.onGenSummaryMeta.promise(res)
    await this.hooks.afterGenSummaryMeta.promise(res)
    return res
  }
}
