import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

export class SummaryMetaBaseBuildPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateSummaryMetaBuilder.tapPromise(SummaryMetaBaseBuildPlugin.name, async (builder) => {
      builder.hooks.onSummaryMetaBuild.tapPromise(
        { name: SummaryMetaBaseBuildPlugin.name, stage: Number.MAX_SAFE_INTEGER },
        async (derivedMeta) => {
          return {
            time: {
              timeDimensionName: derivedMeta.timeSqlPart?.timeDimensionName ?? '',
              groupBy: derivedMeta.timeSqlPart?.groupBy,
              where: derivedMeta.timeSqlPart?.where || '',
              whereForPeriodOverPeriod: derivedMeta.timeSqlPart?.whereForPeriodOverPeriod || '',
            },
            // 这里没有加上 whereDerived.dimensionNames
            groupBys: derivedMeta.queryParams.groupBys ?? [],
            metricNames: Array.from(
              new Set([...derivedMeta.queryParams.metricNames, ...derivedMeta.whereDerived.metricNames]),
            ),
            sharedWhere: derivedMeta.whereDerived.sharedWhere,
            lastWhere: derivedMeta.whereDerived.lastWhere,
            limit: derivedMeta.queryParams.limit,
            orderBys: derivedMeta.queryParams.orderBys,
            periodOverPeriods: derivedMeta.queryParams.periodOverPeriods,
          }
        },
      )
    })
  }
}
