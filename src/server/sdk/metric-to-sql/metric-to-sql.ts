import * as tapable from 'tapable'
import { nanoid } from 'nanoid'
import { merge } from 'lodash'
import { Hookable, PluginClass, RowsMetadata, Plugin } from 'src/shared'
import { PROCESS_ENV } from 'src/server/server-constants'
import { MetricConfig, QueryParams, QueryParamsVerified } from 'src/shared/metric-types'
import { DerivedMetaBuilder, DerivedMeta, DerivedMetaPlugin } from './derived-meta'
import { SqlBuilder, SqlPlugin } from './sql'
import { SqlMetaBuilder, SqlMeta, SqlMetaPlugin } from './sql-meta'
import { SummaryMetaBuilder, SummaryMeta, SummaryMetaPlugin } from './summary-meta'
import { VerifiedMetaBuilder, VerifiedMeta, VerifiedMetaPlugin } from './verified-meta'
import { VerifiedQueryParamsBuilder, VerifiedQueryParamsPlugin } from './verified-query-params'
import { QueryParamsBuilder, QueryParamsPlugin } from './query-params'
import { TemplatePlugin, BaowuPlugin, ZhonghuaPlugin, LoggerPlugin, RowsMetadataPlugin } from './plugins'
import { MetricToSqlMetadata } from './metadata'

// 支持 filter 的逻辑，filter 即 where 条件。
// 只考虑单表，所有所有 filter 都来源于一个表。
// 检查提取的 metric 中是否有 filter，如果有，就按如下规则拼接 SQL：
// 检查指标间的依赖关系，如果有派生指标，就生成派生指标间的依赖关系
// 1. 判断是否需要生成维度虚拟表：不需要生成的场景为：有多个普通指标且所有指标都没有filter，其他情况都需要生成维度虚拟表。生成维度虚拟表的方法为：查询 groupBys 中的码值，写一个子查询。select distinct groupBy1, groupBy2 from table。
// 1.1：如果 groupBys + time groupBy 为空，那么就直接查询出指标，然后使用 cross join 生成一行数据，相当于列合并。
// 1.2 判断 where 的 left or right 是否有指标，如果有，就把这个指标加到依赖树中，也做成子查询。
// 1.3 AVG 先不做特殊处理，直接求 AVG
// 2. 派生指标的计算，需要多个表 join，方法是先用 groupBys 中码值查询出维度的虚拟表，然后再 join 指标表，join 完成后去掉指标都为 null 的行。此步需要拼接 groupBy
// 3. 普通指标的计算，直接使用子查询加上 filter 即可。此步需要拼接 groupBy
// 4. 最后大查询，把用到的 groupBys 和 metric 都 select 出来。
// 所以当 groupBys + time 非空的时候，需要3部分：维度虚拟表，依赖关系=>指标虚拟表，最后大查询。
// where 要下推，全局的 where 除了指标的过滤之外，其他的都下推到具体的 where 中

/* 同环比的实现逻辑：只支持月和季度的同环比，不支持年和日的同环比。
 // 目前只支持原子指标的同环比
 提问的方法：环比，环比增长额，同比，同比增长额
 1. 上个月的 revenue、cost 以及同环比
 2. 最近3个月的 revenue 和同环比
 3. 最近3个月各部门的 revenue 和同环比
 判断用户提问的日期，需要按照月/季来聚合。如果没有日期，就使用上个月
 把同环比的指标和原来的指标放在同一个指标虚拟表的 sql 中。同环比有3类：
    3.1 上个周期的实际值 pre_month_revenue/pre_year_month_revenue/pre_quarter_revenue/pre_year_quarter_revenue
    3.2 月的变化量和比值 mom_growth_revenue/yoy_month_growth_revenue/mom_growth_rate_revenue/yoy_month_growth_rate_revenue
    3.3 季的变化量和比值 qoq_growth_revenue/yoy_quarter_growth_revenue/qoq_growth_rate_revenue/yoy_quarter_growth_rate_revenue
 4. 指标的计算逻辑：
   1. 先扩充日期，往前推 13 个月、5 个季度。
   2. join 两个表查询
   3. 计算 growth 和 growth_rate
 */

export interface MetricToSqlConfig {
  id: string
  metricConfig: MetricConfig
  QueryParamsBuilder: typeof QueryParamsBuilder
  VerifiedQueryParamsBuilder: typeof VerifiedQueryParamsBuilder
  VerifiedMetaBuilder: typeof VerifiedMetaBuilder
  DerivedMetaBuilder: typeof DerivedMetaBuilder
  SummaryMetaBuilder: typeof SummaryMetaBuilder
  SqlMetaBuilder: typeof SqlMetaBuilder
  SqlBuilder: typeof SqlBuilder
  MetricToSqlMetadata: typeof MetricToSqlMetadata
  plugins: PluginClass<MetricToSql>[]
  version?: '2024' | '2025'
}

export const getDefaultMetricToSqlConfig: () => Partial<MetricToSqlConfig> = () => ({
  id: nanoid(),
  QueryParamsBuilder,
  VerifiedQueryParamsBuilder,
  VerifiedMetaBuilder,
  DerivedMetaBuilder,
  SummaryMetaBuilder,
  SqlMetaBuilder,
  SqlBuilder,
  MetricToSqlMetadata,
  plugins: [
    new LoggerPlugin(),
    new RowsMetadataPlugin(),
    new TemplatePlugin(),
    new QueryParamsPlugin(),
    new VerifiedQueryParamsPlugin(),
    new VerifiedMetaPlugin(),
    new DerivedMetaPlugin(),
    new SummaryMetaPlugin(),
    new SqlMetaPlugin(),
    new SqlPlugin(),
    new BaowuPlugin(),
    new ZhonghuaPlugin(),
  ],
  version: PROCESS_ENV.METRIC_TO_SQL_DEFAULT_VERSION as MetricToSqlConfig['version'],
})

export class MetricToSql extends Hookable {
  config!: MetricToSqlConfig
  // knex = knex({})
  hooks = Object.freeze({
    beforeInvoke: new tapable.AsyncParallelHook<[QueryParams]>(['queryParams']),
    afterInvoke: new tapable.AsyncParallelHook<[{ sql: string; rowsMetadata: RowsMetadata }]>(['result']),

    onPluginUse: new tapable.SyncWaterfallHook<Plugin<this>>(['plugin']),
    afterPluginUse: new tapable.SyncHook<Plugin<this>>(['plugin']),

    onNormalizeConfig: new tapable.SyncWaterfallHook<MetricToSqlConfig>(['config']),
    afterNormalizeConfig: new tapable.SyncHook<MetricToSqlConfig>(['config']),

    onCreateQueryParamsBuilder: new tapable.AsyncSeriesWaterfallHook<[QueryParamsBuilder]>(['builder']),
    afterCreateQueryParamsBuilder: new tapable.AsyncParallelHook<[QueryParamsBuilder]>(['builder']),

    onCreateVerifiedQueryParamsBuilder: new tapable.AsyncSeriesWaterfallHook<[VerifiedQueryParamsBuilder, QueryParams]>(
      ['builder', 'queryParams'],
    ),
    afterCreateVerifiedQueryParamsBuilder: new tapable.AsyncParallelHook<[VerifiedQueryParamsBuilder]>(['builder']),

    onCreateVerifiedMetaBuilder: new tapable.AsyncSeriesWaterfallHook<[VerifiedMetaBuilder, QueryParamsVerified]>([
      'builder',
      'queryParamsVerified',
    ]),
    afterCreateVerifiedMetaBuilder: new tapable.AsyncParallelHook<[VerifiedMetaBuilder]>(['builder']),

    onCreateDerivedMetaBuilder: new tapable.AsyncSeriesWaterfallHook<[DerivedMetaBuilder, VerifiedMeta]>([
      'builder',
      'meta',
    ]),
    afterCreateDerivedMetaBuilder: new tapable.AsyncParallelHook<[DerivedMetaBuilder]>(['builder']),

    onCreateSummaryMetaBuilder: new tapable.AsyncSeriesWaterfallHook<[SummaryMetaBuilder, VerifiedMeta]>([
      'builder',
      'meta',
    ]),
    afterCreateSummaryMetaBuilder: new tapable.AsyncParallelHook<[SummaryMetaBuilder]>(['builder']),

    onCreateSqlMetaBuilder: new tapable.AsyncSeriesWaterfallHook<[SqlMetaBuilder, SummaryMeta]>(['builder', 'meta']),
    afterCreateSqlMetaBuilder: new tapable.AsyncParallelHook<[SqlMetaBuilder]>(['builder']),

    onCreateSqlBuilder: new tapable.AsyncSeriesWaterfallHook<[SqlBuilder, SqlMeta]>(['sqlBuilder', 'sqlMeta']),
    afterCreateSqlBuilder: new tapable.AsyncParallelHook<[SqlBuilder]>(['builder']),

    onCreateMetadata: new tapable.AsyncSeriesWaterfallHook<[MetricToSqlMetadata]>(['metadata']),
    afterCreateMetadata: new tapable.AsyncParallelHook<[MetricToSqlMetadata]>(['metadata']),
  })

  constructor(config: Partial<MetricToSqlConfig>)
  constructor(config: MetricConfig)
  constructor(config: any) {
    super()
    this.normalizeConfig(config)
    for (const plugin of this.config.plugins) {
      this.use(plugin)
    }
  }

  async createMetadata() {
    const metadata = await this.hooks.onCreateMetadata.promise(new this.config.MetricToSqlMetadata())
    await this.hooks.afterCreateMetadata.promise(metadata)
    return metadata
  }

  private async normalizeConfig(config: any) {
    if (config instanceof MetricConfig) config = { metricConfig: config }
    this.config = this.hooks.onNormalizeConfig.call(merge(getDefaultMetricToSqlConfig(), config))
    this.hooks.afterNormalizeConfig.call(this.config)
  }

  async createQueryParamsBuilder() {
    const builder = await this.hooks.onCreateQueryParamsBuilder.promise(new this.config.QueryParamsBuilder())
    await this.hooks.afterCreateQueryParamsBuilder.promise(builder)
    return builder
  }

  async createVerifiedQueryParamsBuilder(queryParams: QueryParams) {
    const builder = await this.hooks.onCreateVerifiedQueryParamsBuilder.promise(
      new this.config.VerifiedQueryParamsBuilder(),
      queryParams,
    )
    await this.hooks.afterCreateVerifiedQueryParamsBuilder.promise(builder)
    return builder
  }

  async createVerifiedMetaBuilder(queryParamsVerified: QueryParamsVerified) {
    const builder = await this.hooks.onCreateVerifiedMetaBuilder.promise(
      new this.config.VerifiedMetaBuilder(),
      queryParamsVerified,
    )
    await this.hooks.afterCreateVerifiedMetaBuilder.promise(builder)
    return builder
  }

  async createDerivedMetaBuilder(meta: VerifiedMeta) {
    const builder = await this.hooks.onCreateDerivedMetaBuilder.promise(new this.config.DerivedMetaBuilder(), meta)
    await this.hooks.afterCreateDerivedMetaBuilder.promise(builder)
    return builder
  }

  async createSummaryMetaBuilder(meta: DerivedMeta) {
    const builder = await this.hooks.onCreateSummaryMetaBuilder.promise(new this.config.SummaryMetaBuilder(), meta)
    await this.hooks.afterCreateSummaryMetaBuilder.promise(builder)
    return builder
  }

  async createSqlMetaBuilder(meta: SummaryMeta) {
    const builder = await this.hooks.onCreateSqlMetaBuilder.promise(new this.config.SqlMetaBuilder(), meta)
    await this.hooks.afterCreateSqlMetaBuilder.promise(builder)
    return builder
  }

  async createSqlBuilder(meta: SqlMeta) {
    const builder = await this.hooks.onCreateSqlBuilder.promise(new this.config.SqlBuilder(), meta)
    await this.hooks.afterCreateSqlBuilder.promise(builder)
    return builder
  }

  async invoke(queryParams: QueryParams) {
    await this.hooks.beforeInvoke.promise(queryParams)

    const metadata = await this.createMetadata()

    const queryParamsBuilder = await this.createQueryParamsBuilder()
    queryParams = await queryParamsBuilder.invoke(queryParams)

    const verifiedQueryParamsBuilder = await this.createVerifiedQueryParamsBuilder(queryParams)
    const verifiedQueryParams = await verifiedQueryParamsBuilder.invoke(queryParams)

    const metricParamsBuilder = await this.createVerifiedMetaBuilder(verifiedQueryParams)
    const verifiedMeta = await metricParamsBuilder.invoke(verifiedQueryParams)

    const derivedMetaBuilder = await this.createDerivedMetaBuilder(verifiedMeta)
    const derivedMeta = await derivedMetaBuilder.invoke(verifiedMeta)

    const summaryMetaBuilder = await this.createSummaryMetaBuilder(derivedMeta)
    const summaryMeta = await summaryMetaBuilder.invoke(derivedMeta)

    const sqlMetaBuilder = await this.createSqlMetaBuilder(summaryMeta)
    const sqlMeta = await sqlMetaBuilder.invoke(summaryMeta)

    const sqlBuilder = await this.createSqlBuilder(sqlMeta)
    const sql = await sqlBuilder.invoke(sqlMeta)

    const res = { sql, rowsMetadata: metadata.rowsMetadata, verifiedQueryParams }

    // console.log('='.repeat(20))
    // console.log(queryParams)
    // console.log(verifiedQueryParams)
    // console.log(verifiedMeta)
    // console.log(derivedMeta)
    // console.log(summaryMeta)
    // console.log(sqlMeta)
    // console.log(sql)
    await this.hooks.afterInvoke.promise(res)
    return res
  }
  // compat
  async toSql(...args: Parameters<typeof this.invoke>) {
    return this.invoke(...args)
  }
}

export class Metric2Sql extends MetricToSql {}
