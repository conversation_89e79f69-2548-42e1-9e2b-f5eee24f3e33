import * as tapable from 'tapable'
import { Hookable } from 'src/shared'
import { QueryParams, QueryParamsVerified } from 'src/shared/metric-types'

export class VerifiedQueryParamsBuilder extends Hookable {
  hooks = Object.freeze({
    onVerifiedQueryParamsBuild: new tapable.AsyncSeriesBailHook<[QueryParams], QueryParamsVerified | undefined>([
      'queryParamsVerified',
    ]),
    afterVerifiedQueryParamsBuild: new tapable.AsyncParallelHook<[QueryParamsVerified]>(['queryParamsVerified']),

    onGenVerifiedQueryParams: new tapable.AsyncSeriesWaterfallHook<[QueryParamsVerified]>(['queryParamsVerified']),
    afterGenVerifiedQueryParams: new tapable.AsyncParallelHook<[QueryParamsVerified]>(['queryParamsVerified']),
  })

  async invoke(queryParams: QueryParams) {
    let res: QueryParamsVerified
    res = (await this.hooks.onVerifiedQueryParamsBuild.promise(queryParams)) as QueryParamsVerified
    await this.hooks.afterVerifiedQueryParamsBuild.promise(res)
    res = await this.hooks.onGenVerifiedQueryParams.promise(res)
    await this.hooks.afterGenVerifiedQueryParams.promise(res)
    return res
  }
}
