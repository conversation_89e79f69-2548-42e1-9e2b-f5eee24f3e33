import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

/**
 * metricNames 和 groupBys 不能同时为空
 */
export class VerifiedQueryParamsCheckerPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateVerifiedQueryParamsBuilder.tapPromise(
      VerifiedQueryParamsCheckerPlugin.name,
      async (builder) => {
        builder.hooks.afterGenVerifiedQueryParams.tapPromise(
          VerifiedQueryParamsCheckerPlugin.name,
          async (queryParamsVerified) => {
            const {
              queryParams: { metricNames, groupBys = [] },
              extraParams,
            } = queryParamsVerified
            // metricNames 和 groupBys 不能同时为空
            if (metricNames.length === 0 && groupBys.length === 0) {
              throw new Error('metricNames 和 groupBys 不能同时为空')
            }
            if (extraParams.extraMetricNames.length > 0) {
              throw new Error(`metricNames ${JSON.stringify(extraParams.extraMetricNames)} 不存在`)
            }
            if (extraParams.extraGroupBys.length > 0) {
              throw new Error(`groupBys ${extraParams.extraGroupBys} 不存在`)
            }
          },
        )
      },
    )
  }
}
