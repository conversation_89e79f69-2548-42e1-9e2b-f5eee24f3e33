import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { ExcludeDateDimensions } from '../../utils'

/**
 * 找出提参中不存在的指标和维度放到 extraParams 中
 */
export class ExtractExtraParamsPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    instance.hooks.afterCreateVerifiedQueryParamsBuilder.tapPromise(ExtractExtraParamsPlugin.name, async (builder) => {
      builder.hooks.onGenVerifiedQueryParams.tapPromise(ExtractExtraParamsPlugin.name, async (queryParamsVerified) => {
        const { metricNames, groupBys = [], orderBys = [] } = queryParamsVerified.queryParams

        const extraMetricNames = metricNames.filter((name) => !metricConfig.hasMetric(name))

        const extraGroupBys = groupBys.filter(
          (name) => !metricConfig.hasDimension(name) && !ExcludeDateDimensions.includes(name),
        )
        const extraOrderBys = orderBys.filter((name) => {
          const colName = name.split(' ')[0] // 去掉 asc、desc
          return (
            !metricConfig.hasMetric(colName) &&
            !metricConfig.hasDimension(colName) &&
            !ExcludeDateDimensions.includes(colName)
          )
        })

        queryParamsVerified.extraParams = {
          extraMetricNames,
          extraGroupBys,
          extraOrderBys,
        }

        return queryParamsVerified
      })
    })
  }
}
