import { cloneDeep } from 'lodash'
import { QueryParams } from 'src/shared/metric-types'
import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

/**
 * 基础校验QueryParams生成QueryParamsVerified
 */
export class QueryParamsVerifyBaseBuildPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    let originalQueryParams: QueryParams
    instance.hooks.beforeInvoke.tapPromise(QueryParamsVerifyBaseBuildPlugin.name, async (queryParams) => {
      originalQueryParams = cloneDeep(queryParams)
    })

    instance.hooks.afterCreateVerifiedQueryParamsBuilder.tapPromise(
      QueryParamsVerifyBaseBuildPlugin.name,
      async (builder) => {
        builder.hooks.onVerifiedQueryParamsBuild.tapPromise(
          {
            name: QueryParamsVerifyBaseBuildPlugin.name,
            stage: Number.MAX_SAFE_INTEGER,
          },
          async (queryParams) => {
            return {
              queryParams,
              originalQueryParams: originalQueryParams ?? cloneDeep(queryParams),
              extraParams: {
                extraMetricNames: [],
                extraGroupBys: [],
                extraOrderBys: [],
              },
            }
          },
        )
      },
    )
  }
}
