import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { QueryParamsVerifyBaseBuildPlugin } from './query-params-verify-base-build-plugin'
import { VerifiedQueryParamsCheckerPlugin } from './verified-query-params-checker-plugin'
import { ExtractExtraParamsPlugin } from './extract-extra-params-plugin'

export class VerifiedQueryParamsPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance
      .use(new QueryParamsVerifyBaseBuildPlugin())
      .use(new VerifiedQueryParamsCheckerPlugin())
      .use(new ExtractExtraParamsPlugin())
  }
}
