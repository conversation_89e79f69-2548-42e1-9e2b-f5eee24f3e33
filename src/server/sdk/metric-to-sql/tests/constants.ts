import { nanoid } from 'nanoid'
import { DEFAULT_FORMAT_DECIMAL, DEFAULT_FORMAT_RATIO } from 'src/shared/common-utils'
import {
  Dimension,
  NoListMetric,
  ListMetric,
  ExternalReport,
  TimeDimensionDatum,
  Measure,
  SemanticModel,
  MetricConfig,
} from 'src/shared/metric-types'

export const allDimensions: Dimension[] = [
  {
    id: nanoid(),
    name: 'area_id',
    label: 'area id',
    synonyms: [],
    filterSwitch: true,
    description: 'area id description',
    typeParams: {},
    type: 'categorical',
    expr: 'area_id_column',
  },
  {
    id: nanoid(),
    name: 'shop_id',
    label: 'shop id',
    synonyms: [],
    filterSwitch: true,
    description: 'shop id description',
    typeParams: {},
    type: 'categorical',
    expr: 'shop_id_column',
  },
  {
    id: nanoid(),
    name: 'login_dt',
    label: 'login time',
    synonyms: [],
    filterSwitch: true,
    description: 'fake login time',
    typeParams: {
      timeGranularity: 'day',
      timeType: 'string',
      timeFormat: 'yyyy_MM_DD',
    },
    type: 'time',
    expr: 'time_column',
  },
  {
    id: nanoid(),
    name: 'pay_dt',
    label: 'pay time',
    synonyms: [],
    filterSwitch: true,
    description: 'fake pay time',
    typeParams: {
      timeGranularity: 'day',
      timeType: 'string',
      timeFormat: 'yyyy/MM/DD',
    },
    type: 'time',
    expr: 'pay_dt',
  },
]
export const allMeasures: Measure[] = [
  {
    id: nanoid(),
    name: 'cost_sum',
    label: 'cost label',
    synonyms: [],
    expr: 'SUM(cost_column)',
    agg: 'sum',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'revenue_sum',
    label: 'revenue label',
    synonyms: [],
    expr: 'SUM(sales_data.revenue_sum)',
    agg: 'sum',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'balance_sum',
    label: 'balance label',
    synonyms: [],
    expr: 'SUM(balance_column)',
    agg: 'sum',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'revenue_avg',
    label: 'revenue avg label',
    synonyms: [],
    expr: 'AVG(revenue_column)',
    agg: 'avg',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
  {
    id: nanoid(),
    name: 'revenue_max',
    label: 'revenue max label',
    synonyms: [],
    expr: 'MAX(revenue_column)',
    agg: 'max',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
  },
]
export const semanticModel: SemanticModel = {
  name: 'sales_data',
  tableName: 'vt_test_sales_table',
  sourceCreateTime: 1719563575847,
}

export const allNoListMetrics: NoListMetric[] = [
  {
    id: nanoid(),
    name: 'cost_sum',
    label: 'cost label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'cost_sum',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'shop_40_cost_sum',
    label: 'cost label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'cost_sum',
    },
    filter: "shop_id_column = 'shop_40'",
    rank: -1,
    keypoint: false,
    category: '类目1%1',
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'shop_30_cost_sum',
    label: 'cost label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'cost_sum',
    },
    filter: "shop_id_column = 'shop_30'",
    rank: -1,
    keypoint: false,
    category: '类目2%2',
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'revenue_sum',
    label: 'revenue label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_sum',
    },
    rank: -1,
    keypoint: false,
    category: '类目3%3',
    isCumulative: true,
    updatedAt: new Date(),
  },

  {
    id: nanoid(),
    name: 'revenue_sum2',
    label: 'revenue label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_sum',
    },
    rank: -1,
    keypoint: false,
    category: '类目3%3',
    isCumulative: true,
    updatedAt: new Date(),
    filter: 'shop_id IN ("shop_45")',
  },

  {
    id: nanoid(),
    name: 'balance_sum',
    label: 'balance label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'balance_sum',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'revenue_avg',
    label: 'revenue avg label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_avg',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'revenue_max',
    label: 'revenue max label',
    synonyms: [],
    type: 'simple',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      measure: 'revenue_max',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'profit',
    label: 'profit metric',
    synonyms: [],
    description: 'cost metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      expr: 'revenue_sum - cost_sum',
      metrics: [
        {
          name: 'revenue_sum',
        },
        {
          name: 'cost_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'profit_increment',
    label: 'profit increment',
    synonyms: [],
    description: 'profit increment metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      expr: 'revenue_sum / cost_sum - 1',
      metrics: [
        {
          name: 'revenue_sum',
        },
        {
          name: 'cost_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'shop_30_profit',
    label: 'shop_30 profit metric',
    synonyms: [],
    description: 'shop_30 profit metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    updatedAt: new Date(),
    typeParams: {
      expr: 'revenue_sum - shop_30_cost_sum',
      metrics: [
        {
          name: 'revenue_sum',
        },
        {
          name: 'shop_30_cost_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
  },
  {
    id: nanoid(),
    name: 'balance_ratio',
    label: 'balance ratio metric',
    synonyms: [],
    description: 'balance ratio metric description',
    type: 'ratio',
    formatTemplate: DEFAULT_FORMAT_RATIO,
    typeParams: {
      numerator: 'balance_sum',
      denominator: 'balance_sum',
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
  {
    id: nanoid(),
    name: 'zoo',
    label: 'zoo metric',
    synonyms: [],
    description: 'cost metric description',
    type: 'derived',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      expr: 'balance_ratio + revenue_sum2 + profit / cost_sum',
      metrics: [
        {
          name: 'profit',
        },
        {
          name: 'balance_ratio',
        },
        {
          name: 'cost_sum',
        },
        {
          name: 'revenue_sum2',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    isCumulative: false,
    updatedAt: new Date(),
  },
]

export const listMetrics: ListMetric[] = [
  {
    id: nanoid(),
    name: 'money_list',
    label: 'money list',
    synonyms: [],
    type: 'list',
    formatTemplate: DEFAULT_FORMAT_DECIMAL,
    typeParams: {
      metrics: [
        {
          name: 'cost_sum',
        },
        {
          name: 'revenue_sum',
        },
      ],
    },
    rank: -1,
    keypoint: false,
    category: undefined,
    updatedAt: new Date(),
  },
]

export const allMetrics = [...allNoListMetrics, ...listMetrics]

export const allExternalReports: ExternalReport[] = [
  {
    id: nanoid(),
    name: 'external_report1',
    label: 'external_report1',
    synonyms: [],
    type: 'baowu_report',
    createdAt: new Date(),
    updatedAt: new Date(),
    semanticSceneId: '',
    semanticProjectId: '',
  },
]

export const timeDimensionDatum: TimeDimensionDatum = {
  timeDimensionName: 'order_dt',
  timeDimensionType: 'string',
  timeDimensionFormat: 'yyyyMM',
  timeGranularityMin: 'month',
}

export const timeDimensionDatumYyyyMMDD: TimeDimensionDatum = {
  timeDimensionName: 'order_dt',
  timeDimensionType: 'string',
  timeDimensionFormat: 'yyyy-MM-DD',
  timeGranularityMin: 'day',
}

export const timeDimensionDatumYyyyMMDDSlash: TimeDimensionDatum = {
  timeDimensionName: 'order_dt',
  timeDimensionType: 'string',
  timeDimensionFormat: 'yyyy/MM/DD',
  timeGranularityMin: 'day',
}

export const metricConfig = new MetricConfig(
  nanoid(),
  semanticModel.name,
  allDimensions,
  allMeasures,
  allNoListMetrics,
  allNoListMetrics,
  allExternalReports,
  0,
  timeDimensionDatum,
)
