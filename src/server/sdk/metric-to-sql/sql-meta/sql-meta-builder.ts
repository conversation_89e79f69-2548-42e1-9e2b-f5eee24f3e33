import * as tapable from 'tapable'
// import knex from 'knex'
import { Hookable, RowsMetadata } from 'src/shared'
import { PeriodOverPeriodType } from 'src/shared/metric-types'
import { SummaryMeta } from '../summary-meta'

/* 第四步：生成最终 SQL 的元信息，包含3部分：维度虚拟表、依赖的指标虚拟表、最后的 SQL
 * 处理派生指标，生成所有指标间的依赖关系
 * 判断是否要生成维度虚拟表，以及做基础的提参和时间提参
 */
export type SqlMeta = {
  /** 判断是否需要生成维度虚拟表：不需要生成的场景为：全部为普通指标且都没有filter，其他情况都需要生成维度虚拟表。生成维度虚拟表的方法为：查询 groupBys 中的码值，写一个子查询。select distinct groupBy1, groupBy2 from table。 */
  isAllSimpleNoFilterMetrics: boolean
  /** 找出派生指标依赖的指标，并按指标的依赖关系，组织成按照依赖顺序的 Metric 列表，下面的 Metric 可以依赖上面的，后续为每一个元素创作一个 metric CTE 虚拟表，这里面包含了派生指标依赖的指标 */
  tableMetricNames: string[]
  // metricNames: string[]
  timeGroupBy?: string // 时间的 groupBy 使用别名
  timeWhereForPeriodOverPeriod: string // 时间的 where
  sharedGroupBys: string[] // 除 lastSqlPart 之外都复用的 groupBys，会包含 where 中用到的维度
  sharedWhereWithTime: string // 除 lastSqlPart 之外都复用的 where
  sharedWhereWithoutTime: string // 除去时间过滤的 where，用于同环比的计算
  // lastSql 不需要 groupBy，因为前面的查询都做过了 groupBy
  lastSqlPart: {
    select: string
    metricNames: string[] // 需要 select 出来的 metric 列表
    // lastSql 单独使用的 where，就是 left 或者 right 值有 metric 的 where
    where?: string
    // groupBys: string[]
    orderBys: string[]
    limit?: number
  }
  // 每一列的元信息，对应 lastSqlPart 的 select 部分
  rowsMetadata: RowsMetadata
  periodOverPeriods?: PeriodOverPeriodType[]
  // where中是否有指标
  isMetricInWhere: boolean
}

export class SqlMetaBuilder extends Hookable {
  hooks = Object.freeze({
    onSqlMetaBuild: new tapable.AsyncSeriesBailHook<[SummaryMeta], SqlMeta | undefined>(['sqlMeta']),
    afterSqlMetaBuild: new tapable.AsyncParallelHook<[SqlMeta]>(['sqlMeta']),

    onGenSqlMeta: new tapable.AsyncSeriesWaterfallHook<[SqlMeta]>(['sqlMeta']),
    afterGenSqlMeta: new tapable.AsyncParallelHook<[SqlMeta]>(['sqlMeta']),
  })

  async invoke(meta: SummaryMeta) {
    let res: SqlMeta
    res = (await this.hooks.onSqlMetaBuild.promise(meta)) as SqlMeta
    await this.hooks.afterSqlMetaBuild.promise(res)
    res = await this.hooks.onGenSqlMeta.promise(res)
    await this.hooks.afterGenSqlMeta.promise(res)
    return res
  }
}
