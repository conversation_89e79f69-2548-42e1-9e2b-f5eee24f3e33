import { PluginClass } from 'src/shared'
import { DATE_ALIAS, PeriodOverPeriodMetricConfig } from 'src/shared/metric-types'
import { MetricToSql } from '../../metric-to-sql'
import { MetricToSqlMetadata } from '../../metadata'

export class SqlMetaBaseBuildPlugin implements PluginClass<MetricToSql> {
  metadata!: MetricToSqlMetadata

  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config

    instance.hooks.afterCreateMetadata.tapPromise(SqlMetaBaseBuildPlugin.name, async (meta) => {
      this.metadata = meta
    })

    instance.hooks.afterCreateSqlMetaBuilder.tapPromise(SqlMetaBaseBuildPlugin.name, async (builder) => {
      builder.hooks.onSqlMetaBuild.tapPromise(
        { name: SqlMetaBaseBuildPlugin.name, stage: Number.MAX_SAFE_INTEGER },
        async (summaryMeta) => {
          const metrics = metricConfig.getAllNoListMetrics()
          const isAllSimpleNoFilterMetrics =
            metrics.filter((m) => summaryMeta.metricNames.includes(m.name) && m.type === 'simple' && m.filter == null)
              .length === summaryMeta.metricNames.length
          const tableMetricNames: string[] = metricConfig.getOrderedMetrics(summaryMeta.metricNames).levels.flat()
          // 如果指标列表为空 需要注意','
          let selectElements: string[] = []
          if (summaryMeta.time.groupBy) {
            // 加上日期提参的 group by
            selectElements.push('DIM_TBL.' + DATE_ALIAS)
          }
          if (summaryMeta.groupBys.length > 0) {
            selectElements = selectElements.concat(summaryMeta.groupBys.map((groupBy) => 'DIM_TBL.' + groupBy))
          }
          // 添加 metric
          summaryMeta.metricNames.forEach((name) => {
            const metric = metrics.find((m) => m.name === name)
            if (!metric) {
              throw new Error(`metric ${name} not found`)
            }
            selectElements.push(`${name}_TBL.${name}`)

            // 添加同环比的指标
            if (summaryMeta.periodOverPeriods) {
              summaryMeta.periodOverPeriods.forEach((type) => {
                selectElements.push(`${name}_TBL.${PeriodOverPeriodMetricConfig[type].metricPrefix}${name}`)
              })
            }
          })

          return {
            isAllSimpleNoFilterMetrics,
            tableMetricNames: tableMetricNames,
            timeGroupBy: summaryMeta.time.groupBy,
            timeWhereForPeriodOverPeriod: summaryMeta.time.whereForPeriodOverPeriod,
            sharedGroupBys: summaryMeta.groupBys,
            sharedWhereWithTime: [summaryMeta.time.where, summaryMeta.sharedWhere]
              .filter((w) => w.length > 0)
              .join(' AND '),
            sharedWhereWithoutTime: summaryMeta.sharedWhere,
            lastSqlPart: {
              select: selectElements.length > 0 ? selectElements.join(', ') : '*',
              metricNames: summaryMeta.metricNames,
              where: summaryMeta.lastWhere,
              // groupBys: summaryMeta.groupBys,
              limit: summaryMeta.limit,
              orderBys: summaryMeta.orderBys || [],
            },
            rowsMetadata: this.metadata.rowsMetadata,
            periodOverPeriods: summaryMeta.periodOverPeriods,
            isMetricInWhere: false,
          }
        },
      )
    })
  }
}
