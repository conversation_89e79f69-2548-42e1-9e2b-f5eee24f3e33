import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { SqlMetaBaseBuildPlugin } from './sql-meta-base-build-plugin'
import { SqlMetaHavingCheckingPlugin } from './sql-meta-having-checking-plugin'

export class SqlMetaPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.use(new SqlMetaBaseBuildPlugin()).use(new SqlMetaHavingCheckingPlugin())
  }
}
