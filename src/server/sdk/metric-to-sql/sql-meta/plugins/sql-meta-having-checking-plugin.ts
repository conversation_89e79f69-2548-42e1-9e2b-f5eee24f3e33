import { PluginClass } from 'src/shared'
import { DerivedMeta, MetricToSql } from '../../../metric-to-sql'

/**
 * 整个指标拼接都不支持Having，都是采用多个指标CTE join得到最终大宽表中，在其中过滤实现having的效果
 */
export class SqlMetaHavingCheckingPlugin implements PluginClass<MetricToSql> {
  derivedMeta!: DerivedMeta
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(SqlMetaHavingCheckingPlugin.name, async (builder) => {
      builder.hooks.afterGenDerivedMeta.tapPromise(SqlMetaHavingCheckingPlugin.name, async (derivedMeta) => {
        this.derivedMeta = derivedMeta
      })
    })

    instance.hooks.afterCreateSqlMetaBuilder.tapPromise(SqlMetaHavingCheckingPlugin.name, async (builder) => {
      builder.hooks.onGenSqlMeta.tapPromise(SqlMetaHavingCheckingPlugin.name, async (sqlMeta) => {
        if (this.derivedMeta.whereDerived.metricNames.length > 0) {
          sqlMeta.isMetricInWhere = true
        }
        return sqlMeta
      })
    })
  }
}
