import * as tapable from 'tapable'
import { Hookable } from 'src/shared'
import { TimeSqlPart, QueryParamsVerified, QueryParams } from 'src/shared/metric-types'

/** 第一步：从大模型提参的参数进行校验和检测，过滤出需要的参数 */
export type VerifiedMeta = {
  queryParams: QueryParams
  timeSqlPart?: TimeSqlPart
}

export class VerifiedMetaBuilder extends Hookable {
  hooks = Object.freeze({
    onVerifiedMetaBuild: new tapable.AsyncSeriesBailHook<[QueryParamsVerified], VerifiedMeta | undefined>([
      'queryParamsVerified',
    ]),
    afterVerifiedMetaBuild: new tapable.AsyncParallelHook<[VerifiedMeta]>(['verifiedMeta']),

    onGenVerifiedMeta: new tapable.AsyncSeriesWaterfallHook<[VerifiedMeta]>(['verifiedMeta']),
    afterGenVerifiedMeta: new tapable.AsyncParallelHook<[VerifiedMeta]>(['verifiedMeta']),
  })

  async invoke(queryParams: QueryParamsVerified) {
    let res: VerifiedMeta
    res = (await this.hooks.onVerifiedMetaBuild.promise(queryParams)) as VerifiedMeta
    await this.hooks.afterVerifiedMetaBuild.promise(res)
    res = await this.hooks.onGenVerifiedMeta.promise(res)
    await this.hooks.afterGenVerifiedMeta.promise(res)
    return res
  }
}
