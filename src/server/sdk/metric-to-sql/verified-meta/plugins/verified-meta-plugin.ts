import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { VerifiedMetaBaseBuildPlugin } from './verified-meta-base-build-plugin'
import { TimeSqlPartBuilderPlugin } from './time-sql-part-builder-plugin'

export class VerifiedMetaPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.use(new VerifiedMetaBaseBuildPlugin()).use(new TimeSqlPartBuilderPlugin())
  }
}
