import { PluginClass } from 'src/shared'
import { isTimeDimension, TimeDimension, TimeSqlPart } from 'src/shared/metric-types'
import Time2Sql from 'src/server/MetricStore/metric2sql/time2sql'
import { MetricToSql } from '../../metric-to-sql'

/**
 * 把 timeQueryParams 转换后的 groupBy 和 where 添加到 queryParams 的 groupBys 和 where 当中
 * 如果 groupBys 为空，只有 timeQueryParams 中的 groupBy，那么就加一个时间排序。
 */
export class TimeSqlPartBuilderPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    instance.hooks.afterCreateVerifiedMetaBuilder.tapPromise(TimeSqlPartBuilderPlugin.name, async (builder) => {
      builder.hooks.onGenVerifiedMeta.tapPromise(TimeSqlPartBuilderPlugin.name, async (verifiedMeta) => {
        const timeQueryParams = verifiedMeta.queryParams.timeQueryParams

        let timeSqlPart: TimeSqlPart | undefined
        const timeDimensions: TimeDimension[] = metricConfig.allDimensions.filter(isTimeDimension)
        if (metricConfig.timeDimensionDatum != null) {
          const time2sql = new Time2Sql(metricConfig.timeDimensionDatum, timeDimensions)
          timeSqlPart = timeQueryParams && time2sql.toSql(timeQueryParams)
        }

        verifiedMeta.timeSqlPart = timeSqlPart
        return verifiedMeta
      })
    })
  }
}
