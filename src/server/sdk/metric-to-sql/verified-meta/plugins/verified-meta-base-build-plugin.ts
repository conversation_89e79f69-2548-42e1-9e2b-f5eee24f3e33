import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

export class VerifiedMetaBaseBuildPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateVerifiedMetaBuilder.tapPromise(VerifiedMetaBaseBuildPlugin.name, async (builder) => {
      builder.hooks.onVerifiedMetaBuild.tapPromise(
        {
          name: VerifiedMetaBaseBuildPlugin.name,
          stage: Number.MAX_SAFE_INTEGER,
        },
        async (queryParamsVerified) => {
          return {
            queryParams: queryParamsVerified.queryParams,
          }
        },
      )
    })
  }
}
