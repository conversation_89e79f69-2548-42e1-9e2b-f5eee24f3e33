import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

/**
 * HACK: 大模型不知道2024年是闰月，经常提成2024.2.28 手动转换成 2024.2.29，方便后续处理
 */
export class LeapYearResolverPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(LeapYearResolverPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(LeapYearResolverPlugin.name, async (queryParams) => {
        if (queryParams.timeQueryParams && queryParams.timeQueryParams.timeEndFunction?.type === 'specificDate') {
          const { year, month, day } = queryParams.timeQueryParams.timeEndFunction
          if (year === 2024 && month === 2 && day === 28) {
            queryParams.timeQueryParams.timeEndFunction = {
              type: 'specificDate',
              year: 2024,
              month: 2,
              day: 29,
            }
          }
        }
        if (queryParams.timeQueryParams && queryParams.timeQueryParams.timeStartFunction?.type === 'specificDate') {
          const { year, month, day } = queryParams.timeQueryParams.timeStartFunction
          if (year === 2024 && month === 2 && day === 28) {
            queryParams.timeQueryParams.timeStartFunction = {
              type: 'specificDate',
              year: 2024,
              month: 2,
              day: 29,
            }
          }
        }
        return queryParams
      })
    })
  }
}
