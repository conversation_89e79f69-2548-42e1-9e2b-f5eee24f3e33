import { PluginClass } from 'src/shared'
import { processNonCumulativeMetrics } from 'src/server/MetricStore/metric2sql/customize'
import { MetricToSql } from '../../metric-to-sql'

/**
 * 处理不可累加的时间区间
 */
export class ProcessNonCumulativeMetricsPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(ProcessNonCumulativeMetricsPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(ProcessNonCumulativeMetricsPlugin.name, async (queryParams) => {
        return processNonCumulativeMetrics(queryParams, metricConfig)
      })
    })
  }
}
