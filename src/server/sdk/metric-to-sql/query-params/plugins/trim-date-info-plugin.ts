import { PluginClass } from 'src/shared'
import { DATE_ALIAS } from 'src/shared/metric-types'
import { MetricToSql } from '../../metric-to-sql'
import { ExcludeDateDimensions } from '../../utils'

/**
 * 前置处理
 * groupBys 中去掉 date_month, date_day, date_quarter, date_year
 * orderBys 当中也去掉 date_month, date_day, date_quarter, date_year
 * 当 orderBy 中有一些列不在 groupBy 或者 metricNames 中的时候，需要把这些列加入到 groupBy 或者 metricNames 中
 */
export class TrimDateInfoPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(TrimDateInfoPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(TrimDateInfoPlugin.name, async (queryParams) => {
        const newGroupBys = (queryParams.groupBys ?? []).filter(
          (name) => !ExcludeDateDimensions.includes(name) && metricConfig.hasDimension(name),
        )
        queryParams.groupBys = newGroupBys

        const newOrderBys = (queryParams.orderBys ?? []).filter(
          (name) =>
            !ExcludeDateDimensions.includes(name) &&
            (metricConfig.hasDimension(name.trim().split(' ')[0]) || metricConfig.hasMetric(name.trim().split(' ')[0])),
        )
        queryParams.orderBys = newOrderBys

        // handleOrderByAbsent
        const newMetricNames = Array.from(queryParams.metricNames ?? [])
        // 去掉 DATE_ALIAS
        const allFieldsInOrderBy = newOrderBys
          .filter((orderBy) => orderBy.indexOf(DATE_ALIAS) === -1)
          .map((orderBy) => orderBy.split(' ')[0])
        for (const field of allFieldsInOrderBy) {
          if (!newGroupBys.includes(field) && !newMetricNames.includes(field)) {
            // 如果是维度，就加到 groupBys 当中。如果是指标，就加到 metricNames 当中
            const dimension = metricConfig.findDimensionByNameWithoutErr(field)
            if (dimension) {
              newGroupBys.push(field)
            } else {
              newMetricNames.push(field)
            }
          }
        }

        queryParams.metricNames = newMetricNames
        return queryParams
      })
    })
  }
}
