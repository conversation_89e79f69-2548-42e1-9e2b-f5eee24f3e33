import { PluginClass } from 'src/shared'
import { DATE_ALIAS } from 'src/shared/metric-types'
import { MetricToSql } from '../../metric-to-sql'

/**
 * 对日期进行分组，排序中没有日期的时候，需要加上日期
 */
export class NormalizeTimeOrderByPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(NormalizeTimeOrderByPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(NormalizeTimeOrderByPlugin.name, async (queryParams) => {
        if (queryParams.timeQueryParams && queryParams.timeQueryParams.timeGranularity !== 'total') {
          if (!queryParams.orderBys) {
            queryParams.orderBys = [`${DATE_ALIAS} ASC`]
          }
          if (queryParams.orderBys.join(',').indexOf(DATE_ALIAS) === -1) {
            queryParams.orderBys.push(`${DATE_ALIAS} ASC`)
          }
        }
        return queryParams
      })
    })
  }
}
