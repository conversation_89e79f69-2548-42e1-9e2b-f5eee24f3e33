import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

/**
 * 把 list 指标展开
 */
export class ExpandListMetricPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(ExpandListMetricPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(ExpandListMetricPlugin.name, async (queryParams) => {
        // 把 list 指标展开
        let metricNamesExpanded: string[] = []
        queryParams.metricNames.forEach((name) => {
          const metric = metricConfig.findMetricByName(name)
          if (metric.type === 'list') {
            metricNamesExpanded = metricNamesExpanded.concat(metric.typeParams.metrics.map((m) => m.name))
          } else {
            metricNamesExpanded.push(name)
          }
        })
        queryParams.metricNames = metricNamesExpanded

        // 模型提参结果按照 list 指标排序，替换为 list 指标中第一个指标
        console.info('list metric order by begin>>>')
        const listMetricOrderByParams: string[] = []
        queryParams.orderBys?.forEach((orderParam) => {
          // 切出指标，格式示例 metric_name desc
          const metricName: string = orderParam.split(' ')[0]
          // 只处理指标，不处理维度或其他
          if (metricConfig.hasMetric(metricName)) {
            const metric = metricConfig.findMetricByName(metricName)
            if (metric.type === 'list') {
              const listSubMetrics = metricNamesExpanded.concat(metric.typeParams.metrics.map((m) => m.name))
              if (listSubMetrics && listSubMetrics.length > 0) {
                const result: string = orderParam.replace(metricName, listSubMetrics[0])
                listMetricOrderByParams.push(result)
                console.info('list metric order by param [', orderParam, '] replace to [', result, ']')
              }
            } else {
              listMetricOrderByParams.push(orderParam)
            }
          } else {
            listMetricOrderByParams.push(orderParam)
          }
        })
        queryParams.orderBys = listMetricOrderByParams
        return queryParams
      })
    })
  }
}
