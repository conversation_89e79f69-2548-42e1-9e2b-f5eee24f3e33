import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

/** 忽略 queryParams 结尾的问号，metricNames 和 groupBys 结尾的问号代表可选，主要用于 NL2Metric 阶段的e2e检查 */
export class RemoveTailingQuestionMarkPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(RemoveTailingQuestionMarkPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(RemoveTailingQuestionMarkPlugin.name, async (queryParams) => {
        const metricNames = (queryParams.metricNames || []).map((name) => name.replace(/\?$/, ''))
        const groupBys = (queryParams.groupBys || []).map((name) => name.replace(/\?$/, ''))
        const orderBys = (queryParams.orderBys || []).map((name) => name.replace(/\?$/, ''))
        return {
          ...queryParams,
          metricNames,
          groupBys,
          orderBys,
        }
      })
    })
  }
}
