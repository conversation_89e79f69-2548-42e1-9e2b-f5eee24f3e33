import chalk from 'chalk'
import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'

/**
 * 当有 avg 指标，但没有分组，那么就添加最近1年的时间，按月分组
 */
export class AvgMetricResolverPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(AvgMetricResolverPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(AvgMetricResolverPlugin.name, async (queryParams) => {
        const avgMetrics = queryParams.metricNames
          .map((name) => metricConfig.findMetricByNameWithoutErr(name))
          .filter((m) => m && m.type === 'simple' && metricConfig.findMeasureByName(m.typeParams.measure).agg === 'avg')
        // 平均值的先不处理，因为有些数据已经做了汇总去重，可以直接平均
        // if (avgMetrics.length > 0) {
        if (avgMetrics.length < 0) {
          const timeQueryParams = queryParams.timeQueryParams
          if (!timeQueryParams) {
            console.info(chalk.yellow('avg 指标缺少时间提参，添加一个最近1年的时间提参'))
            // 如果没有 groupBy，就添加一个时间提参
            queryParams.timeQueryParams = {
              timeGranularity: 'total',
              timeStartFunction: {
                type: 'recentMonths',
                months: 12,
              },
              timeEndFunction: {
                type: 'recentMonths',
                months: 0,
              },
            }
          }
        }
        return queryParams
      })
    })
  }
}
