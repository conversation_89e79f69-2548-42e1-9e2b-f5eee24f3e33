import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { RemoveTailingQuestionMarkPlugin } from './remove-tailing-question-mark-plugin'
import { TrimDateInfoPlugin } from './trim-date-info-plugin'
import { ProcessNonCumulativeMetricsPlugin } from './process-non-cumulative-metrics-plugin'
import { NormalizeTimeOrderByPlugin } from './normalize-time–order-by-plugin'
import { AvgMetricResolverPlugin } from './avg-metric-resolver-plugin'
import { LeapYearResolverPlugin } from './leap-year-resolver-plugin'
import { ExpandListMetricPlugin } from './expand-list-metric-plugin'

export class QueryParamsPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance
      .use(new RemoveTailingQuestionMarkPlugin())
      .use(new TrimDateInfoPlugin())
      .use(new ProcessNonCumulativeMetricsPlugin())
      .use(new NormalizeTimeOrderByPlugin())
      .use(new AvgMetricResolverPlugin())
      .use(new LeapYearResolverPlugin())
      .use(new ExpandListMetricPlugin())
  }
}
