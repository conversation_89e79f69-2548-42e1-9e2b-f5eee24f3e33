import * as tapable from 'tapable'
import { Hookable } from 'src/shared'
import { QueryParams } from 'src/shared/metric-types'

export class QueryParamsBuilder extends Hookable {
  hooks = Object.freeze({
    onGenQueryParams: new tapable.AsyncSeriesWaterfallHook<[QueryParams]>(['queryParams']),
    afterGenQueryParams: new tapable.AsyncParallelHook<[QueryParams]>(['queryParams']),
  })

  async invoke(res: QueryParams) {
    res = await this.hooks.onGenQueryParams.promise(res)
    await this.hooks.afterGenQueryParams.promise(res)
    return res
  }
}
