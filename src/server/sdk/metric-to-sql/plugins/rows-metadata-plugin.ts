import { nanoid } from 'nanoid'
import { PluginClass } from 'src/shared'
import {
  DATE_ALIAS,
  MetricConfig,
  PeriodOverPeriodMetricConfig,
  QueryParams,
  VirtualTimeDimension,
} from 'src/shared/metric-types'
import { MetricToSql } from '../metric-to-sql'
import { MetricToSqlMetadata } from '../metadata'

export class RowsMetadataPlugin implements PluginClass<MetricToSql> {
  metadata?: MetricToSqlMetadata
  queryParams?: QueryParams
  metricConfig!: MetricConfig

  updateMetadataFromMetric(metricName: string) {
    const metric = this.metricConfig.findMetricByName(metricName)
    this.metadata?.rowsMetadata.push({
      type: 'metric',
      value: metric,
    })
    // 添加同环比的指标
    for (const type of this.queryParams?.periodOverPeriods ?? []) {
      this.metadata?.rowsMetadata.push({
        type: 'metric',
        value: {
          id: nanoid(),
          name: `${PeriodOverPeriodMetricConfig[type].metricPrefix}${metric.name}`,
          label: `${metric.label}${PeriodOverPeriodMetricConfig[type].labelSuffix}`,
          synonyms: [],
          type: 'periodOverPeriod',
          formatTemplate:
            PeriodOverPeriodMetricConfig[type].formatTemplate === 'same_with_metric'
              ? metric.formatTemplate
              : PeriodOverPeriodMetricConfig[type].formatTemplate,
          typeParams: {
            type: type,
            metric: metric.name,
          },
        },
      })
    }
  }

  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    this.metricConfig = metricConfig
    instance.hooks.afterCreateMetadata.tapPromise(RowsMetadataPlugin.name, async (meta) => {
      this.metadata = meta
    })

    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(RowsMetadataPlugin.name, async (builder) => {
      builder.hooks.afterGenQueryParams.tapPromise(RowsMetadataPlugin.name, async (queryParams) => {
        this.queryParams = queryParams
        for (const groupBy of queryParams.groupBys ?? []) {
          const dim = metricConfig.findDimensionByNameWithoutErr(groupBy)
          if (!dim) continue
          this.metadata?.rowsMetadata.push({
            type: 'dimension',
            // FIXME: rowsMetaData中暂时去掉码值，否则导致数据太庞大，Out of sort memory
            value: { ...dim, values: [] },
          })
        }

        for (const metricName of queryParams.metricNames) {
          this.updateMetadataFromMetric(metricName)
        }
      })
    })

    instance.hooks.afterCreateVerifiedMetaBuilder.tapPromise(RowsMetadataPlugin.name, async (builder) => {
      builder.hooks.afterGenVerifiedMeta.tapPromise(RowsMetadataPlugin.name, async (meta) => {
        if (meta.timeSqlPart?.groupBy) {
          const virtualTimeDimension: VirtualTimeDimension = {
            id: nanoid(),
            type: 'virtual-time',
            name: DATE_ALIAS,
            label: '日期',
            synonyms: [],
            filterSwitch: true,
            expr: meta.timeSqlPart.groupBy,
          }
          this.metadata?.rowsMetadata.push({
            type: 'dimension',
            value: virtualTimeDimension,
          })
        }
      })
    })

    instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(RowsMetadataPlugin.name, async (builder) => {
      builder.hooks.afterGenDerivedMeta.tapPromise(RowsMetadataPlugin.name, async (meta) => {
        for (const metricName of meta.whereDerived.metricNames) {
          this.updateMetadataFromMetric(metricName)
        }
      })
    })
  }
}
