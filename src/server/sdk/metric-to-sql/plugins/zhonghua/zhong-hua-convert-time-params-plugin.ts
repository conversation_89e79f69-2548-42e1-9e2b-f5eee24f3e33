import { PluginClass } from 'src/shared'
import { convertTimeParamsForZhongHua, isZhongHua } from 'src/server/MetricStore/metric2sql/customize/zhonghua'
import { MetricToSql } from '../../metric-to-sql'

export class ZhongHuaConvertTimeParamsPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    if (!isZhongHua(metricConfig.name)) return
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(ZhongHuaConvertTimeParamsPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(ZhongHuaConvertTimeParamsPlugin.name, async (queryParams) => {
        return convertTimeParamsForZhongHua(queryParams, metricConfig)
      })
    })
  }
}
