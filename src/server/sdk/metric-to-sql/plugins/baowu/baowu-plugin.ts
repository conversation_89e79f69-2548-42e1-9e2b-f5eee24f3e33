import { PluginClass } from 'src/shared'
import { MetricToSql } from '../../metric-to-sql'
import { BaowuFinancialCustomLogicPlugin } from './baowu-financial-custom-logic-plugin'
import { BaowuCostCustomLogicPlugin } from './baowu-cost-custom-logic-plugin'
import { BaowuAmtCustomLogicPlugin } from './baowu-amt-custom-logic-plugin'

export class BaowuPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    instance
      .use(new BaowuFinancialCustomLogicPlugin())
      .use(new BaowuCostCustomLogicPlugin())
      .use(new BaowuAmtCustomLogicPlugin())
  }
}
