import { PluginClass } from 'src/shared'
import { isBaoWuCost } from 'src/shared/baowu-share-utils'
import { baowuCostCustomLogic } from 'src/server/MetricStore/metric2sql/customize/baowu'
import { MetricToSql } from '../../metric-to-sql'

export class BaowuCostCustomLogicPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    if (!isBaoWuCost(metricConfig.name)) return
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(BaowuCostCustomLogicPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(BaowuCostCustomLogicPlugin.name, async (queryParams) => {
        return baowuCostCustomLogic(queryParams, metricConfig)
      })
    })
  }
}
