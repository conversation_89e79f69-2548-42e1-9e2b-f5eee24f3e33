import { PluginClass } from 'src/shared'
import { isBaoWuFinancial } from 'src/shared/baowu-share-utils'
import { baowuFinancialCustomLogic } from 'src/server/MetricStore/metric2sql/customize/baowu'
import { MetricToSql } from '../../metric-to-sql'

// FIXME: 删除：宝武的定制逻辑：
export class BaowuFinancialCustomLogicPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    if (!isBaoWuFinancial(metricConfig.name)) return
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(BaowuFinancialCustomLogicPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(BaowuFinancialCustomLogicPlugin.name, async (queryParams) => {
        return baowuFinancialCustomLogic(queryParams, metricConfig)
      })
    })
  }
}
