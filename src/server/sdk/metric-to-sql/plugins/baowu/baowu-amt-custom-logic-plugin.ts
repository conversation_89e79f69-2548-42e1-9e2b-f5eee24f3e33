import { PluginClass } from 'src/shared'
import { isBaoWuAmt } from 'src/shared/baowu-share-utils'
import { baowuAmtCustomLogic } from 'src/server/MetricStore/metric2sql/customize/baowu'
import { MetricToSql } from '../../metric-to-sql'

export class BaowuAmtCustomLogicPlugin implements PluginClass<MetricToSql> {
  apply(instance: MetricToSql): void {
    const { metricConfig } = instance.config
    if (!isBaoWuAmt(metricConfig.name)) return
    instance.hooks.afterCreateQueryParamsBuilder.tapPromise(BaowuAmtCustomLogicPlugin.name, async (builder) => {
      builder.hooks.onGenQueryParams.tapPromise(BaowuAmtCustomLogicPlugin.name, async (queryParams) => {
        return baowuAmtCustomLogic(queryParams, metricConfig)
      })
    })
  }
}
