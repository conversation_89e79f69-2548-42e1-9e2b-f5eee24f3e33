import winston from 'winston'
import { PluginClass } from 'src/shared'
import { MetricToSql } from '../metric-to-sql'

export class LoggerPlugin implements PluginClass<MetricToSql> {
  logger = winston.createLogger({
    format: winston.format.combine(
      // winston.format.align(),
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message }) => {
        return `[${timestamp}] ${level}: ${message}`
      }),
    ),
    transports: [new winston.transports.Console()],
  })

  infoFormat(instance: MetricToSql, type: string, data: any) {
    return `MetricToSql(${instance.config.id}).${type} = [${JSON.stringify(data)}]`
  }

  apply(instance: MetricToSql): void {
    instance.hooks.beforeInvoke.tapPromise(LoggerPlugin.name, async (queryParams) => {
      this.logger.info(this.infoFormat(instance, 'beforeInvoke', queryParams))
    })

    // instance.hooks.afterCreateQueryParamsBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenQueryParams.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'queryParams', data))
    //   })
    // })

    // instance.hooks.afterCreateVerifiedQueryParamsBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenVerifiedQueryParams.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'verifiedQueryParams', data))
    //   })
    // })

    // instance.hooks.afterCreateVerifiedMetaBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenVerifiedMeta.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'verifiedMeta', data))
    //   })
    // })

    // instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenDerivedMeta.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'derivedMeta', data))
    //   })
    // })

    // instance.hooks.afterCreateSummaryMetaBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenSummaryMeta.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'summaryMeta', data))
    //   })
    // })

    // instance.hooks.afterCreateSqlMetaBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenSqlMeta.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'sqlMeta', data))
    //   })
    // })

    // instance.hooks.afterCreateSqlBuilder.tapPromise(LoggerPlugin.name, async (builder) => {
    //   builder.hooks.afterGenSql.tapPromise(LoggerPlugin.name, async (data) => {
    //     this.logger.info(this.infoFormat(instance, 'sql', data))
    //   })
    // })

    instance.hooks.afterInvoke.tapPromise(LoggerPlugin.name, async (data) => {
      this.logger.info(this.infoFormat(instance, 'afterInvoke', data))
    })
  }
}
