/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/naming-convention */
import * as tapable from 'tapable'
import { Request } from 'express'
import axios, { AxiosRequestConfig } from 'axios'
import { ChatResponseErrorTypes } from 'src/shared/common-types'
import { Hookable, ChatRequest } from 'src/shared/types'
import { PROCESS_ENV } from 'src/server/server-constants'
import { reporter, ReportLogParams } from 'src/server/winston-log'
import { getRequestInfo } from 'src/server/utils'

export type ChatResponse = any

export interface ChatConfig {
  req: Request
}

export class ChatBackend extends Hookable {
  hooks = Object.freeze({
    onGenRequestConfig: new tapable.AsyncSeriesWaterfallHook<[AxiosRequestConfig<any>]>(['config']),
    afterGenRequestConfig: new tapable.AsyncParallelHook<[AxiosRequestConfig<any>]>(['config']),
    afterRequest: new tapable.AsyncParallelHook<[any]>(['data']),
  })

  getAgentChatData(requestData: ChatRequest) {
    const {
      messages,
      sceneId,
      projectId,
      taskId,
      enableInternetSearch,
      enableDocSearch,
      enableBi,
      enableMetricExactMatch,
      fileIds,
      dirIds,
      additionalInfo,
    } = requestData

    return {
      messages: messages,
      model_id: sceneId,
      project_id: sceneId ? undefined : projectId,
      task_id: taskId,
      enable_internet_search: enableInternetSearch,
      enable_doc_search: enableDocSearch,
      enable_bi: enableBi,
      file_ids: fileIds,
      dir_ids: dirIds,
      additional_info: {
        force_exact_match: enableMetricExactMatch,
        hint_dynamic_limit: additionalInfo?.hintDynamicLimit,
        hint_dense_weight: additionalInfo?.hintDenseWeight,
        hint_sparse_weight: additionalInfo?.hintSparseWeight,
      },
      call_back_addr: PROCESS_ENV.ASK_BI_HOST,
    }
  }

  async invoke(requestData: ChatRequest) {
    const { currentParamsExtractApi } = requestData
    const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
    const config = await this.hooks.onGenRequestConfig.promise({
      url: `${baseUrl}/api/v1/agent`,
      data: this.getAgentChatData(requestData),
      method: 'post',
    })
    await this.hooks.afterGenRequestConfig.promise(config)
    const res = await axios.request(config)
    const resData = res.data
    await this.hooks.afterRequest.promise(resData)
    if (resData.code !== 0 || !resData.data) throw res.data
    return resData.data
  }
}

export class Chat extends Hookable {
  hooks = Object.freeze({
    beforeInvoke: new tapable.AsyncParallelHook<[ChatRequest]>(['requestData']),
    afterInvoke: new tapable.AsyncParallelHook<[ChatResponse]>(['responseData']),
    afterGenChatRequest: new tapable.AsyncParallelHook<[ChatRequest]>(['requestData']),
    onGenChatRequest: new tapable.AsyncSeriesWaterfallHook<[ChatRequest]>(['requestData']),
    onGenChatResponse: new tapable.AsyncSeriesWaterfallHook<[ChatResponse, ChatRequest]>([
      'responseData',
      'requestData',
    ]),
    onError: new tapable.AsyncParallelHook<[any, ChatRequest]>(['err', 'requestData']),
    onGenBackend: new tapable.AsyncSeriesWaterfallHook<[ChatBackend]>(['chatBackend']),
    afterGenBackend: new tapable.AsyncParallelHook<[ChatBackend]>(['chatBackend']),
  })

  async createBackend() {
    return new ChatBackend()
  }

  constructor(public config: ChatConfig) {
    super()
    this.use(new ChatLogger())
  }

  async invoke(requestData: ChatRequest): Promise<{ code: number; data: ChatResponse }> {
    try {
      await this.hooks.beforeInvoke.promise(requestData)
      requestData = await this.hooks.onGenChatRequest.promise(requestData)
      await this.hooks.afterGenChatRequest.promise(requestData)
      const backend = await this.hooks.onGenBackend.promise(await this.createBackend())
      await this.hooks.afterGenBackend.promise(backend)
      const response = await this.hooks.onGenChatResponse.promise(await backend.invoke(requestData), requestData)
      await this.hooks.afterGenChatRequest.promise(response)
      return response
    } catch (error) {
      await this.hooks.onError.promise(error, requestData)
      const response = await this.hooks.onGenChatResponse.promise(
        {
          code: -1,
          data: {
            taskType: 'chat-error',
            errType: ChatResponseErrorTypes.E_UNKNOWN,
            unreadyReason: '超纲了，大模型正在努力学习中...',
            ready: false,
            originResponse: { response: error, type: '兜底错误' },
            sceneId: requestData.sceneId,
            conversationId: requestData.conversationId,
          },
        },
        requestData,
      )
      await this.hooks.afterGenChatRequest.promise(response)
      return response
    }
  }

  use(obj: { apply: (chat: Chat) => void } | ((chat: Chat) => void)) {
    if (typeof obj === 'function') {
      obj(this)
    } else {
      obj.apply(this)
    }
    return this
  }
}

class ChatLogger {
  reportLogsParams!: ReportLogParams

  apply(chat: Chat) {
    const { host, username, traceId = '' } = getRequestInfo(chat.config.req)
    this.reportLogsParams = {
      moduleType: 'MULTI_AGENT_AGENT_CHAT',
      host,
      username,
      traceId,
      startTime: 0,
      resultCode: 500,
      input: {},
      output: '',
      debug: {},
    }

    chat.hooks.beforeInvoke.tapPromise(ChatLogger.name, async () => {
      this.reportLogsParams.startTime = Date.now()
    })

    chat.hooks.afterGenChatRequest.tapPromise(ChatLogger.name, async (chatRequest) => {
      this.reportLogsParams.input = chatRequest.messages
      this.reportLogsParams.semanticProjectId = chatRequest.projectId
      this.reportLogsParams.semanticSceneId = chatRequest.sceneId
      this.reportLogsParams.debug.sceneId = chatRequest.sceneId
      this.reportLogsParams.debug.conversationId = chatRequest.conversationId
    })

    chat.hooks.afterGenBackend.tapPromise(ChatLogger.name, async (backend) => {
      backend.hooks.afterGenRequestConfig.tapPromise(ChatLogger.name, async (config) => {
        this.reportLogsParams.debug.url = config.url
        this.reportLogsParams.debug.method = config.method
        this.reportLogsParams.debug.data = config.data
      })
      backend.hooks.afterRequest.tapPromise(ChatLogger.name, async (data) => {
        this.reportLogsParams.resultCode = data.code
        this.reportLogsParams.output = this.reportLogsParams.debug.response = data
        reporter.report(this.reportLogsParams)
      })
    })

    chat.hooks.onError.tapPromise(ChatLogger.name, async (err) => {
      this.reportLogsParams.resultCode = 500
      this.reportLogsParams.output = this.reportLogsParams.debug.response = err
      reporter.report(this.reportLogsParams)
    })
  }
}

// export async function extractRequestDataFromTraceId(traceId: string) {
//   const list = await prisma.converChat.findMany({ where: { traceId } })
//   return list.find((v) => v.response && (v.response as any)?.type === 'AskChat')
// }
