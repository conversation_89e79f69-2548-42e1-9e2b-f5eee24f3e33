/* eslint-disable @typescript-eslint/naming-convention */
import { Prisma, SemanticScene } from '@prisma/client'
import _ from 'lodash'
import * as casbin from 'casbin'
import { PrismaAdapter } from 'casbin-prisma-adapter'
import {
  contactUserData,
  extractInfoFromEnforcer,
  contactData,
  SPLIT_CHAR,
  ResourceTypes,
  ActionTypes,
} from 'src/shared/auth'
import { SceneConfigValueTypeMap } from 'src/shared/config-management-constants'
import { getConfigManagement } from 'src/server/AskBI/configManagement/dao'
import { ALL_LLMS } from '../utils'
import { prisma } from '../dao/prisma-init'

const diObjMatchFunctionName = `di_obj_match`

export const modelConfig = `
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub) && (r.obj == p.obj || ${diObjMatchFunctionName}(r.obj, p.obj)) && r.act == p.act || r.sub == "user:root"
`

export type UnionUserRole =
  | {
      type: 'user'
      id: string
    }
  | {
      type: 'roles'
      ids: string[]
    }
  | {
      type: 'groups'
      ids: string[]
    }

export class Enforcer {
  e!: casbin.Enforcer
  async refresh() {
    await this.e.loadPolicy()
  }
  async init() {
    const a = await PrismaAdapter.newAdapter()
    const m = new casbin.Model()
    m.loadModelFromText(modelConfig)
    this.e = await casbin.newEnforcer(m, a)
    await this.e.addFunction(diObjMatchFunctionName, this.diObjMatchFunction)
  }

  diObjMatchFunction(key1: string, key2: string) {
    try {
      const [k1, id1] = key1.split(SPLIT_CHAR)
      const [k2, id2] = key2.split(SPLIT_CHAR)
      if (k1 !== k2) return false
      if (id2 === '*') return true
      return id1 === id2
    } catch (_) {
      return false
    }
  }

  async getAllPoliciesForUser(id: string) {
    // 获取和当前用户有关的权限 包括继承的 比如继承了 admin 那也会有 admin xxx read
    const policies = await this.e.getImplicitPermissionsForUser(contactUserData(id))
    return policies
  }

  // 当前用户是否有权限
  async matchAllFromUserId(id: string, resourceTypes: ResourceTypes, actionType: ActionTypes) {
    // whether has permission for this request
    return this.e.enforce(contactUserData(id), contactData(resourceTypes, '*'), actionType)
  }

  async getLlmsFromUserId(id: string) {
    // 如果有权限 则直接返回
    if (await this.matchAllFromUserId(id, 'llm', 'read')) return ALL_LLMS
    // 否则获取当前用户所有的权限
    const policies = await this.getAllPoliciesForUser(id)
    // 获取当前用户的所有 llm 权限 然后从总 lla进行filter 有一些可能没启用 所以 filter 了一下
    const llmTypeSet = new Set(
      policies.filter((v) => v[1].startsWith('llm:')).map((v) => extractInfoFromEnforcer(v[1]).id),
    )
    return ALL_LLMS.filter((v) => llmTypeSet.has(v.type))
  }

  async getGroupsFromUserId(id: string) {
    const roles = await this.e.getFilteredGroupingPolicy(0, contactUserData(id), '')
    const groups = await prisma.xGroup.findMany({
      where: {
        id: {
          in: roles
            .map((v) => extractInfoFromEnforcer(v[1]))
            .filter((v) => v.type === 'group')
            .map((v) => v.id),
        },
      },
      select: {
        id: true,
        groupName: true,
      },
    })
    return groups
  }

  async getRolesFromUserId(id: string) {
    const roles = await this.e.getFilteredGroupingPolicy(0, contactUserData(id), '')
    const groups = await prisma.xRole.findMany({
      where: {
        id: {
          in: roles
            .map((v) => extractInfoFromEnforcer(v[1]))
            .filter((v) => v.type === 'role')
            .map((v) => v.id),
        },
      },
      select: {
        id: true,
        roleName: true,
      },
    })
    return groups
  }

  async getUserIdFromUsername(username: string) {
    const user = await prisma.xUser.findFirst({ where: { username } })
    return user?.id
  }

  async unionUserRole({ v0, v1 }: { v0: UnionUserRole; v1: UnionUserRole }) {
    if (v0.type === 'user' && (v1.type === 'roles' || v1.type === 'groups')) {
      const key0 = contactData(v0.type, v0.id)
      const prefixV1 = v1.type.substring(0, v1.type.length - 1)
      const newKey1Arr = v1.ids.map((id) => contactData(prefixV1, id))
      const policies = await prisma.casbinRule.findMany({ where: { v0: key0, v1: { startsWith: prefixV1 } } })
      const oldKey1Arr = policies.map((v) => v.v1!)
      await prisma.casbinRule.deleteMany({
        where: { v1: { in: _.difference(oldKey1Arr, newKey1Arr) }, v0: key0 },
      })
      await prisma.casbinRule.createMany({
        data: _.difference(newKey1Arr, oldKey1Arr).map((v1) => ({ v0: key0, v1, ptype: 'g' })),
      })
      await this.refresh()
    } else {
      throw new Error('未知联合方式')
    }
  }

  async compatSceneModelNames(
    sceneList: SemanticScene[],
  ): Promise<(SemanticScene & { moduleNames?: string[] } & SceneConfigValueTypeMap)[]> {
    const modelNames = await prisma.semanticScenesModels.findMany({
      where: { sceneId: { in: sceneList.map((v) => v.id) } },
    })

    for (const modelName of modelNames) {
      const scene = sceneList.find((v) => v.id === modelName.sceneId) as any
      if (scene) {
        scene.modelNames ??= []
        scene.modelNames.push(modelName.modelName)
      }
    }

    await Promise.all(
      sceneList.map(async (list) => {
        const sceneConfig = await getConfigManagement('scene', list.id as string)
        Object.assign(list, sceneConfig)
      }),
    )

    return sceneList as (SemanticScene & { moduleNames?: string[] } & SceneConfigValueTypeMap)[]
  }

  async normalizeProjects(projects: Prisma.SemanticProjectGetPayload<{ include: { semanticScenes: true } }>[]) {
    await Promise.all(
      projects.map(async (p) => {
        const scenes = p.semanticScenes
        // 兼容老的
        ;(p as any).scenes = await this.compatSceneModelNames(scenes)
      }),
    )
    return projects
  }

  async getProjectsFromUserId(id: string) {
    const checkAll = await this.matchAllFromUserId(id, 'project', 'read')
    if (checkAll) {
      const projects = await prisma.semanticProject.findMany({
        include: {
          semanticScenes: {},
        },
      })
      const normalizedProjects = await this.normalizeProjects(projects)
      return normalizedProjects
    }

    const policies = (await enforcer.getAllPoliciesForUser(id))
      .map((arr) => {
        return {
          resource: extractInfoFromEnforcer(arr[1]),
          action: arr[2],
        }
      })
      .filter((v) => v.action === 'read')

    const projects = await this.normalizeProjects(
      await prisma.semanticProject.findMany({
        where: {
          id: {
            in: policies.filter((v) => v.resource.type === 'project').map((v) => v.resource.id) as string[],
          },
        },
        include: {
          semanticScenes: {
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      }),
    )

    const sceneIds = policies.filter((v) => v.resource.type === 'scene').map((v) => v.resource.id) as string[]

    const scenes = await prisma.semanticScene.findMany({
      where: {
        id: {
          in: sceneIds,
        },
        semanticProjectId: {
          notIn: projects.map((v) => v.id),
        },
      },
      select: { semanticProjectId: true },
    })

    const projectsFromScenes = await this.normalizeProjects(
      await prisma.semanticProject.findMany({
        where: {
          id: {
            in: scenes.map((v) => v.semanticProjectId),
          },
        },
        include: {
          semanticScenes: {
            where: { id: { in: sceneIds } },
          },
        },
      }),
    )

    return projects.concat(projectsFromScenes)
  }
}

export const enforcer = new Enforcer()
