{
  "words": [
    "achat",
    "acomplete",
    "addoption",
    "ahooks",
    "ahooksjs",
    "alicdn",
    "allm",
    "antd",
    "antgroup",
    "antv",
    "askbi",
    "askbot",
    "ASKDI",
    "askdoc",
    "astify",
    "astream",
    "astype",
    "Automations",
    "autouse",
    "avatarbase",
    "avatarchannel",
    "axios",
    "BAAI",
    "baichuan",
    "baowu",
    "bbwsy",
    "bbwsytest",
    "busi",
    "businesstype",
    "bytenode",
    "Casbin",
    "Cascader",
    "cghttz",
    "Charsets",
    "chatbi",
    "chatdoc",
    "chatglm",
    "classname",
    "clsx",
    "compat",
    "computetype",
    "concat",
    "conver",
    "convers",
    "curdate",
    "dagre",
    "dataframe",
    "DATAMODEL",
    "datasource",
    "datasources",
    "DATAZOOM",
    "datetime",
    "dateutil",
    "decomp",
    "descs",
    "dianxin",
    "dipeak",
    "distore",
    "ditest",
    "dotenv",
    "dropna",
    "Dtype",
    "dyft",
    "echarts",
    "envalid",
    "excinfo",
    "executemany",
    "fanwei",
    "fieldname",
    "fkey",
    "flink",
    "FOUC",
    "fullstr",
    "geekblue",
    "gmtcreated",
    "goldendb",
    "gonghang",
    "hanlp",
    "hengsheng",
    "heroicons",
    "hljs",
    "Hookable",
    "huaxia",
    "Hudi",
    "huggingface",
    "HUOY",
    "icbc",
    "iconfont",
    "iflytek",
    "immer",
    "Instrumentor",
    "invoicedate",
    "invoiceno",
    "iservice",
    "iterrows",
    "JDBC",
    "JIAO",
    "jiaohang",
    "JINGFEN",
    "JSESSIONID",
    "jsonlogger",
    "keyof",
    "keypoint",
    "Langchain",
    "levelname",
    "levelno",
    "lingjing",
    "llm",
    "llmbase",
    "llms",
    "lmdi",
    "LTWH",
    "LTWHP",
    "makereport",
    "mediumint",
    "memberof",
    "mercht",
    "MERNO",
    "metastore",
    "metricmodel",
    "metricstype",
    "minio",
    "mistralai",
    "mixtral",
    "modifyitems",
    "msword",
    "mvjobstatus",
    "mytest",
    "nocheck",
    "nodeid",
    "nodesep",
    "nofilter",
    "npmignore",
    "Nums",
    "oceanbase",
    "odbc",
    "officedocument",
    "OLAP",
    "openai",
    "opentelemetry",
    "openxmlformats",
    "pagesinit",
    "pannable",
    "Parens",
    "partitionstatus",
    "pfundabbr",
    "Popconfirm",
    "postprocessor",
    "prefiltering",
    "preprompt",
    "prepufa",
    "ptable",
    "pufa",
    "pydantic",
    "pyenv",
    "pyhive",
    "pymysql",
    "pymysqlpool",
    "pytest",
    "pythonjsonlogger",
    "qwen",
    "rankdir",
    "ranksep",
    "rehype",
    "relationwithsourcemv",
    "relativedelta",
    "rerank",
    "reranker",
    "resave",
    "RRGGBBAA",
    "rwatermark",
    "SASL",
    "SAST",
    "sdfh",
    "semconv",
    "sessionfinish",
    "sessionstart",
    "setdt",
    "SFBK",
    "shaoyin",
    "shibing",
    "Sider",
    "SMALLINT",
    "smartx",
    "Snapline",
    "songshaoyin",
    "sparkline",
    "spreadsheetml",
    "sqlalchemy",
    "sqlify",
    "SQLJSON",
    "starfire",
    "starrocks",
    "strify",
    "stylelint",
    "subquery",
    "tailwindcss",
    "textlayerrendered",
    "thfund",
    "thsso",
    "TIAN",
    "tianhong",
    "timeformat",
    "tinyint",
    "tqdm",
    "traceid",
    "Treemap",
    "treenode",
    "TTSA",
    "typeof",
    "UIAS",
    "UNION_UPSERT",
    "unitprice",
    "unthrowable",
    "Usergroup",
    "userid",
    "varchar",
    "vconsole",
    "viewtype",
    "virtualtabletype",
    "vite",
    "VLLM",
    "vtable",
    "webextensions",
    "wordprocessingml",
    "XENGINE",
    "xflow",
    "xfwd",
    "Xpaths",
    "xusers",
    "XYKXF",
    "yinlian",
    "Yuanjing",
    "ywjhz",
    "YYMM",
    "zhipu",
    "ZHONG",
    "zhonghua",
    "zhongou",
    "zhongyuan",
    "zhuoxue",
    "zoomable",
    "zrender",
    "zyhl",
    "zyhltest"
  ],
  "ignorePaths": [
    "src/client/components/ChatBoxMock.ts",
    // "python/",
    "src/server/MetricStore/data/"
  ]
}
