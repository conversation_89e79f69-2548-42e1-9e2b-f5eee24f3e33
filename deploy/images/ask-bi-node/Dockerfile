FROM registry.gitlab.dipeak.com/dipeak/generic-repository/node:keep-20.18.1-alpine3.21 as base
RUN npm config set registry https://registry.npmmirror.com

FROM base as server-builder
WORKDIR /app

# Install global npm first for better caching
RUN npm install -g npm@10.5.0

# Copy package files for dependency installation (better layer caching)
COPY package.json package-lock.json ./
RUN npm install --canvas_binary_host_mirror=https://registry.npmmirror.com/-/binary/canvas

# Copy source code and build server
COPY . .
RUN npm run prisma-generate
RUN npm run server-build

# Since client code runs in the browser. There's no architecture-specific code in the client build.
# We can build the client code on the build platform, and then copy it to the final image.
# This way, we can avoid building the client code for each architecture.
FROM --platform=$BUILDPLATFORM registry.gitlab.dipeak.com/dipeak/generic-repository/node:keep-20.18.1-alpine3.21 as client-builder
RUN npm config set registry https://registry.npmmirror.com
WORKDIR /app

# Install global npm first for better caching
RUN npm install -g npm@10.5.0

# Copy package files for dependency installation (better layer caching)
COPY package.json package-lock.json ./
RUN npm install --canvas_binary_host_mirror=https://registry.npmmirror.com/-/binary/canvas

# Copy source code and build client
COPY . .
RUN npm run client-build

# 生产环境镜像
FROM base
WORKDIR /app
COPY --from=server-builder /app/package.json .
COPY --from=server-builder /app/package-lock.json .
COPY --from=server-builder /app/node_modules ./node_modules
COPY --from=server-builder /app/dist-server ./dist-server
COPY --from=client-builder /app/dist-client ./dist-client
COPY --from=server-builder /app/prisma ./prisma

EXPOSE 8000

# 设置启动命令
ENV USER_COMMAND="npm run prod-start"

CMD sh -c "$USER_COMMAND"
