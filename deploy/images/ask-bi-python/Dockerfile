FROM registry.gitlab.dipeak.com/dipeak/generic-repository/pytorch:23.10-py3-dipeak as base
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# Use a specific platform for the downloader stage, so that when building multi-arch images,
# we only need to download once.
FROM --platform=$BUILDPLATFORM registry.gitlab.dipeak.com/dipeak/generic-repository/pytorch:23.10-py3-dipeak as downloader
RUN curl -fsSL --user "user:MzvkQb8ns_DHcG29KaR2" "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/bge/20250211/bge.zip" > /tmp/bge.zip \
    && unzip -q /tmp/bge.zip -d /tmp/bge

FROM base


WORKDIR /ask-bi/python/nl2metric
COPY python/nl2metric .
COPY --from=downloader /tmp/bge/data/public_model/BGE_b5c9d86d763d9945f7c0a73e549a4a39c423d520 /resources/bge-model

ENV embedding_model embedding_service_api
ENV bge_model_path /resources/bge-model
ENV PORT "21002"
ENV MODEL_PATH "/model/mixtral-8-7b"
ENV GPU_NUM "4"
ENV TRANSFORMERS_OFFLINE="1"

CMD ["bash", "start.sh"]
