FROM registry.gitlab.dipeak.com/dipeak/generic-repository/pytorch:23.10-py3

# 设置 pip 镜像源（uv 也会自动读取 pip 配置）
RUN pip config set global.index-url https://mirrors.bfsu.edu.cn/pypi/web/simple

# 更换 apt 源
RUN set -eux; \
  dpkgArch="$(dpkg --print-architecture)"; \
  case "${dpkgArch##*-}" in \
  amd64) sed -i "s@http://.*archive.ubuntu.com@http://mirrors.ustc.edu.cn@g" /etc/apt/sources.list; \
         sed -i "s@http://.*security.ubuntu.com@http://mirrors.ustc.edu.cn@g" /etc/apt/sources.list;; \
  arm64) sed -i "s@http://ports.ubuntu.com/ubuntu-ports@http://mirrors.ustc.edu.cn/ubuntu-ports@g" /etc/apt/sources.list;; \
  esac;

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y \
    less wget curl vim net-tools iputils-ping dnsutils \
    wkhtmltopdf ttf-wqy-zenhei fonts-wqy-zenhei \
    libreoffice poppler-utils libcairo2-dev libgirepository1.0-dev \
 && rm -rf /var/lib/apt/lists/*
ENV TZ=Asia/Shanghai

# Download uv based on architecture
ARG TARGETARCH
RUN set -eux; \
  case "${TARGETARCH}" in \
  amd64) uvArch="x86_64-unknown-linux-gnu";; \
  arm64) uvArch="aarch64-unknown-linux-gnu";; \
  *) echo "Unsupported architecture: ${TARGETARCH}" >&2; exit 1;; \
  esac; \
  curl -o /tmp/uv.tar.gz "$(curl -u 'user:MzvkQb8ns_DHcG29KaR2' -s -L -w '%{url_effective}' -o /dev/null "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/uv/0.6.14/uv-${uvArch}.tar.gz")" && \
  tar -xzf /tmp/uv.tar.gz -C /tmp && \
  mv /tmp/uv-${uvArch}/uv /usr/local/bin/uv && \
  chmod +x /usr/local/bin/uv && \
  rm -rf /tmp/uv.tar.gz /tmp/uv-${uvArch}
RUN uv --version
# 使用 uv 安装 requirements.txt
COPY python/nl2metric/nl2document/builder/requirements.txt /tmp/requirements.txt
RUN echo "CACHEBUST=$(md5sum /tmp/requirements.txt | cut -d ' ' -f1)" && \
    uv pip install --system -r /tmp/requirements.txt -i https://mirrors.bfsu.edu.cn/pypi/web/simple

# 其余 pip 安装也用 uv 替代
ARG PASSPHRASE=QcKQ8CKnnwf9uUM5kbPZ
RUN set -ex \
    && uv pip install pyconcrete --system -i https://mirrors.bfsu.edu.cn/pypi/web/simple --config-settings=setup-args=-Dpassphrase=${PASSPHRASE} \
    && uv pip install smolagents==1.10.0 -i https://mirrors.bfsu.edu.cn/pypi/web/simple --system \
    && pip uninstall -y opencv opencv-contrib-python opencv-python opencv-python-headless

RUN uv pip install --system paddlepaddle-gpu==3.0.0rc1 -i https://www.paddlepaddle.org.cn/packages/stable/cu118/
RUN uv pip install -i https://mirrors.bfsu.edu.cn/pypi/web/simple paddlex==3.0rc0 --system
RUN uv pip install -i https://mirrors.bfsu.edu.cn/pypi/web/simple paddleocr==2.9.1 --system
RUN uv pip install -i https://mirrors.bfsu.edu.cn/pypi/web/simple llama-index-core==0.10.68.post1 --system
RUN uv pip install -i https://mirrors.bfsu.edu.cn/pypi/web/simple opencv-contrib-python==********* --system
RUN uv pip install -i https://mirrors.bfsu.edu.cn/pypi/web/simple opencv-python==******** --system
RUN python3 -c "from paddlex.utils.fonts import get_font_file_path; \
get_font_file_path('PingFang-SC-Regular.ttf'); \
get_font_file_path('simfang.ttf')"



WORKDIR /ask-bi/python/nl2metric
COPY python/nl2metric .

COPY python/copy-doc-builder-resource.py .
RUN python3 copy-doc-builder-resource.py

RUN set -ex \
    &&  pyecli compile \
        --source=/ask-bi/python/nl2metric \
        --pye \
        --remove-py \
        --remove-pyc \
        -i "**/main*.py" "**/builder_server.py"
