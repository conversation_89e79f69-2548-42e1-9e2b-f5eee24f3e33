#!/bin/sh

set -euo pipefail

BASE_DIR=`cd $(dirname $0)/../../; pwd`
echo "---------------- base_dir is ${BASE_DIR}"

# Cleanup function for trap
cleanup() {
  echo "Cleaning up background processes and temporary files..."

  # Kill all background build processes
  if [ -n "$build_pids" ]; then
    for pid in $build_pids; do
      if kill -0 "$pid" 2>/dev/null; then
        echo "Terminating build process $pid"
        kill "$pid" 2>/dev/null || true
      fi
    done

    # Wait a moment for graceful shutdown
    sleep 2

    # Force kill if still running
    for pid in $build_pids; do
      if kill -0 "$pid" 2>/dev/null; then
        echo "Force killing build process $pid"
        kill -9 "$pid" 2>/dev/null || true
      fi
    done
  fi

  # Clean up temporary log files
  if [ -n "$build_logs" ]; then
    for log_file in $build_logs; do
      rm -f "$log_file" "${log_file}.exit_code" 2>/dev/null || true
    done
  fi

  echo "Cleanup completed"
}

# Set up trap for cleanup on script exit or interruption
trap cleanup EXIT INT TERM

# Initialize variables for trap cleanup
build_pids=""
build_logs=""

# 参数
if [ -n "${CI:-}" ]; then
  echo "Running in CI environment"
  image_name_prefix="${CI_REGISTRY}/dipeak/generic-repository/"
  formatted_timestamp="${CI_COMMIT_TIMESTAMP:0:4}${CI_COMMIT_TIMESTAMP:5:2}${CI_COMMIT_TIMESTAMP:8:2}${CI_COMMIT_TIMESTAMP:11:2}${CI_COMMIT_TIMESTAMP:14:2}"
  image_tag="${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}-${formatted_timestamp}"
else
  echo "Running in local environment"
  image_name_prefix=""
  image_tag="latest"
fi

# Dynamically discover images from deploy/images subdirectories
images_dir="$BASE_DIR/deploy/images"
if [ ! -d "$images_dir" ]; then
  echo "Error: Images directory $images_dir does not exist"
  exit 1
fi

# Get list of image directories
image_names=""
image_count=0
for dir in "$images_dir"/*; do
  if [ -d "$dir" ] && [ -f "$dir/Dockerfile" ]; then
    image_name=$(basename "$dir")
    if [ -z "$image_names" ]; then
      image_names="$image_name"
    else
      image_names="$image_names $image_name"
    fi
    image_count=$((image_count + 1))
    echo "Found image: $image_name"
  fi
done

if [ $image_count -eq 0 ]; then
  echo "Error: No valid image directories found in $images_dir"
  echo "Each subdirectory must contain a Dockerfile"
  exit 1
fi

echo "Building images with tag: ${image_tag}"

# Setup docker multi-platform build only in GitLab CI environment
if [ -n "${CI:-}" ]; then
  echo "Detected GitLab CI environment, setting up docker buildx"
  docker buildx create --use --name mybuilder --driver docker-container \
    --driver-opt image=registry.gitlab.dipeak.com/dipeak/generic-repository/moby/buildkit:keep-buildx-stable-1
  docker buildx inspect --bootstrap
else
  echo "Not in CI environment, skipping docker buildx setup"
fi

# Push on CI
if [ -n "${CI:-}" ]; then
  echo "Running in CI environment, will push images"
  push_args="--push"
else
  echo "Running in local environment, will not push images"
  push_args=""
fi

# Build all discovered images in parallel
image_full_names=""

for image_name in $image_names; do
  image_full_name="${image_name_prefix}${image_name}:${image_tag}"
  if [ -z "$image_full_names" ]; then
    image_full_names="$image_full_name"
  else
    image_full_names="$image_full_names $image_full_name"
  fi
  log_file="/tmp/build_${image_name}_$$.log"
  if [ -z "$build_logs" ]; then
    build_logs="$log_file"
  else
    build_logs="$build_logs $log_file"
  fi

  echo "Starting build for image: ${image_full_name}"

  # Check if image is excluded from multi-arch builds
  if [ -f "$images_dir/$image_name/.multi-arch-excluded" ]; then
    echo "Image $image_name is excluded from multi-arch builds, using single platform"
    platform_args="--platform linux/amd64"
  else
    echo "Image $image_name will be built for multiple architectures"
    platform_args="--platform linux/amd64,linux/arm64"
  fi

  # Setup cache configuration for this image (only in CI)
  cache_args=""

  if [ -n "${CI:-}" ]; then
    default_branch_cache_tag="${image_name_prefix}${image_name}:keep-${CI_DEFAULT_BRANCH}-cache"
    if [ "$CI_COMMIT_REF_SLUG" != "$CI_DEFAULT_BRANCH" ]; then
      current_branch_cache_tag="${image_name_prefix}${image_name}:${CI_COMMIT_REF_SLUG}-cache"
    else
      current_branch_cache_tag="${default_branch_cache_tag}"
    fi

    # Cache from: try to use cache from the registry
    # Use multiple cache sources for better hit rate
    cache_args="--cache-from type=registry,ref=${current_branch_cache_tag}"
    if [ "$current_branch_cache_tag" != "$default_branch_cache_tag" ]; then
      cache_args="$cache_args --cache-from type=registry,ref=${default_branch_cache_tag}"
    fi

    # Cache to: export cache to registry
    cache_args="$cache_args --cache-to type=registry,ref=${current_branch_cache_tag},mode=max"
  fi

  # Start build in background
  (
    docker buildx build "$BASE_DIR" \
      $platform_args \
      $push_args \
      $cache_args \
      -f "$images_dir/$image_name/Dockerfile" \
      -t "${image_full_name}" 2>&1 | tee "$log_file"
    echo $? > "${log_file}.exit_code"
  ) &

  if [ -z "$build_pids" ]; then
    build_pids="$!"
  else
    build_pids="$build_pids $!"
  fi
done

echo "Waiting for all builds to complete..."

# Wait for all background builds to finish and check results
failed_builds=""
pid_index=1
image_index=1
log_index=1

for pid in $build_pids; do
  # Get corresponding image name and log file
  image_name=$(echo $image_names | cut -d' ' -f$image_index)
  log_file=$(echo $build_logs | cut -d' ' -f$log_index)

  wait "$pid"

  # Check exit code
  if [ -f "${log_file}.exit_code" ]; then
    exit_code=$(cat "${log_file}.exit_code")
    if [ "$exit_code" -ne 0 ]; then
      if [ -z "$failed_builds" ]; then
        failed_builds="$image_name"
      else
        failed_builds="$failed_builds $image_name"
      fi
      echo "Build failed for image: $image_name (exit code: $exit_code)"
      echo "Log output:"
      cat "$log_file"
    else
      echo "Build completed successfully for image: $image_name"
    fi
    rm -f "${log_file}.exit_code"
  else
    if [ -z "$failed_builds" ]; then
      failed_builds="$image_name"
    else
      failed_builds="$failed_builds $image_name"
    fi
    echo "Build failed for image: $image_name (no exit code found)"
  fi

  # Clean up log file
  rm -f "$log_file"

  pid_index=$((pid_index + 1))
  image_index=$((image_index + 1))
  log_index=$((log_index + 1))
done

# Check if any builds failed
if [ -n "$failed_builds" ]; then
  echo "The following image builds failed:"
  for failed_image in $failed_builds; do
    echo "  - $failed_image"
  done
  exit 1
fi

echo "Successfully built Docker images:"
for image_full_name in $image_full_names; do
  echo "  - ${image_full_name}"
done
