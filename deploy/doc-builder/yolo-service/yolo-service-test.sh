#!/bin/bash

# 定义服务地址
SERVICE_URL="http://localhost:51080"

# 检查是否提供了图片文件路径
if [ -z "$1" ]; then
  echo "用法: $0 <图片文件路径>"
  echo "将自动测试所有支持的模型类型: ouryolo, doclayout, yolotabledet"
  exit 1
fi

IMAGE_PATH="$1"

# 检查图片文件是否存在
if [ ! -f "$IMAGE_PATH" ]; then
  echo "错误: 图片文件 '$IMAGE_PATH' 不存在。"
  exit 1
fi

# 定义所有支持的模型类型
MODEL_TYPES=("ouryolo" "doclayout" "yolotabledet")

# 将图片文件转换为base64编码
echo "正在转换图片为base64编码..."
BASE64_IMAGE=$(base64 "$IMAGE_PATH" | tr -d '\n')

echo "=== YOLO 服务接口测试 ==="
echo "图片路径: $IMAGE_PATH"
echo "服务地址: $SERVICE_URL"
echo "支持的模型类型: ${MODEL_TYPES[*]}"
echo ""

# 测试所有YOLO模型类型
for MODEL_TYPE in "${MODEL_TYPES[@]}"; do
  echo "=========================================="
  echo "=== 测试模型: $MODEL_TYPE ==="
  echo "=========================================="

  echo "调用 /yolo-ocr/inference 接口..."
  curl -X POST \
    -H "Content-Type: application/json" \
    --data-binary @- "$SERVICE_URL/yolo-ocr/inference" <<EOF
{
  "image": "$BASE64_IMAGE",
  "model_type": "$MODEL_TYPE",
  "conf": 0.5
}
EOF

  echo -e "\n"
  echo "模型 $MODEL_TYPE 测试完成"
  echo ""
done

echo "=========================================="
echo "=== 测试 BERT 文本处理接口 ==="
echo "=========================================="
curl -X POST \
  -H "Content-Type: application/json" \
  --data-binary @- "$SERVICE_URL/bert/inference" <<EOF
{
  "text": "这是一个测试文本，用于验证BERT模型的文本处理功能。Test text for BERT model validation."
}
EOF

echo -e "\n\n=========================================="
echo "=== 所有测试完成 ==="
echo "=========================================="
echo "已测试的模型类型: ${MODEL_TYPES[*]}"
echo "BERT文本处理: 已测试"