#!/bin/bash

# 定义服务地址
SERVICE_URL="http://localhost:51081"

# 检查是否提供了图片文件路径
if [ -z "$1" ]; then
  echo "用法: $0 <图片文件路径>"
  exit 1
fi

IMAGE_PATH="$1"

# 检查图片文件是否存在
if [ ! -f "$IMAGE_PATH" ]; then
  echo "错误: 图片文件 '$IMAGE_PATH' 不存在。"
  exit 1
fi

# 将图片文件转换为base64编码
BASE64_IMAGE=$(base64 "$IMAGE_PATH" | tr -d '\n')

echo "=== 测试 PaddleOCR 文本识别接口 ==="
curl -X POST \
  -H "Content-Type: application/json" \
  --data-binary @- "$SERVICE_URL/paddle-ocr/inference" <<EOF
{
  "image": "$BASE64_IMAGE",
  "threshold": 0.5,
  "batch_size": 1
}
EOF

echo -e "\n\n=== 测试 表格检测接口 ==="
curl -X POST \
  -H "Content-Type: application/json" \
  --data-binary @- "$SERVICE_URL/table-det/inference" <<EOF
{
  "image": "$BASE64_IMAGE",
  "threshold": 0.5,
  "batch_size": 1
}
EOF

echo -e "\n\n=== 测试 表格单元格检测接口 ==="
curl -X POST \
  -H "Content-Type: application/json" \
  --data-binary @- "$SERVICE_URL/table-cell-det/inference" <<EOF
{
  "image": "$BASE64_IMAGE",
  "threshold": 0.5,
  "batch_size": 1
}
EOF

echo -e "\n\n=== 测试脚本执行完毕 ==="