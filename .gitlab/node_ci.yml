# 执行语法格式检查和单元测试
.package-cache-dirs: &package_cache_dirs
  - .npm
  - .bun_cache

stages:
  - setup
  - lint
  - review
  - test
  - package

variables:
  NPM_CONFIG_CACHE: '$CI_PROJECT_DIR/.npm'
  BUN_INSTALL_CACHE_DIR: "$CI_PROJECT_DIR/.bun_cache"

cache:
  key:
    files:
      - package-lock.json
  paths:
    - .npm
    - node_modules

.docker-base:
  # from https://gitlab.dipeak.com/dipeak/docker
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/docker:keep-28.2.1-buildx-0.24.0-main-feac3e9-20250530124736
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  services:
    - name: registry.gitlab.dipeak.com/dipeak/generic-repository/docker:keep-28.2.1-buildx-0.24.0-main-feac3e9-20250530124736
      command: ["--tls=false"]
      alias: docker
  before_script:
    - echo "$GENERIC_REPOSITORY_DEPLOY_PASSWORD" | docker login -u generic-repository-deploy --password-stdin registry.gitlab.dipeak.com

node_setup:
  stage: setup
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine
  script:
    - export http_proxy="http://**************:7890"
    - export https_proxy="http://**************:7890"
    - |
      if [ ! -d "node_modules" ] || [ "$(git diff --name-only $CI_COMMIT_BEFORE_SHA $CI_COMMIT_SHA | grep package-lock.json)" ]; then
        echo "Installing dependencies..."
        npm install -g npm@10.5.0 --registry=https://registry.npmmirror.com
        npm ci --registry=https://registry.npmmirror.com --canvas_binary_host_mirror=https://registry.npmmirror.com/-/binary/canvas
      else
        echo "Using cached node_modules"
      fi
  artifacts:
    paths:
      - node_modules # 确保 lint 阶段能够访问到 node_modules

node_lint:
  stage: lint
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine
  script:
    - export http_proxy="http://**************:7890"
    - export https_proxy="http://**************:7890"
    - echo "Linting TypeScript files."
    - npm run check-all
    - echo "Finish linting TypeScript files."
  dependencies:
    - node_setup
  artifacts:
    paths:
      - node_modules # 确保 test 阶段能够访问到 node_modules

python_lint:
  stage: lint
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/diplus-dev-ci:keep-826a2fb46c9a80869fcf05c0494370a8c1ac7015
  script:
    - echo "Linting Python files."
    - dev/lint/format-python.sh --check
    - echo "Finish linting Python files."

gpt-review:
  stage: review
  image: "registry.gitlab.dipeak.com/dipeak/generic-repository/ubuntu-web:latest"
  variables:
    GITLAB_TOKEN: "$CODE_REVIEW_GPT_GITLAB_TOKEN"
    GITLAB_HOST: "https://gitlab.dipeak.com"
    HTTP_PROXY: "http://**************:7893"
    HTTPS_PROXY: "http://**************:7893"
    NO_PROXY: ".dipeak.com,127.0.0.1,localhost,*********/8,***********/16"
  before_script:
    - if ! git cat-file -t "${CI_MERGE_REQUEST_DIFF_BASE_SHA}"; then git fetch --depth=1
      origin "${CI_MERGE_REQUEST_DIFF_BASE_SHA}"; fi
  script:
    - curl -fsSL https://bun.sh/install | bash
    - export PATH=~/.bun/bin:"$PATH"
    - git clone
      https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.dipeak.com/third-party/code-review-gpt.git
    - cd code-review-gpt && bun i && bun run build && npm install -g . && cd ..
    - code-review-gpt review --ci=gitlab
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
      == $CI_DEFAULT_BRANCH'
      when: always
    - when: never
  allow_failure: true

node_test:
  stage: test
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine
  script:
    - export http_proxy="http://**************:7890"
    - export https_proxy="http://**************:7890"
    - npm run prisma-generate
    - DISABLE_SQL_GENERATE_BYPASS=false npm run test:once
  dependencies:
    - node_lint

python_test:
  stage: test
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/ask-bi-python-dev:latest
  script:
    - curl -fsSL --user "user:MzvkQb8ns_DHcG29KaR2" "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/askbot/20250210/cached_server-bge-20250210.zip" > python/nl2metric/cached_server.zip
    - cd python/nl2metric && unzip cached_server.zip && cd -
    - curl -fsSL --user "user:8sMDSKRKKbsNgk_A9hwu" "https://gitlab.dipeak.com/api/v4/projects/255/packages/generic/ask_bi_nltk/0.0.1/nltk_dir.tar.gz" > python/nl2metric/nltk_dir.tar.gz
    - cd python/nl2metric && tar -zxvf nltk_dir.tar.gz -C /resources/punkt_dir  && cd -
    - echo "Set environment variables"
    - export DATABASE_URL=mysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/askbi_mix?allowPublicKeyRetrieval=true
    - export AI_DATA_OPERATOR_DATABASE_URL=mysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/ai_data_operator?allowPublicKeyRetrieval=true
    - export ASK_BI_HOST="http://***************:8000"
    - export ENABLE_NL2DOCUMENT="False"
    - export ENABLE_NL2DOCUMENT_BUILDER="False"
    - export ENABLE_REPORT_GENERATE='False'
    - export embedding_service_model=BGE
    - export NLTK_DATA=/resources/punkt_dir
    - export bge_model_path=/resource/bge/data/public_model/BGE_b5c9d86d763d9945f7c0a73e549a4a39c423d520
    - export embedding_model=embedding_service_api
    - echo "Starting main_embedding_service.py in the background."
    - python python/nl2metric/main_embedding_service.py &
    - sleep 5
    - echo "Running pytest to execute Python unit tests."
   # - pytest python/nl2metric/tests/mock_tests/

package_all_image:
  extends: .docker-base
  stage: package
  script:
    - echo "CI_REGISTRY is $CI_REGISTRY"
    - deploy/script/build_and_push.sh
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "web"'
      when: on_success
    - when: never
