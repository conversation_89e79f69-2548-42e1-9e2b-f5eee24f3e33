{"name": "ask-bi", "private": true, "version": "1.0.0", "type": "commonjs", "scripts": {"postinstall": "patch-package", "client-start": "vite", "client-start-host": "vite --host", "client-build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "client-build:visualizer": "cross-env VITE_VISUALIZER=true NODE_OPTIONS=--max-old-space-size=4096 vite build", "client-build-askdoc-upload": "cross-env VITE_ASKDOC_SHOW_UPLOAD=true npm run client-build", "client-preview": "vite preview", "lint": "npm run check-all", "prisma-generate": "PRISMA_ENGINES_MIRROR=https://registry.npmmirror.com/-/binary/prisma prisma generate", "prisma-migrate": "PRISMA_ENGINES_MIRROR=https://registry.npmmirror.com/-/binary/prisma prisma migrate deploy", "prisma-db-push": "PRISMA_ENGINES_MIRROR=https://registry.npmmirror.com/-/binary/prisma prisma db push", "server-build-inside": "tsc -p tsconfig.server.json", "server-build": "webpack", "server-start": "npm run prisma-generate && npx nodemon", "server-start-local-database-container": "npm run prisma-migrate && npm run prisma-generate && npx prisma db seed && npx nodemon", "prod-start-inside": "npx prisma migrate deploy && npm run prisma-generate && node dist-server/server/server.js", "prod-start": "npm run prisma-migrate && npm run prisma-generate && bytenode ./dist-server/server/bundle.min.jsc", "prod-start-with-prisma-seeding": "npm run prisma-migrate && npm run prisma-generate && npx prisma db seed && bytenode ./dist-server/server/bundle.min.jsc", "prepufa-prod-start": "node /app/prisma/updateRelationMode.mjs", "pufa-prod-start": "npm run prisma-db-push -- --skip-generate && bytenode ./dist-server/server/bundle.min.jsc", "lint-staged": "lint-staged", "test": "cross-env NODE_ENV=test vitest", "test:once": "cross-env NODE_ENV=test vitest run", "test:watch": "cross-env NODE_ENV=test vitest watch", "test:coverage": "cross-env NODE_ENV=test vitest run --coverage", "check-all": "npm run prisma-generate && npm run check-ts && npm run check-eslint && npm run check-css", "check-ts": "tsc --noEmit", "check-eslint": "eslint src --ext .js,.jsx,.ts,.tsx --max-warnings 0", "check-css": "stylelint \"src/**/*.css\"", "deploy": "npm run client-build && sh ./scripts/deploy.sh", "deploy-inside": "npm run client-build && npm run server-build-inside && DEPLOY_INSIDE=true sh ./scripts/deploy.sh", "build-askbi-chrome": "vite --config vite.chrome.config.ts build", "release-askbi-chrome": "npm run build-askbi-chrome && sh ./scripts/release-askbi-chrome.sh", "tar-all": "npm run client-build && npm run server-build-inside && sh ./scripts/tar-package.sh", "tar-frontend": "npm run client-build && npm run server-build-inside && ONLY_TAR_FRONTEND=true sh ./scripts/tar-package.sh", "tar-python": "ONLY_TAR_PYTHON=true sh ./scripts/tar-package.sh", "py": "concurrently \"cd python/nl2metric; source .venv/bin/activate; python main_embedding_service.py\" \"cd python/nl2metric; source .venv/bin/activate; python main_shared_service.py\" \"cd python/nl2metric; source .venv/bin/activate; python main.py\"", "prepare": "husky"}, "lint-staged": {"*.{scss,less,css}": ["stylelint --fix --quiet --allow-empty-input", "prettier --write"], "*.{ts,tsx,js}": ["prettier --write", "eslint --fix --quiet"]}, "prisma": {"seed": "ts-node -r tsconfig-paths/register -P tsconfig.server.json prisma/seed.ts"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/charts": "^2.1.1", "@ant-design/graphs": "^1.4.1", "@ant-design/icons": "^5.3.1", "@ant-design/plots": "^2.3.2", "@ant-design/pro-components": "^2.7.13", "@ant-design/pro-layout": "^7.19.10", "@ant-design/pro-table": "^3.14.2", "@antv/g6": "^4.8.24", "@antv/x6": "^2.18.1", "@antv/x6-geometry": "^2.0.5", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-react-shape": "2.0.8", "@antv/xflow": "^2.0.4", "@heroicons/react": "^2.1.1", "@js-preview/excel": "^1.7.12", "@mdx-js/mdx": "^3.1.0", "@microlink/react-json-view": "^1.26.2", "@prisma/client": "^6.1.0", "@sentry/node": "^9.25.1", "@sentry/react": "^9.22.0", "@types/tapable": "^2.2.7", "ahooks": "^3.8.1", "alipay-sdk": "^4.13.0", "antd": "^5.20.6", "axios": "^1.7.4", "body-parser": "^1.20.2", "bytenode": "^1.5.6", "casbin": "^5.34.0", "chalk": "^4.1.2", "clsx": "^2.1.1", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.2.1", "crypto-js": "^4.2.0", "csv-parse": "^5.6.0", "dayjs": "^1.11.12", "docx": "^8.5.0", "docx-preview": "^0.3.2", "dotenv": "^16.4.5", "echarts": "^5.5.1", "envalid": "^8.0.0", "eventemitter3": "^5.0.1", "express": "^4.19.2", "express-rate-limit": "^7.4.0", "express-session": "^1.18.0", "file-saver": "^2.0.5", "form-data": "^4.0.0", "fs-extra": "^11.3.0", "github-markdown-css": "^5.8.1", "html2canvas": "^1.4.1", "immer": "^10.1.1", "jotai": "^2.9.3", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "katex": "^0.16.21", "knex": "^3.1.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-table": "^3.0.4", "module-alias": "^2.2.3", "monaco-editor": "^0.50.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.0", "nanoid": "^3.3.7", "node-sql-parser": "^4.18.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdfjs-dist": "~3.8", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-photo-view": "^1.2.6", "react-reflex": "^4.2.6", "react-router-dom": "^6.26.1", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-directive": "^4.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-toc": "^9.0.0", "semver": "^7.6.3", "sql-formatter": "^15.4.0", "tapable": "^2.2.1", "ts-node": "^10.9.2", "use-immer": "^0.10.0", "vconsole": "^3.15.1", "winston": "^3.14.2", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.24.7", "@babel/runtime": "^7.25.0", "@types/chrome": "^0.0.270", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/dom-speech-recognition": "^0.0.4", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/file-saver": "^2.0.7", "@types/fs-extra": "^11.0.4", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.7", "@types/lodash-es": "^4.17.12", "@types/module-alias": "^2.0.4", "@types/multer": "^1.4.11", "@types/node": "^20.14.15", "@types/nunjucks": "^3.2.6", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/react": "^18.2.48", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/semver": "^7.5.8", "@types/ws": "^8.5.12", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/coverage-v8": "^1.6.0", "autoprefixer": "^10.4.20", "babel-loader": "^9.1.3", "casbin-prisma-adapter": "^1.7.0", "concurrently": "^9.1.2", "cookie": "^1.0.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-compat": "^4.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "husky": "^9.1.7", "lint-staged": "^15.2.9", "nodemon": "^3.1.4", "patch-package": "^8.0.0", "postcss": "^8.4.41", "postcss-nesting": "^12.1.5", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "prisma": "^6.1.0", "query-string": "^9.1.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "style-loader": "^3.3.4", "stylelint": "^16.8.2", "stylelint-config-standard": "^36.0.1", "stylelint-config-tailwindcss": "^0.0.7", "stylelint-order": "^6.0.4", "tailwindcss": "^3.4.10", "tsconfig-paths": "^4.2.0", "tsx": "^4.17.0", "typescript": "~5.5.4", "vite": "^5.4.1", "vite-plugin-compression": "^0.5.1", "vitest": "^1.4.0", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0", "zx": "^8.3.0"}, "engines": {"node": ">=20"}, "browserslist": ["chrome 80"], "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "4.9.5"}}