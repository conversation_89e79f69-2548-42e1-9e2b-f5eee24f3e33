import json

from abc import ABC, abstractmethod
from pydantic import BaseModel
from typing import Optional, Dict

from common.llm.fake_passthrough_llm import (
    FAKE_PASSTHROUGH_PROMPTS,
    FAKE_PASSTHROUGH_PROMPTS_MODE,
    get_fake_passthrough_prompt,
)
from common.logging.logger import get_logger
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from config.app_config import MODEL_TYPE_FAKE_PASSTHROUGH
from langchain_core.prompt_values import StringPromptValue
from langchain_core.runnables import RunnableConfig

logger = get_logger(__name__)

DEFAULT_TEMPLATE_FORMAT = "jinja2"


class PromptSelectorBase(BaseModel, ABC):
    project_name: Optional[str] = None
    project_id: Optional[str] = None
    # JobType.PROJECT_PARAMS_EXTRACT has None model_name
    model_name: Optional[str] = None
    model_id: Optional[str] = None
    model_type: str

    @abstractmethod
    def _select_prompt(self, stage: str):
        """Returns FewShotPromptTemplate or PromptTemplate"""

    def gen_prompt(self, input, stage, config: RunnableConfig):
        logger.info(f"start gen_prompt for stage {stage}")

        # This is for ut/debug
        model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]
        if model_type == MODEL_TYPE_FAKE_PASSTHROUGH:
            fake_passthrough_prompts = (
                config[CHAIN_META]
                .get(ChainMeta.ADDITIONAL_INFO, {})
                .get(FAKE_PASSTHROUGH_PROMPTS, None)
            )
            fake_passthrough_prompts_mode = (
                config[CHAIN_META]
                .get(ChainMeta.ADDITIONAL_INFO, {})
                .get(FAKE_PASSTHROUGH_PROMPTS_MODE, None)
            )

            prompt = get_fake_passthrough_prompt(
                fake_prompts=fake_passthrough_prompts,
                mode=fake_passthrough_prompts_mode,
                stage=stage,
                question=config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION],
            )
            return StringPromptValue(text=prompt)
        if isinstance(input, dict):
            input.update(
                config[CHAIN_META][ChainMeta.RUN_TIME].get(ChainRuntime.HINT, {})
            )
        prompt = self._select_prompt(stage)
        ret = prompt.invoke(input, config=config)
        logger.info(f"gen_prompt for stage {stage} succeed")
        return ret
