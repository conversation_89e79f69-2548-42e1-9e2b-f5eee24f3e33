from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

BRAIN_SYSTEM_PROMPT = """你的任务是根据用户问题和环境信息，选择合适的工具进行数据查询收集。

<thought-process>
你必须严格遵守下面的思考流程，分点思考：
1. 首先拆解问题，但不能改写查询
2. 检查环境信息：
(1) 检查用户问题涉及的指标和维度是否在[Metric]和[Dimension]中存在
(2) 检查[Document]是否与用户问题相关
(3) 检查[Web Search]是否为true，且<tools></tools>中是否存在web_search工具
3. 分析[Hint]中的提示，判断是否存在特殊处理规则
4. 选择合适的工具进行数据查询
</thought-process>

<rules>
- 你只能拆分问题，调用多个工具，不可以改写查询。
- 你只能拆分用户问题中的指标和维度。例如，“移动、联通、电信的中标金额分别是多少”可以拆分为：“移动的中标金额是多少？”、“联通的中标金额是多少？”、“电信的中标金额是多少？”
- 你不能拆分用户问题中的时间和计算方式。例如，“2024年各个季度的销售额是多少”或“24年中标金额同比和环比分别是多少”不能拆分“各季度”和“同比、环比”。
- 对于用户问题，你只需要调用工具获取有用的数据即可。
- 除嵌套查询外，如果用户问题包含多个子问题，必须拆解问题，分别调用工具回答。
- 当问题是嵌套查询时，无需拆分问题，直接将完整的查询传递给工具。
- 必须严格遵循 [Hint] 中的要求，如果 [Hint] 中的要求与 System Prompt 冲突，以 [Hint] 为准。
- 输入工具的查询中的指标和维度名称必须在 [Metric] 和 [Dimension] 中存在。
- 当且仅当 [Web Search] 的值为 true 且 <tools></tools> 中存在 web_search 工具时，才可以使用 web_search 工具。
- 当且仅当 [Document] 的内容与用户问题相关时，且 <tools></tools> 中存在 doc_retrieval 工具时，才可以使用 doc_retrieval 工具。
</rules>

"""

brain_system_prompt_suffix = """
<tools>
{{tools}}
</tools>

<output-format>
对于每个函数调用，返回一个 json 对象，其中包含 <tool_call></tool_call> XML 标记内的函数名称和参数：
<tool_call>
{"name": <function-name>, "arguments": <args-json-object>}</tool_call>
</output-format>
"""

nl2agent_brain_prompt = ""

# 注意这里name: {{ m.label }}，用m.name的话后面容易直接把bi的入参直接改成英文的name，导致提参失败
prompt_suffix = """[Metric]:
{% for m in metrics -%}
(name: {{ m.label }}, description: "{{m.prompt_description}}")
{% endfor -%}

[Dimension]:
{% for m in dimensions -%}
(name: {{ m.label }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}

[Hint]:
{{brain_hint}}

[Question]: {{question}}
"""


def gen_nl2agent_brain(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_brain_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.BRAIN] = sub_json_prompts

    sub_json_prompts_tool = {}
    sub_json_prompts_tool[DEFAULT_PROMPT] = gen_basic_json(
        BRAIN_SYSTEM_PROMPT, brain_system_prompt_suffix
    )
    json_prompts[ParamsExtractStage.BRAIN_TOOL] = sub_json_prompts_tool
