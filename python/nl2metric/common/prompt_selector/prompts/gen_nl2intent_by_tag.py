from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

from common.prompt_selector.utils import DEFAULT_PROMPT
from nl2metric.few_shots import BAOWU_PROJECT_NAME

nl2intent_by_tag_prompt = """你的任务是根据用户问题和环境对用户意图进行分类
<output-format>
{"intent_list":["intent1","intent2"]}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- intent_list的值只能包含以下选项：["连续问", "与召回相关", "非召回相关", "维度详情", "指标详情", "维度列表", "指标列表", "数据概述", "模型列表", "闲聊", "问指标", "问码值", "问知识", "需排序", "需对比", "需分析", "需同环比", "需对比全部指标", "需占比", "需分布"]
- 如果意图中包含连续问这个意图，还需要提取连续问中的首个问题，例如{"intent_list":["连续问"],"first_question":"问题内容"}
</rules>
"""

prompt_suffix = """下面是用户问题和环境，请根据这些信息提取意图：
[Question]: {{question}}
[Metrics]: {{metrics}}
[Dimensions]:
{% for m in dimensions -%}
(description: {{m.prompt_description}}, values: {{m.values_to_json}})
{% endfor -%}"""


def gen_nl2intent_by_tag(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2intent_by_tag_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.NL2INTENT_BY_TAG] = sub_json_prompts
