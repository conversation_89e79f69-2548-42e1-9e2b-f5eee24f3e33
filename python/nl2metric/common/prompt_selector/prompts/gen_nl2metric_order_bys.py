from common.prompt_selector.prompts.common import gen_fewshot_json, gen_basic_json
from common.prompt_selector.prompts.common import gen_fewshot_json, gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from metastore.base import Metric, Dimension
from nl2metric.example_selector import SimpleFilterExampleSelector
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    FEW_SHOT_PROJECT_NAME_KEY,
    JINGFEN_BI,
    TIANHONG_PROJECT_NAME,
    CHINA_LIFE_NAME,
    SCIENCE_CITY_NAME,
    BANK_PROJECT,
    DEFAULT_PROJECT_NAME,
)

PROMPT_PREFIX_BAOWU = """
我希望你扮演专业的数据分析师，你精通SQL领域知识，并能将其运用到从用户问题中提取参数。
你的目标是：根据问题的描述，只提取出相关的orderBys和limit，忽略其他参数的提取，并按照指定 JSON 格式返回，格式类似:
```json
{
    "orderBys": ["Metric1 DESC/ASC"],
    "limit": 0/1/n
}
```
你需要了解的信息包括：指标([Metric])列表的信息
[Metric]指标列表的格式为(name: #指标英文字段, description: #指标中文含义)


你的处理步骤为：
1. 排序需求：
- 理解问题语义，识别问题中是否包含排序相关词："最大"、"最小"、"前N"、"排名"、"由大到小"等
- 如果没有排序需求，orderBys置为空并跳过后续步骤
2. 排序字段：
- 从问题中识别要排序的对象
- 在Metric列表中找到对应的指标
- 确保选择的指标在给定列表中存在，不要编造字段
- 确保选择的指标name确实对应着你要使用的description
3. 排序顺序：
- 降序(DESC)场景：包含"最大"、"最高"、"最多"、"由大到小"、"最优"、"大于0"等
- 升序(ASC)场景：包含"最小"、"最低"、"最少"、"由小到大"、"最差"、"小于0"等
- 如无明确方向，默认使用降序
4. 判断输出限制
- 数字限制：出现"前N名"、"前N个"时，limit设为N
- 单条限制：出现"最大"、"最小"、"最多"、"最少"、"最高"、"最低"等单个极值时，limit设为1
- 无限制：未提及数量时，limit设为0

特别注意：
1. 在选择指标时，必须严格匹配指标的name和description的对应关系。不要仅根据description的相似性来选择指标，必须确保选择的指标name确实对应着你要使用的description。
2. 问题中出现带有REPORT的指标时，不能对REPORT指标进行排序

下面是几个实例，每个示例提供的信息中[Question]是问题、[Metric]是指标信息、[Think]是思考过程，[Result]是根据思考过程得到的提参结果。请你仔细分析每个示例中的[Think]思考过程，学习如何从用户问题，指标列表和维度列表信息中提取出我想要的参数。
"""

BAOWU_EXAMPLE_TPL = """
[Question]: {{question}}
[Metric]
{% for m in metrics -%}
(name: {{ m.name }}, description: {{m.prompt_description}})
{% endfor %}
[Think]: 
{{think}}
[Result]:
```json
{{ result | pretty_to_json }}
```
"""

PROMPT_SUFFIX_WITH_COT = """
下面是用户的问题，请根据信息给出你的[Think]和[Result]。[Think]中只需要包含[Question]中需要排序的实体，如果没有则为空。注意:请以json格式给出提参结果。
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: {{m.prompt_description}})
{% endfor %}
[Result]:
输出你的[Think]和[Result]
"""

BAOWU_EXAMPLES = [
    # 默认降序
    {
        "question": "查询分行的利润排名",
        "metrics": [
            Metric(name="I1000_SUM", label="利润"),
            Metric(name="I2000_SUM", label="净利润"),
            Metric(name="I3500_SUM", label="线下销售利润"),
        ],
        "dimensions": [
            Dimension(name="bch_nme", label="分行名称"),
            Dimension(name="sap_cnl_csf", label="销售渠道"),
        ],
        "think": """1. 排序需求：问题包含"排名"关键词，需要orderBy
2. 排序字段：利息收入对应指标I1000_SUM
3. 排序方向：排名默认从高到低，使用desc
4. 输出限制：未提及具体数量，limit为0""",
        "result": {"orderBys": ["I1000_SUM desc"], "limit": 0},
    },
    # 降序
    {
        "question": "哪年的流转税金及附加金额最低？",
        "metrics": [
            Metric(name="I1000_SUM", label="手续费支出-银联收单品牌服务费支出"),
            Metric(name="I2000_SUM", label="总销售营收"),
            Metric(name="I3500_SUM", label="线下销售利润"),
            Metric(name="C1700_SUM", label="流转税金及附加金额"),
        ],
        "dimensions": [
            Dimension(
                name="bch_nme",
                label="分行名称",
            ),
            Dimension(
                name="sap_cnl_csf",
                label="销售渠道",
            ),
        ],
        "think": """1. 排序需求：问题包含"最低"关键词，需要orderBy
2. 排序字段：流转税金及附加金额对应指标C1700_SUM
3. 排序方向：最低要求升序，使用asc
4. 输出限制：最低表示取第一条，limit为1""",
        "result": {"orderBys": ["C1700_SUM asc"], "limit": 1},
    },
    # 小于0
    {
        "question": "查询分行的利润小于0排名",
        "metrics": [
            Metric(name="I1000_SUM", label="利润"),
            Metric(name="I2000_SUM", label="净利润"),
            Metric(name="I3500_SUM", label="线下销售利润"),
            Metric(name="I4000_SUM", label="毛利润"),
        ],
        "dimensions": [
            Dimension(name="bch_nme", label="分行名称"),
            Dimension(name="sap_cnl_csf", label="销售渠道"),
        ],
        "think": """1. 排序需求：问题包含"排名"关键词，需要orderBy
2. 排序字段：利润对应指标I1000_SUM
3. 排序方向：问题中提到"小于0"，表示需要升序，使用asc
4. 输出限制：未提及具体数量，limit为0""",
        "result": {"orderBys": ["I1000_SUM asc"], "limit": 0},
    },
    # 大于0
    {
        "question": "今年分行净利润大于0排名前十",
        "metrics": [
            Metric(name="I1000_SUM", label="利润"),
            Metric(name="I2000_SUM", label="净利润"),
            Metric(name="I3500_SUM", label="线下销售利润"),
            Metric(name="I4000_SUM", label="毛利润"),
        ],
        "dimensions": [
            Dimension(name="bch_nme", label="分行名称"),
            Dimension(name="sap_cnl_csf", label="销售渠道"),
        ],
        "think": """1. 排序需求：问题包含"排名"关键词，需要orderBy
2. 排序字段：净利润对应指标I2000_SUM
3. 排序方向：问题中提到"大于0"，表示需要降序，使用desc
4. 输出限制：排名前十，limit为10""",
        "result": {"orderBys": ["I2000_SUM desc"], "limit": 10},
    },
    # 无排序需求
    {
        "question": "2023年各分行的管理费用是？",
        "metrics": [
            Metric(name="monthly_cumulative_right_to_use_assets", label="月累计使用权资产"),
            Metric(name="monthly_cumulative_management_expenses", label="月累计管理费用"),
            Metric(name="management_expenses", label="管理费用"),
            Metric(name="monthly_cumulative_total_profit", label="月累计利润总额"),
            Metric(
                name="monthly_cumulative_total_operating_costs",
                label="月累计营业总成本",
            ),
            Metric(name="monthly_cumulative_selling_expenses", label="月累计销售费用"),
        ],
        "dimensions": [],
        "think": """1. 排序需求：问题中无排序相关关键词
2. 结论：不需要orderBy和limit""",
        "result": {"orderBys": [""], "limit": 0},
    },
]


nl2metric_orderbys_prompt = """
你的任务是根据用户问题和指标列表提取问题对应的排序条件
<output-format>
{"orderBys":["NAME ORDER_TYPE"]}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 当问题需要筛选前N条数据时，需要添加"limit"条件，格式为{"orderBys":["NAME ORDER_TYPE"],"limit N"}
- ORDER_TYPE可选值为：asc、desc
- 当问题没有涉及排序时，直接输出空列表，即 {"orderBys":[]}
- 当问题涉及多个排序条件时，可以使用逗号连接，格式为{"orderBys":["NAME1 ORDER_TYPE1","NAME2 ORDER_TYPE2"]}
</rules>
下面是用户问题和指标列表，请根据这些信息提取排序条件：
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2metric_order_bys(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2metric_orderbys_prompt, "")

    # sub_json_prompts[BAOWU_PROJECT_NAME] = gen_fewshot_json(
    #     prompt_prefix=PROMPT_PREFIX_BAOWU,
    #     prompt_suffix=PROMPT_SUFFIX_WITH_COT,
    #     example_tpl=BAOWU_EXAMPLE_TPL,
    #     few_shot_fallback_examples=BAOWU_EXAMPLES,
    # )

    json_prompts[ParamsExtractStage.NL2METRIC_ORDER_BYS] = sub_json_prompts
