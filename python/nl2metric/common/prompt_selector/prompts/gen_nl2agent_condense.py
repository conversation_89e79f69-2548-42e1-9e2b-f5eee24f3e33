from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT


condense_prompt = """你的任务是针对多轮对话场景，结合历史对话信息，补全新的用户查询的细节信息，生成一个完整的用户查询。
<thought-process>
判断用户问题是否属于追问：
- 如果不属于，直接返回原问题
- 如果属于，根据对话历史改写用户查询。
- 参考对话历史中的指标、维度、时间等信息，明确用户新问题中的指标、维度、时间等信息
</thought-process>
<rules>
- 你只需要考虑追问的情况，只补全用户追问的细节信息
- 对于非追问的情况，直接返回原问题
- 对于用户首次提问的情况，直接返回原问题
- 不要在查询中加入任何其他多余的信息，保留用户查询的本意
</rules>
<hint>
{{condense_hint}}
</hint>
"""

condense_prompt_suffix = """
<hint>
{{condense_hint}}
</hint>
下面是对话历史信息：其中 <question> 标记用户问题，<response> 标记助手回答
{% for m in history -%}
{% if m.role == 'user' -%}
<question>
{{m.content}}
</question>
{% else -%}
<response>
</response>
{% endif %}
{% endfor %}
下面是用户本轮的问题，你要基于上面的历史记录condense成一个新的问题，不要思考直接给出结果：
<new_question>
{{question}}
</new_question>
"""

condense_no_history_response_prompt_suffix = """
<hint>
{{condense_hint}}
</hint>

下面是对话历史信息：其中 <question> 标记用户问题，<response> 标记助手回答
{% for m in history -%}
{% if m.role == 'user' -%}
<question>
{{m.content}}
</question>
{% endif %}
{% endfor %}
下面是用户本轮的问题，你要基于上面的历史记录condense成一个新的问题，不要思考直接给出结果：
<new_question>
{{question}}
</new_question>
"""


def gen_nl2agent_condense(json_prompts: dict):
    sub_json_prompts = {
        DEFAULT_PROMPT: gen_basic_json(condense_prompt, condense_prompt_suffix)
    }
    json_prompts[ParamsExtractStage.AGENT_CONDENSE] = sub_json_prompts


def gen_nl2agent_condense_no_history_response(json_prompts: dict):
    sub_json_prompts = {
        DEFAULT_PROMPT: gen_basic_json(
            condense_prompt, condense_no_history_response_prompt_suffix
        )
    }
    json_prompts[
        ParamsExtractStage.AGENT_CONDENSE_NO_HISTORY_RESPONSE
    ] = sub_json_prompts
