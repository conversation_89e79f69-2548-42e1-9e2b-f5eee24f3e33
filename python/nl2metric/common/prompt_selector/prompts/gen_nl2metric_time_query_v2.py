from common.prompt_selector.prompts.common import gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage

prompt_nl2time_query_v2 = """
你的任务是根据用户问题和环境时间提取问题对应的时间范围

<output-format>
{"timeStartFunction":{"year":YYYY,"month":MM,"day":DD},"timeEndFunction":{"year":YYYY,"month":MM,"day":DD},"timeGranularity":"TIME_GRANULARITY"}
</output-format>

<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- timeGranularity可选值为：total、year、quarter、month、day
- 当用户问题没有涉及时间时，直接输出空json对象，即 {}
</rules>
下面是用户问题和环境时间，请根据这些信息提取时间范围：
[Question]: {{question}}
[Current Time]: {{current_date_str}}
[Hint]
{{time_range_hint}}
"""


def gen_nl2metric_time_query_v2(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(prompt_nl2time_query_v2, "")
    json_prompts[ParamsExtractStage.NL2METRIC_TIME_QUERY_V2] = sub_json_prompts
