from common.prompt_selector.prompts.common import gen_fewshot_json, gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from metastore.base import Metric, Dimension, DimensionValue
from nl2metric.example_selector import SimpleFilterExampleSelector
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    FEW_SHOT_PROJECT_NAME_KEY,
    JINGFEN_BI,
    CHINA_LIFE_NAME,
)

PROMPT_PREFIX_BAOWU = """
我希望你扮演专业的数据分析师,你精通SQL领域知识,并能将其运用到从用户问题中提取参数。
你的目标是：根据问题的描述,提取出相关的groupBys维度及其groupByLevel层级, 并按照指定 JSON 格式返回,格式类似:
```json
{"groupBysWithLevel": [{"groupBys": "dimension", "groupByLevel": int}]}
```

我会提供候选的维度([Dimension])信息：
1. [Dimension]维度列表的格式为：(name: #维度英文字段, description: #维度的中文含义，多个同义词用';'隔开, values: #维度码值数组)

"groupBys"数组中的字段对应SQL中的GROUP BY子句,以下两种情况需要将维度加入groupBys:
1. 需要按照维度分组的场景:
- 问题中包含分组关键词:"每个"、"各个"、"按照"、"根据"、"以"、"按"、"分别"等
- 问题需要对维度进行分组统计
- 问题需要查询2个及以上维度的码值
2. 维度涉及指标操作的场景:
- 需要对维度下的指标进行排序
- 需要对该维度下的码值进行筛选，展示满足指标过滤条件的码值
- 需要对该维度下的码值进行筛选，统计有多少满足指标过滤条件的码值，展示数量
- 维度需要出现在SELECT子句中
3. groupByLevel: 维度的层级,规则如下:
- 如果问题中没有明确指出层级,则默认groupByLevel为0
- 如果问题中提出要求查询所有层级的码值，则groupByLevel为-1
- 如果问题中明确指出维度的层级(如"一级"、"二级"、"三级"等),则使用指定的层级数字，层级数字对应关系: 一级=1, 二级=2, 三级=3, 以此类推

你的处理步骤为：
1. 识别[Question]中的时间信息，并忽略掉。
2. 分析[Question]，[Metric]和[Dimension]，识别以下情况：
- 是否需要按照某个维度分组查询数据，需要groupby
- 是否需要查询某个维度的指标，并且对维度按照指标进行排序，需要groupby
- 是否需要查询某个维度，并且只展示满足问题中提出的指标过滤条件的码值，需要groupby
- 是否需要查询某个维度，并且对满足问题中提出的指标过滤条件的码值进行计数，需要groupby
- 查询某个维度的总和，不需要分组查看时，不需要groupby
3. 如果出现上述任一情况，且维度名出现在[Dimension]中，将相关维度放入到groupBys中；否则，请忽略
4. 判断[Question]中维度的层级，放入到groupByLevel中
- 问题中包含码值，查询当前码值的数据，groupByLevel为0
- 问题中包含码值，查询当前码值的子码值的数据，groupByLevel为-1
- 问题中包含码值，且指定了层级，groupByLevel为指定层级
- 问题中不包含码值，groupByLevel为0
- 问题中不包含码值，且指定了层级，groupByLevel为指定层级
5. 如果问题中没有提到任何[Dimension]，或者只是单纯查询指标值，将结果置为空

特别注意：
- 对于(name: COMPANY_INNER_CODE_DES, description: 企业;公司))这个维度，查询某公司的子公司，groupByLevel为-1；查询具体某公司，groupByLevel为0；查询某公司二级子公司，groupByLevel为2
- 时间维度不需要加入groupby
- 指标过滤条件指对问题中指标需要满足的过滤条件，通常为 指标>0 或者 指标<0 等
- 判断groupby需要忽略指标名中可能包含的维度名带来的影响。同时理解指标和维度，如果需要提取的维度，来自于指标名，和指标名重叠，此时请判断不需要groupby
"""

EXAMPLE_TPL_DEFAULT = """
[Question]: {{question}}

[Dimension]: 
{% for m in dimensions -%}
(name: {{ m.name }}, description: {{m.prompt_description}})
{% endfor -%}
[Think]: 
{{think}}
[Result]:
```json
{{result|pretty_to_json}}
```
"""

PROMPT_SUFFIX_DEFAULT = """
下面是用户的问题,请给出你的[Think]和[Result](JSON 提参结果)。注意:请以json格式给出提参结果,不要附带任何其他信息 
[Question]: {{question}}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: {{m.prompt_description}}), values: {{m.values_to_json}})
{% endfor -%}
[Think]:
"""

PROMPT_SUFFIX_BAOWU = """
下面是用户的问题，请根据信息给出你的[Result]。注意:请以json格式给出提参结果。
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: {{m.prompt_description}}), values: {{m.values_to_json}})
{% endfor -%}
[Hint]
{{groupby_hint}}
[Result]:
输出你的[Think]和[Result]
"""


nl2metric_gourpby_prompt = """
你的任务是根据用户问题和维度列表提取问题对应的分组条件
<output-format>
{"groupBys":["DIM_NAME"]}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 分组条件的值必须存在于维度列表中
- 当问题没有涉及分组时，直接输出空列表，即 {"groupBys":[]}
- 当问题涉及多个分组时，可以使用逗号连接，格式为{"groupBys":["DIM_NAME1","DIM_NAME2"]}
</rules>
下面是用户问题和环境，请根据这些信息提取分组条件：
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
[Hint]
{{groupby_hint}}
"""


def gen_nl2metric_group_bys(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2metric_gourpby_prompt, "")

    sub_json_prompts[BAOWU_PROJECT_NAME] = gen_fewshot_json(
        prompt_prefix=PROMPT_PREFIX_BAOWU,
        prompt_suffix=PROMPT_SUFFIX_BAOWU,
        example_tpl=EXAMPLE_TPL_DEFAULT,
    )

    json_prompts[ParamsExtractStage.NL2METRIC_GROUP_BYS] = sub_json_prompts
