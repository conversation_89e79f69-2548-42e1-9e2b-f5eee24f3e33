from urllib.parse import urljoin
from uuid import UUID

import requests
import simplejson as json
import math
import threading
from datetime import datetime

from enum import Enum

from common.trace import tracer
from config import app_config
from nl2agent.dag.node import Node
from nl2agent.dag.dag_executor import DagExecutor
from colorama import Fore
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.runnables import Runnable
from langfuse.callback import <PERSON>back<PERSON><PERSON><PERSON>
from langfuse.utils import _get_timestamp

from common.logging.logger import get_logger, get_langchain_debug_logger, get_elk_logger
from common.trace.langfuse import BILangfuseCallbackHandler
from common.types.base import (
    ParamsExtractStage,
    JobType,
    META_EXCLUDE_FROM_TRACE,
    ChainMeta,
    ChainRuntime,
    CB_CLOSE_MARK,
)
from dataclasses_json import DataClassJsonMixin
from typing import Any, List, Literal, Optional, Dict
from llama_index.core.schema import BaseNode, NodeWithScore
from llama_index.core.indices.query.schema import QueryBundle
from langchain_core.load.serializable import Serializable
from langchain_core.tracers.base import BaseTracer
from langchain_core.tracers.schemas import Run
from pydantic import BaseModel
from common.utils.compat import BaseModel as langchain_BaseModel
from langchain_core.pydantic_v1 import BaseModel as langchain_BaseModel
from nl2agent.types import ToolType
from nl2document.common.models.model import insert_or_update_request_stage_info
from nl2intent.nl2intent_by_tag import IntentTags
from nl2agent.common.csv_mgr import CsvMgr

logger = get_logger(__name__)


def safe_get_str(obj):
    try:
        return str(obj)
    except Exception as e:
        return str(type(obj))


agent_delta_builds_types, agent_steps_list_types = ToolType.report_values()


class ProgressRunStatus(Enum):
    NULL = 0
    SUCCEED = 1
    FAILED = 2
    NEED_MANUAL_SELECT = 3


class ProgressStatus(BaseModel):
    task_id: str = None
    nl2intent: ProgressRunStatus = ProgressRunStatus.NULL
    nl2intent_msg: str = ""
    nl2metric_time_query: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_time_query_msg: str = ""
    nl2metric_metrics: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_metrics_msg: str = ""
    nl2metric_group_bys: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_group_bys_msg: str = ""
    nl2metric_order_bys: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_order_bys_msg: str = ""
    nl2metric_where: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_where_msg: str = ""
    nl2meta: ProgressRunStatus = ProgressRunStatus.NULL
    nl2meta_msg: str = ""
    attr_analysis_time: ProgressRunStatus = ProgressRunStatus.NULL
    attr_analysis_time_msg: str = ""
    attr_analysis_param: ProgressRunStatus = ProgressRunStatus.NULL
    attr_analysis_param_msg: str = ""

    nl2agent_build: ProgressRunStatus = ProgressRunStatus.NULL
    nl2agent_build_msg: str = ""
    nl2agent_delta_builds: list = []
    nl2agent_steps_list: list = []
    nl2agent_status: list = []

    close: bool = False

    def to_dict(self):
        return {
            "task_id": self.task_id,
            "nl2intent": self.nl2intent.value,
            "nl2intent_msg": self.nl2intent_msg,
            "nl2metric_time_query": self.nl2metric_time_query.value,
            "nl2metric_time_query_msg": self.nl2metric_time_query_msg,
            "nl2metric_metrics": self.nl2metric_metrics.value,
            "nl2metric_metrics_msg": self.nl2metric_metrics_msg,
            "nl2metric_group_bys": self.nl2metric_group_bys.value,
            "nl2metric_group_bys_msg": self.nl2metric_group_bys_msg,
            "nl2metric_order_bys": self.nl2metric_order_bys.value,
            "nl2metric_order_bys_msg": self.nl2metric_order_bys_msg,
            "nl2metric_where": self.nl2metric_where.value,
            "nl2metric_where_msg": self.nl2metric_where_msg,
            "nl2meta": self.nl2meta.value,
            "nl2meta_msg": self.nl2meta_msg,
            "attr_analysis_time": self.attr_analysis_time.value,
            "attr_analysis_time_msg": self.attr_analysis_time_msg,
            "attr_analysis_param": self.attr_analysis_param.value,
            "attr_analysis_param_msg": self.attr_analysis_param_msg,
            "nl2agent_build": self.nl2agent_build.value,
            "nl2agent_build_msg": self.nl2agent_build_msg,
            "nl2agent_delta_builds": self.nl2agent_delta_builds,
            "nl2agent_steps_list": self.nl2agent_steps_list,
            "nl2agent_status": self.nl2agent_status,
            "close": self.close,
        }


def post_progress_status(
    trace_id,
    task_id,
    progress_status: ProgressStatus,
    call_back_addr: Optional[str] = None,
):
    # 优先使用传入的回调地址，如果没有则使用配置中的地址
    host = call_back_addr or app_config.ASK_BI_HOST
    if not host:
        raise ValueError("ASK_BI_HOST is not set and no call_back_addr provided")
    header = {tracer.TRACE_ID_KEY: trace_id, "Content-Type": "application/json"}
    if not host.startswith("http://") and not host.startswith("https://"):
        host = f"http://{host}"
    progress_status.task_id = task_id
    data = progress_status.to_dict()
    url = urljoin(
        host,
        f"api/chats/chat-progress-callback?taskId={task_id}",
    )
    if progress_status.close:
        logger.info(f"start post_progress_status, url: {url}, data {data}")

    try:
        response = requests.post(url, headers=header, json=data, timeout=10)
        if response.json().get("code", 0) != 0:
            logger.error(
                f"post_progress_status response invalid: {response.text}, url: {url}"
            )
    except requests.exceptions.Timeout:
        logger.error(f"post_progress_status timeout, url: {url}")
    except Exception as e:
        logger.error(f"post_progress_status failed {e}, url: {url}")


def report_nl2document_progress_status_end(
    trace_id, task_id, uuid_to_stages, progress_status, *args, **kwargs
):
    uuid = kwargs.get("run_id", None)
    stage_name = uuid_to_stages.get(uuid, None)
    if stage_name is None:
        return
    if stage_name == ParamsExtractStage.QUERY_MEETING:
        insert_or_update_request_stage_info(trace_id, "complete")


def report_progress_status_close(
    trace_id,
    task_id,
    progress_status: ProgressStatus,
    call_back_addr: Optional[str] = None,
):
    if not app_config.ENABLE_PROGRESS_STATUS:
        return
    if not task_id:
        return
    progress_status.close = True
    post_progress_status(trace_id, task_id, progress_status, call_back_addr)


def _get_msg_from_args(args):
    if not args:
        return ""
    msg = args[0]
    if msg is None:
        return ""
    if isinstance(msg, str):
        return msg
    elif isinstance(msg, Exception):
        return str(msg)
    return json.dumps(
        msg, indent=2, ensure_ascii=False, cls=BIJsonEncoder, ignore_nan=True
    )


def report_progress_status_end(
    run_status,
    trace_id,
    task_id,
    uuid_to_stages,
    progress_status: ProgressStatus,
    *args,
    **kwargs,
):
    if not app_config.ENABLE_PROGRESS_STATUS:
        return
    if not task_id:
        return
    uuid = kwargs.get("run_id", None)
    stage_name = uuid_to_stages.get(uuid, None)
    if stage_name is None:
        return

    # 从配置中获取回调地址
    call_back_addr = kwargs.get("call_back_addr", None)

    if stage_name == "nl2intent":
        progress_status.nl2intent = run_status
        progress_status.nl2intent_msg = _get_msg_from_args(args)
    elif stage_name == "query_metrics":
        progress_status.nl2metric_metrics = run_status
        progress_status.nl2metric_metrics_msg = _get_msg_from_args(args)
    elif stage_name == "query_groupbys":
        progress_status.nl2metric_group_bys = run_status
        progress_status.nl2metric_group_bys_msg = _get_msg_from_args(args)
    elif stage_name == "query_time":
        progress_status.nl2metric_time_query = run_status
        progress_status.nl2metric_time_query_msg = _get_msg_from_args(args)
    elif stage_name == "query_where":
        progress_status.nl2metric_where = run_status
        progress_status.nl2metric_where_msg = _get_msg_from_args(args)
    elif stage_name == "query_orderbys":
        progress_status.nl2metric_order_bys = run_status
        progress_status.nl2metric_order_bys_msg = _get_msg_from_args(args)
    elif stage_name == "nl2meta":
        progress_status.nl2meta = run_status
        progress_status.nl2meta_msg = _get_msg_from_args(args)
    elif stage_name == "attr_analysis_time":
        progress_status.attr_analysis_time = run_status
        progress_status.attr_analysis_time_msg = _get_msg_from_args(args)
    elif stage_name == "attr_analysis_param":
        progress_status.attr_analysis_param = run_status
        progress_status.attr_analysis_param_msg = _get_msg_from_args(args)
    elif stage_name == "agent_build":
        progress_status.nl2agent_build = run_status
        progress_status.nl2agent_build_msg = _get_msg_from_args(args)
    elif stage_name in agent_delta_builds_types:
        progress_status.nl2agent_delta_builds.append(
            [run_status.value, stage_name, _get_msg_from_args(args)]
        )
    elif stage_name in agent_steps_list_types:
        progress_status.nl2agent_steps_list.append(
            [run_status.value, _get_msg_from_args(args)]
        )
    elif stage_name == "agent_reporter":
        progress_status.nl2agent_status = args[0]
    post_progress_status(trace_id, task_id, progress_status, call_back_addr)
    return


def report_progress_status_begin(uuid_to_stages, trace_id, task_id, *args, **kwargs):
    if not task_id:
        return
    chain_name = kwargs.get("name", None)
    uuid = kwargs.get("run_id", None)
    if chain_name is not None:
        if (
            chain_name == "parse_intent_response"
            or chain_name == "parse_intent_by_tag_response"
        ):
            uuid_to_stages[uuid] = "nl2intent"
        elif chain_name == "query_metrics_verify":
            uuid_to_stages[uuid] = "query_metrics"
        elif chain_name == "query_groupbys_verify":
            uuid_to_stages[uuid] = "query_groupbys"
        elif chain_name == "parse_time_query_response":
            uuid_to_stages[uuid] = "query_time"
        elif chain_name == "query_where_verify":
            uuid_to_stages[uuid] = "query_where"
        elif chain_name == "query_orderbys_verify":
            uuid_to_stages[uuid] = "query_orderbys"
        elif chain_name == "nl2meta":
            uuid_to_stages[uuid] = "nl2meta"
        elif chain_name == "attr_analysis_time":
            uuid_to_stages[uuid] = "attr_analysis_time"
        elif chain_name == "attr_analysis_param":
            uuid_to_stages[uuid] = "attr_analysis_param"
        elif chain_name == "agent_build":
            uuid_to_stages[uuid] = "agent_build"
        elif chain_name == "agent_reporter":
            uuid_to_stages[uuid] = "agent_reporter"
        elif chain_name in agent_delta_builds_types:
            uuid_to_stages[uuid] = chain_name
        elif chain_name in agent_steps_list_types:
            uuid_to_stages[uuid] = chain_name
        elif chain_name == ParamsExtractStage.QUERY_MEETING:
            uuid_to_stages[uuid] = chain_name
            insert_or_update_request_stage_info(trace_id, "create")
        elif chain_name in [
            ParamsExtractStage.NL2MEETING_PARAMS,
            ParamsExtractStage.NL2DOCUMENT_RETRIEVE_NODES,
            ParamsExtractStage.NL2DOCUMENT_SYNTHESIZE_ANSWER,
        ]:
            uuid_to_stages[uuid] = chain_name
            insert_or_update_request_stage_info(trace_id, chain_name)
    return


class BIJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        # BaseNode/NodeWithScore is BaseModel, so check it first
        if isinstance(obj, BaseNode):
            return f"Node {obj.id_}"
        elif isinstance(obj, CsvMgr):
            return obj.file_descps
        elif isinstance(obj, Node):
            return obj.to_json()
        elif isinstance(obj, DagExecutor):
            return obj.to_json()
        elif isinstance(obj, Runnable):
            return str(obj)
        elif isinstance(obj, NodeWithScore):
            return f"NodeWithScore node {obj.node.id_} score {obj.score}"
        elif isinstance(obj, QueryBundle):
            return f"QueryBundle {obj.query_str}"
        elif isinstance(obj, BaseModel):
            return obj.model_dump_json(indent=2)
        elif isinstance(obj, langchain_BaseModel):
            try:
                return obj.json(indent=2, ensure_ascii=False)
            except Exception as e:
                try:
                    return obj.json()
                except Exception as e:
                    return safe_get_str(obj)
        elif isinstance(obj, Serializable):
            return obj.to_json()
        elif isinstance(obj, DataClassJsonMixin):
            return obj.to_json(indent=2, ensure_ascii=False)
        elif isinstance(obj, IntentTags):
            return obj.tags
        elif isinstance(obj, Enum):
            return obj.value
        else:
            try:
                return super(BIJsonEncoder, self).default(obj)
            except Exception as e:
                return safe_get_str(obj)


def trace_this_chain(name: str):
    if name.startswith("Runnable"):
        return False
    if name.endswith("Retriever"):
        return False
    if name in {"StrOutputParser", "PromptTemplate"}:
        return False
    return True


def try_json_stringify(obj: Any, fallback: str) -> str:
    """
    Try to stringify an object to JSON.
    Args:
        obj: Object to stringify.
        fallback: Fallback string to return if the object cannot be stringified.

    Returns:
        A JSON string if the object can be stringified, otherwise the fallback string.

    """
    try:
        return json.dumps(obj, indent=2, ensure_ascii=False, cls=BIJsonEncoder)
    except Exception as e:
        logger.exception(f"BIJsonEncoder try_json_stringify failed {e} obj: {obj}")
        return fallback


def elapsed(run: Any) -> str:
    """Get the elapsed time of a run.

    Args:
        run: any object with a start_time and end_time attribute.

    Returns:
        A string with the elapsed time in seconds or
            milliseconds if time is less than a second.

    """
    elapsed_time = run.end_time - run.start_time
    milliseconds = elapsed_time.total_seconds() * 1000
    return f"{milliseconds:.0f}ms"


def get_current_ts() -> str:
    current_timestamp = int(datetime.utcnow().timestamp())
    dt_object = datetime.utcfromtimestamp(current_timestamp)
    formatted_str = dt_object.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
    return formatted_str


class LogCallbackHandlerBase(BaseTracer):
    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)

    def _persist_run(self, run: Run) -> None:
        pass

    def get_parents(self, run: Run) -> List[Run]:
        parents = [run]
        current_run = run
        while current_run.parent_run_id:
            parent = self.run_map.get(str(current_run.parent_run_id))
            if parent:
                parents.append(parent)
                current_run = parent
            else:
                break
        return parents

    def get_breadcrumbs(self, run: Run) -> str:
        parents = self.get_parents(run)[::-1]
        type_names = []
        for p in parents:
            type_names.append(f"{p.run_type}:{p.name}")
        return " > ".join(type_names)


class BackendReporterQueueCallbackHandler(CallbackHandler):
    name: str = "BackendReporterQueueCallbackHandler"

    def __init__(
        self,
        trace_id: str,
        task_id: str,
        name: str,
        user_id: str,
        queue,
        call_back_addr: Optional[str] = None,
    ) -> None:
        self.trace_id = trace_id
        self.task_id = task_id
        self.name = name
        self.dipeak_user_id = user_id
        self.queue = queue
        self.call_back_addr = call_back_addr
        self.ended = False

    def __del__(self):
        # python的析构方法不一定会马上调，所以保险起见最好还是主动调一下end方法
        self.close()

    def _trim_metadata(self, metadata: Optional[Dict[str, Any]]):
        if not metadata:
            return metadata
        trim_metadata = metadata.copy()
        for k in META_EXCLUDE_FROM_TRACE:
            if k in trim_metadata:
                del trim_metadata[k]
        return trim_metadata

    def close(self, report_close=True):
        if not self.ended:
            self.ended = True
            if not report_close:
                return
            self.queue.put(
                (
                    self.trace_id,
                    self.dipeak_user_id,
                    self.task_id,
                    self.name,
                    CB_CLOSE_MARK,
                    None,
                    {"call_back_addr": self.call_back_addr},
                )
            )

    def trim_kwargs(self, kwargs):
        # trim metadata that is unnecessary to report or contains objs that cannot be pickled
        metadata = kwargs.get("metadata", None)
        new_metadata = self._trim_metadata(metadata)
        kwargs["metadata"] = new_metadata
        # 添加回调地址到 kwargs
        # if self.call_back_addr:
        kwargs["call_back_addr"] = self.call_back_addr

    def trim_args(self, args, fallback):
        # for some stange reason, class Where cannot be shown on langfuse, so convert it to json first
        # 运行中经常发现，input/output其实是同一个对象，chain中只是对同一个对象做了一些操作。
        # 这样的情况下如果直接把原始的对象放到self.queue里面，langfuse中看到的input/output经常就是一样的
        new_args = []
        for i in range(len(args)):
            # 测试结果看，on_chain_start的args有两个元素，[0]是一个字典，记录了元数据
            # on_chain_end就只有一个元素，代表output
            # 不会出现len(args) >= 3的情况
            if (len(args) == 2 and i == 0) or len(args) >= 3:
                new_args.append(args[i])
            else:
                new_args.append(try_json_stringify(args[i], fallback))

        return new_args

    def on_agent_finish(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_agent_finish",
                args,
                kwargs,
            )
        )

    def on_chain_start(self, *args: Any, **kwargs: Any) -> Any:
        new_args = self.trim_args(args, "[input]")
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chain_start",
                new_args,
                kwargs,
            )
        )

    def on_chain_end(self, *args: Any, **kwargs: Any) -> Any:
        new_args = self.trim_args(args, "[output]")
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chain_end",
                new_args,
                kwargs,
            )
        )

    def on_chat_model_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chat_model_start",
                args,
                kwargs,
            )
        )

    def on_llm_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_start",
                args,
                kwargs,
            )
        )

    def on_agent_action(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_agent_action",
                args,
                kwargs,
            )
        )

    def on_chain_error(self, *args: Any, **kwargs: Any) -> Any:
        new_args = []
        for arg in args:
            if isinstance(arg, Exception):
                # some exception info are json strs, so donnot use repr here
                new_args.append(str(arg))
            else:
                new_args.append(arg)
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chain_error",
                new_args,
                kwargs,
            )
        )

    def on_retriever_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_retriever_start",
                args,
                kwargs,
            )
        )

    def on_retriever_end(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_retriever_end",
                args,
                kwargs,
            )
        )

    def on_retriever_error(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_retriever_error",
                args,
                kwargs,
            )
        )

    def on_tool_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_tool_start",
                args,
                kwargs,
            )
        )

    def on_tool_end(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_tool_end",
                args,
                kwargs,
            )
        )

    def on_tool_error(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_tool_error",
                args,
                kwargs,
            )
        )

    def on_llm_end(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_end",
                args,
                kwargs,
            )
        )

    def on_llm_new_token(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_new_token",
                args,
                kwargs,
            )
        )

    def on_llm_error(self, *args: Any, **kwargs: Any) -> Any:
        new_args = []
        for arg in args:
            if isinstance(arg, Exception):
                # some exception info are json strs, so donnot use repr here
                new_args.append(str(arg))
            else:
                new_args.append(arg)
        self.trim_kwargs(kwargs)
        self.queue.put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_error",
                new_args,
                kwargs,
            )
        )


class LocalBackendReporterCallbackHandler(BaseCallbackHandler):
    name: str = "LocalBackendReporterCallbackHandler"

    def __init__(
        self,
        trace_id: str,
        task_id: str,
        name: str,
        user_id: str,
        call_back_addr: Optional[str] = None,
    ) -> None:
        self.trace_id = trace_id
        self.task_id = task_id
        self.name = name
        self.dipeak_user_id = user_id
        self.call_back_addr = call_back_addr
        self.ended = False
        self.progress_status = ProgressStatus()
        self.uuid_to_stages: Dict[UUID, str] = {}

    def __del__(self):
        # python的析构方法不一定会马上调，所以保险起见最好还是主动调一下end方法
        self.close()

    def _trim_metadata(self, metadata: Optional[Dict[str, Any]]):
        if not metadata:
            return metadata
        trim_metadata = metadata.copy()
        for k in META_EXCLUDE_FROM_TRACE:
            if k in trim_metadata:
                del trim_metadata[k]
        return trim_metadata

    def close(self, report_close=True):
        if not self.ended:
            self.ended = True
            if not report_close:
                return
            report_progress_status_close(
                self.trace_id,
                self.task_id,
                self.progress_status,
                self.call_back_addr,
            )

    def trim_kwargs(self, kwargs):
        # trim metadata that is unnecessary to report or contains objs that cannot be pickled
        metadata = kwargs.get("metadata", None)
        new_metadata = self._trim_metadata(metadata)
        kwargs["metadata"] = new_metadata
        # 添加回调地址到 kwargs
        # if self.call_back_addr:
        kwargs["call_back_addr"] = self.call_back_addr

    def trim_args(self, args, fallback):
        # for some stange reason, class Where cannot be shown on langfuse, so convert it to json first
        # 运行中经常发现，input/output其实是同一个对象，chain中只是对同一个对象做了一些操作。
        # 这样的情况下如果直接把原始的对象放到self.queue里面，langfuse中看到的input/output经常就是一样的
        new_args = []
        for i in range(len(args)):
            # 测试结果看，on_chain_start的args有两个元素，[0]是一个字典，记录了元数据
            # on_chain_end就只有一个元素，代表output
            # 不会出现len(args) >= 3的情况
            if (len(args) == 2 and i == 0) or len(args) >= 3:
                new_args.append(args[i])
            else:
                new_args.append(try_json_stringify(args[i], fallback))

        return new_args

    def on_chain_start(self, *args: Any, **kwargs: Any) -> Any:
        new_args = self.trim_args(args, "[input]")
        self.trim_kwargs(kwargs)
        report_progress_status_begin(
            self.uuid_to_stages, self.trace_id, self.task_id, new_args, kwargs
        )

    def on_chain_end(self, *args: Any, **kwargs: Any) -> Any:
        new_args = self.trim_args(args, "[output]")
        self.trim_kwargs(kwargs)
        report_progress_status_end(
            ProgressRunStatus.SUCCEED,
            self.trace_id,
            self.task_id,
            self.uuid_to_stages,
            self.progress_status,
            new_args,
            kwargs,
        )
        if app_config.ENABLE_NL2DOCUMENT:
            report_nl2document_progress_status_end(
                self.trace_id,
                self.task_id,
                self.uuid_to_stages,
                self.progress_status,
                *args,
                **kwargs,
            )

    def on_chain_error(self, *args: Any, **kwargs: Any) -> Any:
        new_args = []
        for arg in args:
            if isinstance(arg, Exception):
                # some exception info are json strs, so donnot use repr here
                new_args.append(str(arg))
            else:
                new_args.append(arg)
        self.trim_kwargs(kwargs)
        report_progress_status_end(
            ProgressRunStatus.FAILED,
            self.trace_id,
            self.task_id,
            self.uuid_to_stages,
            self.progress_status,
            new_args,
            kwargs,
        )


class BackendReporterTsCallbackHandler(CallbackHandler):
    name: str = "BackendReporterTsCallbackHandler"

    def __init__(self, trace_id: str, task_id: str, name: str, user_id: str) -> None:
        self.trace_id = trace_id
        self.task_id = task_id
        self.name = name
        self.dipeak_user_id = user_id
        self.cbs = []
        self.cb_times = []
        self.cbs_lock: threading.Lock = threading.Lock()
        super().__init__()

    @property
    def data(self):
        with self.cbs_lock:
            return (self.cbs, self.cb_times)

    def _put(self, item, run_id, start_time, end_time):
        with self.cbs_lock:
            self.cbs.append(item)
            self.cb_times.append((start_time, end_time))

    def _trim_metadata(self, metadata: Optional[Dict[str, Any]]):
        if not metadata:
            return metadata
        trim_metadata = metadata.copy()
        for k in META_EXCLUDE_FROM_TRACE:
            if k in trim_metadata:
                del trim_metadata[k]
        return trim_metadata

    def trim_kwargs(self, kwargs):
        # trim metadata that is unnecessary to report or contains objs that cannot be pickled
        metadata = kwargs.get("metadata", None)
        new_metadata = self._trim_metadata(metadata)
        kwargs["metadata"] = new_metadata

    def trim_args(self, args, fallback):
        # for some stange reason, class Where cannot be shown on langfuse, so convert it to json first
        # 运行中经常发现，input/output其实是同一个对象，chain中只是对同一个对象做了一些操作。
        # 这样的情况下如果直接把原始的对象放到self.queue里面，langfuse中看到的input/output经常就是一样的
        new_args = []
        for i in range(len(args)):
            # 测试结果看，on_chain_start的args有两个元素，[0]是一个字典，记录了元数据
            # on_chain_end就只有一个元素，代表output
            # 不会出现len(args) >= 3的情况
            if (len(args) == 2 and i == 0) or len(args) >= 3:
                new_args.append(args[i])
            else:
                new_args.append(try_json_stringify(args[i], fallback))

        return new_args

    def on_agent_finish(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_agent_finish",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_chain_start(self, *args: Any, **kwargs: Any) -> Any:
        new_args = self.trim_args(args, "[input]")
        self.trim_kwargs(kwargs)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chain_start",
                new_args,
                kwargs,
            ),
            kwargs["run_id"],
            _get_timestamp(),
            None,
        )

    def on_chain_end(self, *args: Any, **kwargs: Any) -> Any:
        new_args = self.trim_args(args, "[output]")
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chain_end",
                new_args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_chat_model_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chat_model_start",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            _get_timestamp(),
            None,
        )

    def on_llm_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_start",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            _get_timestamp(),
            None,
        )

    def on_agent_action(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_agent_action",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_chain_error(self, *args: Any, **kwargs: Any) -> Any:
        new_args = []
        for arg in args:
            if isinstance(arg, Exception):
                # some exception info are json strs, so donnot use repr here
                new_args.append(str(arg))
            else:
                new_args.append(arg)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_chain_error",
                new_args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_retriever_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_retriever_start",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            _get_timestamp(),
            None,
        )

    def on_retriever_end(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_retriever_end",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_retriever_error(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_retriever_error",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_tool_start(self, *args: Any, **kwargs: Any) -> Any:
        self.trim_kwargs(kwargs)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_tool_start",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            _get_timestamp(),
            None,
        )

    def on_tool_end(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_tool_end",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_tool_error(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_tool_error",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_llm_end(self, *args: Any, **kwargs: Any) -> Any:
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_end",
                args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )

    def on_llm_error(self, *args: Any, **kwargs: Any) -> Any:
        new_args = []
        for arg in args:
            if isinstance(arg, Exception):
                # some exception info are json strs, so donnot use repr here
                new_args.append(str(arg))
            else:
                new_args.append(arg)
        self._put(
            (
                self.trace_id,
                self.dipeak_user_id,
                self.task_id,
                self.name,
                "on_llm_error",
                new_args,
                kwargs,
            ),
            kwargs["run_id"],
            None,
            _get_timestamp(),
        )


class DebugLogCallbackHandler(LogCallbackHandlerBase):
    name: str = "debug_log_callback_handler"
    chains_we_care = {
        ParamsExtractStage.NL2METRIC_TIME_QUERY,
        ParamsExtractStage.NL2METRIC_METRICS,
        ParamsExtractStage.NL2METRIC_WHERE,
        ParamsExtractStage.NL2METRIC_GROUP_BYS,
        ParamsExtractStage.NL2METRIC_ORDER_BYS,
        ParamsExtractStage.ATTR_ANALYSIS_TIME,
        ParamsExtractStage.ATTR_ANALYSIS_PARAM,
        JobType.PARAMS_EXTRACT,
        "PARALLEL_INTENT: " + JobType.PARAMS_EXTRACT,
        JobType.PROJECT_PARAMS_EXTRACT,
    }

    chains_to_print_meta = {
        JobType.PARAMS_EXTRACT,
        "PARALLEL_INTENT: " + JobType.PARAMS_EXTRACT,
        JobType.PROJECT_PARAMS_EXTRACT,
    }

    def __init__(
        self, id: str, trace_name: str, show_everything: bool = False, **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)
        self.id = id
        # if trace_name has /, create folder will fail
        self.debug_logger = get_langchain_debug_logger(trace_name.replace("/", "_"))
        self.show_everything = show_everything

    def _on_chain_start(self, run: Run) -> None:
        if self.show_everything or run.name in self.chains_we_care:
            if (
                run.name.endswith("etriever") or run.name == "StrOutputParser"
            ):  # Retriever or retriever
                # embedding too long
                self.debug_logger.info(
                    f"{self.id} [chain/start] [{self.get_breadcrumbs(run)}] Entering Chain run"
                )
            else:
                self.debug_logger.info(
                    f"{self.id} [chain/start] [{self.get_breadcrumbs(run)}] Entering Chain run with input:\n{try_json_stringify(run.inputs, '[inputs]')}"
                )

    def _on_chain_end(self, run: Run) -> None:
        if self.show_everything or run.name in self.chains_we_care:
            if run.name.endswith("etriever"):  # Retriever or retriever
                # embedding too long
                self.debug_logger.info(
                    f"{self.id} [chain/end] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting Chain run"
                )
            else:
                self.debug_logger.info(
                    f"{self.id} [chain/end] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting Chain run with outputs:\n{try_json_stringify(run.outputs, '[outputs]')}"
                )

        if self.show_everything or run.name in self.chains_to_print_meta:
            for ok_log in run.metadata[ChainMeta.RUN_TIME][ChainRuntime.OK_LOGS]:
                self.debug_logger.info(
                    f"{self.id} [chain/end] [{self.get_breadcrumbs(run)}] OK_LOGS {ok_log}"
                )
            for err_log in run.metadata[ChainMeta.RUN_TIME][ChainRuntime.ERROR_LOGS]:
                self.debug_logger.error(
                    f"{self.id} [chain/end] [{self.get_breadcrumbs(run)}] ERROR_LOGS {err_log}"
                )

    def _on_chain_error(self, run: Run) -> None:
        self.debug_logger.error(
            f"{self.id} [chain/error] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting Chain run with error:\n{try_json_stringify(run.error, '[error]')}"
        )

    def _on_llm_start(self, run: Run) -> None:
        if "prompts" in run.inputs:
            for p in run.inputs["prompts"]:
                self.debug_logger.info(
                    f"{self.id} [llm/start] [{self.get_breadcrumbs(run)}] Entering LLM run with prompt:\n{p}"
                )
        else:
            self.debug_logger.info(
                f"{self.id} [llm/start] [{self.get_breadcrumbs(run)}] Entering LLM run with input:\n{try_json_stringify(run.inputs, '[prompts]')}"
            )

    def _on_llm_end(self, run: Run) -> None:
        try:
            generations = run.outputs["generations"]
            for tmp_generation in generations:
                for tmp_dict in tmp_generation:
                    llm_text = tmp_dict["text"]
                    self.debug_logger.info(
                        f"{self.id} [llm/end] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting LLM run with answer:\n{llm_text}"
                    )
        except Exception as e:
            self.debug_logger.info(
                f"{self.id} [llm/end] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting LLM run with outputs:\n{try_json_stringify(run.outputs, '[response]')}"
            )

    def _on_llm_error(self, run: Run) -> None:
        self.debug_logger.error(
            f"{self.id} [llm/error] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting LLM run with error:\n{try_json_stringify(run.error, '[error]')}"
        )


class LogCallbackHandler(LogCallbackHandlerBase):
    name: str = "log_callback_handler"

    def __init__(
        self, id: str, host: str = "", user_id: str = None, **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)
        self.id = id
        self.host = host
        self.user_id = user_id

    def _generate_common_log_part(self):
        log_result = {
            "timestamp": get_current_ts(),
            "request_id": self.id,
            "host": self.host,
            "user_id": self.user_id,
            "service_type": "nl2metric_service",
            "result_code": "200",
        }
        return log_result

    def log_query_meeting(self, run: Run) -> None:
        log_result = self._generate_common_log_part()
        log_result["duration"] = elapsed(run)
        log_result["input"] = run.inputs
        log_result["output"] = run.outputs
        chain_name = self.get_breadcrumbs(run)
        if chain_name in [
            "chain:query_meeting > chain:query_meeting_postprocess",
            "chain:query_meeting > chain:RunnableBranch > chain:nl2meeting_query > chain:retrieve_nodes_postprocess",
        ]:
            log_result["module_type"] = chain_name
            get_elk_logger().info(
                try_json_stringify(log_result, "error json stringify query_meeting ")
            )

    def log_langchain(self, run: Run) -> None:
        log_result = self._generate_common_log_part()
        log_result["duration"] = elapsed(run)
        log_result["input"] = run.inputs
        log_result["output"] = run.outputs
        log_result["chain_name"] = run.name
        log_result["module_type"] = self.get_breadcrumbs(run)
        if run.error is not None:
            log_result["error"] = run.error
            log_result["result_code"] = "502"
        get_elk_logger().info(
            try_json_stringify(
                log_result, f"error json stringify langchain call{run.name}"
            )
        )

    def _ELK_Logger_process(self, run: Run) -> None:
        chain_name = self.get_breadcrumbs(run)
        if chain_name.startswith("chain:query_meeting"):
            self.log_query_meeting(run)
        else:
            self.log_langchain(run)
        # elif run.name == 'condense_query':
        #     self.log_condense_query(run)
        # elif run.name == 'RunnableAssign<intent>':
        #     self.log_intent(run)
        # elif run.name == 'RunnableParallel<nl2metric_group_bys,nl2metric_where,nl2metric_time_query,nl2metric_metrics,nl2metric_order_bys>':
        #     self.log_nl2metric(run)
        # elif run.name == 'RunnableParallel<time_attr,param_attr,attr_type,question>':
        #     self.log_analysis_params(run)
        # elif run.name == 'attr_analysis_dimension':
        #     self.log_attr_analysis_dimension(run)
        # elif run.name == 'ChatOpenAI':
        #     self.log_llm_chat(run)

    def _on_chain_start(self, run: Run) -> None:
        if (
            run.name.endswith("etriever") or run.name == "StrOutputParser"
        ):  # Retriever or retriever
            # embedding too long
            logger.info(
                f"{self.id} {Fore.GREEN}[chain/start]{Fore.RESET} [{self.get_breadcrumbs(run)}] Entering Chain run"
            )
        else:
            logger.info(
                f"{self.id} {Fore.GREEN}[chain/start]{Fore.RESET} [{self.get_breadcrumbs(run)}] Entering Chain run with input:\n{try_json_stringify(run.inputs, '[inputs]')}"
            )

    def _on_chain_end(self, run: Run) -> None:
        if run.name.endswith("etriever"):  # Retriever or retriever
            # embedding too long
            logger.info(
                f"{self.id} {Fore.CYAN}[chain/end]{Fore.RESET} [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting Chain run"
            )
        else:
            logger.info(
                f"{self.id} {Fore.CYAN}[chain/end]{Fore.RESET} [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting Chain run with outputs:\n{try_json_stringify(run.outputs, '[outputs]')}"
            )
        self._ELK_Logger_process(run)

    def _on_chain_error(self, run: Run) -> None:
        logger.error(
            f"{self.id} {Fore.RED}[chain/error]{Fore.RESET} [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting Chain run with error:\n{try_json_stringify(run.error, '[error]')}"
        )
        self._ELK_Logger_process(run)

    def _on_llm_start(self, run: Run) -> None:
        if "prompts" in run.inputs:
            for p in run.inputs["prompts"]:
                logger.info(
                    f"{self.id} [llm/start] [{self.get_breadcrumbs(run)}] Entering LLM run with prompt:\n{p}"
                )
        else:
            logger.info(
                f"{self.id} [llm/start] [{self.get_breadcrumbs(run)}] Entering LLM run with input:\n{try_json_stringify(run.inputs, '[prompts]')}"
            )

    def _on_llm_end(self, run: Run) -> None:
        try:
            generations = run.outputs["generations"]
            for tmp_generation in generations:
                for tmp_dict in tmp_generation:
                    llm_text = tmp_dict["text"]
                    logger.info(
                        f"{self.id} [llm/end] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting LLM run with answer:\n{llm_text}"
                    )
            self._ELK_Logger_process(run)
        except Exception as e:
            logger.info(
                f"{self.id} [llm/end] [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting LLM run with outputs:\n{try_json_stringify(run.outputs, '[response]')}"
            )

    def _on_llm_error(self, run: Run) -> None:
        logger.error(
            f"{self.id} {Fore.RED}[llm/error]{Fore.RESET} [{self.get_breadcrumbs(run)}] [{elapsed(run)}] Exiting LLM run with error:\n{try_json_stringify(run.error, '[error]')}"
        )
        self._ELK_Logger_process(run)

    def close(
        self,
    ):
        pass


STAGES_WE_CARE = {
    ParamsExtractStage.NL2METRIC_TIME_QUERY,
    ParamsExtractStage.NL2METRIC_METRICS,
    ParamsExtractStage.NL2METRIC_WHERE,
    ParamsExtractStage.NL2METRIC_GROUP_BYS,
    ParamsExtractStage.NL2METRIC_ORDER_BYS,
    ParamsExtractStage.ATTR_ANALYSIS_TIME,
    ParamsExtractStage.ATTR_ANALYSIS_PARAM,
}


class MemCallbackHandler(BaseTracer):
    """Tracer that logs via the input Logger."""

    name: str = "mem_callback_handler"

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.trace = {}
        self.__trace_map = {}
        self.__lock = threading.Lock()

    def _persist_run(self, run: Run) -> None:
        pass

    def _on_chain_start(self, run: Run) -> None:
        trace = trace_this_chain(run.name)
        if not trace:
            return
        self.__build(
            run,
            {
                "name": run.name,
                "run_type": run.run_type,
                "id": str(run.id),
                # "inputs" : try_json_stringify(run.inputs, '[inputs]')
            },
        )

    def _on_chain_end(self, run: Run) -> None:
        trace = trace_this_chain(run.name)
        if not trace:
            return
        self.__build(
            run,
            {
                "name": run.name,
                "run_type": run.run_type,
                "id": str(run.id),
                "elapse": elapsed(run),
                "outputs": try_json_stringify(run.outputs, "[outputs]"),
            },
        )

    def _on_chain_error(self, run: Run) -> None:
        trace = trace_this_chain(run.name)
        # always trace err
        self.__build(
            run,
            {
                "name": run.name,
                "run_type": run.run_type,
                "id": str(run.id),
                "elapse": elapsed(run),
                "error": try_json_stringify(run.error, "[error]"),
            },
        )

    def _on_llm_start(self, run: Run) -> None:
        prompts = (
            [p.strip() for p in run.inputs["prompts"]]
            if "prompts" in run.inputs
            else run.inputs
        )
        self.__build(
            run,
            {
                "name": run.name,
                "run_type": run.run_type,
                "id": str(run.id),
                "inputs": try_json_stringify(prompts, "[prompts]"),
            },
        )

    def _on_llm_end(self, run: Run) -> None:
        self.__build(
            run,
            {
                "name": run.name,
                "run_type": run.run_type,
                "id": str(run.id),
                "outputs": try_json_stringify(run.outputs, "[response]"),
            },
        )

    def _on_llm_error(self, run: Run) -> None:
        self.__build(
            run,
            {
                "name": run.name,
                "run_type": run.run_type,
                "id": str(run.id),
                "elapse": elapsed(run),
                "error": try_json_stringify(run.error, "[error]"),
            },
        )

    def __locate_stage(self, run: Run) -> str:
        current_run = run
        while True:
            if current_run.name in STAGES_WE_CARE:
                return current_run.name
            if not current_run.parent_run_id:
                break
            parent = self.run_map.get(str(current_run.parent_run_id))
            if not parent:
                break
            current_run = parent
        return None

    def __build(self, run: Run, run_dict: dict):
        with self.__lock:
            run_id = str(run.id)
            if run_id in self.__trace_map:
                self.__trace_map[run_id].update(run_dict)
            else:
                stage = self.__locate_stage(run)
                if stage:
                    if not stage in self.trace:
                        self.trace[stage] = []
                    self.__trace_map[run_id] = run_dict
                    self.trace[stage].append(run_dict)
