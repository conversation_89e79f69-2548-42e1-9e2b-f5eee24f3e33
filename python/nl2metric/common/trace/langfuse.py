from typing import Any, Dict, Optional

from common.types.base import ChainM<PERSON>, META_EXCLUDE_FROM_TRACE
from config import app_config
from langfuse.callback import CallbackHandler


class BILangfuseCallbackHandler(CallbackHandler):
    name: str = "BILangfuseCallbackHandler"

    def trim_metadata(self, metadata: Optional[Dict[str, Any]]):
        if not metadata:
            return metadata
        trim_metadata = metadata.copy()
        for k in META_EXCLUDE_FROM_TRACE:
            if k in trim_metadata:
                del trim_metadata[k]
        return trim_metadata

    def on_chain_start(self, *args: Any, **kwargs: Any) -> Any:
        metadata = kwargs.get("metadata", None)
        new_metadata = self.trim_metadata(metadata)
        kwargs["metadata"] = new_metadata
        super().on_chain_start(*args, **kwargs)

    def on_llm_start(self, *args: Any, **kwargs: Any) -> Any:
        metadata = kwargs.get("metadata", None)
        new_metadata = self.trim_metadata(metadata)
        kwargs["metadata"] = new_metadata
        super().on_llm_start(*args, **kwargs)

    def close(self, report_close=True):
        pass


def get_langfuse_callback():
    if app_config.ENABLE_LANGFUSE:
        return BILangfuseCallbackHandler(
            public_key=app_config.LANGFUSE_PUBLIC_KEY,
            secret_key=app_config.LANGFUSE_SECRET_KEY,
            host=app_config.LANGFUSE_HOST,
        )
    else:
        return None


langfuse_callback = get_langfuse_callback()
