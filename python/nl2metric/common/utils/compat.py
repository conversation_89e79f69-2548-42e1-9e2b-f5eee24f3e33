# compat.py

import importlib.util
from packaging.version import parse as parse_version

try:
    import importlib.metadata as importlib_metadata  # Python 3.8+
except ImportError:
    import importlib_metadata  # type: ignore


def is_langchain_using_pydantic_v1() -> bool:
    """Check whether langchain_core is using pydantic_v1."""
    try:
        version = importlib_metadata.version("langchain-core")
        return parse_version(version) < parse_version("0.3.63")
    except importlib_metadata.PackageNotFoundError:
        # Fallback: try to detect the module directly
        return importlib.util.find_spec("langchain_core.pydantic_v1") is not None


if is_langchain_using_pydantic_v1():
    from langchain_core.pydantic_v1 import BaseModel, Field
else:
    from pydantic import BaseModel, Field

__all__ = ["BaseModel", "Field"]
