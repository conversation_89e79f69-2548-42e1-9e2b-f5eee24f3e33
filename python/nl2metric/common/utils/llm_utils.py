import functools

import simplejson
from langchain_openai import ChatOpenAI

from config.project_config import ProjectConfig


def create_llm_model_by_project_config(model_type, project_config: ProjectConfig):
    llm_model_params = project_config.llm_model_params(model_type)
    if "openai_api_key" not in llm_model_params:
        llm_model_params["openai_api_key"] = "EMPTY"
    if "request_timeout" not in llm_model_params:
        llm_model_params["request_timeout"] = 60
    if "temperature" not in llm_model_params:
        llm_model_params["temperature"] = 0.001
    if "logprobs" not in llm_model_params:
        llm_model_params["logprobs"] = False
    if "top_p" not in llm_model_params:
        llm_model_params["top_p"] = 0.01
    if "max_tokens" not in llm_model_params:
        llm_model_params["max_tokens"] = 8192
    if "extra_body" not in llm_model_params:
        llm_model_params["extra_body"] = {"repetition_penalty": 1.0}
    params_key = simplejson.dumps(llm_model_params, sort_keys=True)
    return create_llm_model(params_key)


def is_model_thinking(model: ChatOpenAI) -> bool:
    if (
        model.extra_body.get("chat_template_kwargs") is not None
        and model.extra_body.get("chat_template_kwargs").get("enable_thinking", True)
        is False
    ):
        return False
    return True


@functools.lru_cache(maxsize=20)
def create_llm_model(params_key: str):
    llm_model_params = simplejson.loads(params_key)
    return ChatOpenAI(**llm_model_params)


if __name__ == "__main__":
    from config.project_config import ProjectConfig

    project_config = ProjectConfig("mock", "mock")
    llm = create_llm_model_by_project_config("qwen3", project_config)
    response = llm.invoke("你好")
    print(response.content)
    llm = create_llm_model_by_project_config("qwen3_nothink", project_config)
    response = llm.invoke("你好")
    print(response.content)
