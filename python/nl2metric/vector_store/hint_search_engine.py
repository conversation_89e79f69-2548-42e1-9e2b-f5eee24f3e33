from typing import List, Dict, Any, Optional, Union

from pymilvus import (
    AnnSearchRequest,
    MilvusClient,
    DataType,
    Function,
    FunctionType,
    WeightedRanker,
)
from pymilvus.orm.iterator import QueryIterator

from common.llm.embedding import create_embedding_model
from common.logging.logger import get_logger
from common.utils.concurrent_utils import synchronized_lru_cache
from config.doc_config import milvus_uri
from vector_store.data_models import (
    DeleteResult,
    HintSearchResult,
    BusinessTermBase,
    SearchResults,
    QueryResult,
)

logger = get_logger(__name__)
embed_model, _, dimension_num = create_embedding_model("embedding_api")


@synchronized_lru_cache(maxsize=100)
def get_query_embedding(query_text: str):
    return embed_model.get_query_embedding(query_text)


class HintHybridSearchEngine:
    """混合搜索引擎，支持向量和关键词的混合搜索"""

    def __init__(
        self,
        milvus_uri: str,
        collection_name: str = "hint_search_collection_v2",
        force_recreate: bool = False,
    ):
        self.collection_name = collection_name
        self.milvus_client = MilvusClient(uri=milvus_uri)
        self.dimension = dimension_num
        collections = self.milvus_client.list_collections()
        if self.collection_name in collections:
            if force_recreate:
                self.milvus_client.drop_collection(self.collection_name)
                logger.info("Collection already exists, recreating collection")
                self.create_collection()
                logger.info("Collection created successfully")
        else:
            self.create_collection()
            logger.info("Collection created successfully")
        self.load_collection()

    def create_collection(self) -> None:
        """创建并初始化集合"""
        schema = MilvusClient.create_schema(
            auto_id=False,
            enable_dynamic_field=True,
        )

        schema.add_field(
            field_name="id", datatype=DataType.VARCHAR, is_primary=True, max_length=50
        )
        schema.add_field(
            field_name="text",
            datatype=DataType.VARCHAR,
            max_length=4096,
            enable_analyzer=True,
        )
        schema.add_field(
            field_name="semantic_project_id",
            datatype=DataType.VARCHAR,
            max_length=128,
        )
        schema.add_field(
            field_name="semantic_scene_id",
            datatype=DataType.VARCHAR,
            max_length=128,
        )
        schema.add_field(field_name="sparse", datatype=DataType.SPARSE_FLOAT_VECTOR)
        schema.add_field(
            field_name="dense", datatype=DataType.FLOAT_VECTOR, dim=self.dimension
        )
        schema.add_field(
            field_name="creator", datatype=DataType.VARCHAR, max_length=1000
        )
        schema.add_field(field_name="type", datatype=DataType.VARCHAR, max_length=50)
        schema.add_field(field_name="created_at", datatype=DataType.INT64)
        schema.add_field(field_name="updated_at", datatype=DataType.INT64)
        schema.add_field(
            field_name="tags",
            datatype=DataType.ARRAY,
            element_type=DataType.VARCHAR,
            max_capacity=100,
            max_length=1024,
        )
        schema.add_field(
            # 额外补充信息字段
            field_name="extra_info",
            datatype=DataType.VARCHAR,
            max_length=4096,
            default=None,
        )
        schema.add_field(
            field_name="agents",
            datatype=DataType.ARRAY,
            element_type=DataType.VARCHAR,
            max_capacity=100,
            max_length=1024,
        )

        bm25_function = Function(
            name="text_bm25_emb",
            input_field_names=["text"],
            output_field_names=["sparse"],
            function_type=FunctionType.BM25,
        )
        schema.add_function(bm25_function)

        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index("tags", index_type="INVERTED", index_name="tags_index")
        index_params.add_index(
            field_name="dense",
            index_name="dense_index",
            index_type="FLAT",
            metric_type="IP",
        )

        index_params.add_index(
            field_name="sparse", index_type="AUTOINDEX", metric_type="BM25"
        )
        index_params.add_index(
            field_name="semantic_scene_id",
            index_type="INVERTED",
            index_name="semantic_scene_id",
        )
        index_params.add_index(
            field_name="semantic_project_id",
            index_type="INVERTED",
            index_name="semantic_project_id",
        )

        index_params.add_index(
            field_name="agents",
            index_type="INVERTED",
            index_name="agents_index",
        )
        self.milvus_client.create_collection(
            collection_name=self.collection_name,
            schema=schema,
            index_params=index_params,
            consistency_level="Strong",
        )

    def upsert(
        self, datas: List[BusinessTermBase], batch_size: Optional[int] = 100
    ) -> None:
        """更新或插入文本数据"""
        # 确保 batch_size 是有效的
        batch_size = min(batch_size or 100, len(datas)) if datas else 0
        if not datas:
            logger.info("No data provided for upsert.")
            return
        total_processed = 0  # 记录已经处理的总数
        for i in range(0, len(datas), batch_size):
            # 获取当前批次的数据
            batch_datas = datas[i : i + batch_size]
            batch_texts = [item.text for item in batch_datas]
            # 获取当前批次的 embeddings
            dense_vectors = embed_model.get_text_embedding_batch(batch_texts)
            # 将当前批次的数据和 embeddings 配对
            data_to_upsert = [
                {**item.model_dump(), "dense": vector}
                for item, vector in zip(batch_datas, dense_vectors)
            ]
            self.milvus_client.upsert(
                collection_name=self.collection_name, data=data_to_upsert
            )
            total_processed += len(batch_datas)
            logger.info(f"Processed {total_processed}/{len(datas)} records")

    def search(
        self, query_text: str, limit: int = 2, offset: int = 0, output_fields=["*"]
    ) -> SearchResults:
        """执行搜索，同时返回向量和关键词搜索结果"""
        query_vector = get_query_embedding(query_text)
        text_results = self.milvus_client.search(
            collection_name=self.collection_name,
            data=[query_text],
            anns_field="sparse",
            params={"metric_type": "BM25", "params": {"drop_ratio_search": 0}},
            limit=limit,
            offset=offset,
            output_fields=output_fields,
            consistency_level="Strong",
        )

        vector_results = self.milvus_client.search(
            collection_name=self.collection_name,
            data=[query_vector],
            anns_field="dense",
            params={"metric_type": "IP"},
            limit=limit,
            offset=offset,
            output_fields=output_fields,
            consistency_level="Strong",
        )

        text_items = text_results[0] if text_results else []
        vector_items = vector_results[0] if vector_results else []

        return SearchResults(
            items=text_items + vector_items,
            total=len(text_items) + len(vector_items),
            page={"offset": offset, "limit": limit},
        )

    def hybrid_search(
        self,
        query_text: str,
        limit: int = 10,
        output_fields: Optional[List[str]] = ["*"],
        timeout: Optional[float] = None,
        partition_names: Optional[List[str]] = None,
        filter: Optional[str] = None,
        offset: int = 0,
        dense_weight: float = 0.5,
        sparse_weight: float = 0.5,
    ) -> HintSearchResult:
        """执行混合搜索，使用加权排序结果"""
        query_vector = get_query_embedding(query_text)

        requests = [
            AnnSearchRequest(
                data=[query_text],
                anns_field="sparse",
                param={"metric_type": "BM25", "params": {"drop_ratio_search": 0}},
                limit=limit,
                expr=filter,
            ),
            AnnSearchRequest(
                data=[query_vector],
                anns_field="dense",
                param={"metric_type": "IP"},
                limit=limit,
                expr=filter,
            ),
        ]

        ranker = WeightedRanker(sparse_weight, dense_weight)

        results = self.milvus_client.hybrid_search(
            collection_name=self.collection_name,
            reqs=requests,
            ranker=ranker,
            limit=limit,
            output_fields=output_fields,
            timeout=timeout,
            partition_names=partition_names,
            offset=offset,
            consistency_level="Strong",
        )

        return HintSearchResult.from_milvus_results(
            results[0] if results else [],
            weights={"dense": dense_weight, "sparse": sparse_weight},
        )

    def load_collection(self) -> None:
        """加载集合到内存"""
        self.milvus_client.load_collection(self.collection_name)
        logger.info("Collection loaded successfully")

    def delete(
        self,
        ids: Optional[Union[List[str], str, str]] = None,
        filter: Optional[str] = None,
        timeout: Optional[float] = None,
        partition_name: Optional[str] = None,
    ) -> DeleteResult:
        """删除记录"""
        if (ids is not None and filter is not None) or (ids is None and filter is None):
            raise ValueError("Must specify either ids or filter, but not both")

        result = self.milvus_client.delete(
            collection_name=self.collection_name,
            ids=ids,
            filter=filter,
            timeout=timeout,
            partition_name=partition_name,
        )

        return DeleteResult.from_milvus_result(result)

    def get_by_ids(
        self, ids: Union[List[str], str], output_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """根据ID获取指定记录"""
        if not output_fields:
            output_fields = ["*"]

        return self.milvus_client.query(
            collection_name=self.collection_name,
            ids=ids,
            output_fields=output_fields,
            consistency_level="Strong",
        )

    def query(
        self,
        limit: int = None,
        offset: int = None,
        filter: str = None,
        output_fields=["*"],
    ) -> QueryResult:
        """标量字段过滤查询"""
        results = self.milvus_client.query(
            collection_name=self.collection_name,
            filter=filter,
            output_fields=output_fields,
            offset=offset,
            limit=limit,
            consistency_level="Strong",
        )

        return QueryResult(items=results, page={"offset": offset, "limit": limit})

    def query_iterator(
        self,
        output_fields=["*"],
        filter: Optional[str] = "",
        batch_size=100,
        collection_name="",
    ):
        if not collection_name:
            collection_name = self.collection_name
        results: QueryIterator = self.milvus_client.query_iterator(
            collection_name=collection_name,
            filter=filter,
            batch_size=batch_size,
            output_fields=output_fields,
            consistency_level="Strong",
        )
        while True:
            hits: Dict = results.next()
            if not hits:
                break
            for hit in hits:
                yield hit


hint_search_engine = HintHybridSearchEngine(
    milvus_uri=milvus_uri,
    force_recreate=False,
)
