import ast
import os
import re

from typing import Type, Dict
from common.llm.general import create_chat_model
from common.trace import tracer
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
)
from common.utils.llm_utils import create_llm_model_by_project_config
from common.utils.retry import retry
from config import app_config
from langchain_core.runnables import RunnableConfig, RunnableLambda
from langchain_core.tools import BaseTool
from common.utils.compat import BaseModel, Field
from langchain_core.output_parsers import StrOutputParser

from config.project_config import get_project_config
from nl2agent.common.agent_reporter import reporter_run_chain
from pathlib import Path
from typing import Optional, List

from nl2agent.tools.base_tool import register_tool
from tools.python_exec import PythonExecutor, KernelGatewayManager

from nl2agent.common.csv_mgr import CsvMgr


class Input(BaseModel):
    query: str = Field(description="具体的任务")


additional_authorized_imports = [
    "time",
    "datetime",
    "json",
    "pandas",
    "numpy",
    "matplotlib",
    "scipy",
]

all_imports = [
    "statistics",
    "stat",
    "math",
    "collections",
    "random",
    "re",
    "unicodedata",
    "queue",
    "itertools",
]
all_imports.extend(additional_authorized_imports)
all_imports.sort()


@register_tool(name="python_code_tool")
class PyCodeInterpreterTool(BaseTool):
    name: str = "python_code_tool"
    description: str = "分析工具。能力范围：计算数据相关性；画图；分析输入的数据"
    args_schema: Type[BaseModel] = Input
    breadcrumbs: Optional[List[str]] = None

    def _preprocess(self, query, config: RunnableConfig):
        if isinstance(query, dict):
            query = query["query"]
        csv_mgr = config[CHAIN_META][ChainMeta.CSV_MGR]

        new_file_path = (
            Path(__file__).parent.parent
            / app_config.AGENT_NEWFILE_PATH
            / tracer.get_trace_id()
        )
        if not os.path.exists(new_file_path):
            os.makedirs(new_file_path)

        return {
            "query": query,
            "all_imports": all_imports,
            "csv_file_descps": csv_mgr.file_descps or "无",
            "new_file_path": str(new_file_path),
        }

    def _create_chat_model(self, prompt, config: RunnableConfig):
        project_config = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        )
        model = create_llm_model_by_project_config("code_model", project_config)
        return reporter_run_chain(
            breadcrumbs=self.breadcrumbs,
            chain=model,
            input=prompt,
            config=config,
            name=self.name,
        )

    def _postprocess(self, input, config: RunnableConfig):
        pattern = r"<think>.*?</think>"
        code = re.sub(pattern, "", input, flags=re.DOTALL).strip()
        match = re.search(r"```python(.*?)```", code, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            return code

    def _run_py_code_tool(self, code, config: RunnableConfig):
        try:
            ast.parse(code)
        except SyntaxError as e:
            return code + f"  error: {str(e)}"
        with KernelGatewayManager() as mgr:
            executor = PythonExecutor([], mgr)
            csv_mgr: CsvMgr = config.get(CHAIN_META, {}).get(ChainMeta.CSV_MGR)
            vars_dict = csv_mgr.file_variables if csv_mgr is not None else None
            exec_code = code
            if vars_dict is not None:
                init_local_val = [f"{k} = '{v}'" for k, v in vars_dict.items()]
                exec_code = "\n".join(init_local_val) + "\n" + code
            logs = executor.run_code_raise_errors(exec_code)
        return logs

    # 出错的时候触发重试，不能间隔太长时间， 大模型本身已经很慢了
    @retry(retries=3, delay=0.1, exceptions=(Exception,))
    def _run(self, query: str, config: RunnableConfig) -> Dict:
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        chain = (
            RunnableLambda(self._preprocess, name=f"py_code_tool_preprocess")
            | RunnableLambda(
                prompt_selector.gen_prompt,
                name="PromptSelectorBase.gen_prompt:"
                + ParamsExtractStage.AGENT_PY_CODE_TOOL,
            ).bind(stage=ParamsExtractStage.AGENT_PY_CODE_TOOL)
            | RunnableLambda(
                self._create_chat_model, name=f"py_code_tool_create_chat_model"
            )
            | StrOutputParser()
            | RunnableLambda(self._postprocess, name=f"py_code_tool_postprocess")
            | RunnableLambda(self._run_py_code_tool, name=f"py_code_tool")
        )
        return chain.invoke(query, config=config)


if __name__ == "__main__":
    tool = PyCodeInterpreterTool()
    # tool
    print(tool.invoke("print('hello world')", config={}))

    code = """
import pandas as pd
import numpy as np  # 用于生成示例数据
# 1. 创建示例 DataFrame
data = {
    'Name': ['Alice', 'Bob', 'Charlie', 'David', 'Eva'],
    'Math': [90, 85, 88, 92, 78],
    'Physics': [88, 90, 78, 85, 80],
    'Chemistry': [92, 85, 80, 88, 75]
}
df = pd.DataFrame(data)

print(df)
print("--------------")
df
    """

    print(tool.invoke(code, config={}))
