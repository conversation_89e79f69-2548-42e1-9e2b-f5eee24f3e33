from typing import Dict, List, Optional
import re
from langchain_core.runnables import RunnableConfig

from common.types import ParamsExtractData, TimeQueryParams, QueryMetricResult
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from metastore.base import Dimension, Metric


def build_metric_info(
    raw_metrics: List[Metric], params_extract_data: List[ParamsExtractData]
) -> List[Dict]:
    raw_metric_dict = {m.name: m for m in raw_metrics}
    metric_infos = []
    for data in params_extract_data:
        for metric_name in data.query_metric.metricNames:
            matched_metric = raw_metric_dict.get(metric_name)
            if matched_metric:
                metric_infos.append(
                    {"label": matched_metric.label, "synonyms": matched_metric.synonyms}
                )
    return metric_infos


def extract_company_ids(params_extract_data: List[ParamsExtractData]) -> List[str]:
    company_ids = {
        dim_value.get("dimension_value_name")
        for data in params_extract_data
        for dim in data.query_metric.where_json.get("dimension_sub_wheres", [])
        if dim.get("dimension_name") == "COMPANY_INNER_CODE_DES"
        for dim_value in dim.get("dimension_values", [])
        if dim_value.get("dimension_value_name")
    }
    return list(company_ids)


def process_time_params(
    params_extract_data: List[ParamsExtractData],
) -> Dict[str, Optional[int]]:
    start_year = None
    end_year = None
    start_month = None
    end_month = None

    for data in params_extract_data:
        param: TimeQueryParams = data.query_metric.timeQueryParams
        if not param:
            continue
        if param.timeStartFunction:
            tmp_start_year = getattr(param.timeStartFunction, "year", None) or getattr(
                param.timeStartFunction, "years", None
            )
            tmp_start_month = getattr(
                param.timeStartFunction, "month", None
            ) or getattr(param.timeStartFunction, "months", None)
            if tmp_start_year is not None:
                start_year = (
                    min(tmp_start_year, start_year) if start_year else tmp_start_year
                )
            if tmp_start_month is not None:
                start_month = (
                    min(tmp_start_month, start_month)
                    if start_month
                    else tmp_start_month
                )
        if param.timeEndFunction:
            tmp_end_year = getattr(param.timeEndFunction, "year", None) or getattr(
                param.timeEndFunction, "years", None
            )
            tmp_end_month = getattr(param.timeEndFunction, "month", None) or getattr(
                param.timeEndFunction, "months", None
            )
            if tmp_end_year is not None:
                end_year = max(tmp_end_year, end_year) if end_year else tmp_end_year
            if tmp_end_month is not None:
                end_month = (
                    max(tmp_end_month, end_month) if end_month else tmp_end_month
                )
    return {
        "start_year": start_year,
        "start_month": start_month,
        "end_year": end_year,
        "end_month": end_month,
    }


def build_query(query: str, metric_infos: List[Dict]) -> str:
    metric_desc = [
        f"{metric_info['label']}（同义词：{metric_info['synonyms']}）"
        for metric_info in metric_infos
    ]
    return f"用户的query：{query}，匹配到的指标：{','.join(metric_desc)}"


def doc_retrieve_preprocess_func(query: str, config: RunnableConfig) -> Dict:
    params_extract_data: List[ParamsExtractData] = config[CHAIN_META][
        ChainMeta.RUN_TIME
    ].get(ChainRuntime.PARAMS_EXTRACT_DATA, [])
    raw_dimensions: List[Dimension] = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.RAW_DIMENSIONS, []
    )
    raw_metrics: List[Metric] = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.RAW_METRICS, []
    )

    file_ids = config[CHAIN_META][ChainMeta.DOC_FILE_IDS]
    dir_ids = config[CHAIN_META][ChainMeta.DOC_DIR_IDS]
    if not params_extract_data:
        return {
            "query": query,
            "ids": file_ids or [],
            "dir_ids": dir_ids or [],
            "only_nodes": True,
            "company_id": [],
            "start_year": None,
            "month": None,
            "end_year": None,
            "end_month": None,
        }

    metric_infos = build_metric_info(raw_metrics, params_extract_data)
    company_ids = extract_company_ids(params_extract_data)
    time_params = process_time_params(params_extract_data)
    query = build_query(query, metric_infos)

    payload = {
        "query": query,
        "ids": file_ids or [],
        "only_nodes": True,
        "company_ids": company_ids,
        "dir_ids": dir_ids,
    }
    payload.update(
        {
            "start_year": str(time_params["start_year"])
            if time_params["start_year"]
            else None,
            "start_month": str(time_params["start_month"])
            if time_params["start_month"]
            else None,
            "end_year": str(time_params["end_year"])
            if time_params["end_year"]
            else None,
            "end_month": str(time_params["end_month"])
            if time_params["end_month"]
            else None,
        }
    )

    return payload


def plan_postprocess(query: str, tool_calls, config: RunnableConfig):
    def is_expected_tool_calls():
        if len(tool_calls) != 2:
            return False
        names = {tool.get("name") for tool in tool_calls}
        return names == {"fast_lookup", "early_stop"}

    if is_expected_tool_calls():
        for call in tool_calls:
            if call.get("name") == "fast_lookup" and "args" in call:
                call["args"]["query"] = query
    for call in tool_calls:
        if call.get("name") == "metric_meta":
            call["name"] = "fast_lookup"
    if len(tool_calls) == 1 and tool_calls[0].get("name") == "bi":
        tool_calls.append({"name": "early_stop", "args": {}})
    return tool_calls
