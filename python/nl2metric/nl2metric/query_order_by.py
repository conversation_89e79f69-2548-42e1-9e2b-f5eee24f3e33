import re
from typing import List, Optional, Tuple

from common.utils.compat import BaseModel

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.utils.string_utils import trim_escape_char, trim_space
from config.project_config import get_project_config
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableBranch,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output
from metastore import get_metastore
from nl2metric.question_classifier import RankMatcher, RankTopMatcher
from langchain_core.outputs import Generation
from config.app_config import ENABLE_LLM_CONFIDENCE_CHECK

logger = get_logger(__name__)

order_by_matchers = [RankMatcher(), RankTopMatcher()]


def need_order_by(question) -> bool:
    for matcher in order_by_matchers:
        if matcher.match(question):
            return True
    return False


def parse_order_by_column(s) -> Tuple[int, str]:
    parts = s.split()
    if len(parts) == 1:
        return int(parts[0]), ""
    return int(parts[0]), parts[1]


class QueryOrderByResult(BaseModel):
    orderBys: Optional[List[str]] = []
    limit: Optional[int] = None


# invoke with question dimensions metrics
def query_order_bys(
    prompt_selector: PromptSelectorBase, model_type: str
) -> Runnable[Input, Output]:
    general_chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.NL2METRIC_ORDER_BYS,
        ).bind(stage=ParamsExtractStage.NL2METRIC_ORDER_BYS)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.NL2METRIC_ORDER_BYS
        )
        | RunnableLambda(save_llm_output, name="save_llm_output")
        | CustomPydanticOutputParser(pydantic_object=QueryOrderByResult)
    )

    chain = (
        RunnableBranch(
            (
                lambda x: not need_order_by(x["question"]),
                lambda x: QueryOrderByResult(),
            ),
            general_chain,
        )
        | RunnableLambda(query_orderbys_postprocess, name="query_orderbys_postprocess")
        | RunnableLambda(query_orderbys_verify, name="query_orderbys_verify")
    )
    chain.name = ParamsExtractStage.NL2METRIC_ORDER_BYS
    return chain


def save_llm_output(
    result: List[Generation], *, partial: bool = False, config: RunnableConfig
):
    # save for compute llm output confidence
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_ORDERBY
    ] = result

    return result


def query_orderbys_postprocess(data: QueryOrderByResult, config: RunnableConfig):
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    default_limit_when_extract_no_result = get_project_config(
        project_name, model_name
    ).default_limit_when_extract_no_result
    if ENABLE_LLM_CONFIDENCE_CHECK:
        confidence = compute_orderby_confidence(config)
    limit = data.limit
    if limit is not None and limit <= 0:
        limit = None
    if not limit and not data.orderBys and default_limit_when_extract_no_result > 0:
        limit = default_limit_when_extract_no_result
    return trim_space(trim_escape_char(data.orderBys)) or [], limit


def query_orderbys_verify(
    input,
    config: RunnableConfig,
):
    # run again for for JobType.PROJECT_PARAMS_EXTRACT
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    if not model_name:
        return input

    order_bys, limit = input
    if not order_bys:
        return order_bys, limit
    new_order_bys = []
    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]

    force_order_by_metrics = get_project_config(
        project_name, model_name
    ).force_order_by_metrics
    found_dimension = False

    for item in order_bys:
        tmp = item.split()
        if len(tmp) == 2:
            name, order = tmp
            order = order.lower()
            if order != "asc" and order != "desc":
                order = "desc"
        elif len(tmp) == 1:
            name = tmp[0]
            order = "desc"
        else:
            continue

        metric = metastore.safe_get_metric(name)
        if metric != None:
            if model_name not in metric.model_names:
                continue
            new_order_bys.append(f"{name} {order}")
        elif (
            metastore.safe_get_dimension(model_name=model_name, dimension_name=name)
            != None
        ):
            found_dimension = True
            if not force_order_by_metrics:
                new_order_bys.append(f"{name} {order}")
            else:
                force_order_by_metrics_order = order

    # first_retrieved_metric will be filled in sub_question_extract_params_postprocess_for_each_model
    # So this check will be called in sub_question_extract_params_postprocess_for_each_model and retry_model
    first_retrieved_metric = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.FIRST_RETRIEVED_METRIC, None
    )

    if (
        force_order_by_metrics
        and found_dimension
        and len(new_order_bys) == 0
        and bool(first_retrieved_metric)
    ):
        new_order_bys.append(f"{first_retrieved_metric} {force_order_by_metrics_order}")
        msg = f"force_order_by_metrics old orderbys {order_bys} new_order_bys {new_order_bys}"
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.OK_LOGS].append(msg)
        logger.info(msg)
    return new_order_bys, limit


def compute_orderby_confidence(config: RunnableConfig):
    tokens = config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_ORDERBY
    ].response_metadata["logprobs"]["content"]
    return sum([token["logprob"] for token in tokens]) / len(tokens)
