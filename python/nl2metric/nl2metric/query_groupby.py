from typing import List, Optional, Dict

from common.utils.compat import BaseModel
from langchain_core.runnables import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Lambda, RunnableConfig
from langchain_core.runnables.utils import Input, Output

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.utils.reflect_util import Importer
from common.utils.string_utils import trim_escape_char, trim_space
from config.project_config import get_project_config, ProjectConfig
from metastore import get_metastore
from langchain_core.outputs import Generation
from config.app_config import ENABLE_LLM_CONFIDENCE_CHECK

logger = get_logger(__name__)


class GroupByWithLevel(BaseModel):
    groupBys: str
    groupByLevel: int


class QueryGroupBysResult(BaseModel):
    groupBys: Optional[List[str]] = []
    notExistGroupBys: Optional[List[str]] = []
    groupBysWithLevel: Optional[List[GroupByWithLevel]] = []

    @property
    def flat_groupbys_with_level(self):
        # class ParamsExtractData has different version BaseModel from here
        ret = []
        if self.groupBysWithLevel:
            for item in self.groupBysWithLevel:
                ret.append(item.dict())
        return ret


# invoke with question dimensions metrics
def query_group_bys(
    prompt_selector: PromptSelectorBase,
    model_type: str,
) -> Runnable[Input, Output]:
    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.NL2METRIC_GROUP_BYS,
        ).bind(stage=ParamsExtractStage.NL2METRIC_GROUP_BYS)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.NL2METRIC_GROUP_BYS
        )
        | RunnableLambda(save_llm_output, name="save_llm_output")
        | CustomPydanticOutputParser(pydantic_object=QueryGroupBysResult)
        | RunnableLambda(query_groupbys_postprocess, name="query_groupbys_postprocess")
        | RunnableLambda(query_groupbys_verify, name="query_groupbys_verify")
    )
    chain.name = ParamsExtractStage.NL2METRIC_GROUP_BYS
    return chain


def save_llm_output(
    result: List[Generation], *, partial: bool = False, config: RunnableConfig
):
    # save for compute llm output confidence
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_GROUPBY
    ] = result

    return result


def query_groupbys_postprocess(data: QueryGroupBysResult, config: RunnableConfig):
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.GROUPBYS_WITH_LEVEL
    ] = data.flat_groupbys_with_level
    project_config: ProjectConfig = get_project_config(
        config[CHAIN_META][ChainMeta.PROJECT_NAME],
        config[CHAIN_META][ChainMeta.MODEL_NAME],
    )
    if data.groupBysWithLevel:
        for item in data.groupBysWithLevel:
            if item.groupBys not in data.groupBys:
                data.groupBys.append(item.groupBys)
    if ENABLE_LLM_CONFIDENCE_CHECK:
        confidence = compute_groupby_confidence(config)
    if project_config.query_groupby_postprocess_func:
        func, _ = Importer.import_module_content(
            project_config.query_groupby_postprocess_func
        )
        data.groupBys = func(data.groupBys, config)
    return trim_space(trim_escape_char(data.groupBys)) or []


def query_groupbys_verify(groupBys, config: RunnableConfig):
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    # run this or query_groupbys_verify_mapping again for JobType.PROJECT_PARAMS_EXTRACT
    if not model_name:
        return groupBys

    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
    model_name = model_name
    groupBys = [
        d
        for d in groupBys
        if metastore.safe_get_dimension(model_name=model_name, dimension_name=d) != None
    ]
    return groupBys


# We need to check mapping and retry
# If query_groupbys_verify_mapping is in query_groupbys_verify, check will always succeed
def query_groupbys_verify_mapping(groupBys, config: RunnableConfig):
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    if not model_name:
        raise RuntimeError("We need model_name to verify groupbys mapping")
    filter_dimension_names = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.MAPPING_DIMENSION_NAMES, None
    )
    if filter_dimension_names:
        return [d for d in groupBys if d in filter_dimension_names]
    else:
        return query_groupbys_verify(groupBys, config)


def compute_groupby_confidence(config: RunnableConfig):
    tokens = config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_GROUPBY
    ].response_metadata["logprobs"]["content"]
    return sum([token["logprob"] for token in tokens]) / len(tokens)
