import re
from enum import Enum
from typing import Optional, Set, List, Callable, Dict
from common.utils.compat import BaseModel

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    JobType,
)
from common.utils.sql_where_utils import (
    remove_is_not_null_condition,
    remove_no_right_condition,
)
from common.utils.string_utils import trim_escape_char, str_is_num
from config.project_config import get_project_config
from langchain_core.runnables import (
    <PERSON>nable,
    RunnableLambda,
    RunnablePassthrough,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output
from metastore.base import (
    Metric,
    Dimension,
    DimensionValue,
    BaseMetaStore,
    DimensionValueWithMeta,
)
from nl2metric.query_where_impl import (
    SubWhereLink,
    DEFAULT_SUB_WHERE_LINK,
    MetricSubWhere,
    OperatorMode,
    DimensionSubWhere,
    Where,
)
from query_data.query import format_query_data
from metastore import get_metastore
from nl2metric.few_shots import BAOWU_PROJECT_NAME
from langchain_core.outputs import Generation
from config.app_config import ENABLE_LLM_CONFIDENCE_CHECK


logger = get_logger(__name__)


class QueryWhereResult(BaseModel):
    where: Optional[str] = ""
    ner: Optional[List[str]] = []
    isSub: bool = False  # 宝武判断是否提问子公司


# invoke with question dimensions metrics Optional(last_data)
def query_where(
    prompt_selector: PromptSelectorBase, model_type: str
) -> Runnable[Input, Output]:
    chain = (
        RunnablePassthrough.assign(
            last_data=lambda x: format_query_data(x.get("last_data", None))
        )
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + ParamsExtractStage.NL2METRIC_WHERE,
        ).bind(stage=ParamsExtractStage.NL2METRIC_WHERE)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.NL2METRIC_WHERE
        )
        | RunnableLambda(save_llm_output, name="save_llm_output")
        | CustomPydanticOutputParser(pydantic_object=QueryWhereResult)
        | RunnableLambda(get_where_sql, name="get_where_sql")
        | RunnableLambda(split_where_sql, name="split_where_sql")
        | RunnableLambda(query_where_postprocess, name="query_where_postprocess")
        | RunnableLambda(query_where_verify, name="query_where_verify")
    )
    chain.name = ParamsExtractStage.NL2METRIC_WHERE
    return chain


def save_llm_output(
    result: List[Generation], *, partial: bool = False, config: RunnableConfig
):
    # save for compute llm output confidence
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.LLM_OUTPUT_QUERY_WHERE] = result
    return result


def get_where_sql(data: QueryWhereResult, config: RunnableConfig):
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.IS_SUB] = data.isSub
    if len(data.ner) > 0:
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.WHERE_NER] = data.ner
    where_sql = trim_escape_char(data.where)
    where_sql = remove_no_right_condition(where_sql)
    where_sql = remove_is_not_null_condition(where_sql)
    # if ENABLE_LLM_CONFIDENCE_CHECK:
    #     confidence = _compute_where_confidence(config)
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    if project_name == BAOWU_PROJECT_NAME:
        # 检查问题中是否包含”净利润“
        # 模型可能会把“利润”识别为“净利润”，实际上用户最常问的指标是利润总额 eg 集团利润大于0企业排名/集团利润大于0企业排名，可能会匹配到“净利润”
        # 模型下次更新后，去掉这个校验
        if not re.search(r"净利润", question):
            where_sql = where_sql.replace("SUMT6300010", "SUMT6200").replace(
                "SUMT6300", "SUMT6200"
            )
    return where_sql


def _get_metric_sub_where(
    metric: Metric, operator: str, condition: str, config: RunnableConfig
):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    if model_name and model_name not in metric.model_names:
        logger.error(
            f"metric {metric.name} model_names {metric.model_names}, operator {operator}, condition {condition}, not in current_model model_name"
        )
        return False, ""

    if (
        operator in (">", "<")
        and condition == "0"
        and project_name != BAOWU_PROJECT_NAME
    ):
        # this is usually some meanless llm output, throw it away
        # this could cause some err. But the odds are small
        logger.info(
            f"metric {metric.name}, operator {operator}, condition {condition}, ignore > 0"
        )
        return False, ""

    # filter out all sub queries
    if "select" in condition.lower():
        logger.info(
            f"metric {metric.name}, operator {operator}, condition {condition}, filter out select"
        )
        return False, ""
    return True, MetricSubWhere(
        metric=metric,
        operator=operator,
        condition=condition,
    )


def _strip_dimension_value(v: str):
    v = v.strip()
    if not v:
        return None
    if v[0] == '"' or v[0] == "'":
        v = v[1:]

    if not v:
        return None
    if v[-1] == ",":
        v = v[:-1]

    if not v:
        return None
    if v[-1] == '"' or v[-1] == "'":
        v = v[:-1]
    return v.strip()


def _find_similar_dimension_value(dimension_value, dimension, config: RunnableConfig):
    dimension_value_mapping = get_project_config(
        config[CHAIN_META][ChainMeta.PROJECT_NAME], None
    ).dimension_value_mapping

    for similar_dimension_value_set in dimension_value_mapping:
        if dimension_value in similar_dimension_value_set:
            for similar_dimension_value in similar_dimension_value_set:
                if not similar_dimension_value:
                    continue
                if dimension_value == similar_dimension_value:
                    continue
                dv = dimension.search_value(similar_dimension_value, None)
                if dv is not None:
                    return dv
    return None


def _get_dimension_sub_where(
    dimension_name: str,
    operator: str,
    condition: str,
    metastore: BaseMetaStore,
    config: RunnableConfig,
):
    dimension: Dimension = metastore.safe_get_dimension(
        model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
        dimension_name=dimension_name,
    )
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(project_name, model_name)
    if not dimension:
        logger.warning(f"dimension_name {dimension_name} not found")
        return False, None
    if not dimension.values:
        logger.warning(f"dimension_name {dimension_name} has no values")
        return False, None

    result = DimensionSubWhere(
        operator=OperatorMode.decode(operator),
        dimension=dimension,
        raw_operator=operator,
        raw_condition=condition,
        include_illegal_dimension_values=project_config.include_illegal_dimension_values,
    )
    if result.operator == OperatorMode.unknown:
        return False, None

    elif (
        result.operator == OperatorMode.positive
        or result.operator == OperatorMode.negative
    ):
        if operator.upper() == "IN" or operator.upper() == "NOT IN":
            if condition.startswith("(") and condition.endswith(")"):
                condition = condition[1:-1]
                raw_dimension_value_strs = condition.split(",")
                dimension_value_strs = set()
                for v in raw_dimension_value_strs:
                    tmp = _strip_dimension_value(v)
                    if tmp:
                        ### lcs 修正
                        if project_config.where_lcs_correction:
                            tmp = _lcs_correct_dimension_value(tmp, config)
                        dimension_value_strs.add(tmp)
                dimension_value_strs = sorted(list(dimension_value_strs))
        else:
            dimension_value_strs = []
            tmp = _strip_dimension_value(condition)
            if tmp:
                ### lcs 修正
                if project_config.where_lcs_correction:
                    tmp = _lcs_correct_dimension_value(tmp, config)
                dimension_value_strs.append(tmp)

            ### 额外修正逻辑:
            # 模型提参结果可能存在维度和码值不匹配的情况，例如错误选择了维度，维度下并没有这个码值
            # 这时候以码值为准去修正维度
            # 通过码值去找到所有有这个码值的维度，然后选lcs最大的，针对有时候模型提参结果可能不准确，
            if project_config.correct_dimension_by_dimension_value:
                corrected_dimension_name = _correct_dimension_by_dimension_value(
                    tmp, dimension, config
                )
                if corrected_dimension_name != dimension.name:
                    # 要通过name重新从metastore拿dimension，才是全码值
                    # 而不能用召回的dimension信息，不然只有召回的码值，丢了信息
                    corrected_dimension: Dimension = metastore.safe_get_dimension(
                        model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
                        dimension_name=corrected_dimension_name,
                    )
                    result.dimension = corrected_dimension

        for v in dimension_value_strs:
            dv = dimension.search_value(v, None)
            if dv:
                result.dimension_values.append(
                    DimensionValueWithMeta(dimension_value=dv)
                )
            else:
                similar_dv = _find_similar_dimension_value(v, dimension, config)
                if similar_dv:
                    result.dimension_values.append(
                        DimensionValueWithMeta(dimension_value=similar_dv)
                    )
                else:
                    result.illegal_dimension_values.append(v)

    elif result.operator == OperatorMode.num_compare:
        if not str_is_num(condition):
            return False, None

    if not result.valid():
        return False, None

    result.verify_and_deduplicate()
    return True, result


def _split_operator_condition(sub_where: str):
    # metric_or_dimension_name, operator, condition = sub_where.split(maxsplit=2)

    # this regex can only match ', not "
    # pattern = r"(\w+)\s*(=|>=|<=|>|<|<>|!=|IS NOT|IS|IN|NOT IN)\s*('[^']*'|\w+|\(.*?\))"

    pattern = r"(\w+)\s*(=|>=|<=|>|<|<>|!=|IS NOT|IS|IN|NOT IN|LIKE)\s*(.+)"
    regex = re.compile(pattern, re.IGNORECASE)
    match = regex.match(sub_where)
    if match:
        metric_or_dimension_name = match.group(1)
        operator = match.group(2).upper()
        condition = match.group(3)
    else:
        raise RuntimeError(f"can't split sub_where: [{sub_where}]")

    if operator == "LIKE":
        raise RuntimeError(f"like in sub_where: [{sub_where}] is not supported")

    # if metric_or_dimension_name.upper() == "NOT":
    #     raise NotImplementedError(f"{sub_where} not supported yet")
    # if operator.upper() == "NOT":
    #     operator2, condition2 = condition.split(maxsplit=1)
    #     return metric_or_dimension_name, f"NOT {operator2}", condition2
    # elif operator.upper() == "IS":
    #     splits = condition.split(maxsplit=1)
    #     if len(splits) == 1:
    #         return metric_or_dimension_name, "IS", condition
    #     elif len(splits) == 2:
    #         if splits[0].upper() == "NOT":
    #             return metric_or_dimension_name, "IS NOT", splits[1]
    return metric_or_dimension_name, operator, condition


def normalized_lcs(s1: str, s2: str):
    m, n = len(s1), len(s2)
    # 创建动态规划表
    dp = [[0] * (n + 1) for _ in range(m + 1)]

    # 填充dp表
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if s1[i - 1] == s2[j - 1]:
                dp[i][j] = dp[i - 1][j - 1] + 1
            else:
                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])

    lcs_length = dp[m][n]
    # 处理空字符串的特殊情况
    denominator = max(m, n)
    return lcs_length / denominator if denominator != 0 else 0.0


def _lcs_correct_metric_or_dimension(
    metric_or_dimension_name: str, config: RunnableConfig
):
    metrics = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS]
    dimensions = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS]

    # find most similar
    max_score = -1
    best_match = ""
    for metric in metrics:
        score = normalized_lcs(metric_or_dimension_name, metric.name)
        if score > max_score:
            max_score, best_match = score, metric.name
    for dimension in dimensions:
        score = normalized_lcs(metric_or_dimension_name, dimension.name)
        if score > max_score:
            max_score, best_match = score, dimension.name
    chain_ok_log(
        logger,
        config,
        f"_lcs_correct_metric_or_dimension, original metric_or_dimension_name  {metric_or_dimension_name}; "
        f"best match {best_match} return",
    )
    return best_match


def _lcs_correct_dimension_value(condition, config: RunnableConfig):
    dimensions = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS]
    max_score = -1
    best_match = ""
    for dimension in dimensions:
        for value in dimension.values:
            score = normalized_lcs(condition, value.name)
            if score > max_score:
                max_score, best_match = score, value.name
    chain_ok_log(
        logger,
        config,
        f"_lcs_correct_dimension_value, original condition  {condition}; "
        f"best match {best_match} return",
    )
    return best_match


def _correct_dimension_by_dimension_value(
    dimension_value: str, original_dimension: Dimension, config: RunnableConfig
):
    dimensions = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS]
    # 先找出所有包含有这个码值的维度
    dimensions_has_this_value = [
        dimension
        for dimension in dimensions
        if dimension.search_value(dimension_value, None) is not None
    ]
    max_score = -1
    # 如果有多个，找出lcs分数最高的那一个维度
    for dimension in dimensions_has_this_value:
        score = normalized_lcs(original_dimension.name, dimension.name)
        if score > max_score:
            max_score, best_match = score, dimension

    chain_ok_log(
        logger,
        config,
        f"_correct_dimension_by_dimension_value, original dimension_name:  {original_dimension.name}; "
        f"dimensions_has_this_value : {dimensions_has_this_value}; "
        f"best match {best_match.name} return",
    )
    return best_match.name


def split_where_sql(
    where: str,
    config: RunnableConfig,
) -> Where:
    # with both 'and' and 'or', priority association rule of the filtered_where can be wrong
    # eg. where: a > 0 and b > 0 or c > 0
    # filtered_where: a > 0 and c > 0
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(project_name, model_name)
    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
    pattern_and = re.compile(r"\s+AND\s+", re.IGNORECASE)
    found_and = bool(pattern_and.search(where))
    pattern_or = re.compile(r"\s+OR\s+", re.IGNORECASE)
    found_or = bool(pattern_or.search(where))
    if found_and and found_or:
        raise RuntimeError(f"found both 'and' and 'or' in [{where}]")

    if found_and:
        sub_where_link = SubWhereLink.AND
    elif found_or:
        sub_where_link = SubWhereLink.OR
    else:
        sub_where_link = DEFAULT_SUB_WHERE_LINK
    result = Where(sub_where_link=sub_where_link)

    # spit where by (and/or surrounded by blank), ignore case.
    pattern = re.compile(r"\s+(AND|OR)\s+", re.IGNORECASE)
    substrings = pattern.split(where)
    for i in range(0, len(substrings), 2):
        if (i + 1 < len(substrings)) and (
            substrings[i + 1].strip().upper() not in {"AND", "OR"}
        ):
            raise RuntimeError(f"substrings {substrings}, and/or not found at {i+1}")
        sub_where = substrings[i].strip()
        if not sub_where:
            continue
        try:
            metric_or_dimension_name, operator, condition = _split_operator_condition(
                sub_where
            )
            if project_config.where_lcs_correction:
                # 模型提出的sub_where字符串有可能有拼写或者错误，这里采用一个取最大lcs分数的方式，模糊匹配
                # 让提出的指标，维度，和码值一定是召回中的，而不是模型幻觉出来的
                metric_or_dimension_name = _lcs_correct_metric_or_dimension(
                    metric_or_dimension_name, config
                )

            # eg 宝武集团二级子公司利润排名 SUMZZ10002=2
            # todo remove when llm updated
            if project_name == BAOWU_PROJECT_NAME:
                if metric_or_dimension_name in ("SUMZZ10001", "SUMZZ10002"):
                    continue
        except Exception as e:
            logger.error(
                f"filter_where original where [{where}], i {i}, sub_where [{sub_where}] illegal, exception {e}"
            )
            continue

        if (
            (not metric_or_dimension_name)
            or (metric_or_dimension_name.upper() in {"AND", "OR", "NOT"})
            or ("(" in metric_or_dimension_name)
            or (")" in metric_or_dimension_name)
        ):
            logger.error(
                f"i {i}, substrings {substrings}, found illegal metric_or_dimension_name {metric_or_dimension_name}"
            )
            continue

        possible_metric = metastore.safe_get_metric(metric_or_dimension_name)
        if possible_metric != None:
            success, metric_sub_where = _get_metric_sub_where(
                possible_metric,
                operator,
                condition,
                config,
            )
            if not success:
                continue
            result.metric_sub_wheres.append(metric_sub_where)
        else:
            success, dimension_sub_where = _get_dimension_sub_where(
                metric_or_dimension_name,
                operator,
                condition,
                metastore,
                config,
            )
            if not success:
                continue
            result.dimension_sub_wheres.append(dimension_sub_where)

    chain_ok_log(
        logger,
        config,
        f"split_where_sql succeed, original where [{where}], result {result}]",
    )

    return result


def query_where_postprocess(where: Where, config: RunnableConfig):
    where.filter_dimension_sub_where_no_modelid(config)
    return where


def query_where_verify(where: Where, config: RunnableConfig):
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    job_type = config[CHAIN_META][ChainMeta.JOB_TYPE]
    # run this or query_where_verify_mapping again for JobType.PROJECT_PARAMS_EXTRACT
    if not model_name:
        where.filter_valid()
        return where

    metastore = get_metastore(project_id)
    new_dimension_sub_wheres = []
    for dimension_sub_where in where.dimension_sub_wheres:
        if job_type == JobType.PROJECT_PARAMS_EXTRACT:
            # 多场景的时候，这里拿到的维度是多场景merge出来的维度，所以要替换成本场景的维度
            # 此外需要过滤掉非本场景的维度
            new_dimension = metastore.safe_get_dimension(
                model_name=model_name, dimension_name=dimension_sub_where.dimension.name
            )
            if new_dimension == None:
                continue
            dimension_sub_where.dimension = new_dimension.model_copy(deep=True)
            # add this deep-copy because run_verify again has deep-copy
            # might be unnecessary
        dimension_sub_where.unfold_synonyms()
        dimension_sub_where.verify_and_deduplicate()
        new_dimension_sub_wheres.append(dimension_sub_where)
    where.dimension_sub_wheres = new_dimension_sub_wheres
    where.filter_valid()
    return where


# We need to check mapping and retry
# If query_where_verify_mapping is in query_where_verify, check will always succeed
def query_where_verify_mapping(where: Where, config: RunnableConfig):
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    if not model_name:
        raise RuntimeError("We need model_name to verify where mapping")

    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    job_type = config[CHAIN_META][ChainMeta.JOB_TYPE]
    metastore = get_metastore(project_id)
    new_dimension_sub_wheres = []
    for dimension_sub_where in where.dimension_sub_wheres:
        if job_type == JobType.PROJECT_PARAMS_EXTRACT:
            # 多场景的时候，这里拿到的维度是多场景merge出来的维度，所以要替换成本场景的维度
            # 此外需要过滤掉非本场景的维度
            new_dimension = metastore.safe_get_dimension(
                model_name=model_name, dimension_name=dimension_sub_where.dimension.name
            )
            if new_dimension == None:
                continue
            dimension_sub_where.dimension = new_dimension.model_copy(deep=True)
            # add this deep-copy because run_verify again has deep-copy
            # might be unnecessary
        dimension_sub_where.unfold_synonyms()
        dimension_sub_where.verify_and_deduplicate()
        new_dimension_sub_wheres.append(dimension_sub_where)
    where.dimension_sub_wheres = new_dimension_sub_wheres

    filter_dimension_names = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.MAPPING_DIMENSION_NAMES, None
    )
    where.filter_dimension_names(filter_dimension_names)
    return query_where_verify(where, config)


def _compute_where_confidence(config: RunnableConfig):
    tokens = config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_WHERE
    ].response_metadata["logprobs"]["content"]
    return sum([token["logprob"] for token in tokens]) / len(tokens)


def filter_where_by_wordcount(
    where: Where, as_mang_as_possible: bool, config: RunnableConfig
):
    where.filter_metric_sub_where_by_wordcount(config)
    where.filter_dimension_sub_where_by_wordcount(as_mang_as_possible, config)
    return where


def create_rule_dimension_sub_where(
    rule_where_dimension, rule_operator_str, config: RunnableConfig
):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(project_name, model_name)

    operator = OperatorMode(rule_operator_str)
    assert operator != OperatorMode.unknown
    return DimensionSubWhere(
        operator=operator,
        dimension=rule_where_dimension,
        raw_operator="",
        raw_condition="",
        include_illegal_dimension_values=project_config.include_illegal_dimension_values,
    )
