from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.trace.langfuse import get_langfuse_callback, langfuse_callback
from common.types import Message
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ParamsExtractStage,
    gen_chain_meta_without_model,
    JobType,
)
from common.types.callback_handler import LogCallbackHandler
from common.utils.string_utils import class_to_dict
from condense.condense_query_v2 import CondenseQueryV2Result
from config import app_config
from langchain_core.runnables import RunnableLambda, RunnableConfig

from pydantic import BaseModel
from typing import List, Optional

logger = get_logger(__name__)


class CondenseRequest(BaseModel):
    messages: List[Message]
    model_type: str
    project_id: Optional[str] = None


class CondenseResponse(BaseModel):
    data: str
    code: int = 0


def condense_preprocess(input: List[Message]):
    return [m.content for m in input if m.role == "user"]


def condense_query_postprocess(result: CondenseQueryV2Result, config: RunnableConfig):
    if result.new_query:
        for tmp in result.new_query:
            condense_query = tmp.strip()
            if not condense_query:
                continue
            return condense_query
    raise RuntimeError(f"condense_query got nothing, new_query {result.new_query}")


def loop_condense(querys, config: RunnableConfig):
    if not querys:
        raise RuntimeError("nothing to condense")
    elif len(querys) == 1:
        return querys[0]

    prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
    model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]

    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.CONDENSE_QUERY_V2,
        ).bind(stage=ParamsExtractStage.CONDENSE_QUERY_V2)
        | create_chat_model(model_type)
        | CustomPydanticOutputParser(pydantic_object=CondenseQueryV2Result)
        | RunnableLambda(condense_query_postprocess, name="condense_query_postprocess")
    )

    pre_condense = querys[0]
    for i in range(1, len(querys)):
        question = querys[i]
        pre_condense = chain.invoke(
            {
                "pre_condense": pre_condense,
                "assistant": "",
                "question": question,
            }
        )
    return pre_condense


def condense(req: CondenseRequest):
    trace_id = tracer.get_trace_id()
    messages = req.messages
    if messages[-1].role != "user":
        raise RuntimeError(f"last msg is not from user: {messages}")
    question = messages[-1].content

    project_name = None
    if req.project_id:
        project_name = get_db_appstore().get_project_by_id(req.project_id).name

    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=req.project_id,
        model_type=req.model_type,
    )
    chain_metadata = gen_chain_meta_without_model(
        job_type=JobType.CONDENSE,
        project_name=project_name,
        project_id=req.project_id,
        model_type=req.model_type,
        force_exact_match=False,
        prompt_selector=prompt_selector,
    )

    chain = RunnableLambda(
        condense_preprocess,
        name="condense_preprocess",
    ) | RunnableLambda(
        loop_condense,
        name="loop_condense",
    )
    chain.name = req.model_type + ":condense"
    trace_name = f"CONDENSE {req.model_type}: {question}"

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    chain_metadata["langfuse_session_id"] = trace_id
    if langfuse_cb != None:
        cbs.append(langfuse_cb)
    result = chain.invoke(
        messages,
        config={
            CHAIN_META: chain_metadata,
            "callbacks": cbs,
            "max_concurrency": app_config.MAX_CONCURRENCY,
            "run_name": trace_name,
        },
    )

    return class_to_dict(CondenseResponse(data=result))
