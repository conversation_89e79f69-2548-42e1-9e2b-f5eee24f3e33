import asyncio

from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.tools.bocha_web_search import bocha_web_search
from common.tools.zhipu_web_search import zhipu_web_search
from common.trace import tracer
from common.trace.langfuse import langfuse_callback
from common.types import Message
from common.types.base import (
    CHAIN_META,
    ParamsExtractStage,
    gen_simple_chain_meta,
    JobType,
)
from common.types.callback_handler import LogCallbackHandler
from common.utils.base import convert_messages
from config import app_config
from fastapi.responses import StreamingResponse
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.runnables import RunnableLambda
from pydantic import BaseModel
from typing import List

logger = get_logger(__name__)


class ChitchatRequest(BaseModel):
    messages: List[Message]
    model_type: str = app_config.MODEL_TYPE_DEEPSEEK_14B
    web_search: bool = False


def chitchat_preprocess(input: List[Message]):
    return convert_messages(input)


def chitchat(req: ChitchatRequest):
    trace_id = tracer.get_trace_id()
    messages = req.messages
    if messages[-1].role != "user":
        raise RuntimeError(f"last msg is not from user: {messages}")
    question = messages[-1].content
    web_search_content = ""
    urls = ""
    web_search_failed = False
    if req.web_search:
        try:
            if app_config.WEB_SEARCH_TOOL == "aliyun":
                from common.tools.aliyun_web_search import aliyun_generic_search

                web_search_result = asyncio.run(
                    aliyun_generic_search(question, trace_id)
                )
            elif app_config.WEB_SEARCH_TOOL == "bocha":
                web_search_result = bocha_web_search(question)
            else:
                web_search_result = zhipu_web_search(question, trace_id)
            if not web_search_result:
                raise RuntimeError(f"web_search question {question} found nothing")
            for i in range(len(web_search_result)):
                title, content, url = web_search_result[i]
                if i > 0:
                    web_search_content += "\n"
                web_search_content += f"[{i+1}] {content}"
                urls += f"\n- [{title}]({url})"
            prompt_selector = MemPromptSelector(
                model_type=req.model_type,
            )
            messages[-1].content = prompt_selector.gen_prompt(
                input={"search_result": web_search_content, "question": question},
                stage=ParamsExtractStage.WEB_SEARCH,
                config={
                    CHAIN_META: gen_simple_chain_meta(JobType.CHITCHAT, req.model_type)
                },
            ).text
        except Exception as e:
            logger.error(f"web_search {question} failed {e}")
            web_search_failed = True

    chain = (
        RunnableLambda(
            chitchat_preprocess,
            name="chitchat_preprocess",
        )
        | create_chat_model(req.model_type)
        | StrOutputParser()
    )
    chain.name = req.model_type + ":chitchat"
    trace_name = f"CHITCHAT {req.model_type}: {question}"

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    if langfuse_cb != None:
        cbs.append(langfuse_cb)
    meta = gen_simple_chain_meta(JobType.CHITCHAT, req.model_type)
    meta["langfuse_session_id"] = trace_id

    def generate():
        for chunk in chain.stream(
            messages,
            config={
                CHAIN_META: meta,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
                "run_name": trace_name,
            },
        ):
            yield chunk

        if web_search_failed:
            yield "\n\n搜索网页失败"
        # if urls:
        #     yield urls

    return StreamingResponse(generate())
