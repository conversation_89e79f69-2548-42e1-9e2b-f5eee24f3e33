from typing import Optional

import pandas as pd
from langchain_core.messages import ChatMessage, HumanMessage
from langchain_core.output_parsers import StrOutputParser
from langfuse.decorators import observe, langfuse_context
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from common.trace import tracer
from common.types.base import CHAIN_META, ChainMeta
from common.types.callback_handler import LogCallbackHandler
from common.utils.llm_utils import create_llm_model_by_project_config
from config import app_config
from config.app_config import MODEL_TYPE_DEEPSEEK_14B
from config.project_config import get_project_config
from metastore.base import Model, Project
from metastore.service import get_db_appstore
from nl2agent.fallback_llm.text2sql import create_nl2sql_converter, extract_sql


class ChatRequest(BaseModel):
    query: str
    model_type: str = "base_model"
    scenes_id: Optional[str] = None
    trace_id: Optional[str] = None
    stream: bool = False


@observe()
def nl2sql_converter(req: ChatRequest):
    trace_id = req.trace_id or tracer.get_trace_id()
    trace_name = f"nl2sql {req.model_type}: {req.query}"
    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_context.update_current_trace(
        name=trace_name,
        session_id=trace_id,
        user_id=app_config.CLUSTER_ID,
    )
    langfuse_cb = langfuse_context.get_current_langchain_handler()
    if langfuse_cb != None:
        cbs.append(langfuse_cb)
    model: Model = get_db_appstore().get_model_by_id(req.scenes_id)
    project: Project = get_db_appstore().get_project_by_id(model.semantic_project_id)
    config = {
        "callbacks": cbs,
        "max_concurrency": app_config.MAX_CONCURRENCY,
        CHAIN_META: {
            ChainMeta.MODEL_NAME: model.table_name,
            ChainMeta.PROJECT_NAME: project.name,
        },
    }
    project_config = get_project_config(
        project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
        model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
    )
    converter = create_nl2sql_converter()
    chain = converter.gen_chain(
        query=req.query,
        model_type=req.model_type,
        semantic_scenes_id=req.scenes_id,
        config=config,
    )
    client = converter.db_client

    def generate():
        result = ""
        for chunk in chain.stream(req.query, config=config):
            yield chunk
            result += chunk
        sql = extract_sql(result) or result
        if sql == "":
            yield "extract_sql error\n"
            return
        col = None
        try:
            col, qdata = client.query(sql)
        except Exception as e:
            yield f"query error: {e}\n"
            return
        data = pd.DataFrame(qdata, columns=col)
        mk_str = data.to_markdown(index=False)
        yield mk_str
        yield "\n"
        content = f"用户查询的问题：{req.query}\n NL2SQL 生成的sql:\n {sql} \n sql 执行的结果:\n {mk_str}\n 请根据用户的要求进行分析并生成一个符合要求的答案。"

        c = (
            create_llm_model_by_project_config(req.model_type, project_config)
            | StrOutputParser()
        )
        for chunk in c.stream(
            [HumanMessage(role="user", content=content)], config=config
        ):
            yield chunk

    if req.stream:
        return StreamingResponse(generate())
    result = ""
    for chunk in chain.stream(req.query, config=config):
        result += chunk
    sql = extract_sql(result)
    if not sql:
        return {
            "sql": "",
            "data": {},
            "answer": "",
            "error": f"{result} extract_sql error",
        }
    exec_sql_err = ""
    col = None
    try:
        col, qdata = client.query(sql)
    except Exception as e:
        exec_sql_err = f"query error: {e}\n"
        return {
            "sql": sql,
            "data": {},
            "answer": "",
            "error": exec_sql_err,
        }
    data = pd.DataFrame(qdata, columns=col)
    mk_str = data.to_markdown(index=False)
    content = f"用户查询的问题：{req.query}\n NL2SQL 生成的sql:\n {sql} \n sql 执行的结果:\n {mk_str}\n 请根据用户的要求进行分析并生成一个符合要求的答案。"
    final_res = ""
    c = (
        create_llm_model_by_project_config(req.model_type, project_config)
        | StrOutputParser()
    )
    for chunk in c.stream([ChatMessage(role="user", content=content)], config=config):
        final_res += chunk
    answer = final_res.split("</think>", 1)[-1]
    return {
        "sql": sql,
        "data": data.to_dict(),
        "answer": answer,
        "error": exec_sql_err,
    }
