#! coding=utf-8
from typing import List, Optional, Dict

from fastapi.responses import StreamingResponse
from langchain_core.output_parsers import StrOutputParser
from pydantic import BaseModel

from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.trace import tracer
from common.types.base import (
    CHAIN_META,
    gen_chain_meta,
    JobType,
    CHART_INSIGHT_STAGE,
)
from common.types.callback_handler import <PERSON>g<PERSON>allbackHandler
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace.langfuse import get_langfuse_callback, langfuse_callback
from config import app_config
from langchain_core.runnables import RunnableLambda

logger = get_logger(__name__)


class ChartInsightRequest(BaseModel):
    model_id: str
    model_type: str
    metric_feature_prompt: str
    chart_title: str
    chart_type: str
    rows: List[Dict]
    sql: str
    business_background: Optional[str] = None


class ChartInsightResponse(BaseModel):
    data: str
    code: int = 0


def chart_insight(req: ChartInsightRequest):
    trace_id = tracer.get_trace_id()
    model_type = app_config.chart_insight_model_type

    model = get_db_appstore().get_model_by_id(req.model_id)
    project_name = model.semantic_project_name
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=model.semantic_project_id,
        model_name=model.table_name,
        model_type=req.model_type,
        model_id=req.model_id,
    )

    chain_metadata = gen_chain_meta(
        job_type=JobType.CHART_INSIGHT,
        project_name=project_name,
        project_id=model.semantic_project_id,
        model_name=model.table_name,
        model_label=model.label,
        model_id=req.model_id,
        model_type=req.model_type,
        prompt_selector=prompt_selector,
    )

    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + CHART_INSIGHT_STAGE,
        ).bind(stage=CHART_INSIGHT_STAGE)
        | create_chat_model(model_type)
        | StrOutputParser()
    )
    chain.name = req.model_type + ":chart_insight"

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    if langfuse_cb != None:
        cbs.append(langfuse_cb)

    def generate():
        for chunk in chain.stream(
            {
                "businessBackground": req.business_background,
                "chartTitle": req.chart_title,
                "chartType": req.chart_type,
                "rows": req.rows,
                "metricFeaturePrompt": req.metric_feature_prompt,
                "sql": req.sql,
                "metadata": {
                    "langfuse_session_id": trace_id,
                },
                "run_name": f"{req.model_type}:数据洞察生成",
            },
            config={
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
            },
        ):
            yield chunk

    return StreamingResponse(generate())
