import re

from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.trace.langfuse import langfuse_callback
from common.types import Message
from common.types.base import (
    CHAIN_META,
    ParamsExtractStage,
    gen_simple_chain_meta,
    JobType,
)
from common.types.callback_handler import LogCallbackHandler
from config import app_config
from fastapi.responses import StreamingResponse
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import <PERSON>nableLambda
from pydantic import BaseModel
from typing import List


logger = get_logger(__name__)


class ResultAnalysisRequest(BaseModel):
    messages: List[Message]
    model_type: str = app_config.MODEL_TYPE_DEEPSEEK_14B


def result_analysis_preprocess(input: List[Message]):
    question = input[-2].content
    result = input[-1].content
    pattern = r"<think>.*?</think>"
    result = re.sub(pattern, "", result, flags=re.DOTALL).strip()

    history_questions = [m.content for m in input[:-2] if m.role == "user"]
    history_questions = f"[{','.join(history_questions)}]"
    return {
        "question": question,
        "result": result,
        "history_questions": history_questions,
    }


def result_analysis(req: ResultAnalysisRequest):
    trace_id = tracer.get_trace_id()
    messages = req.messages
    if (
        len(messages) < 2
        or messages[-1].role != "assistant"
        or messages[-2].role != "user"
    ):
        raise RuntimeError(f"invalid messages: {messages}")
    question = messages[-2].content
    prompt_selector = MemPromptSelector(
        model_type=req.model_type,
    )

    chain = (
        RunnableLambda(
            result_analysis_preprocess,
            name="result_analysis_preprocess",
        )
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + ParamsExtractStage.RESULT_ANALYSIS,
        ).bind(stage=ParamsExtractStage.RESULT_ANALYSIS)
        | create_chat_model(req.model_type)
        | StrOutputParser()
    )
    chain.name = req.model_type + ":result_analysis"
    trace_name = f"RESULT_ANALYSIS {req.model_type}: {question}"

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    if langfuse_cb != None:
        cbs.append(langfuse_cb)

    meta = gen_simple_chain_meta(JobType.RESULT_ANALYSIS, req.model_type)
    meta["langfuse_session_id"] = trace_id

    def generate():
        for chunk in chain.stream(
            messages,
            config={
                CHAIN_META: meta,
                "run_name": trace_name,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
            },
        ):
            yield chunk

    return StreamingResponse(generate())
