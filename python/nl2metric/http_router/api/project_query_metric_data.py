from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.trace.langfuse import langfuse_callback
from common.types import ParamsExtractData
from common.types.base import (
    JobType,
    gen_chain_meta_without_model,
    add_model_to_chain_meta,
    del_model_from_chain_meta,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.callback_handler import LogCallbackHandler
from common.types.exceptions import ParamsExtractEmptyResult
from config import app_config
from http_router.api.params_extract_handler import params_extract_nl2metric_postprocess
from http_router.api.project_params_extract_handler import (
    project_params_extract_handler_postprocess,
)
from langchain_core.runnables import RunnableLambda, RunnableConfig
from metastore.service import get_db_appstore
from nl2metric.service import nl2metric
from pre_filter.retrive import call_retrive
from typing import Dict

from vector_store.data_models import AgentType

logger = get_logger(__name__)


def project_query_metric_data_postprocess(
    input: Dict[str, ParamsExtractData],
    require_metric_num: int,
):
    result = {}
    for model_id, response_data in input.items():
        query_metric = response_data.query_metric
        if query_metric == None:
            continue
        if require_metric_num > 0:
            if (
                not query_metric.metricNames
                # calculator时要求查数结果只能有一个指标，且必须有一个指标
                # 上述规则容易出错，还是不要要求这么严格
                or len(query_metric.metricNames) < require_metric_num
            ):
                continue
            query_metric.metricNames = query_metric.metricNames[:require_metric_num]
        result[model_id] = response_data
    return result


def project_params_extract_nl2metric_postprocess(
    query_metric_data, config: RunnableConfig
):
    response_data = {}
    for model_id, tmp in query_metric_data.items():
        tmp_ret: ParamsExtractData = params_extract_nl2metric_postprocess(tmp, config)
        response_data[model_id] = tmp_ret
    return response_data


def _get_project_query_metric_data_chain(
    project_name,
    project_id,
    model_type,
    require_metric_num,
):
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=None,
        model_type=model_type,
        model_id=None,
    )
    chain = (
        call_retrive()
        | nl2metric(
            project_name=project_name,
            model_name=None,
            model_type=model_type,
            prompt_selector=prompt_selector,
            job_type=JobType.PROJECT_PARAMS_EXTRACT,
        )
        | RunnableLambda(
            project_params_extract_nl2metric_postprocess,
            name="project_params_extract_nl2metric_postprocess",
        )
        | RunnableLambda(
            project_params_extract_handler_postprocess,
            name="project_params_extract_handler_postprocess",
        )
        | RunnableLambda(
            project_query_metric_data_postprocess,
            name="project_query_metric_data_postprocess",
        ).bind(
            require_metric_num=require_metric_num,
        )
    )
    chain.name = "project_query_metric_data"
    return chain, prompt_selector


def project_query_metric_data(
    param_key,
    trace_id,
    question,
    project_name,
    project_id,
    model_type,
    user_id,
    force_exact_match,
    require_metric_num=0,
    additional_info=None,
    metric_ner=None,
    where_ner=None,
    hint_info=None,
):
    if not hint_info:
        hint_info = {
            f"{agent_type_enum.value}_hint": "" for agent_type_enum in AgentType
        }
    if isinstance(question, dict):
        question = question["question"]
    tracer.set_trace_id(trace_id)
    chain, prompt_selector = _get_project_query_metric_data_chain(
        project_name=project_name,
        project_id=project_id,
        model_type=model_type,
        require_metric_num=require_metric_num,
    )
    chain_metadata = gen_chain_meta_without_model(
        job_type=JobType.PROJECT_PARAMS_EXTRACT,
        project_name=project_name,
        project_id=project_id,
        model_type=model_type,
        force_exact_match=force_exact_match,
        prompt_selector=prompt_selector,
        user_id=user_id,
    )
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = question
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.METRIC_NER] = metric_ner
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.WHERE_NER] = where_ner
    # without this, wait_for_intent will keep waiting for a long time
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.INTENT] = None
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.HINT] = hint_info
    chain_metadata[ChainMeta.CALCULATOR_PARAM_KEY] = param_key
    chain_metadata["langfuse_session_id"] = trace_id
    chain_metadata["langfuse_user_id"] = (
        app_config.CLUSTER_ID if not user_id else app_config.CLUSTER_ID + ":" + user_id
    )
    if additional_info is not None:
        chain_metadata[ChainMeta.ADDITIONAL_INFO] = additional_info

    trace_name = f"PROJECT_QUERY_METRIC_DATA {model_type}: {question}"
    cbs = []
    backend_cb = None
    if app_config.ENABLE_LANGFUSE:
        backend_cb = langfuse_callback
        if backend_cb is not None:
            cbs.append(backend_cb)
    if not app_config.USE_ASYNC_LANGCHAIN_LOG:
        cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))
    try:
        result = chain.invoke(
            question,
            config={
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
                "run_name": trace_name,
            },
        )
        if not result:
            raise ParamsExtractEmptyResult(
                f"project {project_id} project_query_metric_data got no model"
            )
        logger.info(f"project_query_metric_data question {question} result {result}")
        return result
    finally:
        if backend_cb is not None:
            backend_cb.close()
