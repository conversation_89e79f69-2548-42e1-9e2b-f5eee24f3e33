#! coding=utf-8
from typing import Optional

from colorama import Fore
from langchain_core.output_parsers import StrOutputParser
from pydantic import BaseModel
from common.llm.general import create_chat_model
from common.utils.json_utils import extract_json_from_string
from config.app_config import MODEL_TYPE_YI_34B
from common.logging.logger import get_logger
from common.trace import tracer
from common.types.base import (
    JobType,
    MEASURE_DIM_RECOMMENDATION_STAGE,
)
from common.types.callback_handler import Log<PERSON>allback<PERSON>and<PERSON>, try_json_stringify
from cache_updater.cached import cached
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace.langfuse import langfuse_callback
from common.utils.string_utils import class_to_dict
from config import app_config
from langchain_core.runnables import RunnableLambda

logger = get_logger(__name__)


class MeasureDimRecommendRequest(BaseModel):
    sample: str
    model_type: Optional[str] = MODEL_TYPE_YI_34B


class MeasureDimRecommendResponse(BaseModel):
    code: int = 0
    result: str


def measure_dim_recommend(
    req: MeasureDimRecommendRequest,
) -> MeasureDimRecommendResponse:
    trace_id = tracer.get_trace_id()
    model_type = req.model_type

    project_name = "度量维度推荐prompt"
    project_id = "Mock_project_id"
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=None,
        model_type=req.model_type,
    )

    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + MEASURE_DIM_RECOMMENDATION_STAGE,
        ).bind(stage=MEASURE_DIM_RECOMMENDATION_STAGE)
        | create_chat_model(model_type)
        | StrOutputParser()
        | RunnableLambda(extract_json_from_string)
    )

    chain.name = JobType.MEASURE_DIM_RECOMMENDATION

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    if langfuse_cb != None:
        cbs.append(langfuse_cb)

    response = chain.invoke(
        {"data_samples": req.sample},
        config={
            "callbacks": cbs,
            "max_concurrency": app_config.MAX_CONCURRENCY,
            "metadata": {
                "langfuse_session_id": trace_id,
            },
            "run_name": f"度量维度推荐",
        },
    )
    res_json_string = try_json_stringify(response, "模型结果转换成json string 失败")
    logger.info(Fore.CYAN + "度量维度推荐: %s" + Fore.RESET, class_to_dict(res_json_string))
    return class_to_dict(MeasureDimRecommendResponse(result=res_json_string))
