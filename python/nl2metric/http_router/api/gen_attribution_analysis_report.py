#! coding=utf-8
from fastapi.responses import StreamingResponse
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel
from common.llm.general import create_chat_model
from common.types.callback_handler import <PERSON>g<PERSON>allback<PERSON><PERSON><PERSON>
from metastore.service import get_db_appstore
from common.http import hook
from common.logging.logger import get_logger
from common.trace import tracer
from common.types.base import (
    CHAIN_META,
    gen_chain_meta,
    JobType,
)
from common.trace.langfuse import get_langfuse_callback, langfuse_callback
from config import app_config

logger = get_logger(__name__)


class AttrAnalysisReportRequest(BaseModel):
    model_id: str
    model_type: str
    report_doc_prompt: str


def gen_attribution_analysis_report(req: AttrAnalysisReportRequest):
    trace_id = tracer.get_trace_id()
    model_type = req.model_type
    model = get_db_appstore().get_model_by_id(req.model_id)
    project_name = model.semantic_project_name

    chain_metadata = gen_chain_meta(
        job_type=JobType.ATTRIBUTE_ANALYSIS_REPORT,
        project_name=project_name,
        project_id=model.semantic_project_id,
        model_name=model.table_name,
        model_label=model.label,
        model_id=req.model_id,
        model_type=req.model_type,
        prompt_selector=None,
    )
    chain_metadata["langfuse_session_id"] = trace_id

    chain = (
        PromptTemplate.from_template(req.report_doc_prompt, template_format="jinja2")
        | create_chat_model(model_type)
        | StrOutputParser()
    )
    chain.name = req.model_type + ":attribution_analysis_report"

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    if langfuse_cb is not None:
        cbs.append(langfuse_cb)

    def generate():
        config = {
            CHAIN_META: chain_metadata,
            "callbacks": cbs,
            "max_concurrency": app_config.MAX_CONCURRENCY,
            "run_name": f"{req.model_type}:归因报告生成",
        }
        for chunk in chain.stream(
            {},
            config=config,
        ):
            yield chunk

    return StreamingResponse(generate())
