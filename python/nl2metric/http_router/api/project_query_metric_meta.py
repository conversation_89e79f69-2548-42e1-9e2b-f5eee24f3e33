import pandas as pd
import requests

from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.trace.langfuse import langfuse_callback
from common.types.base import (
    JobType,
    gen_chain_meta_without_model,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.callback_handler import LogCallbackHandler
from config import app_config
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>, RunnableConfig, RunnablePassthrough

from metastore import get_metastore
from metastore.service import get_db_appstore
from nl2intent.nl2intent_meta_by_tag import get_nl2intent_by_tags_chain_meta
from nl2meta.nl2meta import call_nl2meta, MetaIntentParams, MetaIntentType
from pre_filter.retrive import call_retrive

logger = get_logger(__name__)


def split_meta_by_models(meta_intent_params: MetaIntentParams, config: RunnableConfig):
    app_store = get_db_appstore()
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    meta_store = get_metastore(project_id)
    models = app_store.get_models_by_project_id(project_id)
    model_ids = {m.id for m in models}
    if meta_intent_params.type == MetaIntentType.DIMENSION_DETAIL:
        dimension_name = meta_intent_params.param
        result = {}
        for model_id in model_ids:
            model_name = app_store.get_model_by_id(model_id).table_name
            if (
                meta_store.safe_get_dimension(
                    model_name=model_name,
                    dimension_name=dimension_name,
                )
                != None
            ):
                result[model_id] = meta_intent_params
        assert result, result
        return result
    elif meta_intent_params.type == MetaIntentType.METRIC_DETAIL:
        metric_name = meta_intent_params.param
        metric = meta_store.get_metric(metric_name)
        result = {}
        for model_name in metric.model_names:
            model_id = app_store.get_model_by_name(project_id, model_name).id
            result[model_id] = meta_intent_params
        assert result, result
        return result
    elif meta_intent_params.type in [
        MetaIntentType.DIMENSION_LIST,
        MetaIntentType.METRIC_LIST,
        MetaIntentType.ALL_DATA,
    ]:
        return {model_id: meta_intent_params for model_id in model_ids}
    else:
        raise RuntimeError(
            f"split_meta_by_models unsupported type: {meta_intent_params}"
        )


def _get_project_query_metric_meta_chain(
    project_name,
    project_id,
    model_type,
):
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=None,
        model_type=model_type,
        model_id=None,
    )
    chain = (
        call_retrive()
        | RunnablePassthrough.assign(
            intent=get_nl2intent_by_tags_chain_meta(prompt_selector)
        )
        | call_nl2meta(model_type, prompt_selector)
        | RunnableLambda(split_meta_by_models, name="split_meta_by_models")
    )
    chain.name = "project_query_metric_meta"
    return chain, prompt_selector


def project_query_metric_meta(
    trace_id,
    question,
    project_name,
    project_id,
    model_type,
    user_id,
    additional_info=None,
):
    if isinstance(question, dict):
        question = question["question"]
    tracer.set_trace_id(trace_id)
    chain, prompt_selector = _get_project_query_metric_meta_chain(
        project_name=project_name,
        project_id=project_id,
        model_type=model_type,
    )
    chain_metadata = gen_chain_meta_without_model(
        job_type=JobType.PARAMS_EXTRACT,
        project_name=project_name,
        project_id=project_id,
        model_type=model_type,
        prompt_selector=prompt_selector,
    )
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = question
    # without this, wait_for_intent will keep waiting for a long time
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.INTENT] = None
    chain_metadata["langfuse_session_id"] = trace_id
    chain_metadata["langfuse_user_id"] = (
        app_config.CLUSTER_ID if not user_id else app_config.CLUSTER_ID + ":" + user_id
    )
    if additional_info is not None:
        chain_metadata[ChainMeta.ADDITIONAL_INFO] = additional_info

    trace_name = f"PROJECT_QUERY_METRIC_META {model_type}: {question}"
    cbs = []
    backend_cb = None
    if app_config.ENABLE_LANGFUSE:
        backend_cb = langfuse_callback
        if backend_cb is not None:
            cbs.append(backend_cb)
    if not app_config.USE_ASYNC_LANGCHAIN_LOG:
        cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))
    try:
        result = chain.invoke(
            question,
            config={
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
                "run_name": trace_name,
            },
        )
        logger.info(f"project_query_metric_meta question {question} result {result}")
        return result
    finally:
        if backend_cb is not None:
            backend_cb.close()
