#! coding=utf-8
from fastapi import Header
from typing import List, Optional, Dict, Any

from colorama import Fore
from pydantic import BaseModel

from common.trace.langfuse import langfuse_callback
from common.types.exceptions import SensitiveQuestion
from config.project_config import get_project_config
from metastore.service import get_db_appstore
from cache_updater.cached import cached
from common.logging.logger import get_logger
from common.trace import tracer
from common.types import MessageWithExtraInfo
from common.types.base import (
    CHAIN_META,
    gen_agent_chain_meta,
    JobType,
)
from common.types.exception_cacher import exception_to_code
from common.types.callback_handler import (
    <PERSON>g<PERSON><PERSON>back<PERSON>and<PERSON>,
    LocalBackendReporterCallbackHandler,
)
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.utils.base import convert_messages
from common.utils.string_utils import class_to_dict
from config import app_config
from langchain_core.runnables import (
    RunnableConfig,
)
from nl2agent.agents.brain_agent import BrainAgent
from http_router.api_dispatcher import get_api_dispatcher, ApiType
from backend_stage_reporter.reporter import get_reporter_cb
from common.sensitive_words.sensitive_check import SensitiveCheck

logger = get_logger(__name__)


class AgentRequest(BaseModel):
    messages: List[MessageWithExtraInfo]
    model_type: str = app_config.AGENT_MODEL_TYPE
    chat_model_type: str = app_config.CHAT_MODEL_TYPE
    code_model_type: Optional[str] = app_config.CODE_MODEL_TYPE
    project_id: Optional[str] = None  # 为多场景提参预留，暂时不需要
    model_id: Optional[str] = None
    task_id: Optional[str] = None
    # document search
    file_ids: Optional[List[str]] = None
    dir_ids: Optional[List[int]] = None
    # 是否开启联网搜索
    enable_internet_search: bool = False
    enable_doc_search: bool = False
    enable_bi: bool = True
    # 回调地址
    call_back_addr: Optional[str] = None
    # additional_info key:
    # - force_exact_match
    # - lookup_data_model_type
    # - skip_hint
    additional_info: Dict = {}


class AgentResponse(BaseModel):
    data: Any
    code: int = 0


def agent_postprocess(input, config: RunnableConfig):
    return input


@cached("{model_id}#{model_type}#{project_name}#{project_id}#{model_name}")
def get_agent_chain(
    model_id: str, model_type: str, project_name, project_id, model_name
):
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_type=model_type,
        model_id=model_id,
    )
    # 创建BrainAgent实例并获取其链结构
    brain_agent = BrainAgent()
    agent_chain = brain_agent.create_chain()
    # 返回处理器函数和提示选择器
    return prompt_selector, agent_chain


@exception_to_code
def agent_handler(req: AgentRequest, userId: Optional[str] = Header(None)):
    trace_id = tracer.get_trace_id()
    logger.info(
        f"agent_handler start for user {userId}, trace_id {trace_id}: {req.model_dump_json()}"
    )
    task_id = req.task_id
    if len(req.messages) <= 0:
        raise RuntimeError(f"model {req.model_id} no question asked")
    if req.messages[-1].role != "user":
        raise RuntimeError(f"model {req.model_id} last msg not from user")
    question = req.messages[-1].content
    history = convert_messages(req.messages[:-1])

    force_exact_match = req.additional_info.get("force_exact_match", False)
    lookup_data_model_type = req.additional_info.get(
        "lookup_data_model_type", app_config.MODEL_TYPE_YI_34B
    )

    if req.model_id:
        model = get_db_appstore().get_model_by_id(req.model_id)
        project_name = model.semantic_project_name
        project_id = model.semantic_project_id
        model_name = model.table_name
        model_label = model.label
        model_id = req.model_id
    elif req.project_id:
        project = get_db_appstore().get_project_by_id(req.project_id)
        project_name = project.name
        project_id = req.project_id
        model_name = None
        model_label = None
        model_id = None
    else:
        raise RuntimeError("we need project_id or model_id")

    # 获取 project_config
    project_config = get_project_config(project_name, model_name)
    sensitive_words_file_path = project_config.sensitive_words_file_path
    if sensitive_words_file_path:
        sensitive_check_instance = SensitiveCheck.get_instance(
            sensitive_words_file_path
        )
        sensitive_result = sensitive_check_instance.check(question)
        if sensitive_result:
            logger.warning(
                f"question {question} contains sensitive words {sensitive_result}"
            )
            raise SensitiveQuestion()

    prompt_selector, agent_chain = get_agent_chain(
        req.model_id, req.model_type, project_name, project_id, model_name
    )
    chain_metadata = gen_agent_chain_meta(
        job_type=JobType.AGENT,
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_label=model_label,
        model_id=model_id,
        model_type=req.model_type,
        chat_model_type=req.chat_model_type,
        code_model_type=req.code_model_type,
        prompt_selector=prompt_selector,
        history_messages=history,
        question=question,
        trace_id=trace_id,
        task_id=task_id,
        file_ids=req.file_ids,
        dir_ids=req.dir_ids,
        force_exact_match=force_exact_match,
        lookup_data_model_type=lookup_data_model_type,
        user_id=userId,
        enable_internet_search=req.enable_internet_search,
        enable_doc_search=req.enable_doc_search,
        enable_bi=req.enable_bi,
        additional_info=req.additional_info,
    )

    trace_name = f"AGENT {req.model_type}: {req.messages[-1].content}"
    cbs = []
    backend_cb = None
    if app_config.ENABLE_LANGFUSE:
        chain_metadata["langfuse_session_id"] = trace_id
        chain_metadata["langfuse_user_id"] = (
            app_config.CLUSTER_ID
            if not userId
            else app_config.CLUSTER_ID + ":" + userId
        )

    if app_config.USING_MULTI_PROCESS_REPORTER:
        if app_config.ENABLE_LANGFUSE or app_config.ENABLE_PROGRESS_STATUS:
            backend_cb = get_reporter_cb(
                trace_id, task_id, trace_name, userId, req.call_back_addr
            )
        if backend_cb is not None:
            cbs.append(backend_cb)
    else:
        if app_config.ENABLE_LANGFUSE:
            cbs.append(langfuse_callback)
        if app_config.ENABLE_PROGRESS_STATUS:
            backend_cb = LocalBackendReporterCallbackHandler(
                trace_id, task_id, trace_name, userId, req.call_back_addr
            )
            cbs.append(backend_cb)
    if not app_config.USE_ASYNC_LANGCHAIN_LOG:
        cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))

    need_manual_select = False
    try:
        # 确保正确传递配置和回调
        config = {
            CHAIN_META: chain_metadata,
            "callbacks": cbs,
            "max_concurrency": app_config.MAX_CONCURRENCY,
            "langfuse": {},  # 确保langfuse上下文存在
            "call_back_addr": req.call_back_addr,  # 添加回调地址到配置中，确保第一次调用时能获取到
            "run_name": trace_name,
        }
        response_data = agent_chain.invoke(
            question,
            config=config,
        )
        need_manual_select = any(r["name"] == "manual_selects" for r in response_data)
    finally:
        if backend_cb is not None:
            backend_cb.close(not need_manual_select)
            del backend_cb
    logger.info(
        Fore.CYAN + "agent_handler: %s" + Fore.RESET,
        response_data,
    )

    return class_to_dict(
        AgentResponse(
            data={
                "data": response_data,
                "extra_info": {},
            }
        )
    )


async def async_agent_handler(req: AgentRequest, userId: Optional[str] = Header(None)):
    future = get_api_dispatcher().submit_task(
        req_type=ApiType.agent_handler,
        trace_id=tracer.get_trace_id(),
        req=req,
        userId=userId,
    )
    return await future
