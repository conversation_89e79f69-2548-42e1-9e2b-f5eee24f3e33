#! coding=utf-8
from fastapi import Header
from typing import Optional, Dict

from colorama import Fore
from pydantic import BaseModel

from cache_updater.cached import cached
from common.logging.logger import get_logger
from common.manual_select.manual_select import (
    pop_manual_select_trace,
    BrainManualSelectItem,
)
from common.trace import tracer
from common.trace.langfuse import langfuse_callback
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from common.types.exceptions import ManualSelect<PERSON>ot<PERSON>nough
from common.types.exception_cacher import exception_to_code
from common.types.callback_handler import (
    LogCallbackHandler,
    LocalBackendReporterCallbackHandler,
)
from common.utils.string_utils import class_to_dict
from config import app_config
from langchain_core.runnables import RunnableLambda
from nl2agent.agents.brain_agent import BrainAgent
from http_router.api_dispatcher import get_api_dispatcher, ApiType
from http_router.api.agent_handler import (
    AgentResponse,
    agent_postprocess,
)
from backend_stage_reporter.reporter import get_reporter_cb

logger = get_logger(__name__)


class AgentAfterManualSelectRequest(BaseModel):
    manual_selects_result: Dict
    task_id: Optional[str] = None
    call_back_addr: Optional[str] = None


@cached("")
def get_agent_after_manual_select_chain():
    brain_agent = BrainAgent()
    return brain_agent.create_chain_from_plan() | RunnableLambda(
        agent_postprocess, name="agent_postprocess"
    )


@exception_to_code
def agent_handler_after_manual_select(
    req: AgentAfterManualSelectRequest, userId: Optional[str] = Header(None)
):
    trace_id = tracer.get_trace_id()
    logger.info(
        f"agent_handler_after_manual_select start for user {userId}, trace_id {trace_id}: {req.model_dump_json()}"
    )
    task_id = req.task_id
    brain_manual_select_item: BrainManualSelectItem = pop_manual_select_trace(trace_id)

    if set(brain_manual_select_item.manual_selects.keys()) != set(
        req.manual_selects_result.keys()
    ):
        raise ManualSelectNotEnough(
            f"missing manual_selects, need {brain_manual_select_item.manual_selects.keys()}, got {req.manual_selects_result.keys()}"
        )

    chain_metadata = brain_manual_select_item.config_chain_meta
    chain_metadata[ChainMeta.MANUAL_SELECTS_RESULT] = req.manual_selects_result
    chain = get_agent_after_manual_select_chain()

    model_type = chain_metadata[ChainMeta.MODEL_TYPE]
    question = chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    trace_name = f"AGENT_AFTER {model_type}: {question}"
    cbs = []
    backend_cb = None
    if app_config.ENABLE_LANGFUSE:
        chain_metadata["langfuse_session_id"] = trace_id
        chain_metadata["langfuse_user_id"] = (
            app_config.CLUSTER_ID
            if not userId
            else app_config.CLUSTER_ID + ":" + userId
        )

    if app_config.USING_MULTI_PROCESS_REPORTER:
        if app_config.ENABLE_LANGFUSE or app_config.ENABLE_PROGRESS_STATUS:
            backend_cb = get_reporter_cb(
                trace_id, task_id, trace_name, userId, req.call_back_addr
            )
        if backend_cb is not None:
            cbs.append(backend_cb)
    else:
        if app_config.ENABLE_LANGFUSE:
            cbs.append(langfuse_callback)
        if app_config.ENABLE_PROGRESS_STATUS:
            backend_cb = LocalBackendReporterCallbackHandler(
                trace_id, task_id, trace_name, userId, req.call_back_addr
            )
            cbs.append(backend_cb)
    if not app_config.USE_ASYNC_LANGCHAIN_LOG:
        cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))

    try:
        response_data = chain.invoke(
            brain_manual_select_item.tools_result,
            config={
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
                "call_back_addr": req.call_back_addr,
                "run_name": trace_name,
            },
        )
    finally:
        if backend_cb is not None:
            backend_cb.close()
            del backend_cb
    logger.info(
        Fore.CYAN + "agent_handler_after_manual_select: %s" + Fore.RESET,
        response_data,
    )

    return class_to_dict(
        AgentResponse(
            data={
                "data": response_data,
                "extra_info": {},
            }
        )
    )


async def async_agent_handler_after_manual_select(
    req: AgentAfterManualSelectRequest, userId: Optional[str] = Header(None)
):
    future = get_api_dispatcher().submit_task(
        req_type=ApiType.agent_handler_after_manual_select,
        trace_id=tracer.get_trace_id(),
        req=req,
        userId=userId,
    )
    return await future
