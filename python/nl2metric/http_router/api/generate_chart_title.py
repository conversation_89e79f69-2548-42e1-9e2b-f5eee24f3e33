#! coding=utf-8
from colorama import Fore
from langchain_core.output_parsers import StrOutputParser
from pydantic import BaseModel
from common.llm.general import create_chat_model
from metastore.service import get_db_appstore
from common.logging.logger import get_logger
from common.trace import tracer
from common.types.base import (
    CHAIN_META,
    gen_chain_meta,
    JobType,
    GENERATE_CHART_TITLE_STAGE,
)
from common.types.callback_handler import <PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace.langfuse import get_langfuse_callback, langfuse_callback
from common.utils.string_utils import class_to_dict
from config import app_config
from langchain_core.runnables import RunnableLambda

logger = get_logger(__name__)


class GenerateChartTitleRequest(BaseModel):
    model_id: str
    model_type: str
    message: str
    queryParamsStr: str


class GenerateChartTitleResponse(BaseModel):
    data: str
    code: int = 0


def generate_chart_title(req: GenerateChartTitleRequest) -> GenerateChartTitleResponse:
    trace_id = tracer.get_trace_id()
    model_type = req.model_type

    model = get_db_appstore().get_model_by_id(req.model_id)
    project_name = model.semantic_project_name
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=model.semantic_project_id,
        model_name=model.table_name,
        model_type=req.model_type,
    )

    chain_metadata = gen_chain_meta(
        job_type=JobType.GENERATE_CHART_TITLE,
        project_name=project_name,
        project_id=model.semantic_project_id,
        model_name=model.table_name,
        model_label=model.label,
        model_id=req.model_id,
        model_type=req.model_type,
        prompt_selector=prompt_selector,
    )
    chain_metadata["langfuse_session_id"] = trace_id

    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + GENERATE_CHART_TITLE_STAGE,
        ).bind(stage=GENERATE_CHART_TITLE_STAGE)
        | create_chat_model(model_type)
        | StrOutputParser()
    )

    chain.name = JobType.GENERATE_CHART_TITLE

    log_cb = LogCallbackHandler(id=trace_id)
    cbs = [
        log_cb,
    ]
    langfuse_cb = langfuse_callback
    if langfuse_cb != None:
        cbs.append(langfuse_cb)

    response = chain.invoke(
        {"question": req.message, "queryParamsStr": req.queryParamsStr},
        config={
            CHAIN_META: chain_metadata,
            "callbacks": cbs,
            "max_concurrency": app_config.MAX_CONCURRENCY,
            "run_name": f"{req.model_type}: {req.message}: 标题改写",
        },
    )
    logger.info(Fore.CYAN + "大模型生成标题: %s" + Fore.RESET, class_to_dict(response))
    return class_to_dict(GenerateChartTitleResponse(data=response))
