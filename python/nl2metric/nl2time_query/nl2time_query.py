import datetime
import calendar
import re
from typing import Optional, Union, List

from colorama import Fore, Style
from langchain_core.output_parsers import StrOut<PERSON>Parser
from langchain_core.runnables import RunnableConfig, <PERSON>nableLambda
from pydantic import BaseModel, ValidationError, validator

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types import TimeQueryParams
from common.types.base import CHAIN_META, ChainMeta, ParamsExtractStage, ChainRuntime
from common.types.exception_cacher import no_exception
from common.utils.json_utils import extract_json_from_string
from config.project_config import get_project_config
from nl2metric.few_shots import BAOWU_PROJECT_NAME, CHINA_LIFE_NAME
from langchain_core.outputs import Generation

logger = get_logger(__name__)


# Pydantic 数据格式检查


# 调用大模型
# invoke with question
def call_nl2time_query(
    model_type: str,
    prompt_selector: PromptSelectorBase,
    project_name: str,
    model_name: str,
) -> Optional[TimeQueryParams]:
    project_config = get_project_config(project_name, model_name)
    time_query_params = project_config.special_subchain_params(
        ParamsExtractStage.NL2METRIC_TIME_QUERY
    )
    use_v2 = False
    if time_query_params:
        use_v2 = time_query_params["type"] == ParamsExtractStage.NL2METRIC_TIME_QUERY_V2
    if use_v2:
        chain = (
            RunnableLambda(retrive_for_time_query, name="retrive_for_time_query")
            | RunnableLambda(
                prompt_selector.gen_prompt,
                name="PromptSelectorBase.gen_prompt:"
                + ParamsExtractStage.NL2METRIC_TIME_QUERY_V2,
            ).bind(stage=ParamsExtractStage.NL2METRIC_TIME_QUERY_V2)
            | RunnableLambda(create_chat_model_in_chain).bind(
                stage=ParamsExtractStage.NL2METRIC_TIME_QUERY
            )
            | RunnableLambda(save_llm_output, name="save_llm_output")
            | StrOutputParser()
            | RunnableLambda(
                parse_time_query_response, name="parse_time_query_response"
            )
        )
        chain.name = ParamsExtractStage.NL2METRIC_TIME_QUERY_V2
    else:
        chain = (
            RunnableLambda(retrive_for_time_query, name="retrive_for_time_query")
            | RunnableLambda(
                prompt_selector.gen_prompt,
                name="PromptSelectorBase.gen_prompt:"
                + ParamsExtractStage.NL2METRIC_TIME_QUERY,
            ).bind(stage=ParamsExtractStage.NL2METRIC_TIME_QUERY)
            | RunnableLambda(create_chat_model_in_chain).bind(
                stage=ParamsExtractStage.NL2METRIC_TIME_QUERY
            )
            | RunnableLambda(save_llm_output, name="save_llm_output")
            | StrOutputParser()
            | RunnableLambda(
                parse_time_query_response, name="parse_time_query_response"
            )
        )
        chain.name = ParamsExtractStage.NL2METRIC_TIME_QUERY
    chain.name = ParamsExtractStage.NL2METRIC_TIME_QUERY
    return chain


def save_llm_output(
    result: List[Generation], *, partial: bool = False, config: RunnableConfig
):
    # save for compute llm output confidence
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.LLM_OUTPUT_QUERY_TIME] = result

    return result


# 解析响应
@no_exception
def parse_time_query_response(
    response: str, config: RunnableConfig
) -> Optional[TimeQueryParams]:
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]

    obj = extract_json_from_string(response, "parse_time_query_response")
    if project_name == BAOWU_PROJECT_NAME:
        obj = check_invalid_date(obj)
        obj = check_for_cumulative(question, obj)
    if obj is None:
        logger.info("大模型没有返回有效的日期，返回 None：")
        return None

    time_query_type = obj.get("timeQueryType", None)
    if time_query_type not in {"日", "月", "季", "年", None}:
        logger.error(f"invalid time_query_type {time_query_type}")
        return None
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.TIME_QUERY_TYPE
    ] = time_query_type

    # 使用 Pydantic 模型进行验证
    if not obj.get("timeStartFunction") and not obj.get("timeEndFunction"):
        logger.error("大模型返回的结果不包含 timeStartFunction 和 timeEndFunction" + str(obj))
        return None
    # check invalid date
    if obj.get("timeStartFunction"):
        for value in obj.get("timeStartFunction").values():
            if value == 0:
                return None
    if obj.get("timeEndFunction"):
        for value in obj.get("timeEndFunction").values():
            if value == 0:
                return None
    # 如果不存在 type，则默认为 specificDate
    if obj["timeStartFunction"].get("type") is None:
        obj["timeStartFunction"]["type"] = "specificDate"
    if obj["timeEndFunction"].get("type") is None:
        obj["timeEndFunction"]["type"] = "specificDate"
    # 如果 timeGranularity 不存在，则默认为 total
    if obj.get("timeGranularity") is None:
        obj["timeGranularity"] = "total"

    if (
        obj["timeGranularity"] == "total"
        and project_name == BAOWU_PROJECT_NAME
        and "趋势" in question
    ):
        obj["timeGranularity"] = "month"
    # 人寿
    if project_name == CHINA_LIFE_NAME:
        # 同环比问题只支持月颗粒度，修改颗粒度为month
        if any(
            word in question
            for word in ["同比", "环比", "同环比", "同比增长率", "环比增长率", "同比增幅", "环比增幅"]
        ) and obj.get("timeGranularity"):
            obj["timeGranularity"] = "month"

    return TimeQueryParams(**obj)


def retrive_for_time_query(question: str, config: RunnableConfig):
    if isinstance(question, dict):
        question = question["question"]
    metric_list = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_METRICS][:5]
    metric_list = [
        metric.label + "/" + "/".join(metric.synonyms)
        if metric.synonyms
        else metric.label
        for metric in metric_list
    ]
    metrics = "[" + ", ".join(metric_list) + "]"

    return {"question": question, "metrics": metrics}


def check_for_cumulative(question: str, obj: dict) -> dict:
    """
    累计类问题：
    1. 问题中不包含起始时间时，默认以当年1月作为起始时间
    2. 问题中不包含时间时，默认以今年作为起始和结束时间
    """
    if re.search(r"(累计|累积)(?!折旧)", question) and obj:
        if obj["timeStartFunction"]["month"] == obj["timeEndFunction"]["month"]:
            obj["timeStartFunction"]["month"] = 1
            obj["timeStartFunction"]["day"] = 1
        logger.info("问题中不包含起始时间时，默认以当年1月作为起始时间，obj：" + str(obj))
    elif re.search(r"(累计|累积)(?!折旧)", question):
        now = datetime.datetime.now()
        obj = {
            "timeStartFunction": {"year": now.year, "month": 1, "day": 1},
            "timeEndFunction": {"year": now.year, "month": now.month, "day": now.day},
        }
        logger.info("问题中不包含时间时，默认以今年作为起始和结束时间，obj：" + str(obj))
    return obj


# fix: 2月天数错误
def check_invalid_date(obj):
    obj["timeEndFunction"]["day"] = min(
        obj["timeEndFunction"]["day"],
        calendar.monthrange(
            obj["timeEndFunction"]["year"], obj["timeEndFunction"]["month"]
        )[1],
    )
    return obj


if __name__ == "__main__":
    sr = """好的，我需要解决用户的问题：“今年四月各个公司的销售结算外销价格”。首先，我要确定这个问题中包含哪些时间词。用户提到“今年四月”，这明显指定了时间范围，即今年的四月份。接下来，我需要提取开始和结束时间。今年是2025年，所以四月就是从2025年4月1日到4月30日。

然后，我要判断时间粒度。用户的问题是要查看各个公司的销售结算外销价格，没有明确提到需要按天、月或其他粒度来分组。但因为用户问的是“今年四月”，所以可能需要按月来汇总，不过这里可能不需要进一步分组，因为问题只是询问该月的数据。不过根据之前的例子，如果问题中没有明确的时间粒度，可能默认为month，但这里可能直接使用total，因为用户只是询问四月的整体数据。

接下来是时间查询类型，用户提到的是四月，属于月份，所以时间查询类型应该是“月”。同时，我需要确认是否有趋势或其他要求，但问题中没有提到趋势，只是询问数据，所以不需要调整粒度。此外，检查指标列表，确认“销售结算外销价格”是存在的，但时间词“今年四月”并不在指标列表中，因此需要提取。

最后，整理参数：开始时间和结束时间是2025年4月1日和4月30日，时间粒度可能需要确认，但根据问题，可能不需要分组，所以时间粒度设为month，因为用户明确指定了月份。因此，最终的参数应该是timeStartFunction为2025年4月1日，timeEndFunction为4月30日，timeGranularity为month，timeQueryType为“月”。
</think>{
"Think": ["今年四月"],
"Result": {
"timeStartFunction": {"year": 2025, "month": 4, "day": 1},
"timeEndFunction": {"year": 2025, "month": 4, "day": 30},
"timeGranularity": "month",
"timeQueryType": "月"
}
}"""
    extract_json_from_string(sr)
