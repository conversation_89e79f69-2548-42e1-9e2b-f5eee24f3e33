import simplejson as json
import os
import requests
import threading
import time
import traceback
from typing import Dict, Any, Optional
from urllib.parse import urljoin
from uuid import UUID

from enum import Enum
from langchain_core.messages import get_buffer_string
from multiprocessing import Process, Queue
from pydantic import BaseModel

from common.trace.langfuse import langfuse_callback
from common.types.callback_handler import (
    BIJsonEncoder,
    BackendReporterQueueCallbackHandler,
    LogCallbackHandler,
    DebugLogCallbackHandler,
    report_progress_status_begin,
    report_progress_status_end,
    ProgressRunStatus,
    report_nl2document_progress_status_end,
    report_progress_status_close,
    ProgressStatus,
)
from common.logging.logger import get_logger
from common.types.base import CB_CLOSE_MARK, ParamsExtractStage
from config import app_config


logger = get_logger(__name__)


queue: Queue = None
remote_queue: Queue = None
reporter_process: Process = None


def get_queue():
    return queue


def set_remote_queue(queue):
    global remote_queue
    remote_queue = queue


def get_reporter_cb(trace_id, task_id, trace_name, user_id, call_back_addr=None):
    if remote_queue is not None:
        return BackendReporterQueueCallbackHandler(
            trace_id, task_id, trace_name, user_id, remote_queue, call_back_addr
        )
    elif queue is not None:
        return BackendReporterQueueCallbackHandler(
            trace_id, task_id, trace_name, user_id, queue, call_back_addr
        )
    else:
        return None


def reporter(queue):
    cleaner_thread = threading.Thread(
        target=cleaner_task, args=(data_holder,), daemon=True
    )
    cleaner_thread.start()

    print("Reporter processing started")

    while True:
        try:
            trace_id, user_id, task_id, name, message, args, kwargs = queue.get()

            if message == "exit":
                break

            data_holder.thread_safe_operation(
                trace_id, user_id, task_id, name, message, args, kwargs
            )
        except Exception as e:
            logger.error(
                f"an exception occurred in reporter processing: {e}, traceback: {traceback.format_exc()}"
            )


def backend_stage_reporter_monitor_start():
    global reporter_process
    if reporter_process is not None and reporter_process.is_alive():
        return
    if reporter_process is not None:
        logger.error(
            f"backend reporter {reporter_process.pid} exited code {reporter_process.exitcode}. Restarting..."
        )
    global queue
    queue = Queue()
    reporter_process = Process(target=reporter, name="ask_bi_reporter", args=(queue,))
    reporter_process.start()
    logger.info(
        f"backend reporter {reporter_process.pid} start succeed is_alive {reporter_process.is_alive()}"
    )


class MemDataHolder:
    def __init__(self) -> None:
        self.__data: Dict[str, Any] = {}
        self.__holder_lock: threading.Lock = threading.Lock()

    def get_data(self, key: str):
        with self.__holder_lock:
            if key in self.__data:
                return self.__data[key]
            else:
                return None

    def get_data_without_lock(self, key: str):
        if key in self.__data:
            return self.__data[key]
        else:
            return None

    def add_data(self, key, value):
        with self.__holder_lock:
            self.__data[key] = value

    def add_data_without_lock(self, key, value):
        self.__data[key] = value

    def add_data_if_absent(self, key, value):
        with self.__holder_lock:
            if key in self.__data:
                return self.__data[key]
            else:
                self.__data[key] = value
                return None

    def cleanup(self):
        def remove_condition(value):
            ts, *rest = value
            current_timestamp = time.time()
            if current_timestamp - ts > 600:
                return True
            return False

        with self.__holder_lock:
            keys_to_clean = [
                key for key in self.__data if remove_condition(self.__data[key])
            ]
            for key in keys_to_clean:
                del self.__data[key]

    def thread_safe_operation(
        self,
        trace_id: str,
        user_id: str,
        task_id: str,
        callback_name: str,
        message: str,
        args: Any,
        kwargs: Any,
    ) -> None:
        with self.__holder_lock:
            data_tuple = data_holder.get_data_without_lock(trace_id)
            if data_tuple is None:
                callbacks = []
                if app_config.USE_ASYNC_LANGCHAIN_LOG:
                    callbacks.append(
                        LogCallbackHandler(
                            id=trace_id, host=app_config.CLUSTER_ID, user_id=user_id
                        )
                    )

                if app_config.ENABLE_LANGCHAIN_DEBUG_LOGGER:
                    callbacks.append(
                        DebugLogCallbackHandler(id=trace_id, trace_name=callback_name)
                    )
                progress_status = ProgressStatus()
                uuid_to_stages: Dict[UUID, str] = {}
                # 从配置中获取回调地址
                call_back_addr = kwargs.get("call_back_addr", None)
            else:
                # unpack callback
                (
                    _,
                    callbacks,
                    progress_status,
                    uuid_to_stages,
                    call_back_addr,
                ) = data_tuple

            for callback in callbacks:
                try:
                    data_holder._run_callback(callback, message, args, kwargs)
                except Exception as e:
                    logger.error(
                        f"{callback.name}: trace_id {trace_id}, task_id {task_id}, name {callback_name}, message {message}, exception {e}"
                    )
            if langfuse_callback is not None:
                data_holder._run_callback(langfuse_callback, message, args, kwargs)

            if message == "on_chain_start":
                report_progress_status_begin(
                    uuid_to_stages, trace_id, task_id, *args, **kwargs
                )
            elif message == "on_chain_end":
                report_progress_status_end(
                    ProgressRunStatus.SUCCEED,
                    trace_id,
                    task_id,
                    uuid_to_stages,
                    progress_status,
                    *args,
                    **kwargs,
                )
                if app_config.ENABLE_NL2DOCUMENT:
                    report_nl2document_progress_status_end(
                        trace_id,
                        task_id,
                        uuid_to_stages,
                        progress_status,
                        *args,
                        **kwargs,
                    )
            elif message == "on_chain_error":
                report_progress_status_end(
                    ProgressRunStatus.FAILED,
                    trace_id,
                    task_id,
                    uuid_to_stages,
                    progress_status,
                    *args,
                    **kwargs,
                )
            elif message == CB_CLOSE_MARK:
                report_progress_status_close(
                    trace_id, task_id, progress_status, call_back_addr
                )
            current_timestamp = time.time()
            data_holder.add_data_without_lock(
                trace_id,
                (
                    current_timestamp,
                    callbacks,
                    progress_status,
                    uuid_to_stages,
                    call_back_addr,
                ),
            )

    def _run_callback(self, callback, message, args, kwargs):
        if callback is None:
            return
        if message == "on_agent_finish":
            callback.on_agent_finish(*args, **kwargs)
        elif message == "on_chain_start":
            callback.on_chain_start(*args, **kwargs)
        elif message == "on_chat_model_start":
            try:
                callback.on_chat_model_start(*args, **kwargs)
            except Exception as e:
                # this code is copied from langchain_core/callbacks/manager.py
                message_strings = [get_buffer_string(m) for m in args[1]]
                callback.on_llm_start(
                    args[0],
                    message_strings,
                    *args[2:],
                    **kwargs,
                )
        elif message == "on_llm_start":
            callback.on_llm_start(*args, **kwargs)
        elif message == "on_agent_action":
            callback.on_agent_action(*args, **kwargs)
        elif message == "on_retriever_start":
            callback.on_retriever_start(*args, **kwargs)
        elif message == "on_chain_end":
            callback.on_chain_end(*args, **kwargs)
        elif message == "on_chain_error":
            callback.on_chain_error(*args, **kwargs)
        elif message == "on_retriever_end":
            callback.on_retriever_end(*args, **kwargs)
        elif message == "on_tool_start":
            callback.on_tool_start(*args, **kwargs)
        elif message == "on_retriever_error":
            callback.on_retriever_error(*args, **kwargs)
        elif message == "on_tool_end":
            callback.on_tool_end(*args, **kwargs)
        elif message == "on_tool_error":
            callback.on_tool_error(*args, **kwargs)
        elif message == "on_llm_end":
            callback.on_llm_end(*args, **kwargs)
        elif message == "on_llm_error":
            callback.on_llm_error(*args, **kwargs)
        elif message == "on_llm_new_token":
            callback.on_llm_new_token(*args, **kwargs)
        elif message != CB_CLOSE_MARK:
            logger.error(f"Unknown message type: {message}")


data_holder = MemDataHolder()


def cleaner_task(holder: MemDataHolder):
    while True:
        holder.cleanup()
        time.sleep(2)
