import uuid

from langchain_openai.chat_models.base import BaseChatOpenAI

from common.logging.logger import get_logger
from typing import Tuple, Any, Optional, Dict
from langchain_core.runnables import Runnable, RunnableLambda, RunnableConfig
from langchain_core.messages import AIMessage
from langchain_core.messages.utils import message_chunk_to_message
from common.stream_reporter.service import stream_reporter_put
from common.types.base import (
    CHAIN_META,
    ChainMeta,
)
from common.types.exception_cacher import ResponseCode
from common.types.callback_handler import try_json_stringify
from common.utils.llm_utils import is_model_thinking

logger = get_logger(__name__)


# breadcrumbs表示agent的层级关系
# 比如上来第一个agent也就是brain的breadcrumbs就是（breadcrumbs [0], tool_type None）
# brain调用的其他tool，比如直接调到chat，那就是（breadcrumbs [0], tool_type chat)
# brain调用到其他agent，比如有两个bi查询加一个doc, 然后再接第二个brain
# （breadcrumbs [0], tool_type None, plan {0: bi, 1: bi, 2: doc}),
# （breadcrumbs [0, 0], tool_type bi),
# （breadcrumbs [0, 1], tool_type bi)
# （breadcrumbs [0, 2], tool_type doc)
#  (breadcrumbs [1], tool_type None)
class AgentReporter:
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.steps_dict = {}

    @property
    def steps(self):
        sorted_keys = sorted(self.steps_dict.keys())
        return [self.steps_dict[key] for key in sorted_keys]

    def report(
        self,
        breadcrumbs: Tuple[str],
        config: RunnableConfig,
        tool_type: Optional[str] = None,
        cot_uuid: Optional[str] = None,
        cot: Optional[str] = None,
        plan: Optional[Dict] = None,
        code: Optional[int] = None,  # running/succeed/failed
        data: Optional[Any] = None,
        # 下面的两个参数是专门为兼容以前的老bi上报和前端的协议
        # 跟其他工具无关
        nl2agent_build_msg: Optional[str] = None,
        nl2agent_steps_list_tuple: Optional[Any] = None,
        nl2agent_delta_builds_tuple: Optional[Any] = None,
    ):
        if isinstance(breadcrumbs, list):
            breadcrumbs = tuple(breadcrumbs)
        elif not isinstance(breadcrumbs, tuple):
            raise RuntimeError(
                f"found illegal breadcrumbs {type(breadcrumbs)}: {breadcrumbs}"
            )
        if breadcrumbs not in self.steps_dict:
            self.steps_dict[breadcrumbs] = {
                "status": "running",
                "breadcrumbs": list(breadcrumbs),
            }
        report_data = self.steps_dict[breadcrumbs]
        if tool_type is not None:
            report_data["tool_type"] = tool_type
        if cot_uuid is not None:
            report_data["cot_uuid"] = cot_uuid
        if cot is not None:
            report_data["cot"] = cot
        if plan is not None:
            report_data["plan"] = plan
        if code is not None:
            if code in (0, ResponseCode.NOT_NEED_BI_RESULT.value):
                report_data["status"] = "succeed"
            elif code == ResponseCode.NEED_MANUAL_SELECT.value:
                report_data["status"] = "running"
            else:
                report_data["status"] = "failed"
            report_data["code"] = code
        if data is not None:
            if isinstance(data, str):
                report_data["data"] = data
            else:
                report_data["data"] = try_json_stringify(data, None)

        # 为了复用老bi链路与前端的上报逻辑
        if nl2agent_build_msg is not None:
            report_data["nl2agent_build_msg"] = try_json_stringify(
                nl2agent_build_msg, None
            )
        if nl2agent_steps_list_tuple is not None:
            param_key, nl2agent_steps_list = nl2agent_steps_list_tuple
            if "nl2agent_steps_list" in report_data:
                report_data["nl2agent_steps_list"][param_key] = nl2agent_steps_list
            else:
                report_data["nl2agent_steps_list"] = {param_key: nl2agent_steps_list}
        if nl2agent_delta_builds_tuple is not None:
            param_key, nl2agent_delta_builds = nl2agent_delta_builds_tuple
            if "nl2agent_delta_builds" in report_data:
                report_data["nl2agent_delta_builds"][param_key] = nl2agent_delta_builds
            else:
                report_data["nl2agent_delta_builds"] = {
                    param_key: nl2agent_delta_builds
                }

        # report
        def _report(input):
            return self.steps

        report_chain = RunnableLambda(_report, name="agent_reporter")
        report_chain.invoke(None, config=config)

    def report_cot(
        self,
        breadcrumbs: Tuple[str],
        cot_uuid: str,
        name: str,
        config: RunnableConfig,
    ):
        if not cot_uuid:
            raise RuntimeError("no cot_uuid")
        self.report(
            breadcrumbs=breadcrumbs,
            cot_uuid=cot_uuid,
            tool_type=name,
            config=config,
        )


class MockAgentReporter(AgentReporter):
    def report(
        self,
        breadcrumbs: Tuple[str],
        config: RunnableConfig,
        tool_type: Optional[str] = None,
        cot_uuid: Optional[str] = None,
        cot: Optional[str] = None,
        plan: Optional[Dict] = None,
        code: Optional[int] = None,  # running/succeed/failed
        data: Optional[Any] = None,
        # 下面的两个参数是专门为兼容以前的老bi上报和前端的协议
        # 跟其他工具无关
        nl2agent_build_msg: Optional[str] = None,
        nl2agent_steps_list_tuple: Optional[Any] = None,
        nl2agent_delta_builds_tuple: Optional[Any] = None,
    ):
        pass

    def report_cot(
        self,
        breadcrumbs: Tuple[str],
        cot_uuid: str,
        name: str,
        config: RunnableConfig,
    ):
        pass


def reporter_run_chain(
    breadcrumbs: Tuple[str],
    name: str,
    chain: Runnable,
    input,
    config: RunnableConfig,
    code=None,
):
    if not breadcrumbs:
        return chain.invoke(
            input,
            config=config,
        )
    has_cot = True
    if isinstance(chain, BaseChatOpenAI):
        has_cot = is_model_thinking(chain)
    reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
    cot_uuid = str(uuid.uuid4()) if has_cot else None
    report_cot = True
    chunk_all = None
    for chunk in chain.stream(
        input,
        config=config,
    ):
        if chunk_all is None:
            chunk_all = chunk
        else:
            chunk_all = chunk_all + chunk
        if cot_uuid:
            stream_reporter_put(
                id=cot_uuid,
                chunk=chunk if isinstance(chunk, str) else chunk.content,
                is_end=False,
            )
            if report_cot:
                report_cot = False
                reporter.report_cot(
                    breadcrumbs=breadcrumbs,
                    name=name,
                    cot_uuid=cot_uuid,
                    config=config,
                )
    if cot_uuid:
        stream_reporter_put(id=cot_uuid, chunk="", is_end=True)
    if isinstance(chunk_all, str):
        reporter.report(
            breadcrumbs=breadcrumbs,
            cot_uuid=cot_uuid,
            cot=chunk_all,
            code=code,
            config=config,
        )
        return chunk_all
    else:
        message = message_chunk_to_message(chunk_all)
        reporter.report(
            breadcrumbs=breadcrumbs,
            cot_uuid=cot_uuid,
            cot=message.content,
            code=code,
            config=config,
        )
        return message
