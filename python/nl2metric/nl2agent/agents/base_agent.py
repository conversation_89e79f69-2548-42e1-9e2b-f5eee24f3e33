import re
import copy
import json
from typing import Dict, List, Any, Set, Tuple, Optional, Callable, Union

from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.logging.utils import chain_err_log
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.exception_cacher import _exception_to_code
from config import app_config
from config.project_config import get_project_config, ProjectConfig
from langchain_core.messages import ChatMessage, AIMessage
from langchain_core.runnables import RunnableConfig, Runnable
from langchain_core.utils.function_calling import convert_to_openai_function
from nl2agent.dag.node import Node
from nl2agent.common.agent_reporter import reporter_run_chain

logger = get_logger(__name__)


class BaseAgent:
    """
    Agent基类，定义所有Agent的基础行为和属性

    Attributes:
        name: Agent的名称
        functions: Agent可用的工具函数列表
        system_prompt: Agent的系统提示
    """

    # 默认的工具调用，当未能成功解析工具调用时使用
    FALL_BACK_TOOL_CALL = [{"name": "chat", "args": {"think": "true"}}]

    def __init__(self, name: str):
        """
        初始化BaseAgent

        Args:
            name: Agent的名称
        """
        self.name = name
        self.functions = []
        self.system_prompt = ""

    def _use_tool_call(self, model_type: str) -> bool:
        """
        判断是否使用工具调用

        Args:
            model_type: 模型类型

        Returns:
            是否使用工具调用
        """
        return model_type != app_config.MODEL_TYPE_DEEPSEEK_AGENT_14B

    def _parse_tool_calls(self, message, config: RunnableConfig):
        """
        从消息中解析工具调用

        Args:
            message: AI消息
            config: 可运行配置

        Returns:
            工具调用列表
        """
        result = []
        try:
            pattern = r"<tool_call>(.*?)</tool_call>"
            tool_calls = re.findall(pattern, message.content, re.DOTALL)
            if not tool_calls:
                pattern = r"<tool_call>(.*?)</"
                tool_calls = re.findall(pattern, message, re.DOTALL)
            for tool_call_str in tool_calls:
                tool_call_json = tool_call_str.strip()
                tool_call = json.loads(tool_call_json)
                result.append(
                    {
                        "name": tool_call["name"],
                        "args": tool_call["arguments"]
                        if "arguments" in tool_call
                        else tool_call["args"],
                    }
                )
        except Exception as e:
            chain_err_log(logger, config, f"parse tool call failed {e}")

        # 如果没有解析到工具调用，返回默认的工具调用
        return result or self.FALL_BACK_TOOL_CALL

    def _get_tools_from_config(
        self, config: RunnableConfig, tools_type: str
    ) -> List[Dict]:
        """
        从配置中获取工具

        Args:
            config: 可运行配置
            tools_type: 工具类型, 由子类定义

        Returns:
            工具函数列表
        """
        project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
        model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]

        project_config = get_project_config(
            project_name=project_name,
            model_name=model_name,
        )

        # 获取黑名单
        blacklist = []
        if tools_type == "brain":
            blacklist = project_config.agent_brain_tools_blacklist
        elif tools_type == "judge":
            blacklist = project_config.agent_judge_tools_blacklist

        # 获取工具
        result = self._get_tools(tools_type, project_config)

        # 应用黑名单过滤
        if blacklist:
            return [f for f in result if f["name"] not in blacklist]
        else:
            return result

    def _get_tools(self, tools_type: str, project_config: ProjectConfig) -> List[Dict]:
        """
        获取指定类型的工具，由子类实现

        Args:
            tools_type: 工具类型，由子类定义

        Returns:
            工具函数列表
        """
        return []

    def _filter_tools(
        self,
        model_tool_calls: List[Dict],
        valid_tools: Set[str],
        fallback: List[Dict] = None,
    ) -> List[Dict]:
        """
        过滤掉无效的工具调用

        Args:
            model_tool_calls: 模型工具调用列表
            valid_tools: 有效工具名称集合
            fallback: 回退工具调用(默认为chat)

        Returns:
            过滤后的工具调用列表
        """
        if fallback is None:
            fallback = self.FALL_BACK_TOOL_CALL

        valid_tool_calls = []
        for tool_call in model_tool_calls:
            if tool_call["name"] in valid_tools:
                valid_tool_calls.append(tool_call)
        return valid_tool_calls if valid_tool_calls else fallback

    def _handle_error(self, error, reporter, breadcrumbs, config):
        """
        处理异常

        Args:
            error: 异常
            reporter: 报告器
            breadcrumbs: 面包屑
            config: 配置
        """
        # 转换异常为错误码和数据
        err = _exception_to_code(error)

        # 报告错误
        reporter.report(
            breadcrumbs=breadcrumbs,
            code=int(err["code"]),
            data=err["data"],
            config=config,
        )

    def _report_success(self, reporter, breadcrumbs: Tuple, config: RunnableConfig):
        """
        报告成功

        Args:
            reporter: 报告器
            breadcrumbs: 面包屑
            config: 可运行配置
        """
        reporter.report(
            breadcrumbs=breadcrumbs,
            code=0,
            config=config,
        )

    def run(self, input_data: Any, config: RunnableConfig) -> Any:
        """
        运行Agent

        Args:
            input_data: 输入数据
            config: 可运行配置

        Returns:
            处理结果
        """
        raise NotImplementedError("Subclasses must implement run method")
