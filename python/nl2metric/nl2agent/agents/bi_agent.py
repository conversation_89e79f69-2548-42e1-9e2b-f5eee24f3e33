import json

from langchain_core.output_parsers import Str<PERSON><PERSON><PERSON><PERSON>arser

from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta
from common.utils.json_utils import extract_json_from_string
from langchain_core.runnables import RunnableConfig, RunnableLambda, RunnablePassthrough
from pydantic import BaseModel, Field

from common.utils.llm_utils import create_llm_model_by_project_config
from config.project_config import get_project_config
from nl2agent.agents.base_agent_with_dag import BaseAgentWithDAG
from nl2agent.common.agent_reporter import reporter_run_chain
from nl2agent.dag.dag_executor import DagExecutor
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.tools.calculate import CalculateTool
from nl2agent.tools.filter import FilterTool
from nl2agent.tools.join import Join<PERSON>ool, AutoJoinTool
from nl2agent.tools.lookup_data import LookupDataTool, LookupDataInputWithNER
from nl2agent.tools.order import OrderTool
from nl2agent.tools.predict import PredictTool
from nl2agent.tools.select import SelectTool
from nl2agent.tools.table_tools import TableTools
from nl2agent.tools.time_range import TimeRangeTool
from typing import List, Type

logger = get_logger(__name__)


class BiAgentInput(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="bi")
class BiAgent(BaseAgentWithDAG):
    name: str = "bi"
    description: str = "bi 工具。能力范围：嵌套数据查询；占比；同环比；增长率计算；筛选；排序；最值；topN；bottomN；维度码值查询"
    args_schema: Type[BaseModel] = BiAgentInput
    stage: ParamsExtractStage = ParamsExtractStage.BI
    # 保留字段，提示该Agent使用哪些tool方便阅读理解，
    # 当前不能灵活增删，只能由算法决定，以后要更灵活
    tools: List[BaseTool] = [
        LookupDataTool,
        CalculateTool,
        FilterTool,
        JoinTool,
        AutoJoinTool,
        OrderTool,
        PredictTool,
        SelectTool,
        TimeRangeTool,
        TableTools,
    ]

    def _create_chat_model(
        self, prompt, config: RunnableConfig, model_type="agent_model"
    ):
        project_config = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        )
        chat_model = create_llm_model_by_project_config(model_type, project_config)
        return reporter_run_chain(
            breadcrumbs=self.breadcrumbs,
            chain=chat_model,
            input=prompt,
            config=config,
            name=self.name,
        )

    def _gen_ner_prompt(self, input, config: RunnableConfig):
        bi_planning_result = input["bi_planning_result"]
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        parts = bi_planning_result.split("</think>", 1)
        raw_json = None
        if parts:
            thought = parts[0].strip()
            if thought and thought[-1] == "#":
                thought = thought[:-1]
            if len(parts) > 1:
                raw_json = parts[1].strip()
        else:
            thought = bi_planning_result
        if not raw_json:
            raw_json = bi_planning_result
        input["bi_planning_result"] = raw_json

        return prompt_selector.gen_prompt(
            input, stage=ParamsExtractStage.GENERAL_NER_EXTRACT, config=config
        )

    def _run_with_dag_plan(self, config: RunnableConfig):
        force_exact_match = config[CHAIN_META][ChainMeta.FORCE_EXACT_MATCH]
        planning_sub_chain = (
            # get prompt by intent
            RunnableLambda(self._gen_prompt, name=f"{self.name}_gen_prompt")
            # create dag, complicated questions need second run(currently not implemented)
            | RunnableLambda(
                self._create_chat_model, name=f"{self.name}_create_chat_model"
            )
            | StrOutputParser()
        )
        # 创建基础链（公共部分）
        chain = RunnableLambda(
            self._retrieve, name=f"{self.name}_retrieve"
        ) | RunnablePassthrough.assign(
            bi_planning_result=lambda x: planning_sub_chain.invoke(x)
        )
        if force_exact_match:
            ner_sub_chain = (
                RunnableLambda(self._gen_ner_prompt, name=f"{self.name}_gen_ner_prompt")
                | RunnableLambda(
                    self._create_chat_model, name=f"{self.name}_create_chat_model"
                ).bind(model_type="ner_model")
                | StrOutputParser()
            )
            chain = chain | RunnablePassthrough.assign(
                ner_result=lambda x: ner_sub_chain.invoke(x)
            )

        chain = chain | (
            RunnableLambda(self._build, name=f"{self.name}_build")
            # preprocess for agent
            | RunnableLambda(self._preprocess, name=f"{self.name}_preprocess")
            # execute agent dag
            | RunnableLambda(self._run_plan, name=f"{self.name}_run_plan")
            # postprocess for agent
            | RunnableLambda(self._postprocess, name=f"{self.name}_postprocess")
        )
        chain.name = f"{self.name}_agent_plan_tool"
        return chain

    # TODO(bhx): 重新实现一下cacluculator后删除此代码以及AutoJoinDagExecutor
    def _build(self, input, config: RunnableConfig):
        force_exact_match = config[CHAIN_META][ChainMeta.FORCE_EXACT_MATCH]
        response = input["bi_planning_result"]
        # parts = response.split("答案整理阶段", 1)
        # # 尽量使用"答案整理阶段"提取json，因为很多“规划阶段”也会生成json
        # 2025/04/27 修改：不再使用"答案整理阶段" 作为分割词，现在模型输出分割词为"</think>"
        parts = response.split("</think>", 1)
        raw_json = None
        if parts:
            thought = parts[0].strip()
            if thought and thought[-1] == "#":
                thought = thought[:-1]
            if len(parts) > 1:
                raw_json = parts[1].strip()
        else:
            thought = response
        if not raw_json:
            raw_json = response
        data = extract_json_from_string(raw_json, "agent_build")
        chain_ok_log(logger, config, f"agent_build {raw_json}, json: {data}")
        dag_executor = DagExecutor.build(data, breadcrumbs=self.breadcrumbs)
        if force_exact_match:

            def extract_ner_result(response: str, config: RunnableConfig):
                parts = response.split("</think>", 1)
                raw_json = None
                if parts:
                    if len(parts) > 1:
                        raw_json = parts[1].strip()
                if not raw_json:
                    raw_json = response
                data = extract_json_from_string(raw_json, "ner_extract_result")
                chain_ok_log(
                    logger, config, f"ner_extract_result {raw_json}, json: {data}"
                )
                return data

            ner_data = extract_ner_result(input["ner_result"], config)
            for param_key, ner_info in ner_data.items():
                node = dag_executor.get_node(param_key)
                if node.tool.name != "lookup_data":
                    continue
                metric_ner_list = []
                where_ner_list = []
                # Update metric parameters
                if "metric_ner" in ner_info:
                    for metric_ner in ner_info["metric_ner"]:
                        metric_ner_list.append(metric_ner)
                        node.tool_kw_params["metric_ner"] = metric_ner_list

                # Update where parameters
                if "where_ner" in ner_info:
                    for where_ner in ner_info["where_ner"]:
                        where_ner_list.append(where_ner)
                        node.tool_kw_params["where_ner"] = where_ner_list

                node.tool.args_schema = LookupDataInputWithNER
        # 当dag_executor里的node只有lookup data tool的时候，设置query_mom_yoy
        project_config = get_project_config(
            project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
            model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
        )
        if project_config.query_mom_yoy_as_default and all(
            node.tool.name == "lookup_data" for node in dag_executor.nodes.values()
        ):
            for node in dag_executor.nodes.values():
                node.tool.set_query_mom_yoy(True)
        # 这里的"nl2agent_build_msg"复用之前的intent_bi_agent的消息格式
        nl2agent_build_msg = {"steps": dag_executor.steps()}

        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            tool_type=self.name,
            config=config,
            nl2agent_build_msg=nl2agent_build_msg,
        )

        return dag_executor

    def verify_degree(self, degree):
        return degree == 0

    def table_description(self, kwargs):
        return kwargs["query"]
