import pandas as pd

from nl2agent.dag.node import NodeData
from nl2agent.utils import parse_function_call, convert_to_datetime
from nl2agent.types import TableColumn, UnknownColumn, TableColumnMeta
from typing import Dict, List, Any, Optional, Union


class TableNodeData(NodeData):
    schema: Optional[List[TableColumn]] = []

    def empty(self):
        return self.data is None or self.data.empty

    def to_json(self):
        if self.data is not None:
            if hasattr(self.data, "to_dict") and callable(self.data.to_dict):
                table = self.data.to_dict(orient="records")
            elif isinstance(self.data, list):
                table = []
                for item in self.data:
                    if isinstance(item, dict):
                        table.append(item)
                    elif hasattr(item, "to_dict") and callable(item.to_dict):
                        table.append(item.to_dict())
                    elif hasattr(item, "__dict__"):
                        table.append(item.__dict__)
                    else:
                        table.append(item)  # 保留原始格式

        else:
            table = []
        if self.schema is not None:
            schema = [col.to_json() for col in self.schema]
        else:
            schema = []
        return {
            "table": table,
            "schema": schema,
        }

    def copy_schema(self):
        return [s.copy() for s in self.schema]

    def calculable_column_names(self):
        return [s.column_name for s in self.schema if s.is_calculable]

    def non_calculable_column_names(self):
        return [s.column_name for s in self.schema if not s.is_calculable]

    @property
    def column_names(self):
        if not self.schema:
            return []
        return [col.column_name for col in self.schema]

    @property
    def columns_dict(self):
        if not self.schema:
            return {}
        result = {}
        for col in self.schema:
            result[col.column_name] = col
        return result

    def find_column(self, column_name, safe=False):
        for col in self.schema:
            if col.column_name == column_name:
                return col
        if safe:
            return None
        raise RuntimeError(f"cannot find col {column_name}")

    def verify(self):
        column_names = self.column_names
        if not column_names:
            raise RuntimeError(f"no column_names")
        if len(set(column_names)) != len(column_names):
            raise RuntimeError(f"column_names duplicate: {column_names}")
        df_columns_names = list(self.data.columns)
        if df_columns_names != column_names:
            raise RuntimeError(
                f"df columns {df_columns_names} doesnot match schema columns {column_names}"
            )

    def check_column_name(self, column_name):
        column_names = set(self.column_names)
        if isinstance(column_name, str):
            return bool(column_name in column_names)
        else:
            for tmp in column_name:
                if tmp not in column_names:
                    return False
            return True

    def rename(self, raw_rename_columns: Dict[str, str]):
        rename_columns = {}
        if raw_rename_columns:
            for src, dst in raw_rename_columns.items():
                if src != dst:
                    rename_columns[src] = dst
        if not rename_columns:
            return
        if len(rename_columns.values()) != len(set(rename_columns.values())):
            raise RuntimeError(
                f"cannot rename multi col to same col name: {rename_columns}"
            )
        # rename注意这种情况：{a:b, b:c}
        # 这里为了简化问题，所以校验的很严格
        column_names = set(self.column_names)
        for src, dst in rename_columns.items():
            if src not in column_names:
                raise RuntimeError(f"rename src {src} does not exist")
            if dst in column_names:
                raise RuntimeError(f"rename dst {dst} already exist")

        # rename df
        self.data.rename(columns=rename_columns, inplace=True)

        # rename schema
        for c in self.schema:
            if c.column_name in rename_columns:
                c.column_name = rename_columns[c.column_name]
        return

    def chose_one_calculable_and_rename(self, new_name):
        new_df = pd.DataFrame()
        new_schema = []
        found_calculable = False

        if new_name and new_name in self.column_names:
            for c in self.schema:
                if c.column_name == new_name:
                    if not c.is_calculable:
                        raise RuntimeError(
                            f"column {new_name} exist but not calculable"
                        )
                    new_schema.append(c)
                    new_df[c.column_name] = self.data[c.column_name]
                    found_calculable = True
                elif not c.is_calculable:
                    new_schema.append(c)
                    new_df[c.column_name] = self.data[c.column_name]
        else:
            # use the first calculable
            for c in self.schema:
                if c.is_calculable:
                    if found_calculable:
                        continue
                    else:
                        found_calculable = True
                        if new_name:
                            # rename c.column_name after self.data[c.column_name]
                            new_df[new_name] = self.data[c.column_name]
                            c.column_name = new_name
                            new_schema.append(c)
                        else:
                            new_schema.append(c)
                            new_df[c.column_name] = self.data[c.column_name]
                else:
                    new_schema.append(c)
                    new_df[c.column_name] = self.data[c.column_name]
        assert found_calculable, f"chose_one_calculable_and_rename found no calculable"
        self.data = new_df
        self.schema = new_schema

    def create_tmp_column(self, expr: Union[str, List[str]]):
        if not expr:
            return

        column_names = self.column_names

        def _run(single_expr):
            if single_expr in column_names:
                return
            func_name, args, kwargs = parse_function_call(single_expr)
            if not func_name:
                raise RuntimeError(f"expr {single_expr} is neither column nor expr")
            if func_name == "date_offset":
                args.insert(0, single_expr)
                return self._tmp_date_offset(*args, **kwargs)
            else:
                raise RuntimeError(f"expr {single_expr} not supported")

        if isinstance(expr, str):
            _run(expr)
        else:
            assert len(expr) == len(set(expr)), f"invalid tmp_column expr {expr}"
            for tmp in expr:
                _run(tmp)

    def clean_tmp_column(self):
        clean_schema = []
        tmp_schema = []
        for s in self.schema:
            if s.meta.get(TableColumnMeta.TMP_COLUMN, False):
                tmp_schema.append(s)
            else:
                clean_schema.append(s)
        if not tmp_schema:
            return
        clean_column_names = [s.column_name for s in clean_schema]
        self.schema = clean_schema
        self.data = self.data[clean_column_names]

    def _tmp_date_offset(self, tmp_column_name, column_name, *args, **kwargs):
        date_time, format = convert_to_datetime(self.data[column_name])
        date_time = date_time + pd.DateOffset(*args, **kwargs)
        self.data[tmp_column_name] = date_time.dt.strftime(format)
        self.schema.append(
            TableColumn(
                column_name=tmp_column_name,
                column_detail=UnknownColumn(
                    original_name="",
                ),
                meta={TableColumnMeta.TMP_COLUMN: True},
            )
        )
