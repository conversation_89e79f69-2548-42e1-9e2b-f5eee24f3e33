from backend_stage_reporter.reporter import ProgressRunStatus
from common.llm.general import create_chat_model_in_chain
from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ParamsExtractStage,
    ChainRuntime,
)
from common.utils.json_utils import extract_json_from_string
from metastore import get_metastore
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.node import Node
from nl2agent.types import TableColumnMeta
from typing import Type, List
from langchain_core.output_parsers import StrOutputParser
from pre_filter.retrive import dimension_retrive_value
from pydantic import BaseModel, Field
from langchain_core.runnables import (
    RunnableLambda,
    RunnableConfig,
)

logger = get_logger(__name__)


def table_tools_retrive(input, config: RunnableConfig):
    question = input["question"]
    parent_nodes = input["parent_nodes"]
    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
    result = ""
    for node in parent_nodes:
        result += f'"{node.name}": "{node.description}"\n'
        for s in node.node_data.schema:
            if s.is_metric:
                result += f'(name: {s.column_name}, type: "{s.column_type.value}", description: "{s.column_detail.prompt_description}")\n'
            elif s.is_time_dimension:
                format = s.meta.get(TableColumnMeta.TIME_DIMENSION_FORMAT, "")
                result += f'(name: {s.column_name}, type: "time_dimension", description: "{s.column_detail.description}", format: "{format}")\n'
            elif s.is_dimension:
                dimension = s.column_detail
                raw_dimension = metastore.safe_get_dimension(
                    model_name=dimension.model_name, dimension_name=dimension.name
                )
                dimension_values = dimension_retrive_value(
                    question, raw_dimension, config
                )
                dimension_values = [dv.name for dv in dimension_values]
                result += f'(name: {s.column_name}, type: "{s.column_type.value}", description: "{s.column_detail.prompt_description}", values: {dimension_values})\n'
            else:
                result += f'(name: {s.column_name}, type: "{s.column_type.value}", description: "{s.column_detail.description}")\n'
    return {
        "question": question,
        "table_schemas": result,
        **config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT],
    }


def table_tools_build(input, config: RunnableConfig):
    # extract from behind 答案整理阶段
    # there are json in 规划阶段
    parts = input.split("答案整理阶段", 1)
    if parts and len(parts) > 1:
        raw_json = parts[1].strip()
    else:
        raw_json = input
    data = extract_json_from_string(raw_json, "table_tools_build")
    chain_ok_log(logger, config, f"table_tools_build {raw_json}, json: {data}")
    return Node.from_json(data)


def table_tools(
    question: str, parent_nodes: List[Node], config: RunnableConfig
) -> List[Node]:
    prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
    chain = (
        RunnableLambda(
            table_tools_retrive,
        )
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + ParamsExtractStage.TABLE_TOOLS,
        ).bind(stage=ParamsExtractStage.TABLE_TOOLS)
        | RunnableLambda(
            create_chat_model_in_chain,
            # 这里原来用的是NL2AGENT，走配置的modeltype
            # 现在换成BI，因为新agent链路就要走外面传入的modeltype
        ).bind(stage=ParamsExtractStage.TABLE_TOOLS)
        | StrOutputParser()
        | RunnableLambda(table_tools_build, name="table_tools_build")
    )
    return chain.invoke(
        {"question": question, "parent_nodes": parent_nodes}, config=config
    )


class TableToolsInput(BaseModel):
    question: str = Field(description="要对查数获得的临时表进行的sql操作")


@register_tool(name="table_tools")
class TableTools(BaseTool):
    name: str = "table_tools"
    description: str = "对查数结果进行一系列sql操作"
    args_schema: Type[BaseModel] = TableToolsInput

    def _run(self, question: str, config: RunnableConfig):
        try:
            parent_nodes = [
                self.dag_executor.get_node(node_name)
                for node_name in self.target.parent_node_names
            ]
            new_nodes = table_tools(question, parent_nodes, config)
            self.dag_executor.replace_nodes(
                {
                    "node_name_to_replace": self.target.name,
                    "nodes": new_nodes,
                }
            )

        # for langfuse and reporter
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_delta_builds_tuple = (
                self.target.param_key,
                [ProgressRunStatus.FAILED, self.name, e],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                config=config,
                nl2agent_delta_builds_tuple=nl2agent_delta_builds_tuple,
            )
            raise e
        result = self.dag_executor.steps()
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        nl2agent_delta_builds_tuple = (
            self.target.param_key,
            [ProgressRunStatus.SUCCEED, self.name, result],
        )
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            config=config,
            nl2agent_delta_builds_tuple=nl2agent_delta_builds_tuple,
        )
        return result

    def verify_degree(self, degree):
        return degree > 0

    def table_description(self, kwargs):
        return kwargs["question"]

    def verify_result(self, target: Node):
        return True, None
