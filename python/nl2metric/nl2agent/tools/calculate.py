from langchain_core.runnables import RunnableConfig

from backend_stage_reporter.reporter import ProgressRunStatus
from common.types.base import CHAIN_META, ChainMeta
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.node import Node
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.tools.join import auto_join
from nl2agent.tools.tool_utils import eval_df_column_expression
from nl2agent.types import (
    ColumnType,
    CalculatedColumn,
    TableColumnMeta,
    TableColumn,
)
from pydantic import BaseModel, Field
from typing import Type, Optional
import re


def calculate(
    input: Node,
    target: Node,
    column_name: str,
    expression: str,
):
    target.node_data.data = input.node_data.data.copy(deep=True)
    target.node_data.schema = input.node_data.copy_schema()
    assert column_name not in target.node_data.column_names

    target.node_data.data[column_name] = eval_df_column_expression(
        target.node_data.data, expression
    )
    target.node_data.schema.append(
        TableColumn(
            column_name=column_name,
            column_detail=CalculatedColumn(
                description="",
                expression_type=ColumnType.CALCULATE,
                expression=expression,
            ),
            meta={TableColumnMeta.ORIGINAL_PARAM_KEY: target.param_key},
        )
    )


class CalculatInput(BaseModel):
    expression: str = Field(
        description="要计算的数学表达式，例如 '(`b` - `a`) / `a`'或 '(`a` + `b`) / `a`'"
    )
    description: Optional[str] = Field(default=None, description="对计算结果的描述，生成新的指标名")
    mapping: Optional[dict] = Field(default=None, description="传入的变量的映射")


@register_tool(name="calculator")
class CalculateTool(BaseTool):
    name: str = "calculator"
    description: str = "在表上新增一列，其值由给定的公式计算得出"
    args_schema: Type[BaseModel] = CalculatInput

    def _run(
        self,
        expression: str,
        description: Optional[str],
        mapping: Optional[dict],
        config: RunnableConfig,
    ):
        try:
            if not self.target.parent_node_names:
                raise RuntimeError("no nodes to calculate")
            nodes = []
            for node_name in self.target.parent_node_names:
                node = self.dag_executor.get_node(node_name)
                calculable_column_names = node.node_data.calculable_column_names()
                if len(calculable_column_names) < 1:
                    raise RuntimeError(
                        f"input calculable_column_num invalid: {node.to_json()}"
                    )
                node_copy = node.deepcopy()
                new_name = None
                if mapping and node.param_key in mapping:
                    new_name = mapping[node.param_key]
                node_copy.node_data.chose_one_calculable_and_rename(new_name)
                nodes.append(node_copy)

            if len(nodes) == 1:
                input = nodes[0]
            else:
                # auto join
                input = Node(
                    param_key=f"{self.target.param_key}_auto_join",
                    tool_name="auto_join",
                    node_data=TableNodeData(),
                )
                auto_join(nodes, input)

            # 检查左右表都只能有一个指标/calculate
            # 替换表达式中的列名
            param_key_2_column_name = {}
            for column in input.node_data.schema:
                if not column.is_calculable:
                    continue
                original_param_key = column.meta[TableColumnMeta.ORIGINAL_PARAM_KEY]
                if original_param_key not in param_key_2_column_name:
                    param_key_2_column_name[original_param_key] = column.column_name
                else:
                    raise RuntimeError(f"calculator input illegal: {input.to_json()}")

            def replace_expression(expr, mapping_dict):
                def replacer(match):
                    key = match.group(1)
                    return f"`{mapping_dict.get(key, key)}`"

                return re.sub(r"`(\w+)`", replacer, expr)

            expression = replace_expression(expression, param_key_2_column_name)

            # calculate
            column_name = description or self.target.param_key
            self.target.node_data = TableNodeData()
            calculate(input, self.target, column_name, expression)
            # 将column与原始node的param_key的关联关系存在此node中
            self.target.tool_kw_params["mapping"] = param_key_2_column_name
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_steps_list_tuple = (
                self.target.param_key,
                [ProgressRunStatus.FAILED, e],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                config=config,
                nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
            )
            raise e
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        nl2agent_steps_list_tuple = (
            self.target.param_key,
            [ProgressRunStatus.SUCCEED, self.target],
        )
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            config=config,
            nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
        )
        return self.target

    def verify_degree(self, degree):
        return degree >= 1

    def table_description(self, kwargs):
        return kwargs["description"]
