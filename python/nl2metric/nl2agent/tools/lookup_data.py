import pandas as pd

from backend_stage_reporter.reporter import ProgressRunStatus
from common.frontend.query_frontend_sql import query_frontend_sql
from common.types import ParamsExtractData, QueryMetricResult
from metastore.base import Dimension, Metric
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.node import Node
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.types import UnknownColumn, TableColumnMeta, TableColumn
from nl2intent.types import Intent
from common.manual_select.manual_select import ManualSelectTool
from common.types.base import CHAIN_META, ChainMeta, JobType, ChainRuntime
from common.trace import tracer
from common.types.exceptions import NeedManualSelect
from config.project_config import get_project_config
from langchain_core.runnables import RunnableConfig

# 注意这里不能直接用默认的BaseModel，因为BaseTool继承的是langchain的BaseModel
from common.utils.compat import BaseModel, Field
from metastore import get_metastore
from metastore.service import get_db_appstore
from typing import Type, Optional, List, Dict, Union


# label只有在calculator的时候模型会给出，这里就要求查出来的数据只能有一个指标，而且必须有一个指标
# 然后把这个指标重命名为label
# 如果没有label的话，calculator做autojoin的时候会自动重命名，就很丑
def lookup_data(
    target: Node,
    query: str,
    do_query,
    model_type,
    extra_additional_info,
    config: RunnableConfig,
    metric_ner: Optional[List[str]] = None,
    where_ner: Optional[List[str]] = None,
    query_mom_yoy: Optional[bool] = False,
):
    additional_info = config[CHAIN_META].get(ChainMeta.ADDITIONAL_INFO, {})
    hint_info: Dict[str:str] = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.HINT, {}
    )
    if extra_additional_info:
        # donnot update ChainMeta.ADDITIONAL_INFO
        additional_info = {**additional_info, **extra_additional_info}
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    raw_metrics = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.RAW_METRICS, []
    )
    from http_router.api.query_metric_data import query_metric_data
    from http_router.api.project_query_metric_data import project_query_metric_data

    is_project_param_extract = not config[CHAIN_META][ChainMeta.MODEL_ID]
    if not get_project_config(
        project_name, config[CHAIN_META][ChainMeta.MODEL_NAME]
    ).enable_manual_select:
        check_manual_select = False
    else:
        check_manual_select = additional_info.get(
            "always_check_manual_select", False
        ) or (config[CHAIN_META][ChainMeta.JOB_TYPE] == JobType.AGENT and do_query)
    if is_project_param_extract:
        project_response_data = project_query_metric_data(
            param_key=target.param_key,
            trace_id=f"{tracer.get_trace_id()}_{target.param_key}",
            question=query,
            project_name=project_name,
            project_id=project_id,
            model_type=model_type,
            user_id=config[CHAIN_META].get(ChainMeta.USER_ID, None),
            force_exact_match=config[CHAIN_META][ChainMeta.FORCE_EXACT_MATCH],
            require_metric_num=0,
            additional_info=additional_info,
            metric_ner=metric_ner,
            where_ner=where_ner,
            hint_info=hint_info,
        )
        (
            doc_params_extract_data,
            bi_params_extract_data,
        ) = split_project_params_extract_data(project_response_data, raw_metrics)
        check_manual_select = check_manual_select or len(doc_params_extract_data) > 0
        if check_manual_select:
            if len(project_response_data) > 1:
                return {
                    "need_manual_select": True,
                    "manual_selects": {
                        "type": "project_query_metric",
                        "data": project_response_data,
                        "query": query,
                    },
                }
            for model_response_data in project_response_data.values():
                if model_response_data.need_manual_select():
                    return {
                        "need_manual_select": True,
                        "manual_selects": {
                            "type": "project_query_metric",
                            "data": project_response_data,
                            "query": query,
                        },
                    }
        target.meta["query_metric_result"] = project_response_data

        if len(doc_params_extract_data) > 0:
            doc_values: list = config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.PARAMS_EXTRACT_DATA
            ]
            for model_id, response_data in doc_params_extract_data.items():
                doc_values.append(response_data)

        if not do_query or not bi_params_extract_data:
            target.meta["not_need_bi_result"] = not bi_params_extract_data
            return {"need_manual_select": False}
        else:
            project_query_metric = {
                model_id: response_data.query_metric
                for model_id, response_data in bi_params_extract_data.items()
            }
            # 如果没有置信度选择，取第一个
            model_id, query_metric = next(iter(project_query_metric.items()))
            model_name = get_db_appstore().get_model_by_id(model_id).table_name
            _do_frontend_query_sql(
                target=target,
                project_id=project_id,
                model_name=model_name,
                model_id=model_id,
                model_type=model_type,
                query_metric=query_metric,
                config=config,
                query_mom_yoy=query_mom_yoy,
            )
            return {"need_manual_select": False}
    else:
        model_id = config[CHAIN_META][ChainMeta.MODEL_ID]
        response_data = query_metric_data(
            param_key=target.param_key,
            trace_id=f"{tracer.get_trace_id()}_{target.param_key}",
            question=query,
            project_name=project_name,
            project_id=project_id,
            model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            model_label=config[CHAIN_META][ChainMeta.MODEL_LABEL],
            model_type=model_type,
            model_id=model_id,
            user_id=config[CHAIN_META].get(ChainMeta.USER_ID, None),
            force_exact_match=config[CHAIN_META][ChainMeta.FORCE_EXACT_MATCH],
            require_metric_num=0,
            additional_info=additional_info,
            metric_ner=metric_ner,
            where_ner=where_ner,
            hint_info=hint_info,
        )
        not_need_bi_result = is_all_doc_metrics(raw_metrics, response_data)
        if not_need_bi_result:
            doc_values: list = config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.PARAMS_EXTRACT_DATA
            ]
            doc_values.append(response_data)
            target.meta["not_need_bi_result"] = True
        if (
            check_manual_select
            and response_data.need_manual_select()
            and not not_need_bi_result
        ):
            return {
                "need_manual_select": True,
                "manual_selects": {
                    "type": "query_metric",
                    "data": {model_id: response_data},
                    "query": query,
                },
            }
        target.meta["query_metric_result"] = {model_id: response_data}
        if not do_query or not_need_bi_result:
            return {"need_manual_select": False}
        else:
            query_metric = response_data.query_metric
            _do_frontend_query_sql(
                target=target,
                project_id=project_id,
                model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
                model_id=config[CHAIN_META][ChainMeta.MODEL_ID],
                model_type=model_type,
                query_metric=query_metric,
                config=config,
                query_mom_yoy=query_mom_yoy,
            )
            return {"need_manual_select": False}


def lookup_data_after_manual_select(
    target: Node,
    do_query,
    model_type,
    model_id,
    query_metric_json,
    config: RunnableConfig,
    query_mom_yoy: Optional[bool] = False,
):
    query_metric = QueryMetricResult(**query_metric_json["query_metric"])
    raw_metrics = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.RAW_METRICS, []
    )
    params_extract = ParamsExtractData(
        type=query_metric_json["type"], query_metric=query_metric
    )
    target.meta["query_metric_result"] = {model_id: params_extract}

    if is_all_doc_metrics(raw_metrics, params_extract):
        config[CHAIN_META][ChainMeta.RUN_TIME][
            ChainRuntime.PARAMS_EXTRACT_DATA
        ] = params_extract
        target.meta["not_need_bi_result"] = True
        return
    if not do_query:
        return
    model_name = get_db_appstore().get_model_by_id(model_id).table_name
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    _do_frontend_query_sql(
        target=target,
        project_id=project_id,
        model_name=model_name,
        model_id=model_id,
        model_type=model_type,
        query_metric_json=query_metric_json["query_metric"],
        config=config,
        query_mom_yoy=query_mom_yoy,
    )


def _do_frontend_query_sql(
    target: Node,
    project_id: str,
    model_name: str,
    model_id: str,
    model_type: str,
    config: RunnableConfig,
    query_metric=None,
    query_metric_json=None,
    query_mom_yoy=False,
):
    metastore = get_metastore(project_id)
    query_metric_json = query_metric_json or query_metric.model_dump()
    user_id = config[CHAIN_META][ChainMeta.USER_ID]
    data, response = query_frontend_sql(
        {
            "param_key": target.param_key,
            "model_id": model_id,
            "query_metric_json": query_metric_json,
            "model_type": model_type,
            "user_id": user_id,
            "disable_mom_yoy": not query_mom_yoy,
        },
        config=config,
    )
    df = pd.DataFrame(data, columns=response.get("sqlColumnName", None))
    target.node_data = TableNodeData(data=df, schema=[])
    target.meta["metric-query-response"] = response
    readable_column_names = []
    df_columns = list(df.columns)
    for column_name in df_columns:
        # column is metric
        metric = metastore.safe_get_metric(column_name)
        if metric is not None:
            readable_name = metric.label
            target.node_data.schema.append(
                TableColumn(
                    column_name=readable_name,
                    column_detail=metric,
                    meta={TableColumnMeta.ORIGINAL_PARAM_KEY: target.param_key},
                )
            )
            readable_column_names.append(readable_name)
            try:
                df[column_name] = pd.to_numeric(df[column_name], errors="coerce")
            except Exception as e:
                raise RuntimeError(
                    f'column {column_name} convert to float failed {e}, data: {df.to_dict(orient="records")}'
                )
            continue

        # column is dimension
        dimension = metastore.safe_get_dimension(
            model_name=model_name,
            dimension_name=column_name,
        )
        if dimension is not None:
            dimension_without_values = Dimension(
                name=dimension.name,
                label=dimension.label,
                synonyms=dimension.synonyms,
                description=dimension.description,
                type=dimension.type,
                expr=dimension.expr,
                model_name=dimension.model_name,
                model_names=dimension.model_names,
                _high_risk_dimension_value_strs=dimension._high_risk_dimension_value_strs,
            )
        else:
            dimension_without_values = None
        if dimension is not None:
            readable_name = dimension.label
            target.node_data.schema.append(
                TableColumn(
                    column_name=readable_name,
                    column_detail=dimension_without_values,
                    meta={TableColumnMeta.ORIGINAL_PARAM_KEY: target.param_key},
                )
            )
            readable_column_names.append(readable_name)
            continue

        # column is ignored_dimension
        ignored_dimension = metastore.safe_get_ignored_dimension(
            model_name=model_name,
            dimension_name=column_name,
        )
        if ignored_dimension is not None:
            readable_name = ignored_dimension.label
            target.node_data.schema.append(
                TableColumn(
                    column_name=readable_name,
                    column_detail=ignored_dimension,
                    meta={TableColumnMeta.ORIGINAL_PARAM_KEY: target.param_key},
                )
            )
            readable_column_names.append(readable_name)
            continue

        # column is unknown
        readable_name = "日期" if column_name == "V_DATE_" else column_name
        target.node_data.schema.append(
            TableColumn(
                column_name=readable_name,
                column_detail=UnknownColumn(original_name=column_name),
                meta={TableColumnMeta.ORIGINAL_PARAM_KEY: target.param_key},
            )
        )
        readable_column_names.append(readable_name)

    df.fillna(0, inplace=True)
    assert len(set(readable_column_names)) == len(
        readable_column_names
    ), f"repeated column name in readable_column_names {readable_column_names}"
    target.node_data.data.columns = readable_column_names

    timeQueryParams = query_metric_json.get("timeQueryParams", None)
    if timeQueryParams:
        time_granularity = timeQueryParams["timeGranularity"]
        time_dimension_format = metastore.safe_get_time_dimension_format(
            model_name=model_name,
            time_granularity=time_granularity,
        )
        for s in target.node_data.schema:
            if s.is_time_dimension:
                s.meta[TableColumnMeta.TIME_DIMENSION_FORMAT] = time_dimension_format


def is_all_doc_metrics(
    raw_metrics: List[Metric],
    params_extract_data: Union[List[ParamsExtractData], ParamsExtractData] = None,
) -> bool:
    if not params_extract_data or not raw_metrics:
        return False

    metric_infos: List[Metric] = []
    if isinstance(params_extract_data, ParamsExtractData):
        new_params_extract_data = [params_extract_data]
    else:
        new_params_extract_data = params_extract_data
    for data in new_params_extract_data:
        for metric_name in data.query_metric.metricNames:
            # 在原始指标元数据中查找匹配项
            matched_metric = next(
                (m for m in raw_metrics if m.name == metric_name), None
            )
            if matched_metric:
                metric_infos.append(matched_metric)
    return metric_infos and all(metric.is_doc_metric() for metric in metric_infos)


def split_project_params_extract_data(
    project_params_extract_data: Dict[str, ParamsExtractData],
    raw_metrics: List[Metric],
):
    doc_params_extract_data: Dict[str, ParamsExtractData] = {}
    bi_params_extract_data: Dict[str, ParamsExtractData] = {}
    for scene_id, params_extract_data in project_params_extract_data.items():
        if is_all_doc_metrics(raw_metrics, params_extract_data):
            doc_params_extract_data[scene_id] = params_extract_data
        else:
            bi_params_extract_data[scene_id] = params_extract_data
    return doc_params_extract_data, bi_params_extract_data


class LookupDataInput(BaseModel):
    query: str = Field(description="用户输入的查询")


class LookupDataInputWithNER(BaseModel):
    query: str = Field(description="用户输入的查询")
    metric_ner: List[str] = Field(default_factory=list, description="指标命名实体识别结果")
    where_ner: List[str] = Field(default_factory=list, description="筛选条件命名实体识别结果")


@register_tool(name="lookup_data")
class LookupDataTool(BaseTool):
    name: str = "lookup_data"
    description: str = "查询数据"
    args_schema: Type[BaseModel] = LookupDataInput
    do_query: bool = True
    query_mom_yoy: bool = False
    # 这里如果直接manual_select_tool = ManualSelectTool()
    # 那ManualSelectTool只会在import的时候初始化一次
    manual_select_tool: ManualSelectTool = Field(default_factory=ManualSelectTool)
    extra_additional_info: Dict = {}

    def set_query_mom_yoy(self, query_mom_yoy: bool):
        self.query_mom_yoy = query_mom_yoy

    def _run(
        self,
        query,
        config: RunnableConfig,
        metric_ner: Optional[List[str]] = None,
        where_ner: Optional[List[str]] = None,
    ):
        try:
            model_type = config[CHAIN_META].get(
                ChainMeta.LOOKUP_DATA_MODEL_TYPE,
                config[CHAIN_META][ChainMeta.MODEL_TYPE],
            )
            if self.manual_select_tool.need_manual_select:
                all_manual_selects_result = config[CHAIN_META].get(
                    ChainMeta.MANUAL_SELECTS_RESULT, {}
                )
                manual_select_result = all_manual_selects_result.get(
                    self.manual_select_tool.manual_select_id, {}
                )["data"]
                if not manual_select_result:
                    raise NeedManualSelect(self.manual_select_tool.manual_selects)
                model_id, query_metric_json = next(iter(manual_select_result.items()))
                lookup_data_after_manual_select(
                    target=self.target,
                    do_query=self.do_query,
                    model_type=model_type,
                    model_id=model_id,
                    query_metric_json=query_metric_json,
                    config=config,
                    query_mom_yoy=self.query_mom_yoy,
                )
            else:
                result = lookup_data(
                    self.target,
                    query,
                    self.do_query,
                    model_type,
                    self.extra_additional_info,
                    config,
                    metric_ner,
                    where_ner,
                    query_mom_yoy=self.query_mom_yoy,
                )
                if result["need_manual_select"]:
                    self.manual_select_tool.need_manual_select = True
                    self.manual_select_tool.manual_selects = {
                        self.manual_select_tool.manual_select_id: result[
                            "manual_selects"
                        ]
                    }
                    raise NeedManualSelect(self.manual_select_tool.manual_selects)
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_steps_list_tuple = (
                self.target.param_key,
                [
                    ProgressRunStatus.NEED_MANUAL_SELECT
                    if isinstance(e, NeedManualSelect)
                    else ProgressRunStatus.FAILED,
                    e,
                ],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
                config=config,
            )
            raise e

        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        nl2agent_steps_list_tuple = (
            self.target.param_key,
            [ProgressRunStatus.SUCCEED, self.target],
        )
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            config=config,
            nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
        )
        return self.target

    def verify_degree(self, degree):
        return degree == 0

    def table_description(self, kwargs):
        return kwargs["query"]

    # def verify_result(self, target: Node):
    #     if target.meta.get("not_need_bi_result", False):
    #         return True, None
    #     return super().verify_result(target)


class FastLookupDataInput(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="fast_lookup")
class FastLookupTool(BaseTool):
    name: str = "fast_lookup"
    description: str = "快速查数工具。能力范围：筛选、排序、最值、topN、bottomN、维度码值查询"
    args_schema: Type[BaseModel] = FastLookupDataInput
    do_query: bool = True
    # 这里如果直接manual_select_tool = ManualSelectTool()
    # 那ManualSelectTool只会在import的时候初始化一次
    manual_select_tool: ManualSelectTool = Field(default_factory=ManualSelectTool)
    extra_additional_info: Dict = {}

    def _run(
        self,
        query,
        config: RunnableConfig,
    ):
        self.manual_fill_params({"query": query})
        model_type = config[CHAIN_META].get(
            ChainMeta.LOOKUP_DATA_MODEL_TYPE, config[CHAIN_META][ChainMeta.MODEL_TYPE]
        )
        if self.manual_select_tool.need_manual_select:
            all_manual_selects_result = config[CHAIN_META].get(
                ChainMeta.MANUAL_SELECTS_RESULT, {}
            )
            manual_select_result = all_manual_selects_result.get(
                self.manual_select_tool.manual_select_id, {}
            ).get("data", None)
            if not manual_select_result:
                raise NeedManualSelect(self.manual_select_tool.manual_selects)
            model_id, query_metric_json = next(iter(manual_select_result.items()))
            lookup_data_after_manual_select(
                target=self.target,
                do_query=self.do_query,
                model_type=model_type,
                model_id=model_id,
                query_metric_json=query_metric_json,
                config=config,
            )
        else:
            result = lookup_data(
                self.target,
                query,
                self.do_query,
                model_type,
                self.extra_additional_info,
                config,
            )
            if result["need_manual_select"]:
                self.manual_select_tool.need_manual_select = True
                self.manual_select_tool.manual_selects = {
                    self.manual_select_tool.manual_select_id: result["manual_selects"]
                }
                raise NeedManualSelect(self.manual_select_tool.manual_selects)
        return self.target

    def verify_degree(self, degree):
        return degree == 0

    def table_description(self, kwargs):
        return kwargs["query"]
