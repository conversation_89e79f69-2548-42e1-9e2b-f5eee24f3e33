import re
from pandas import DataFrame


# str：
# "std(`日交易绝对量`)"，表示对 DataFrame 中列 `日交易绝对量` 应用标准差函数
# "`日交易绝对量` * 2 * 3", 表示对 DataFrame 中列 `日交易绝对量` 进行乘法运算，并返回结果
def eval_df_column_expression(df: DataFrame, expression: str):
    expression = re.sub(r"`(.+?)`", r'df["\1"]', expression)

    # 将 DSL 函数 std(...) 替换为 pandas Series 方法，如 std(df["列名"]) -> df["列名"].std()
    expression = re.sub(r'std\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].std()', expression)
    expression = re.sub(
        r'mean\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].mean()', expression
    )
    expression = re.sub(r'sum\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].sum()', expression)
    expression = re.sub(r'max\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].max()', expression)
    expression = re.sub(r'min\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].min()', expression)
    expression = re.sub(
        r'median\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].median()', expression
    )
    expression = re.sub(r'var\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].var()', expression)
    expression = re.sub(
        r'count\s*\(\s*df\["(.+?)"\]\s*\)', r'df["\1"].count()', expression
    )

    result = eval(expression, {"df": df})
    return result
