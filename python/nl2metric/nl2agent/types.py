import copy

from enum import Enum
from pydantic import BaseModel
from typing import List, Dict, Optional, Union

from metastore.base import Metric, Dimension
from nl2metric.query_where_impl import SubWhereLink


class NodeType(Enum):
    table = "table"
    text = "text"
    pict = "picture"


class ToolType(Enum):
    lookup_data = "lookup_data"
    select = "select"
    filter = "filter"
    join = "join"
    order = "order"

    auto_join = "auto_join"
    calculate = "calculator"
    time_range = "time_range"
    predict = "time_series_service"
    table_tools = "table_tools"

    chat = "chat"

    # agent
    intent_bi_agent = "intent_bi_agent"

    @classmethod
    def report_values(cls):
        # report in steps; report in agent_delta_builds
        return {ToolType.table_tools.value}, {
            item.value for item in cls if item != ToolType.table_tools
        }


class ColumnType(Enum):
    COLUMN = "COLUMN"
    CALCULATE = "CALCULATE"
    COUNT = "COUNT"
    COUNT_DISTINCT = "COUNT_DISTINCT"
    SUM = "SUM"
    AVG = "AVG"
    MAX = "MAX"
    MIN = "MIN"

    PREDICT = "PREDICT"
    STDDEV = "STDDEV"

    def can_select(self):
        return self != ColumnType.PREDICT

    def is_agg(self):
        return self in {
            ColumnType.COUNT,
            ColumnType.COUNT_DISTINCT,
            ColumnType.SUM,
            ColumnType.AVG,
            ColumnType.MAX,
            ColumnType.MIN,
        }

    def df_agg_type(self):
        return {
            ColumnType.COLUMN: "first",
            ColumnType.COUNT: "count",
            ColumnType.COUNT_DISTINCT: "nunique",
            ColumnType.SUM: "sum",
            ColumnType.AVG: "mean",
            ColumnType.MAX: "max",
            ColumnType.MIN: "min",
        }[self]


class CalculatedColumn(BaseModel):
    description: str
    expression_type: ColumnType
    expression: Optional[str] = None


class UnknownColumn(BaseModel):
    original_name: str
    description: str = ""


class TableColumnMeta(Enum):
    TMP_COLUMN = "tmp_column"
    ORIGINAL_PARAM_KEY = "original_param_key"
    TIME_DIMENSION_FORMAT = "time_dimension_format"


class TableColumnType(Enum):
    metric = "metric"
    dimension = "dimension"
    calculate = "calculate"
    unknown = "unknown"


class TableColumn(BaseModel):
    column_name: str
    column_type: TableColumnType
    column_detail: Union[Metric, Dimension, CalculatedColumn, UnknownColumn]
    meta: Dict = {}

    def __init__(self, column_name, column_detail, meta):
        if isinstance(column_detail, Metric):
            column_type = TableColumnType.metric
        elif isinstance(column_detail, Dimension):
            column_type = TableColumnType.dimension
        elif isinstance(column_detail, CalculatedColumn):
            column_type = TableColumnType.calculate
        elif isinstance(column_detail, UnknownColumn):
            column_type = TableColumnType.unknown
        else:
            raise RuntimeError("invalid column_detail")
        return super().__init__(
            column_name=column_name,
            column_type=column_type,
            column_detail=column_detail,
            meta=meta,
        )

    def copy(self, column_name=None):
        if column_name is None:
            column_name = self.column_name
        return TableColumn(
            column_name=column_name,
            column_detail=self.column_detail,
            meta=copy.copy(self.meta),
        )

    @property
    def is_metric(self):
        return self.column_type == TableColumnType.metric

    @property
    def is_calculable(self):
        return (
            self.column_type == TableColumnType.metric
            or self.column_type == TableColumnType.calculate
        )

    @property
    def is_dimension(self):
        return self.column_type == TableColumnType.dimension

    @property
    def is_time_dimension(self):
        return (
            self.column_type == TableColumnType.dimension
            and self.column_detail.is_time()
        ) or (
            self.column_type == TableColumnType.unknown
            and self.column_detail.original_name == "V_DATE_"
        )

    @property
    def is_calculated(self):
        return self.column_type == TableColumnType.calculate

    @property
    def is_unknown(self):
        return self.column_type == TableColumnType.unknown

    def to_json(self):
        result = {
            "column_name": self.column_name,
            "column_type": self.column_type.value,
            TableColumnMeta.ORIGINAL_PARAM_KEY.value: self.meta.get(
                TableColumnMeta.ORIGINAL_PARAM_KEY, None
            ),
        }
        if isinstance(self.column_detail, Metric):
            result["metric_name"] = self.column_detail.name
        elif isinstance(self.column_detail, Dimension):
            result["dimension_name"] = self.column_detail.name
        elif isinstance(self.column_detail, CalculatedColumn):
            result["expression_type"] = self.column_detail.expression_type.value
            result["expression"] = self.column_detail.expression
        elif isinstance(self.column_detail, UnknownColumn):
            result["original_name"] = self.column_detail.original_name
        else:
            raise RuntimeError("invalid column_detail")

        return result


# select, calculate and groupby


class SelectItem(BaseModel):
    name: str
    expression_type: ColumnType
    expression: str

    def is_agg(self):
        return self.expression_type.is_agg()

    @property
    def df_agg_type(self):
        return self.expression_type.df_agg_type()


# join


class JoinType(Enum):
    INNER_JOIN = "inner_join"
    LEFT_JOIN = "left_join"
    RIGHT_JOIN = "right_join"
    FULL_OUTER_JOIN = "full_join"
    CROSS_JOIN = "cross_join"


# orderby


class OrderByType(Enum):
    ASC = "ASC"
    DESC = "DESC"
