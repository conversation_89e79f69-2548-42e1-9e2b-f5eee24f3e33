import re
from typing import Union, Dict

import pandas as pd
import requests
from langchain_core.messages import ChatMessage, HumanMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import <PERSON>nableLambda

from common.db_model.model import get_scenes_model_by_scene_id, ScenesModel
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.types.base import (
    gen_chain_meta,
    JobType,
    ChainMeta,
    ChainRuntime,
    CHAIN_META,
)
from common.utils.concurrent_utils import synchronized_lru_cache
from common.utils.llm_utils import create_llm_model_by_project_config
from config import app_config
from config.doc_config import xengine_backend_addr
from config.project_config import get_project_config
from metastore.base import Model
from metastore.service import get_db_appstore
from pre_filter.retrive import call_retrive

from pydantic import BaseModel, Field
from typing import List, Optional, Any
from metastore.base import Dimension, Metric
from tools.sql_executor import MysqlClient

logger = get_logger(__name__)


class Column(BaseModel):
    name: str
    columnType: str = Field(alias="columnType")  # 数据库列类型，如 VARCHAR, INTEGER
    columnPrecision: int = Field(default=0)  # 精度，默认值 0
    columnScale: int = Field(default=0)  # 绝对值，默认值 0
    comment: Optional[str] = Field(default="")  # 注释，默认为空字符串
    not_allow_null: bool = Field(default=False)  # 是否允许为空，默认 False
    displayList: Optional[List[Any]] = Field(default=None)  # 显示列表，可选
    customSql: Optional[str] = Field(default=None)  # 自定义SQL，可选

    def __str__(self):
        """返回列的字符串表示，用于Schema输出"""
        base_str = f"{self.name} ({self.columnType}"
        if self.columnPrecision or self.columnScale:
            base_str += (
                f", Precision: {self.columnPrecision}, Scale: {self.columnScale}"
            )
        base_str += ")"
        if self.comment:
            base_str += f" - {self.comment}"
        if self.not_allow_null:
            base_str += " NOT NULL"
        return base_str


# Assuming Column, Dimension, and Metric are defined elsewhere with appropriate __str__ methods
# For this example, they are referenced as placeholders


class SchemaDefinition(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    table_name: str
    columns: List[Column]
    dimensions: List[Dimension]
    metrics: List[Metric]
    example_data: pd.DataFrame
    time_column_desc: Dict

    def generate_prompt(self, query: str) -> str:
        """
        生成用于将自然语言查询翻译成SQL的Prompt。

        :param query: 自然语言查询
        :return: 格式化后的Prompt字符串
        """
        # 生成Schema部分
        schema_parts = []
        schema_parts.append(f"**Database Schema:**")
        schema_parts.append(f"- Table: {self.table_name}")
        schema_parts.append(f"  - Columns:")
        # 添加列信息和示例值
        for col in self.columns:
            # 从 example_data 中提取示例值
            if col.name not in self.example_data.columns:
                continue
            col_info = f"    - {col.name} ({col.columnType})"
            if col.comment:
                col_info += f": {col.comment}"
            # 获取前几个非空唯一值作为示例
            example_values = self.example_data[col.name].dropna().unique()
            # 为了避免Prompt过长，我们只取前几个示例
            num_examples = 5
            if len(example_values) > num_examples:
                example_values = example_values[:num_examples]
                col_info += f" (Examples: {', '.join(map(str, example_values))}...)"
            elif len(example_values) > 0:
                col_info += f" (Examples: {', '.join(map(str, example_values))})"
            schema_parts.append(col_info)
        schema_str = "\n".join(schema_parts)

        # 生成指标部分
        metrics_str = (
            "BI 指标大多是通过运算表达式计算和加工原始数据字段得出的结果，而非直接的数据库原始值。\n"
            "核心思想：BI指标 = 原始数据 + 运算表达式 → 计算/加工结果,注意指标不是字段，选取的不要捏造字段"
            "\n**Metrics（指标：）:**\n"
        )
        for metric in self.metrics:
            metrics_str += f"- {metric.name}: {str(metric).strip()}\n"

        # 生成维度部分
        dimensions_str = "**Dimensions:**\n"
        for dim in self.dimensions:
            dimensions_str += f"- {dim.name}: {str(dim).strip()}\n"

        # 生成维度码值部分
        dimension_codes_str = "**Dimension Codes:**\n"
        for dim in self.dimensions:
            if dim.distinct_values:
                dimension_codes_str += f"- {dim.name}:\n"
                for _, val in dim.distinct_values.items():
                    dimension_codes_str += f"  - {val}\n"
            else:
                dimension_codes_str += f"- {dim.name}: No distinct values provided.\n"
        # 添加时间列描述
        time_column_desc_str = f"**Time Column Descriptions:**\n"
        if self.time_column_desc:
            time_column_desc_str += f"- column:\n"
            for key, value in self.time_column_desc["column"].items():
                time_column_desc_str += f"  - {key}: {value}\n"
            if "granularity" in self.time_column_desc:
                time_column_desc_str += (
                    f"- granularity: {self.time_column_desc['granularity']}\n"
                )
            if "formatPattern" in self.time_column_desc:
                time_column_desc_str += (
                    f"- formatPattern: {self.time_column_desc['formatPattern']}\n"
                )
            if "partition" in self.time_column_desc:
                time_column_desc_str += (
                    f"- partition: {self.time_column_desc['partition']}\n"
                )
        else:
            time_column_desc_str += "No time column description provided.\n"

        # 拼接Schema、指标、维度和维度码值
        schema_info = (
            schema_str
            + "\n"
            + metrics_str
            + "\n"
            + dimensions_str
            + "\n"
            + dimension_codes_str
            + "\n"
            + time_column_desc_str
        )

        # Prompt模板
        prompt_template = f"""
你是一个AI助手，专门负责将自然语言查询转化为SQL语句。
你的任务是根据你提供的数据库Schema、指标、维度和维度码值，生成用于特定数据库引擎XEngine的SQL查询。XEngine兼容MySQL方言，但有一个重要要求：查询时必须指定catalog。
因此，请为我生成准确、符合MySQL方言、能够正确检索所需数据，你的SQL里使用的table要使用完整的名称，格式为catalog.database.table，例如：bird_test.bird_car_retails.vt_car_retails_wide ，里面已经包含catalog信息的，以确保SQL能在XEngine环境中正常执行
{schema_info}

在翻译SQL之前，请先分析自然语言查询，识别其中的指标、维度和过滤条件。请按以下步骤进行思考：
1. 识别查询中的指标（如销售额、利润）。
2. 识别查询中的维度（如时间、部门）。
3. 识别查询中的过滤条件（如2020年、特定产品）。
4. 确定查询的聚合方式（如SUM、AVG）和排序需求（如DESC、ASC）。

**翻译要求：**
- 当前时间使用 current_date 表示，不支持 CURDATE()。
- 支持的函数：current_date。
- 不支持的函数：CURDATE/IFNULL
- 翻译的 SQL 查询必须避免使用不支持的函数。
- 当前查询问题与指标维度都不配, 可以根据表的schema来确定。

**自然语言查询：**
{query}

**SQL查询：**
请将SQL查询包装在```sql```代码块中，例如：
```sql
SELECT column1, column2 FROM table_name WHERE condition;
```
指标匹配时，优先走指标， 指标不匹配时，基于table schema去推导
如果无法将自然语言查询翻译成SQL，请说明原因，并指出查询中哪些部分不明确或需要进一步澄清。

**无法翻译格式**：要求在无法翻译时使用“[无法翻译] 原因：{{具体原因}}”，并给出示例以确保格式一致。


**示例：**
如果自然语言查询是“显示2020年销售额最高的部门”，则SQL查询应为：
```sql
SELECT department, SUM(sales) FROM sales_table WHERE year = 2020 GROUP BY department ORDER BY SUM(sales) DESC LIMIT 1;
```
示例2（无法翻译）：

自然语言查询：“显示销售额最高的部门”

返回：
[无法翻译] 原因：缺少年份信息，无法确定具体时间范围。
"""
        return prompt_template.strip()


class ModelConfig:
    def __init__(self):
        self.backend_addr = xengine_backend_addr
        self.meta_model_api = "/api/engine/v1/metricmodel/meta"
        self.default_model_type = app_config.MODEL_TYPE_DEEPSEEK_14B


class ModelMetaFetcher:
    def __init__(self, config: ModelConfig):
        self.config = config

    @synchronized_lru_cache(maxsize=64)
    def get_model_meta(self, model_name: str) -> dict:
        url = f"{self.config.backend_addr}{self.config.meta_model_api}"
        response = requests.get(
            url, params={"name": model_name}, headers={"Authorization": "init"}
        )
        response.raise_for_status()
        resp = response.json()
        return resp


class NL2SQLConverter:
    def __init__(
        self, config: ModelConfig, db_appstore, meta_fetcher: ModelMetaFetcher
    ):
        self.config = config
        self.db_appstore = db_appstore
        self.meta_fetcher = meta_fetcher
        self.db_client = MysqlClient.get_instance_by_params(
            host=app_config.xengine_backend_host,
            port=app_config.xengine_backend_port,
            user=app_config.xengine_username,
            passwd=app_config.xengine_password,
            database=app_config.xengine_database,
        )

    def _setup_prompt_selector(
        self, model, model_type: str, semantic_scenes_id: str
    ) -> "MemPromptSelector":
        return MemPromptSelector(
            project_name=model.semantic_project_name,
            project_id=model.semantic_project_id,
            model_name=model.table_name,
            model_type=model_type,
            model_id=semantic_scenes_id,
        )

    def _create_chain_metadata(
        self,
        model,
        query: str,
        model_type: str,
        semantic_scenes_id: str,
        prompt_selector,
    ) -> dict:
        metadata = gen_chain_meta(
            job_type=JobType.PARAMS_EXTRACT,
            project_name=model.semantic_project_name,
            project_id=model.semantic_project_id,
            model_name=model.table_name,
            model_label=model.label,
            model_id=semantic_scenes_id,
            model_type=model_type,
            prompt_selector=prompt_selector,
        )
        metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = query
        return metadata

    def generate_prompt(
        self,
        question: Union[str, Dict],
        semantic_scenes_id: str,
        table_name: str,
        config: Dict,
    ) -> list[ChatMessage]:
        metrics = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS]
        dimensions = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS]

        scene_model: List[ScenesModel] = get_scenes_model_by_scene_id(
            semantic_scenes_id
        )
        model_meta = self.meta_fetcher.get_model_meta(scene_model[0].model_name)
        columns: List[Column] = [
            Column(**col) for col in model_meta.get("data", {})["columns"]
        ]
        time_column_desc = (
            model_meta.get("data", {})
            .get("dataModelDesc", {})
            .get("timeColumnDesc", {})
        )
        logger.info(f"time_column_desc:{time_column_desc}")
        cols, dataset = self.db_client.query(f"select * from {table_name} limit 3")
        schema = SchemaDefinition(
            table_name=table_name,
            columns=columns,
            dimensions=dimensions,
            metrics=metrics,
            example_data=pd.DataFrame(dataset, columns=cols),
            time_column_desc=time_column_desc or {},
        )
        if type(question) == dict:
            question = question["question"]
        return [HumanMessage(role="user", content=schema.generate_prompt(question))]

    def gen_chain(self, query: str, semantic_scenes_id: str, model_type: str, config):
        model: Model = self.db_appstore.get_model_by_id(semantic_scenes_id)
        prompt_selector = self._setup_prompt_selector(
            model, model_type, semantic_scenes_id
        )
        chain_metadata = self._create_chain_metadata(
            model, query, model_type, semantic_scenes_id, prompt_selector
        )

        config[CHAIN_META] = chain_metadata
        project_config = get_project_config(
            project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
            # model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            model_name=model.table_name,
        )
        # First chain to get metrics and dimensions
        chain = (
            call_retrive()
            | RunnableLambda(self.generate_prompt).bind(
                semantic_scenes_id=semantic_scenes_id, table_name=model.table_name
            )
            | create_llm_model_by_project_config(model_type, project_config)
            | StrOutputParser()
        )

        return chain


# Usage
def create_nl2sql_converter():
    config = ModelConfig()
    meta_fetcher = ModelMetaFetcher(config)
    converter = NL2SQLConverter(config, get_db_appstore(), meta_fetcher)
    return converter


def extract_sql(response):
    """
    从AI助手的响应中提取SQL查询。

    :param response: AI助手的响应文本
    :return: 提取的SQL查询字符串，如果未找到或无法翻译则返回None
    """
    # 尝试提取```sql```代码块中的SQL查询
    sql_block_pattern = r"```sql\n(.*?)\n```"
    match = re.search(sql_block_pattern, response, re.DOTALL)
    if match:
        return match.group(1).strip()

    # 检查是否包含“[无法翻译]”的标记
    if "[无法翻译]" in response:
        return None

    # 如果没有找到SQL代码块且没有“[无法翻译]”，返回None
    return None


def nl2sql_str(query, model_type, semantic_scenes_id, config=None) -> str:
    if config is None:
        config = dict()
    converter = create_nl2sql_converter()
    chain = (
        converter.gen_chain(
            query=query,
            model_type=model_type,
            semantic_scenes_id=semantic_scenes_id,
            config=config,
        )
        | extract_sql
    )
    result = chain.invoke(query, config=config)
    return result
