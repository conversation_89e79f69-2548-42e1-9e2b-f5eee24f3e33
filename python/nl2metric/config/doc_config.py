import logging
import os
import socket
from distutils.util import strtobool
from enum import Enum

from config.app_config import M<PERSON>EL_TYPE_YI_34B_16K

ask_doc_ip = os.environ.get("env_ip_port", "0.0.0.0")
ask_doc_port = os.environ.get("ask_doc_port", "8099")
file_sever_address = os.environ.get("file_server_address", "127.0.0.1:8099")

ask_doc_dir = os.environ.get("ask_doc_dir", "./ask_doc")
assert len(ask_doc_dir) > 0
if ask_doc_dir[-1] != "/":
    ask_doc_dir += "/"

ask_doc_similarity_top_k = int(os.environ.get("ask_doc_similarity_top_k", "50"))
assert ask_doc_similarity_top_k > 0

ask_doc_similarity_threshold = float(
    os.environ.get("ask_doc_similarity_threshold", "0.01")
)

ask_doc_suggestion_num = int(os.environ.get("ask_doc_suggestion_num", "3"))
ask_doc_chat_suggestion_num = int(os.environ.get("ask_doc_chat_suggestion_num", "0"))
ask_doc_rank_model = os.environ.get("ask_doc_rank_model", "Customer")
ask_doc_mng_worker_num = int(os.environ.get("ask_doc_mng_worker_num", "4"))
ask_doc_mng_check_worker_num = int(os.environ.get("ask_doc_mng_check_worker_num", "4"))
assert ask_doc_mng_worker_num > 0
assert ask_doc_mng_check_worker_num > 0
ask_doc_max_embedding_wait_time_sec = int(
    os.environ.get("ask_doc_max_embedding_wait_time", "5")
)

ask_doc_use_ocr = eval(os.environ.get("ask_doc_use_ocr", "True"))
assert ask_doc_use_ocr is True or ask_doc_use_ocr is False

ask_doc_llm_context_windows = int(os.environ.get("ask_doc_llm_context_windows", "2048"))

ask_doc_index_temp_file_dir = os.environ.get("ask_doc_index_temp_file_dir", None)
ask_doc_index_doc_table_split = strtobool(
    os.environ.get("ask_doc_index_doc_table_split", "True")
)  # false 不拆分doc内的表 true (按内容token长度)拆分doc内的表
ask_doc_index_doc_table_row_split = strtobool(
    os.environ.get("ask_doc_index_doc_table_row_split", "False")
)  # false 不按行拆分doc内的表 true 按行拆分doc内的表
ask_doc_index_excel_split = strtobool(
    os.environ.get("ask_doc_index_excel_split", "False")
)  # false 不拆分 一个sheet作为一个node  true 按行拆分
ask_doc_merge_sub_chapters = strtobool(
    os.environ.get("ask_doc_merge_sub_chapters", "False")
)  # false 不合并小章节 按最细的粒度切分chunk  true 合并小章节 按最大的chunk_size切分chunk

ask_doc_doc_parse_libreoffice = strtobool(
    os.environ.get("ask_doc_doc_parse_libreoffice", "False")
)  # docx文档解析是否使用libreoffice把docx转成pdf用于获取页码

layoutlm_model_path = os.environ.get(
    "layoutlm_model_path",
    "/resources/nl2document_resources/ask_doc_models/layoutlm_model_path/model_final.pth",
)

hugging_face_embedding_model_path = os.environ.get(
    "hugging_face_embedding_model_path", "shibing624/text2vec-base-chinese-paraphrase"
)
embedding_model_path = os.environ.get(
    "embedding_model_path",
    "Pristinenlp/alime-embedding-large-zh",
)
rank_model_script_path = os.environ.get(
    "rank_model_script_path", "/data/public_file/ranking_script_model.pt"
)
rank_model_config_path = os.environ.get(
    "rank_model_config_path", "/data/public_file/LLM_model/chinese-roberta-wwm-ext"
)
rank_model_path = os.environ.get(
    "rank_model_path", "/data2/public_file/LLM_model/BGE_model/bge_reranker_model_v2/"
)
rank_model_device = os.environ.get("rank_model_device", "cpu")
rank_model_work_num = int(os.environ.get("rank_model_work_num", "4"))

rank_topk = int(os.environ.get("rank_topk", "5"))

chatglm_model_path = os.environ.get(
    "chatglm_model_path", "/data/public_file/LLM_model/ChatGLM-6B_industry_bg_1108"
)

LOGGING_LEVEL_MAP = {
    "CRITICAL": logging.CRITICAL,
    "FATAL": logging.FATAL,
    "ERROR": logging.ERROR,
    "WARN": logging.WARNING,
    "WARNING": logging.WARNING,
    "INFO": logging.INFO,
    "DEBUG": logging.DEBUG,
}
ask_doc_log_level = LOGGING_LEVEL_MAP[
    os.environ.get("ask_doc_log_level", "INFO").upper()
]
ask_doc_use_folder_index = eval(os.environ.get("use_folder_index", "True"))
ask_doc_use_mysql_engine = bool(os.environ.get("user_mysql_engine", "True"))

ASK_DOC_S3_BUCKET_NAME = os.environ.get("s3_bucket_name", "ask-doc")
ASK_DOC_BUSINESS_NAME = os.environ.get("business_name", "dianxin")

ASK_DOC_OCR_PATH = os.environ.get(
    "ASK_DOC_OCR_PATH", "/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR/"
)
ASK_DOC_OCR_INFERENCE_PATH = os.environ.get(
    "ASK_DOC_OCR_INFERENCE_PATH",
    "/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR_inference/",
)

ASK_DOC_ANALYSIS_LLM_ADDRESS = os.environ.get(
    "ask_doc_analysis_llm_address", "http://*************:8085/v1"
)

ASK_DOC_ANALYSIS_LLM_MODEL = os.environ.get(
    "ask_doc_analysis_llm_model", "/model/Qwen1___5-14B-Chat"
)

llm_predictor_model = os.environ.get("llm_predictor_model", MODEL_TYPE_YI_34B_16K)


DB_HOST = os.environ.get("doc_db_host", "**************")
DB_PORT = os.environ.get("doc_db_port", "3306")
DB_USER = os.environ.get("doc_db_user", "root")
DB_PASSWORD = os.environ.get("doc_db_password", "123")
DB_NAME = os.environ.get("doc_db_name", "askdoc_test")
auto_refresh = True

milvus_uri = os.environ.get("milvus_uri", "http://**************:31870")
milvus_dim = int(os.environ.get("dim", 1024))
milvus_collection_name = os.environ.get("collection_name", "AskDoc")
milvus_meta_collection_name = os.environ.get(
    "milvus_meta_collection_name", "AskDocMeta"
)

vector_store_type = os.environ.get("vector_store_type", "milvus")


class DeployMode(str, Enum):
    cmcc = "cmcc"
    common = "common"
    bj_telecom = "bj_telecom"


deployMode = os.environ.get("deployMode", DeployMode.cmcc)
cmcc_access_key = os.environ.get("cmcc_access_key", "8A5196E038F249E2BB62D340569A2F86")
cmcc_access_secret = os.environ.get(
    "cmcc_access_secret", "DCE80B1AE75D4F27A6BED719586298DC"
)
cmcc_ec_id = os.environ.get("cmcc_ec_id", "1007542122103963648")
cmcc_get_file_url = os.environ.get("cmcc_get_file_url", "http://**************:8080")

eb_app_id = os.environ.get("eb_app_id", "1856220483713114112")
eb_app_key = os.environ.get("eb_app_key", "7ac8dcf737424b94b9797cd9934caaf1")
eb_get_file_url = os.environ.get(
    "eb_get_file_url", "https://meetingdev-hw.dashipin.cn:8443"
)


rerank_api_base = os.environ.get("rerank_api_base", "http://**************:9997")
rerank_model_name = os.environ.get("rerank_model_name", "alime-reranker-large-zh")
rerank_rate_limit = int(os.environ.get("rerank_rate_limit", 30))

doc_builder_loop_gap = int(os.environ.get("doc_builder_loop_gap", "10"))
xengine_backend_addr = os.environ.get(
    "xengine_backend_addr", "http://**************:30323"
)

default_s3_local_dir = os.environ.get("s3_local_dir", "./ask_doc_s3/")
default_access_key = os.environ.get("s3_access_key", "pxpxMQIu2972J3GGgBlK")
default_secret_key = os.environ.get(
    "s3_secret_key", "09DvvwWeKaNBHV3ox0E8SN4yRiOcbC6HolqWi5wh"
)
default_region_name = os.environ.get("s3_region_name", "us-east-1")
default_endpoint_url = os.environ.get("s3_endpoint_url", "http://**************:9000")
default_bucket_name = os.environ.get("s3_bucket_name", "ask-bi")
default_local_dir = os.environ.get("local_dir", "resources")
default_s3_folder = os.environ.get("s3_folder", "ask-metric/resources")
S3_ETAGS_FILE = os.environ.get("s3_etags_file", ".local_etags.json")

report_generate_llm_address = os.environ.get(
    "report_generate_llm_address", "http://**************:8085"
)
report_generate_llm_model = os.environ.get(
    "report_generate_llm_model", "/model/Qwen1___5-14B-Chat"
)
report_fetch_data_in_batch = eval(os.environ.get("report_fetch_data_in_batch", "False"))
report_fetch_data_in_batch_size = int(
    os.environ.get("report_fetch_data_in_batch_size", "200000")
)
report_default_model_name = os.environ.get("report_default_model_name", "")

ENABLE_FEISHU_ALERT = eval(os.environ.get("ENABLE_FEISHU_ALERT", "False"))
FEISHU_ALERT_WEBHOOK_URL = os.environ.get("FEISHU_ALERT_WEBHOOK_URL", "")

HOST_NAME = os.environ.get("HOST_NAME", "")

# 电信BPM
BPM_FTP_FILE_SYSTEM_URL = os.environ.get("BPM_FTP_FILE_SYSTEM_URL", "**************")
BPM_FTP_FILE_SYSTEM_USER = os.environ.get("BPM_FTP_FILE_SYSTEM_USER", "yuanyuan")
BPM_FTP_FILE_SYSTEM_PASSWORD = os.environ.get(
    "BPM_FTP_FILE_SYSTEM_PASSWORD", "yuanyuan"
)
BPM_FTP_FILE_SYSTEM_PREFIX_DIR = os.environ.get(
    "BPM_FTP_FILE_SYSTEM_PREFIX_DIR", "./nl2document/bpm/bpm_test/"
)

BPM_REDIS_HOST = os.environ.get("BPM_REDIS_HOST", "**************")
save_middle_data = strtobool(os.environ.get("save_middle_data", "False"))
BPM_USE_FTP = strtobool(os.environ.get("BPM_USE_FTP", "False"))
BPM_TASK_RESULT_CALLBACK_URL = os.environ.get(
    "BPM_TASK_RESULT_CALLBACK_URL", "http://**************:50010/bpm/callback"
)
BPM_FILE_DOWNLOAD_PREFIX = os.environ.get(
    "BPM_FILE_DOWNLOAD_PREFIX", "http://**************:8080/download"
)

bpm_llm_rate_limit = int(os.environ.get("bpm_llm_rate_limit", 3))

QUERY_MEETING_LLM_CONTEXT_WINDOW = int(
    os.environ.get("QUERY_MEETING_LLM_CONTEXT_WINDOW", "8000")
)

BJ_TELECOM_FILE_DOWNLOAD_PREFIX = os.environ.get(
    "BJ_TELECOM_FILE_DOWNLOAD_PREFIX", "http://**************:8080/download"
)
BJ_TELECOM_INTERNAL_MOCK = eval(os.environ.get("BJ_TELECOM_INTERNAL_MOCK", "False"))

enable_trace = eval(os.environ.get("enable_trace", "True"))
enable_meta_retriever = eval(os.environ.get("enable_meta_retriever", "True"))
enable_build_meta_indexes = eval(os.environ.get("enable_build_meta_indexes", "True"))
max_delete_batch_size = int(os.environ.get("max_delete_batch_size", "1000"))

doc_embedding_model = os.environ.get("doc_embedding_model", "embedding_api")
doc_query_document_context_window = os.environ.get(
    "doc_query_document_context_window", 4000
)
HELP_USER_ID = os.environ.get("HELP_USER_ID", "FFFF-FFFF-FFFF-FFFF")

llama_verbose = eval(os.environ.get("llama_verbose", "False"))

ppt_hidden_dimension_values = os.environ.get("ppt_hidden_dimension_values", "其他部门")
