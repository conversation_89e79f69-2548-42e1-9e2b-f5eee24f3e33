import copy
import os

import yaml
from pathlib import Path
from typing import Dict, Any, List

from common.logging.logger import get_logger
from common.stopwords.base import dipeak_stopwords, dipeak_single_stopwords
from common.types.base import ParamsExtractStage
from config.app_config import MODEL_TYPE_PRE_GLM_4_9B_AGENT, MODEL_TYPE_PRE_GLM_4_9B
from cache_updater.cached import cached
from nl2metric.few_shots import (
    FENGHUO_PROJECT_NAME,
    BAOWU_PROJECT_NAME,
    TIANHONG_PROJECT_NAME,
    JINGFEN_BI,
    CHINA_LIFE_NAME,
    LINGSHI_NAME,
    SCIENCE_CITY_NAME,
    BANK_PROJECT,
    DEFAULT_PROJECT_NAME,
    HUAXIA_PROJECT_NAME,
    SHILI_PROJECT_NAME,
    SHENZHEN_BASHI_PROJECT_NAME,
    BEIJIAOSUO_PROJECT_NAME,
    BEIYINLICAI_PROJECT_NAME,
)
from nl2intent.types import Intent, MetaIntentType

logger = get_logger(__name__)


class EnvVarLoader(yaml.SafeLoader):
    """
    一个自定义的 YAML 加载器，支持从环境变量中取值。
    - `!ENV VAR_NAME:default_value`: 获取字符串环境变量，如果未设置则使用默认值。
    - `!ENV_INT VAR_NAME:default_value`: 获取整数环境变量，如果未设置则使用默认值并转换为整数。
    """

    def construct_env_var(self, node):
        """
        处理 !ENV 标签：获取字符串类型环境变量。
        """
        value = self.construct_scalar(node)  # 获取标签后面的值，例如 "DB_HOST:localhost"
        var_name = value
        default_value = None

        if ":" in value:
            parts = value.split(":", 1)
            var_name = parts[0].strip()
            default_value = parts[1].strip()

        env_value = os.environ.get(var_name)

        if env_value is None:
            if default_value is not None:
                return default_value
            else:
                raise ValueError(
                    f"Configuration Error: Environment variable '{var_name}' not set and no default provided."
                )
        return env_value

    def construct_env_int(self, node):
        """
        处理 !ENV_INT 标签：获取整数类型环境变量。
        """
        value = self.construct_scalar(node)
        var_name = value
        default_value = None

        if ":" in value:
            parts = value.split(":", 1)
            var_name = parts[0].strip()
            default_value = parts[1].strip()

        env_value = os.environ.get(var_name)

        if env_value is None:
            if default_value is not None:
                try:
                    return int(default_value)
                except ValueError:
                    raise ValueError(
                        f"Configuration Error: Default value '{default_value}' for '{var_name}' is not a valid integer."
                    )
            else:
                raise ValueError(
                    f"Configuration Error: Environment variable '{var_name}' not set and no default provided."
                )
        try:
            return int(env_value)
        except ValueError:
            raise ValueError(
                f"Configuration Error: Environment variable '{var_name}' value '{env_value}' is not a valid integer."
            )


# 将自定义构造器添加到 EnvVarLoader
EnvVarLoader.add_constructor("!ENV", EnvVarLoader.construct_env_var)
EnvVarLoader.add_constructor("!ENV_INT", EnvVarLoader.construct_env_int)

DEFAULT_MODEL = "default"

PROJECT_CONFIG_FILES = {
    FENGHUO_PROJECT_NAME: "fenghuo_config.yaml",
    BAOWU_PROJECT_NAME: "baowu_config.yaml",
    TIANHONG_PROJECT_NAME: "tianhong_config.yaml",
    JINGFEN_BI: "dianxin_jingfen_config.yaml",
    CHINA_LIFE_NAME: "china_life_config.yaml",
    LINGSHI_NAME: "lingshi_config.yaml",
    SCIENCE_CITY_NAME: "kexuecheng_config.yaml",
    BANK_PROJECT: "bank_config.yaml",
    DEFAULT_PROJECT_NAME: "default_config.yaml",
    HUAXIA_PROJECT_NAME: "huaxia_config.yaml",
    SHILI_PROJECT_NAME: "shili_config.yaml",
    SHENZHEN_BASHI_PROJECT_NAME: "shenzhen_bashi_config.yaml",
    BEIJIAOSUO_PROJECT_NAME: "bei_jiao_suo_config.yaml",
    BEIYINLICAI_PROJECT_NAME: "beiyinlicai_config.yaml",
}


@cached("{config_file}")
def _load_project_config(config_file: str) -> Dict:
    file_path = Path(__file__).parent / "project_configs" / config_file
    with open(file_path, "r", encoding="utf8") as file:
        data = yaml.load(file, Loader=EnvVarLoader)
        logger.info(f"project {config_file} load config from {file_path} succeed")
    return data


CONFIG_NOT_FOR_MODELS = {
    "dimension_mapping",
    "dimension_value_mapping",
    "check_in_all_dimension_value",
    "always_refresh_cache",
}


CHINA_PROVINCES = [
    "山东",
    "河北",
    "河南",
    "山西",
    "陕西",
    "黑龙江",
    "吉林",
    "辽宁",
    "江苏",
    "浙江",
    "安徽",
    "福建",
    "江西",
    "湖北",
    "湖南",
    "广东",
    "海南",
    "四川",
    "贵州",
    "云南",
    "青海",
    "甘肃",
    "台湾",
    "北京",
    "天津",
    "上海",
    "重庆",
    "广西",
    "内蒙古",
    "西藏",
    "宁夏",
    "新疆",
    "香港",
    "澳门",
]


class ProjectConfig:
    def __init__(self, project_name: str, model_name: str):
        self.__config = {}
        default_config_file = PROJECT_CONFIG_FILES[DEFAULT_PROJECT_NAME]
        if (Path(__file__).parent / "project_configs" / default_config_file).exists():
            default_config = _load_project_config(default_config_file)
            if DEFAULT_MODEL in default_config:
                self.__config.update(default_config[DEFAULT_MODEL])
            else:
                self.__config = copy.deepcopy(default_config)
        config_file = (
            PROJECT_CONFIG_FILES[project_name]
            if project_name in PROJECT_CONFIG_FILES
            else ""
        )
        if (
            not config_file
            and (
                Path(__file__).parent / "project_configs" / f"{project_name}.yaml"
            ).exists()
        ):
            config_file = f"{project_name}.yaml"
        if config_file:
            all_config = _load_project_config(config_file)
            if DEFAULT_MODEL in all_config:
                self.__config.update(all_config[DEFAULT_MODEL])
            if model_name and model_name in all_config:
                model_config = all_config[model_name]
                for key in model_config.keys():
                    if key in CONFIG_NOT_FOR_MODELS:
                        raise RuntimeError(
                            f"project {project_name} model {model_name} config {key} only for project"
                        )
                self.__config.update(model_config)

        # cache some config that requires calculation
        raw_dimension_mapping = self.__config.get("dimension_mapping", [])
        self._dimension_mapping = []
        for tmp in raw_dimension_mapping:
            self._dimension_mapping.append(set(tmp))

        raw_dimension_value_mapping = self.__config.get("dimension_value_mapping", [])
        self._dimension_value_mapping = []
        for tmp in raw_dimension_value_mapping:
            self._dimension_value_mapping.append(set(tmp))

        raw_dimension_values_filters = self.__config.get("dimension_values_filters", [])
        self._dimension_values_filters = raw_dimension_values_filters

        def get_stopwords_from_key(key: str) -> List[str]:
            raw_stopwords = list(self.__config.get(key, []))
            stop_words = set()
            single_stop_words = set()
            for w in raw_stopwords:
                if not w:
                    continue
                stop_words.add(w)
                if len(w) == 1:
                    single_stop_words.add(w)
            stop_words.update(dipeak_stopwords)
            single_stop_words.update(dipeak_single_stopwords)
            return stop_words, single_stop_words

        self._metric_stopwords, self._metric_single_stopwords = get_stopwords_from_key(
            "metric_stopwords"
        )
        (
            self._dimension_value_stopwords,
            self._dimension_value_single_stopwords,
        ) = get_stopwords_from_key("dimension_value_stopwords")

        self._dimension_value_keywords = (
            list(self.__config.get("dimension_value_keywords", [])) + CHINA_PROVINCES
        )
        self._dimension_value_keywords = list(set(self._dimension_value_keywords))

    @property
    def enable_time_query(self):
        return bool(self.__config.get("enable_time_query", True))

    @property
    def enable_metrics(self):
        return bool(self.__config.get("enable_metrics", True))

    @property
    def enable_time_dimension(self):
        return bool(self.__config.get("enable_time_dimension", False))

    @property
    def enable_where(self):
        return bool(self.__config.get("enable_where", True))

    @property
    def enable_group_bys(self):
        return bool(self.__config.get("enable_group_bys", True))

    @property
    def enable_order_bys(self):
        return bool(self.__config.get("enable_order_bys", True))

    @property
    def enable_metric_dimension_mapping(self):
        return bool(self.__config.get("enable_metric_dimension_mapping", False))

    @property
    def dimension_value_list(self):
        return self.__config.get("dimension_value_list", [])

    @property
    def force_order_by_metrics(self):
        return bool(self.__config.get("force_order_by_metrics", False))

    # does not support config for different model
    @property
    def dimension_mapping(self):
        return self._dimension_mapping

    # does not support config for different model
    @property
    def dimension_value_mapping(self):
        return self._dimension_value_mapping

    @property
    def dimension_values_filters(self):
        return self._dimension_values_filters

    # does not support config for different model
    @property
    def check_in_all_dimension_value(self):
        return bool(self.__config.get("check_in_all_dimension_value", False))

    @property
    def limit_num_of_dimension_from_value_node(self):
        limit = int(self.__config.get("limit_num_of_dimension_from_value_node", 0))
        limit_total = int(
            self.__config.get("total_limit_num_of_dimension_from_value_node", 0)
        )
        if limit <= 0:
            return 0, 0
        return limit, limit_total

    @property
    def recommend_prefilter_if_no_metric(self):
        return int(self.__config.get("recommend_prefilter_if_no_metric", 0))

    @property
    def recommend_history_and_prefilter(self):
        return bool(self.__config.get("recommend_history_and_prefilter", False))

    @property
    def default_limit_when_extract_no_result(self):
        return int(self.__config.get("default_limit_when_extract_no_result", -1))

    @property
    def question_keyword_black_list(self):
        return list(self.__config.get("question_keyword_black_list", []))

    @property
    def filter_metric_labels_list(self):
        return list(self.__config.get("filter_metric_labels_list", []))

    @property
    def metric_dimension_retrieval_keyword_threshold(self):
        return float(
            self.__config.get("metric_dimension_retrieval_keyword_threshold", 0)
        )

    @property
    def dimension_value_jieba_exact_topk(self):
        return int(self.__config.get("dimension_value_jieba_exact_topk", 10))

    @property
    def dimension_value_jieba_short_topk(self):
        return int(self.__config.get("dimension_value_jieba_short_topk", 10))

    @property
    def dimension_value_jieba_topk(self):
        return int(self.__config.get("dimension_value_jieba_topk", 10))

    @property
    def jieba_exact_match_threshold(self):
        return int(self.__config.get("jieba_exact_match_threshold", 6))

    @property
    def dimension_value_jieba_topk_per_dimension(self):
        return int(self.__config.get("dimension_value_jieba_topk_per_dimension", 0))

    @property
    def dimension_value_lcs_match_ratio(self):
        r = float(self.__config.get("dimension_value_lcs_match_ratio", 3 / 4))
        assert r > 0 and r < 1
        return r

    @property
    def dimension_value_index_type(self):
        return self.__config.get("dimension_value_index_type", "")

    @property
    def filter_by_wordcount_limit(self):
        return int(self.__config.get("filter_by_wordcount_limit", 2))

    @property
    def dimension_value_fuzzy_match_cnt(self):
        return int(self.__config.get("dimension_value_fuzzy_match_cnt", 100))

    @property
    def dimension_value_keywords(self):
        return self._dimension_value_keywords

    @property
    def dimension_where_rules(self):
        return self.__config.get("dimension_where_rules", [])

    @property
    def skip_retrive_metrics(self):
        return self.__config.get("skip_retrive_metrics", False)

    @property
    def skip_retrive_dimensions(self):
        return self.__config.get("skip_retrive_dimensions", False)

    @property
    def do_rule_dimension_retrive(self):
        return self.__config.get("do_rule_dimension_retrive", True)

    @property
    def vector_store_type(self):
        return self.__config.get("vector_store_type", "")

    @property
    def param_extract_confidence_threshold(self):
        return float(
            self.__config.get("param_extract_confidence_threshold", 0.4)
        )  # '宝武' Vs '宝武集团' should pass this check

    def llm_model_params(self, model_type: str) -> Dict[str, Any]:
        if model_type not in self.__config:
            return {
                "model_name": "deepseek",
                "openai_api_base": "http://*************:9999/v1",
                "max_tokens": 8192,
            }
        model_params_value = self.__config[model_type]
        if not isinstance(model_params_value, dict):
            raise ValueError(
                f"model_type {model_type} config must be dict, got {model_params_value}"
            )
        return model_params_value

    @property
    def metric_stopwords(self):
        return self._metric_stopwords

    @property
    def metric_single_stopwords(self):
        return self._metric_single_stopwords

    @property
    def dimension_value_stopwords(self):
        return self._dimension_value_stopwords

    @property
    def dimension_value_single_stopwords(self):
        return self._dimension_value_single_stopwords

    @property
    def high_risk_dimension_values(self):
        return list(
            self.__config.get("high_risk_dimension_values", [])
        )  # '宝武' Vs '宝武集团' should pass this check

    @property
    def chart_insight_row_num(self):
        return int(self.__config.get("chart_insight_row_num", 5))

    @property
    def chart_insight_column_num(self):
        return int(self.__config.get("chart_insight_column_num", 5))

    # need_refresh cache checks the ts from frontend
    # TODO(bhx): too many bug in frontend, turn this off when frontend ok
    @property
    def always_refresh_cache(self):
        return bool(self.__config.get("always_refresh_cache", True))

    @property
    def not_allowed_intent_list(self):
        tmp = list(self.__config.get("not_allowed_intent_list", []))
        return [Intent(i) for i in tmp]

    @property
    def not_allowed_meta_intent_list(self):
        tmp = list(self.__config.get("not_allowed_meta_intent_list", []))
        return [MetaIntentType(i) for i in tmp]

    @property
    def query_mom_yoy_as_default(self):
        return bool(self.__config.get("query_mom_yoy_as_default", False))

    def special_subchain_params(self, stage: ParamsExtractStage):
        result = None
        params = self.__config.get("special_subchain_params", None)
        if params:
            result = params.get(stage, None)
        if not result:
            if stage in {
                ParamsExtractStage.NL2AGENT,
                ParamsExtractStage.INTENT_BI,
            }:
                return {"model_type": MODEL_TYPE_PRE_GLM_4_9B_AGENT}

            if stage in {
                ParamsExtractStage.NL2META,
                ParamsExtractStage.NL2METRIC_GROUP_BYS,
                ParamsExtractStage.NL2METRIC_METRICS,
                ParamsExtractStage.NL2METRIC_ORDER_BYS,
                ParamsExtractStage.NL2METRIC_WHERE,
                ParamsExtractStage.ATTR_ANALYSIS_TIME,
                ParamsExtractStage.NL2TIME_DIMENSION,
            }:
                return {"model_type": MODEL_TYPE_PRE_GLM_4_9B}

            if stage == ParamsExtractStage.NL2METRIC_TIME_QUERY:
                return {
                    "type": "nl2metric_time_query_v2",
                    "model_type": MODEL_TYPE_PRE_GLM_4_9B,
                }

            if stage == ParamsExtractStage.NL2INTENT:
                return {
                    "type": "nl2intent_by_tag_v2",
                    "model_type": MODEL_TYPE_PRE_GLM_4_9B,
                }
            if stage == ParamsExtractStage.CONDENSE_QUERY:
                return {
                    "type": "condense_query_v2",
                    "model_type": MODEL_TYPE_PRE_GLM_4_9B,
                }
        return result

    # some project select dimension, so this is not always true
    @property
    def nl2metric_requires_metrics(self):
        return bool(self.__config.get("nl2metric_requires_metrics", False))

    # baowu is using rules instead of condense for follow-up query
    @property
    def no_condense(self):
        return bool(self.__config.get("no_condense", False))

    @property
    def metric_blacklist_by_groupby_dimension(self):
        return self.__config.get("metric_blacklist_by_groupby_dimension", {})

    @property
    def do_metric_rule(self):
        return bool(self.__config.get("do_metric_rule", True))

    @property
    def tags_2_intent(self):
        return self.__config.get("tags_2_intent", {})

    @property
    def where_lcs_correction(self):
        return bool(self.__config.get("where_lcs_correction", False))

    @property
    def correct_dimension_by_dimension_value(self):
        return bool(self.__config.get("correct_dimension_by_dimension_value", False))

    @property
    def filter_intent_list(self):
        return list(self.__config.get("filter_intent_list", []))

    @property
    def agent_brain_fail_if_no_succeed_toolcall(self):
        return bool(self.__config.get("agent_brain_fail_if_no_succeed_toolcall", False))

    @property
    def agent_brain_tools_whitelist(self):
        return set(
            self.__config.get(
                "agent_brain_tools_whitelist",
                {
                    "nl2agent.agents.bi_agent.BiAgent",
                    "nl2agent.tools.metric_meta.MetricMetaTool",
                    "nl2agent.tools.metric_attr.MetricAttrTool",
                    "nl2agent.tools.chat.ChatTool",
                    "tools.code_tool.PyCodeInterpreterTool",
                },
            )
        )

    @property
    def agent_brain_tools_blacklist(self):
        return set(self.__config.get("agent_brain_tools_blacklist", set()))

    @property
    def agent_judge_tools_whitelist(self):
        return set(
            self.__config.get(
                "agent_judge_tools_whitelist",
                {
                    "tools.code_tool.PyCodeInterpreterTool",
                    "nl2agent.tools.chat.ChatTool",
                    "nl2agent.tools.early_stop.EarlyStopTool",
                },
            )
        )

    @property
    def agent_judge_tools_blacklist(self):
        return set(self.__config.get("agent_judge_tools_blacklist", set()))

    @property
    def sensitive_words_file_path(self):
        raw_path = self.__config.get("sensitive_words_file_path", "")
        if not raw_path:
            return ""
        elif raw_path.startswith("/"):
            return raw_path
        else:
            absolute_path = Path(__file__).parent.parent / raw_path
            return str(absolute_path.resolve())

    @property
    def condense_query_in_brain(self):
        return bool(self.__config.get("condense_query_in_brain", True))

    @property
    def enable_condense_model(self):
        return bool(self.__config.get("enable_condense_model", False))

    @property
    def condense_with_history_response(self):
        return bool(self.__config.get("condense_with_history_response", True))

    @property
    def enable_manual_select(self):
        return bool(self.__config.get("enable_manual_select", False))

    @property
    def doc_retrieve_preprocess_func(self):
        return str(
            self.__config.get(
                "doc_retrieve_preprocess_func", "tools.doc_tool.doc_retrieve_preprocess"
            )
        )

    @property
    def brain_plan_postprocess_func(self):
        return str(self.__config.get("brain_plan_postprocess_func", ""))

    @property
    def query_groupby_postprocess_func(self):
        return str(self.__config.get("query_groupby_postprocess_func", ""))

    @property
    def enable_metric_latest_time(self):
        return bool(self.__config.get("enable_metric_latest_time", False))

    @property
    def include_illegal_dimension_values(self):
        return bool(self.__config.get("include_illegal_dimension_values", False))


@cached("{project_name}#{model_name}")
def get_project_config(project_name: str, model_name: str) -> ProjectConfig:
    return ProjectConfig(project_name=project_name, model_name=model_name)


@cached("{project_id}#{model_name}")
def get_project_config_by_id(project_id: str, model_name: str) -> ProjectConfig:
    # avoid circual import
    from metastore.service import get_db_appstore

    project_name = get_db_appstore().get_project_by_id(project_id).name
    return get_project_config(project_name=project_name, model_name=model_name)
