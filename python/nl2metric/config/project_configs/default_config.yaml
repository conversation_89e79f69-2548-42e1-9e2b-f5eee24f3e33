default:
  enable_time_dimension: True
  sensitive_words_file_path: 'common/sensitive_words/sensitive_words.txt'
  deepseek-r1-671b: &deepseek-r1-671b
    model_name: "deepseek-reasoner"
    openai_api_base: "https://api.deepseek.com/v1"
    # `!ENV VAR_NAME:default_value`: 获取字符串环境变量，如果未设置则使用默认值。
    openai_api_key: !ENV deepseek_r1_671b_api_key:xx_swww
    max_tokens: 8192
  deepseek-14b: &deepseek-14b
     model_name: "deepseek"
     openai_api_base: "http://8.147.105.203:9999/v1"
  qwen3_agent: &qwen3_agent
    model_name: "qwen3_32b"
    openai_api_base: "http://123.181.192.99:29002/v1"
  qwen3_agent_nothink: &qwen3_agent_nothink
    <<: *qwen3_agent
    extra_body:
      chat_template_kwargs:
        enable_thinking: False
  base_model: &base_model
    model_name: "qwen3_32b"
    openai_api_base: "http://8.147.105.203:8889/v1"
  base_model_nothink: &base_model_nothink
    <<: *base_model
    extra_body:
      chat_template_kwargs:
        enable_thinking: False
  agent_model: &agent_model
    # ds-qwen_agent 14b
    <<: *qwen3_agent
  ner_model:
    <<: *base_model_nothink
  condense_model:
    <<: *qwen3_agent_nothink
  judge_model:
    <<: *qwen3_agent_nothink
  code_model:
    <<: *base_model_nothink
  special_subchain_params:
    nl2intent:
      type: "nl2intent_by_tag_v2"
      model_type: "qwen3_agent_nothink"
    nl2metric_group_bys:
      model_type: "qwen3_agent_nothink"
    nl2metric_metrics:
      model_type: "qwen3_agent_nothink"
    nl2metric_order_bys:
      model_type: "qwen3_agent_nothink"
    nl2metric_where:
      model_type: "qwen3_agent_nothink"
    attr_analysis_time:
      model_type: "qwen3_agent_nothink"
    nl2time_dimension:
      model_type: "qwen3_agent_nothink"
    nl2metric_time_query:
      type: "nl2metric_time_query_v2"
      model_type: "qwen3_agent_nothink"
    table_tools:
      model_type: "qwen3_agent"
