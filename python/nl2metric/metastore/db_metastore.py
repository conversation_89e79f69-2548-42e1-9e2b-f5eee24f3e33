from collections import defaultdict
from typing import Dict, List, Tuple

from common.db_model.model import (
    get_nl_metric_few_shot_models_by_project_id,
    get_semantic_dimensions,
    get_semantic_measures,
    get_semantic_metrics,
    get_semantic_models,
    get_all_semantic_models,
    get_semantic_projects,
    SemanticProject,
)
from common.logging.logger import get_logger
from metastore.base import (
    BaseMetaStore,
    BaseAppStore,
    Dimension,
    DimensionValue,
    Measure,
    Metric,
    Model,
    Project,
)

logger = get_logger(__name__)


class DatabaseMetaStore(BaseMetaStore):
    def __init__(self, project_id: str):
        logger.debug(f"DatabaseMetaStore for project_id {project_id} creating...")
        self._project_id = project_id
        self._model_map = {m.id: m for m in get_semantic_models(self._project_id)}
        super().__init__(project_id)
        logger.info(f"DatabaseMetaStore for project_id {project_id} created")

    def _fetch_measures(self) -> Dict[str, Dict[str, Measure]]:
        ret: Dict[str, Dict[str, Measure]] = defaultdict(dict)
        for m in get_semantic_measures(self._project_id):
            model = self._model_map.get(m.semantic_model_id)
            if not model:
                logger.warn(
                    "Measure Model not found, model_id=%s, measure=%s",
                    m.semantic_model_id,
                    m.name,
                )
                continue
            model_name = model.table_name
            ret[model_name][m.name] = Measure(
                name=m.name,
                label=m.label,
                synonyms=[s for s in m.synonyms if s],
                description=m.description,
                create_metric=m.create_metric,
                model_name=model_name,
            )
        return ret

    def _fetch_metrics(self) -> Dict[str, Metric]:
        ret = {}
        for m in get_semantic_metrics(self._project_id):
            any_model_not_exists = False
            model_names = []
            for model_id in m.semantic_model_ids:
                model = self._model_map.get(model_id)
                if not model:
                    any_model_not_exists = True
                    logger.warn(
                        "Metric model not found, model_id=%s, metric=%s",
                        model_id,
                        m.name,
                    )
                    continue
                model_names.append(model.table_name)
            if any_model_not_exists:
                continue
            ret[m.name] = Metric(
                name=m.name,
                label=m.label,
                synonyms=[s for s in m.synonyms if s],
                description=m.description,
                model_names=model_names,
            )
        return ret

    def _fetch_external_reports(self) -> Dict[str, Metric]:
        return {}

    def _fetch_dimensions(
        self,
    ) -> Tuple[Dict[str, Dict[str, Dimension]], Dict[str, Dict[str, Dimension]]]:
        ret: Dict[str, Dict[str, Dimension]] = defaultdict(dict)
        for d in get_semantic_dimensions(self._project_id):
            model = self._model_map.get(d.semantic_model_id)
            if not model:
                logger.warn(
                    "Dimension model not found, model_id=%s, dimension=%s",
                    d.semantic_model_id,
                    d.name,
                )
                continue
            model_name = model.table_name
            ret[model_name][d.name] = Dimension(
                name=d.name,
                label=d.label,
                synonyms=[s for s in d.synonyms if s],
                type=d.type,
                expr=d.expr,
                values=DimensionValue.from_list(d.values),
                description=d.description,
                model_name=model_name,
            )
        return ret, defaultdict(dict)

    """
        根据project id 获取对应few shots，并根据scene分组
    """

    def _fetch_nl_metric_few_shot(self) -> Dict[str, List[dict]]:
        ret: Dict[str, List[dict]] = {}
        few_shot_list = get_nl_metric_few_shot_models_by_project_id(self._project_id)
        for few_shot in few_shot_list:
            temp = {
                "question": few_shot.question,
                "metrics": [
                    Metric(
                        name=metric["name"],
                        label=metric["label"],
                    )
                    for metric in few_shot.metrics
                ],
                "dimensions": [
                    Dimension(
                        name=dimension["name"],
                        label=dimension["label"],
                        values=[
                            DimensionValue(
                                name=value["name"], description=value["description"]
                            )
                            for value in dimension["values"]
                        ]
                        if "values" in dimension
                        else [],
                    )
                    for dimension in few_shot.dimensions
                ],
                "think": few_shot.think,
                "result": few_shot.result,
                "labels": few_shot.labels,
            }

            if few_shot.scene in ret:
                ret[few_shot.scene].append(temp)
            else:
                ret[few_shot.scene] = [temp]
        return ret


class DatabaseAppStore(BaseAppStore):
    def _fetch_projects_and_models(self):
        semantic_projects: List[SemanticProject] = get_semantic_projects()
        projects_by_name: Dict[str, Project] = {}
        projects_by_id: Dict[str, Project] = {}
        models_by_name: Dict[str, Dict[str, Model]] = {}
        models_by_id = {}
        models_by_project_id = {}

        for p in semantic_projects:
            if (not p.id) or (not p.name):
                logger.warning(f"ignore invalid project id {p.id}, name {p.name}")
                continue
            project = Project(id=p.id, name=p.name)
            projects_by_name[p.name] = project
            projects_by_id[p.id] = project
            models_by_name[p.id] = {}
            models_by_project_id[p.id] = []

        semantic_models = get_all_semantic_models()
        for m in semantic_models:
            if (not m.id) or (not m.label):
                logger.warning(f"ignore invalid model: id {m.id}, label {m.label}")
                continue
            if m.semantic_project_id not in projects_by_id:
                if not m.semantic_project_id:
                    continue
                else:
                    raise RuntimeError(
                        f"semantic_project_id doesnot exist, semantic_model {m}"
                    )
            semantic_project_name = projects_by_id[m.semantic_project_id].name
            model = Model(
                id=m.id,
                table_name=m.table_name,
                label=m.label,
                description=m.description,
                agg_time_dimension=m.agg_time_dimension,
                table_meta_id=m.table_meta_id,
                semantic_project_name=semantic_project_name,
                semantic_project_id=m.semantic_project_id,
            )
            models_by_name[m.semantic_project_id][m.table_name] = model
            models_by_id[m.id] = model
            models_by_project_id[m.semantic_project_id].append(model)

        return (
            projects_by_name,
            projects_by_id,
            models_by_name,
            models_by_id,
            models_by_project_id,
        )
