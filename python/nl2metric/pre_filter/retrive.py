from typing import List
from common.logging.logger import get_logger
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from common.utils.langchain_tracer import trace_by_chain
from config.project_config import get_project_config
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output
from metastore import get_metastore
from metastore.base import Metric, Dimension
from metastore.score import match_metric_score, match_dimension_score
from nl2metric.few_shots import FENGHUO_PROJECT_NAME, HUAXIA_PROJECT_NAME
from nl2metric.query_metric import filter_metrics_by_wordcount
from nl2metric.query_where import create_rule_dimension_sub_where
from pre_filter.prefilter_service import PreFilterService
from pre_filter.service import get_pre_filter_service

logger = get_logger(__name__)


def fenghuo_filter_metrics(question: str, metrics: List[Metric]):
    filter_metric_labels = {"销售收入完成情况", "回款完成情况", "有效合同完成情况"}
    if "情况" not in question:
        return [m for m in metrics if m.label not in filter_metric_labels]
    else:
        return metrics


def filter_metrics(project_name, model_name, metrics: List[Metric]):
    filter_metric_labels_list = get_project_config(
        project_name, model_name
    ).filter_metric_labels_list
    return [m for m in metrics if m.label not in filter_metric_labels_list]


@trace_by_chain()
def filter_metric_dimension_retrieval_by_keyword(input, config: RunnableConfig):
    metrics = input["metrics"]
    dimensions = input["dimensions"]
    question = input["question"]
    keyword_threshold = input["keyword_threshold"]

    if keyword_threshold <= 0:
        return metrics, dimensions
    metrics_result = []
    for m in metrics:
        if match_metric_score(question, m, keyword_threshold, config, False, True):
            metrics_result.append(m)

    dimensions_result = []
    for d in dimensions:
        if match_dimension_score(question, d, keyword_threshold, config, False, True):
            dimensions_result.append(d)
    return metrics_result, dimensions_result


def _metric_dimension_retrieval(
    question: str, skip_metrics: bool, config: RunnableConfig
):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(project_name, model_name)
    skip_retrive_metrics = project_config.skip_retrive_metrics
    skip_retrive_dimensions = project_config.skip_retrive_dimensions
    dimension_where_rules = project_config.dimension_where_rules

    if (skip_metrics or skip_retrive_metrics) and skip_retrive_dimensions:
        pre_filter = None
    else:
        pre_filter = get_pre_filter_service(project_id, model_name)
    if skip_metrics:
        metrics = []
    elif skip_retrive_metrics:
        metrics = filter_metrics_by_wordcount(
            [], as_many_as_possible=True, config=config
        )
    else:
        metrics = pre_filter.retrieve_metrics(question)
        if project_name == FENGHUO_PROJECT_NAME:
            metrics = fenghuo_filter_metrics(
                config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION], metrics
            )
        metrics = filter_metrics(project_name, model_name, metrics)

    metastore = get_metastore(project_id)

    def _create_rule_dimension_by_rule(rule):
        (
            rule_dimension_name,
            rule_operator_str,
            rule_type,
        ) = rule
        rule_where_dimension = metastore.safe_get_dimension(
            model_name, rule_dimension_name
        )
        if rule_where_dimension == None:
            return None
        dimension_sub_where = create_rule_dimension_sub_where(
            rule_where_dimension, rule_operator_str, config
        )
        if rule_type == "word_count":
            dimension_values_by_wordcount = (
                dimension_sub_where._filter_by_wordcount_impl(
                    question=question,
                    config=config,
                    as_mang_as_possible=True,
                    fuzzy_match_limit=10,
                )
            )
        else:
            raise RuntimeError(f"rule {rule} not supported")
        # no need to unfold_synonyms
        dimension_copy = rule_where_dimension.model_copy()
        dimension_copy.set_values(
            {v.key: v for v in dimension_values_by_wordcount[:10]}
        )
        return dimension_copy

    if skip_retrive_dimensions:
        dimensions = []
        for rule in dimension_where_rules:
            new_sub = _create_rule_dimension_by_rule(rule)
            if new_sub != None:
                dimensions.append(new_sub)
    else:
        raw_dimensions = pre_filter.retrieve_dimensions(question, config)
        if project_config.do_rule_dimension_retrive:
            rules = {rule[0]: rule for rule in dimension_where_rules}
            dimensions = []
            for d in raw_dimensions:
                if d.name not in rules:
                    dimensions.append(d)
                else:
                    dimensions.append(_create_rule_dimension_by_rule(rules[d.name]))
            for rule in dimension_where_rules:
                rule_dimension_name = rule[0]
                if rule_dimension_name and all(
                    rule_dimension_name != d.name for d in dimensions
                ):
                    new_sub = _create_rule_dimension_by_rule(rule)
                    if new_sub != None:
                        dimensions.append(new_sub)
        else:
            dimensions = raw_dimensions
        if project_name == HUAXIA_PROJECT_NAME:
            for dimension in dimensions:
                if dimension.name == "by_month":
                    found_month = False
                    for dimension_value in dimension.values:
                        if dimension_value.name == "按月":
                            found_month = True
                    if not found_month:
                        originl_dimension = metastore.get_dimension(
                            model_name, "by_month"
                        )
                        by_month_dimension_value = originl_dimension.search_value(
                            "按月", None
                        )
                        if by_month_dimension_value is not None:
                            dimension.add_value(by_month_dimension_value)

    # this is for nl2intent
    # nl2intent is inaccurate without metrics/dimensions
    if not skip_metrics:
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_METRICS] = metrics
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_DIMENSIONS] = dimensions

    keyword_threshold = get_project_config(
        project_name, model_name
    ).metric_dimension_retrieval_keyword_threshold
    if keyword_threshold > 0:
        metrics, dimensions = filter_metric_dimension_retrieval_by_keyword(
            {
                "metrics": metrics,
                "dimensions": dimensions,
                "question": question,
                "keyword_threshold": keyword_threshold,
            },
            config=config,
        )

    if not skip_metrics and not metrics:
        msg = f"project {project_name} model {model_name} no metrics retrieved, question {question}"
        logger.error(msg)
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.ERROR_LOGS].append(msg)
    if not dimensions:
        msg = f"project {project_name} model {model_name} no dimensions retrieved, question {question}"
        logger.error(msg)
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.ERROR_LOGS].append(msg)
    return metrics, dimensions


def metric_dimension_retrieval(question: str, config: RunnableConfig):
    if isinstance(question, dict):
        question = question["question"]

    metrics, dimensions = _metric_dimension_retrieval(
        question=question,
        skip_metrics=False,
        config=config,
    )

    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS] = metrics
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS] = dimensions
    return {"question": question}


def call_retrive() -> Runnable[Input, Output]:
    return RunnableLambda(metric_dimension_retrieval, name="metric_dimension_retrieval")


def dimension_retrive_value(
    question: str, dimension: Dimension, config: RunnableConfig
):
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    pre_filter = get_pre_filter_service(project_id, model_name)
    top_k = 10
    result = []
    result_strs = set()

    value_nodes = pre_filter.retrieve_dimension_value_nodes(question, top_k)
    for value_node in value_nodes:
        dv = dimension.search_value(value_node.text, None)
        if dv is None:
            continue
        elif dv.is_synonym:
            if dv.synonyms:
                for s in dv.synonyms:
                    if s not in result_strs:
                        result_strs.add(s)
                        s_dv = dimension.search_value(s, False)
                        if s_dv:
                            result.append(s_dv)
                            if len(result) >= top_k:
                                break
                if len(result) >= top_k:
                    break
        elif dv.name not in result_strs:
            result_strs.add(dv.name)
            result.append(dv)
            if len(result) >= top_k:
                break
    return result
