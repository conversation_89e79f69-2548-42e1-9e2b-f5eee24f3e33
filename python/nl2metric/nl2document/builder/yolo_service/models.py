from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class DetectionResult(BaseModel):
    bbox: List[int]
    label_id: int
    label: str
    confidence: float


class ImageRequest(BaseModel):
    image: str = Field(..., description="Base64编码的图片")
    model_type: str = Field(..., description="模型类型: ouryolo 或 doclayout 或 yolotabledet")
    conf: float = Field(default=0.5, description="置信度阈值")
    bboxs: Optional[List[List[int]]] = Field(default=[], description="检测结果框")


class BertRequest(BaseModel):
    text: str = Field(..., description="需要处理的文本")


class BertResponse(BaseModel):
    code: int = Field(200, description="状态码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="处理结果数据")


class ImageResponse(BaseModel):
    code: int = Field(200, description="状态码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="检测结果数据")
