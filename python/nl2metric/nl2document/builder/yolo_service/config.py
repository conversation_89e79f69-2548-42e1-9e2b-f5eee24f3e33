try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # 模型路径配置
    ASK_DOC_OCR_PATH: str = "/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR/"
    OURYOLO_MODEL_PATH: str = "runs/detect/train39/weights/best.pt"
    DOCLAYOUT_MODEL_PATH: str = (
        "runs/detect/train39/weights/doclayout_yolo_docstructbench_imgsz1024.pt"
    )
    YOLO_TABLE_DET_MODEL_PATH: str = "runs/detect/train39/weights/table_yolo.pt"
    BERT_MODEL_PATH: str = "runs/detect/train39/weights"
    BERT_MODEL_NAME: str = "bert-base-chinese"
    BERT_WEIGHTS: str = "bert_best.pt"

    # 模型配置
    DEVICE: Optional[str] = None
    BERT_DROPOUT: float = 0.3

    # 标签映射
    LABEL2ID: dict = {"A": 0, "B": 1, "0": "A", "1": "B"}

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "logs"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    LOG_TO_CONSOLE: bool = True
    LOG_TO_FILE: bool = True

    class Config:
        env_file = ".env"


settings = Settings()
