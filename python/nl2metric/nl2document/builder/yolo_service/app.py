from fastapi import FastAPI, HTTPException, Depends
from models import (
    BertRequest,
    BertResponse,
    ImageRequest,
    ImageResponse,
    DetectionResult,
)
from services import ModelService
from utils import base64_to_numpy, _replace
from config import settings
from logging_config import setup_logging, get_logger
from typing import List
import traceback

# 设置日志配置
setup_logging(
    log_level=settings.LOG_LEVEL,
    log_dir=settings.LOG_DIR,
    service_name="yolo_service",
    max_bytes=settings.LOG_MAX_BYTES,
    backup_count=settings.LOG_BACKUP_COUNT,
    console_output=settings.LOG_TO_CONSOLE,
    file_output=settings.LOG_TO_FILE,
)
logger = get_logger(__name__)

app = FastAPI(title="YOLO图像检测服务")
model_service = ModelService()


async def get_model_service() -> ModelService:
    return model_service


@app.on_event("startup")
async def startup_event():
    """服务启动时加载模型"""
    logger.info("=== YOLO图像检测服务启动 ===")
    logger.info(f"日志级别: {settings.LOG_LEVEL}")
    logger.info(f"日志目录: {settings.LOG_DIR}")
    logger.info(f"设备配置: {settings.DEVICE or 'auto'}")
    logger.info(f"BERT dropout: {settings.BERT_DROPOUT}")

    try:
        await model_service.load_models()
        logger.info("=== 所有模型加载完成，服务就绪 ===")
    except Exception as e:
        logger.error(f"=== 服务启动失败: {str(e)} ===")
        raise


@app.post("/yolo-ocr/inference", response_model=ImageResponse)
async def yolo_ocr_inference(
    request: ImageRequest, service: ModelService = Depends(get_model_service)
) -> ImageResponse:
    """处理图片的API endpoint

    Args:
        request: 包含base64编码图片的请求
        service: 模型服务实例

    Returns:
        ImageResponse: 检测结果

    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        # 将base64转换为numpy数组
        image_array = base64_to_numpy(request.image)

        # 使用YOLO模型进行推理
        results = await service.yolo_inference(
            image_array, request.model_type, request.conf
        )

        # 提取结果
        detections: List[DetectionResult] = []
        if results.boxes is not None:
            boxes = results.boxes.xyxy.cpu().numpy().astype(int).tolist()
            labels = results.boxes.cls.cpu().numpy().astype(int).tolist()
            confs = results.boxes.conf.cpu().numpy().tolist()

            for box, label_id, conf in zip(boxes, labels, confs):
                detections.append(
                    DetectionResult(
                        bbox=box,
                        label_id=label_id,
                        label=results.names[label_id],
                        confidence=conf,
                    )
                )

        return ImageResponse(
            data={"detections": detections, "shape": image_array.shape}
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"处理图片时发生错误: {str(e)}")
        logger.error("Stack trace: %s", traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/bert/inference", response_model=BertResponse)
async def bert_inference(
    request: BertRequest, service: ModelService = Depends(get_model_service)
) -> BertResponse:
    """使用BERT模型进行文本推理

    Args:
        request: 包含文本的请求体
        service: 模型服务实例

    Returns:
        BertResponse: 处理后的文本响应

    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:

        def _split_by_slice(texts: List[str]) -> str:
            if len(texts) < 2:
                return "".join(texts)

            pred_texts = [texts[0]]
            for text in texts[1:]:
                _text = pred_texts.pop()
                a, b = _text[:-50], _text[-50:]
                pred = service.bert_inference(f"{b}\n{text}")
                pred_texts.extend(f"{a}{pred}".split("\n"))

            return "\n".join(pred_texts)

        if request.text.count("\n") == 0:
            return BertResponse(data={"text": request.text})

        texts = []
        for t in _replace(request.text, request.sign):
            if t.count("\n") == 0:
                texts.append(t)
            elif len(t) < 300:  # token数量小于300
                texts.append(service.bert_inference(t))
            else:
                texts.append(_split_by_slice(t.split("\n")))
        return BertResponse(data={"text": "\n".join(texts)})
    except Exception as e:
        logger.error(f"处理文本时发生错误: {str(e)}")
        logger.error("Stack trace: %s", traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
