from typing import List, <PERSON><PERSON>
from enum import Enum
import torch
import torch.nn as nn
from transformers import <PERSON><PERSON><PERSON><PERSON>, BertModel
from ultralytics import <PERSON><PERSON><PERSON>
from doclayout_yolo import YOLOv10
import numpy as np
from config import settings
from logging_config import get_logger

from utils import get_pos

logger = get_logger(__name__)


class ModelType(Enum):
    OURYOLO = "ouryolo"
    DOCLAYOUT = "doclayout"
    YOLOTABLEDET = "yolotabledet"


class BertClassifier(nn.Module):
    def __init__(self, model_path: str, num_labels: int, dropout: float):
        super().__init__()
        self.bert = BertModel.from_pretrained(model_path)
        config = self.bert.config
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(config.hidden_size, num_labels)

    def forward(
        self, ids_inputs: torch.Tensor, attention_mask: torch.Tensor
    ) -> torch.Tensor:
        hidden_state = self.bert(ids_inputs, attention_mask=attention_mask)[0]
        last_hidden_states = self.dropout(hidden_state)
        logits = self.classifier(last_hidden_states[:, 0, :])
        return logits


class ModelService:
    def __init__(self):
        self.device = self._get_device()
        logger.info(f"使用设备: {self.device}")
        self.ouryolo_model = None
        self.doclayout_model = None
        self.yolotabledet_model = None
        self.bert_model = None
        self.tokenizer = None

    def _get_device(self) -> torch.device:
        if not settings.DEVICE:
            return torch.device("cuda" if torch.cuda.is_available() else "cpu")

        if settings.DEVICE == "cpu":
            return torch.device("cpu")

        if settings.DEVICE.startswith("gpu:"):
            _, gpu_id = settings.DEVICE.split(":")
            return torch.device(f"cuda:{gpu_id}")

        return torch.device("cuda" if torch.cuda.is_available() else "cpu")

    async def load_models(self):
        """加载所有模型"""
        logger.info(f"开始加载模型，使用设备: {self.device}")
        try:
            # 加载YOLO模型
            logger.info("正在加载YOLO模型...")
            ouryolo_model_path = (
                f"{settings.ASK_DOC_OCR_PATH}{settings.OURYOLO_MODEL_PATH}"
            )
            self.ouryolo_model = YOLO(ouryolo_model_path).to(self.device)
            logger.info(f"OurYOLO模型加载完成: {ouryolo_model_path}")

            doclayout_model_path = (
                f"{settings.ASK_DOC_OCR_PATH}{settings.DOCLAYOUT_MODEL_PATH}"
            )
            self.doclayout_model = YOLOv10(doclayout_model_path).to(self.device)
            logger.info(f"DocLayout模型加载完成:{doclayout_model_path}")

            yolotabledet_model_path = (
                f"{settings.ASK_DOC_OCR_PATH}{settings.YOLO_TABLE_DET_MODEL_PATH}"
            )
            self.yolo_table_det_model = YOLO(yolotabledet_model_path).to(self.device)
            logger.info(f"YOLO表格检测模型加载完成: {yolotabledet_model_path}")

            # 加载BERT模型
            logger.info("正在加载BERT模型...")
            bert_model_path = f"{settings.ASK_DOC_OCR_PATH}/{settings.BERT_MODEL_PATH}/{settings.BERT_MODEL_NAME}"
            self.tokenizer = BertTokenizer.from_pretrained(bert_model_path)
            logger.info(f"BERT tokenizer加载完成: {bert_model_path}")

            self.bert_model = BertClassifier(
                bert_model_path, len(settings.LABEL2ID) // 2, settings.BERT_DROPOUT
            )
            bert_weights_path = f"{settings.ASK_DOC_OCR_PATH}/{settings.BERT_MODEL_PATH}/{settings.BERT_WEIGHTS}"
            self.bert_model.load_state_dict(torch.load(bert_weights_path))
            self.bert_model.to(self.device)
            logger.info(f"BERT模型加载完成: {bert_weights_path}")

            logger.info("所有模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}", exc_info=True)
            raise RuntimeError("模型初始化失败")

    def _convert_to_id(self, text: str) -> Tuple[List[List[int]], int]:
        """将文本转换为token ID"""
        input_ids = []
        for span in get_pos(text):
            tokens = self.tokenizer.tokenize(f"[CLS]{span[0]}[SEP]{span[1]}[SEP]")
            input_ids.append(self.tokenizer.convert_tokens_to_ids(tokens))

        return input_ids, max(len(ii) for ii in input_ids)

    def _convert_tensor(self, text: str) -> Tuple[torch.Tensor, torch.Tensor]:
        """将文本转换为模型输入tensor"""
        input_ids, attention_mask = [], []
        _input_ids, max_len = self._convert_to_id(text)

        for i_i in _input_ids:
            i_i_len = len(i_i)
            input_ids.append(i_i + [0] * (max_len - i_i_len))
            attention_mask.append([1] * i_i_len + [0] * (max_len - i_i_len))

        return (
            torch.tensor(input_ids).to(self.device),
            torch.tensor(attention_mask).to(self.device),
        )

    def _format_text(self, texts: List[str], label: str) -> str:
        """格式化文本输出"""
        res = ""
        for i, sub_text in enumerate(texts):
            if i == 0 or (i > 0 and label[i - 1] == "A"):
                res += sub_text
            else:
                res += f"\n{sub_text}"
        return res.strip()

    async def bert_inference(self, text: str) -> str:
        """BERT模型推理"""
        logger.info(f"开始BERT推理，文本长度: {len(text)} 字符")
        try:
            input_tensor, attention_mask = self._convert_tensor(text)
            logger.debug(f"输入tensor形状: {input_tensor.shape}")

            logits = self.bert_model(input_tensor, attention_mask)
            probabilities = torch.nn.functional.softmax(logits, dim=-1)
            pred = [0 if p[0] >= 0.98 else 1 for p in probabilities.tolist()]
            pred_str = "".join(str(settings.LABEL2ID[str(p)]) for p in pred)

            result = self._format_text(text.split("\n"), pred_str)
            logger.info(f"BERT推理完成，预测序列: {pred_str}")
            return result
        except Exception as e:
            logger.error(f"BERT推理失败: {str(e)}", exc_info=True)
            raise

    async def yolo_inference(
        self, image: np.ndarray, model_type: ModelType, conf: float = 0.5
    ):
        """YOLO模型推理"""
        logger.info(f"开始YOLO推理，模型类型: {model_type}, 图像尺寸: {image.shape}, 置信度阈值: {conf}")
        try:
            if model_type == ModelType.OURYOLO.value:
                model = self.ouryolo_model
                model_name = "OurYOLO"
            elif model_type == ModelType.DOCLAYOUT.value:
                model = self.doclayout_model
                model_name = "DocLayout"
            elif model_type == ModelType.YOLOTABLEDET.value:
                model = self.yolo_table_det_model
                model_name = "YOLO表格检测"
            else:
                raise ValueError(f"无效的模型类型: {model_type}")

            logger.info(f"使用{model_name}模型进行推理")
            results = model(image, conf=conf)[0]

            # 统计检测结果
            detection_count = len(results.boxes) if results.boxes is not None else 0
            logger.info(f"{model_name}推理完成，检测到 {detection_count} 个目标")

            return results
        except Exception as e:
            logger.error(f"YOLO推理失败: {str(e)}", exc_info=True)
            raise
