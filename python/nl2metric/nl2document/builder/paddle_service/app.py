from fastapi import FastAPI, HTTPException, Depends
from models import ImageRequest, ImageResponse
from services import PaddleModelService
from utils import base64_to_numpy
from config import settings
from logging_config import setup_logging, get_logger

# 设置日志配置
setup_logging(
    log_level=settings.LOG_LEVEL,
    log_dir=settings.LOG_DIR,
    service_name="paddle_service",
    max_bytes=settings.LOG_MAX_BYTES,
    backup_count=settings.LOG_BACKUP_COUNT,
    console_output=settings.LOG_TO_CONSOLE,
    file_output=settings.LOG_TO_FILE,
)
logger = get_logger(__name__)

app = FastAPI(title="PaddleOCR图像检测服务")
model_service = PaddleModelService()


async def get_model_service() -> PaddleModelService:
    return model_service


@app.on_event("startup")
async def startup_event():
    """服务启动时加载模型"""
    logger.info("=== PaddleOCR服务启动 ===")
    logger.info(f"日志级别: {settings.LOG_LEVEL}")
    logger.info(f"日志目录: {settings.LOG_DIR}")
    logger.info(f"设备配置: {settings.DEVICE or 'auto'}")
    logger.info(f"GPU使用: {settings.USE_GPU}")

    try:
        await model_service.load_models()
        logger.info("=== 所有模型加载完成，服务就绪 ===")
    except Exception as e:
        logger.error(f"=== 服务启动失败: {str(e)} ===")
        raise


@app.post("/paddle-ocr/inference", response_model=ImageResponse)
async def paddle_ocr_inference(
    request: ImageRequest, service: PaddleModelService = Depends(get_model_service)
) -> ImageResponse:
    """PaddleOCR推理

    Args:
        request: 包含base64编码图片的请求
        service: 模型服务实例

    Returns:
        ImageResponse: OCR识别结果

    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        # 将base64转换为numpy数组
        image_array = base64_to_numpy(request.image)

        # 使用PaddleOCR进行推理
        results = await service.paddle_ocr_inference(image_array)

        return ImageResponse(data={"ocr_results": results})

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"处理图片时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/table-det/inference", response_model=ImageResponse)
async def table_det_inference(
    request: ImageRequest, service: PaddleModelService = Depends(get_model_service)
) -> ImageResponse:
    """表格检测推理

    Args:
        request: 包含base64编码图片的请求
        service: 模型服务实例

    Returns:
        ImageResponse: 表格检测结果

    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        # 将base64转换为numpy数组
        image_array = base64_to_numpy(request.image)

        # 使用表格检测模型进行推理
        results = await service.table_det_inference(image_array)

        return ImageResponse(data={"ocr_results": results})

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"处理表格检测时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/table-cell-det/inference", response_model=ImageResponse)
async def table_cell_det_inference(
    request: ImageRequest, service: PaddleModelService = Depends(get_model_service)
) -> ImageResponse:
    """表格单元格检测推理

    Args:
        request: 包含base64编码图片的请求
        service: 模型服务实例

    Returns:
        ImageResponse: 表格单元格检测结果

    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        # 将base64转换为numpy数组
        image_array = base64_to_numpy(request.image)

        # 使用表格单元格检测模型进行推理
        results = await service.table_cell_det_inference(
            image_array, request.threshold, request.batch_size
        )

        return ImageResponse(data={"ocr_results": results})

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"处理表格单元格检测时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8001)
