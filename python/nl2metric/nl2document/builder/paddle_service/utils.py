import base64
import re
from typing import List, <PERSON><PERSON>, Union
import cv2
import numpy as np
import string

from models import OCRResult


def img2base64(img_path: str) -> <PERSON><PERSON>[str, str]:
    """将图片文件转换为base64字符串

    Args:
        img_path: 图片文件路径

    Returns:
        Tuple[str, str]: (base64字符串, 图片路径)
    """
    with open(img_path, "rb") as image_file:
        base64_image = base64.b64encode(image_file.read()).decode("utf-8")
    return base64_image, img_path


def remove_punctuation(text: str, remove_symbol: bool = False) -> str:
    if not remove_symbol:
        return text

    printable = string.digits + string.ascii_letters
    texts = []
    for char in text:
        if "\u4e00" <= char <= "\u9fa5" or char in printable:
            texts.append(char)

    return "".join(texts)


def base64_to_numpy(base64_image: str) -> np.ndarray:
    """将base64字符串转换为numpy数组

    Args:
        base64_image: base64编码的图片字符串

    Returns:
        np.ndarray: 解码后的图片数组

    Raises:
        ValueError: 当base64解码失败时
    """
    try:
        # 移除base64头信息(如果存在)
        if "," in base64_image:
            base64_image = base64_image.split(",")[1]

        # 解码base64字符串
        image_data = base64.b64decode(base64_image)
        nparr = np.frombuffer(image_data, np.uint8)
        image_decoded = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image_decoded is None:
            raise ValueError("Failed to decode image")

        return image_decoded

    except Exception as e:
        raise ValueError(f"Failed to convert base64 to image: {str(e)}")


def cal_rect_area(point: Union[str, Tuple[int, int, int, int]]) -> float:
    """
    计算矩形的面积
    Args:
        x1,y1,x2,y2: 分别是矩形左上角和右下角的坐标
    """
    if isinstance(point, str):
        x1, y1, x2, y2 = str2nums(point)
    else:
        x1, y1, x2, y2 = point

    return (x2 - x1) * (y2 - y1)


def str2nums(string: str) -> Union[List[int], List[float]]:
    texts = [s for s in re.split(r"[^0-9.]+", string) if s]

    if texts[0].isdigit():
        nums = [int(d) for d in texts]
    else:
        nums = [float(d) for d in texts]

    return nums


def nums2str(nums: Union[List, Tuple], is_float: bool = False) -> str:
    """
    将数字列表转成字符串

    Args:
        nums:
        is_float: 是否为浮点数, 默认为整数
    """
    if is_float:
        nums = [float(num) for num in nums]
    else:
        nums = [int(num) for num in nums]

    return str(nums)


def get_ocrs(
    texts: List[str], bboxs: List[Tuple], confs: List[float]
) -> List[OCRResult]:
    res = []
    for text, bbox, confidence in zip(texts, bboxs, confs):
        res.append(
            OCRResult(
                bbox=nums2str(bbox),
                origin_text=text,
                text=remove_punctuation(text, True),
                font=20,
                confidence=confidence,
                label="table_text",
            )
        )

    return res


def crop(input: np.ndarray, bboxs: List[List]) -> List[np.ndarray]:
    """根据bbox将图片切割成N份"""
    res = []
    for x1, y1, x2, y2 in bboxs:
        res.append(input[y1:y2, x1:x2])

    return res
