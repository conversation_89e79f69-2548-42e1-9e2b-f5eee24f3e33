try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # 模型路径配置
    ASK_DOC_OCR_PATH: str = "/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR/"
    TABLE_RECOGNITION_PIPELINE_PATH: str = (
        "runs/detect/train39/weights/table_recognition_v2.yaml"
    )
    TABLE_CELL_DET_MODEL_NAME: str = "RT-DETR-L_wired_table_cell_det"
    TABLE_CELL_DET_MODEL_DIR: str = "runs/detect/train39/weights/table_cell_det/"

    # PaddleOCR配置
    PADDLE_OCR_LANG: str = "ch"
    PADDLE_OCR_USE_ANGLE_CLS: bool = True

    # 设备配置
    DEVICE: Optional[str] = None
    USE_GPU: bool = False
    GPU_ID: int = 0

    # 默认参数
    DEFAULT_THRESHOLD: float = 0.5
    DEFAULT_BATCH_SIZE: int = 1
    DEFAULT_CONFIDENCE: float = 0.5

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "logs"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    LOG_TO_CONSOLE: bool = True
    LOG_TO_FILE: bool = True

    class Config:
        env_file = ".env"


settings = Settings()
