# PaddleOCR服务配置示例
# 复制此文件为 .env 并根据需要修改配置

# 模型路径配置
ASK_DOC_OCR_PATH="/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR/"
TABLE_RECOGNITION_PIPELINE_PATH="runs/detect/train39/weights/table_recognition_v2.yaml"
TABLE_CELL_DET_MODEL_NAME=RT-DETR-L_wired_table_cell_det
TABLE_CELL_DET_MODEL_DIR="runs/detect/train39/weights/table_cell_det/"

# PaddleOCR配置
PADDLE_OCR_LANG=ch
PADDLE_OCR_USE_ANGLE_CLS=true

# 设备配置
DEVICE=cpu
USE_GPU=false
GPU_ID=0

# 默认参数
DEFAULT_THRESHOLD=0.5
DEFAULT_BATCH_SIZE=1
DEFAULT_CONFIDENCE=0.5

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
LOG_TO_CONSOLE=true
LOG_TO_FILE=true
