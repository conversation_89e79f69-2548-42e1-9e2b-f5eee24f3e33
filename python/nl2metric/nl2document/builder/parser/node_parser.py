import copy
import json
import re
from typing import Dict, <PERSON><PERSON>, List

from llama_index.core.node_parser import TokenTextSplitter
from transformers import AutoTokenizer

from common.logging.logger import get_logger, get_elk_logger
from config.app_config import general_parser_chunk_size, general_parser_chunk_overlap
from config.doc_config import (
    embedding_model_path,
    ask_doc_index_temp_file_dir,
    ask_doc_index_doc_table_split,
    ask_doc_merge_sub_chapters,
    ask_doc_index_doc_table_row_split,
    ask_doc_index_excel_split,
    deployMode,
    DeployMode,
)
from nl2document.builder.parser.parsed_document import ContentType

from llama_index.core.schema import (
    BaseNode,
    NodeRelationship,
    TextNode,
)

# metadata 关键词
from nl2document.common.base.const import (
    LLAMA_INDEX_PAGE_LABLE,
    LLAMA_INDEX_FILE_NAME,
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_FOLDER_ID,
    LLAMA_INDEX_META_PART_NAME,
    LLAMA_INDEX_META_SORT_ID,
    LLAMA_INDEX_META_CONTENT_TYPE,
    LLAMA_INDEX_CONFERENCE_CONTENT_TYPE,
    LLAMA_INDEX_META_CONTENT_TABLE_DESCRIPTION,
)

logger = get_logger(__name__)
elk_logger = get_elk_logger()

# tokenizer模型
tokenizer = AutoTokenizer.from_pretrained(embedding_model_path)


class Tokenized:
    def __call__(self, text):
        return tokenizer(text)["input_ids"]


# nodes切分处理 把解析结果json切分处理成nodes
class GeneralNodeParser:
    # nodes的排序id号
    def __init__(
        self,
        chunk_sort_id: int = 0,
        chunk_size: int = general_parser_chunk_size,
        chunk_overlap: int = general_parser_chunk_overlap,
        table_chunk_size: int = 4096 * 6,
        table_chunk_overlap: int = 100,
    ):
        self.chunk_sort_id = chunk_sort_id
        self._splitter = TokenTextSplitter(
            tokenizer=Tokenized(),
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
        )
        self._table_splitter = TokenTextSplitter(
            tokenizer=Tokenized(),
            chunk_size=table_chunk_size,
            chunk_overlap=table_chunk_overlap,
        )

    """
    把解析结果json切分处理成nodes
    Args:
        json_datas: json文件的内容数据
        file_id: 文件的id
        folder_id: 文件所在目录的id
    Return:
        root_nodes: 根节点 List[BaseNode]
        nodes: 内容节点 List[BaseNode]
    Exception:
        若json文件的内容数据的结构不符合要求，则抛出错误异常
    """

    def get_nodes_from_json(
        self, json_datas: Dict, file_id: str, folder_id: str
    ) -> Tuple[List[BaseNode], List[BaseNode]]:
        if "chapters" not in json_datas:
            extra_info = {"file_id": file_id}
            logger.exception(
                msg="json file structure not suit node_parser", extra=extra_info
            )
            elk_logger.info(
                msg="json file structure not suit node_parser", extra=extra_info
            )
            raise RuntimeError(
                "file_name: {} file_id: {} it's json file structure not suit node_parser".format(
                    json_datas.get("file_name", ""), file_id
                )
            )

        root_node = TextNode()
        root_node.relationships = {}
        root_node.metadata = {}
        root_node.metadata[LLAMA_INDEX_FILE_NAME] = json_datas.get("file_name", "")
        root_node.metadata[LLAMA_INDEX_FILE_ID] = file_id
        root_node.metadata[LLAMA_INDEX_FOLDER_ID] = folder_id

        node_list = []
        if json_datas.get("file_name", "").split(".")[-1].lower() in ["xlsx"]:
            if ask_doc_index_excel_split:
                # excel文档解析结果 按行切分处理nodes
                self.excel_traverse_parts_by_row(
                    root_node, json_datas.get("chapters", []), node_list
                )
            else:
                # excel文档解析结果 按sheet切分处理nodes
                self.excel_traverse_parts_by_sheet(
                    root_node, json_datas.get("chapters", []), node_list
                )
        else:  # 正常文档解析结果 按标题层级切分处理nodes
            self.traverse_parts(
                root_node, json_datas.get("chapters", []), node_list, []
            )
        # 保存处理结果node_list
        self.save_nodes(json_datas.get("file_name", ""), node_list)

        return [root_node], node_list

    """
    将处理好的nodes结果保存为json文件，方便查看
    Args:
        file_name: 文件名
        nodes: 处理好的nodes结果 List[BaseNode]
    """

    def save_nodes(self, file_name: str, nodes):
        if ask_doc_index_temp_file_dir:
            file_path = ask_doc_index_temp_file_dir + "/" + file_name + ".nodes.json"
            nodes_info = []
            for node in nodes:
                node_info = {
                    "node_id": node.node_id,
                    "content": node.get_content(),
                    "metadata": node.get_metadata_str(),
                }
                nodes_info.append(node_info)

            with open(file_path, "w", encoding="utf-8") as file:
                json.dump(nodes_info, file, indent=4, ensure_ascii=False)

    """
    excel文档解析结果处理成nodes 按sheet切分nodes，比较大的话会按table_chunk_size切分处理
    Args:
        root_node: 根节点 TextNode
        chapters: 某个标题下的章节内容 List
        node_list: 处理好的nodes结果 List[BaseNode] 这个其实是返回结果
    """

    def excel_traverse_parts_by_sheet(
        self, root_node: TextNode, chapters: List, node_list: List
    ):
        sheet_number = 0
        for chapter in chapters:
            sheet_texts = []
            sheet_number += 1
            part_name = (
                chapter.get("catalog").get("chapter_title", "")
                if chapter.get("catalog", None)
                else ""
            )

            split_table_texts = []
            contents = chapter.get("contents", [])
            last_content_type = ContentType.EXCEL_TEXT.name  # 上一个content的类型
            excel_title_row_num = 0
            is_all_title = True
            for content in contents:
                excel_row_texts = content.get("text", "").split("\t")
                content_type = content.get("content_type", "")
                if all(excel_row_text == "None" for excel_row_text in excel_row_texts):
                    continue
                if content_type == ContentType.EXCEL_TITLE.name:
                    if last_content_type == ContentType.EXCEL_TEXT.name and sheet_texts:
                        # 上下分表
                        if not is_all_title:
                            split_table_texts.extend(
                                self.split_table_json_text(
                                    json.dumps(sheet_texts, ensure_ascii=False),
                                    excel_title_row_num,
                                )
                            )
                        else:
                            split_table_texts.append(
                                json.dumps(sheet_texts, ensure_ascii=False)
                            )
                        sheet_texts = []
                        excel_title_row_num = 0
                        is_all_title = True
                    excel_title_row_num += 1
                else:
                    is_all_title = False
                last_content_type = content_type
                sheet_texts.append(excel_row_texts)
            if sheet_texts:
                if not is_all_title:
                    split_table_texts.extend(
                        self.split_table_json_text(
                            json.dumps(sheet_texts, ensure_ascii=False),
                            excel_title_row_num,
                        )
                    )
                else:
                    split_table_texts.append(
                        json.dumps(sheet_texts, ensure_ascii=False)
                    )
            for split_table_text in split_table_texts:
                content_node = TextNode()
                content_node.text = split_table_text
                content_node.relationships = {}
                content_node.metadata = {}
                content_node.metadata[
                    LLAMA_INDEX_META_CONTENT_TYPE
                ] = ContentType.EXCEL_TEXT.name
                if deployMode == DeployMode.cmcc:
                    content_node.metadata[
                        LLAMA_INDEX_CONFERENCE_CONTENT_TYPE
                    ] = "ordinary_document"
                content_node.metadata[LLAMA_INDEX_META_PART_NAME] = part_name
                content_node.metadata[LLAMA_INDEX_FILE_NAME] = root_node.metadata[
                    LLAMA_INDEX_FILE_NAME
                ]
                content_node.metadata[LLAMA_INDEX_FILE_ID] = str(
                    root_node.metadata[LLAMA_INDEX_FILE_ID]
                )
                content_node.metadata[LLAMA_INDEX_FOLDER_ID] = str(
                    root_node.metadata[LLAMA_INDEX_FOLDER_ID]
                )
                content_node.metadata[LLAMA_INDEX_PAGE_LABLE] = str(sheet_number)
                content_node.metadata[LLAMA_INDEX_META_SORT_ID] = self.chunk_sort_id + 1
                self.chunk_sort_id += 1
                node_list.append(content_node)
        # add relationship
        for index, content_node in enumerate(node_list):
            if index != 0:
                content_node.relationships[NodeRelationship.PREVIOUS] = node_list[
                    index - 1
                ].as_related_node_info()
                # print(f"prev node {content_nodes[index-1].as_related_node_info()}")
            if index != len(node_list) - 1:
                content_node.relationships[NodeRelationship.NEXT] = node_list[
                    index + 1
                ].as_related_node_info()
                # print(f"next node {content_nodes[index+1].as_related_node_info()}")

    """
    excel文档解析结果处理成nodes 按行切分nodes
    Args:
        root_node: 根节点 TextNode
        chapters: 某个标题下的章节内容 List
        node_list: 处理好的nodes结果 List[BaseNode] 这个其实是返回结果
    """

    def excel_traverse_parts_by_row(
        self, root_node: TextNode, chapters: List, node_list: List
    ):
        for chapter in chapters:
            content_nodes = []

            part_name = (
                chapter.get("catalog").get("chapter_title", "")
                if chapter.get("catalog", None)
                else ""
            )
            excel_titles = []  # 所有的标题
            contents = chapter.get("contents", [])
            last_content_type = ContentType.EXCEL_TEXT.name  # 上一个content的类型
            is_all_excel_title = True  # 所有行都是excel_title
            for content in contents:
                # 标题 和 内容都是使用't' 来连接单元格的内容
                if content.get("content_type", "") == ContentType.EXCEL_TITLE.name:
                    if last_content_type == ContentType.EXCEL_TEXT.name:  # 清除前面的文本内容的标题
                        excel_titles = []
                    excel_titles.append(content.get("text", "").split("\t"))
                    last_content_type = ContentType.EXCEL_TITLE.name
                elif content.get("content_type", "") == ContentType.EXCEL_TEXT.name:
                    is_all_excel_title = False
                    last_content_type = ContentType.EXCEL_TEXT.name
                    excel_texts = content.get("text", "").split("\t")
                    if all(excel_text == "None" for excel_text in excel_texts):
                        continue
                    excel_row_number = content.get("extra_info").get("real_page_num")

                    content_node = TextNode()
                    if len(excel_titles) > 0:  # 有表头
                        row_content = {}
                        for column_num in range(len(excel_titles[0])):
                            column_name = ""
                            for i in range(len(excel_titles)):
                                if excel_titles[i][column_num] != "None":
                                    if (
                                        column_name
                                        and excel_titles[i][column_num]
                                        not in column_name
                                    ):
                                        column_name = (
                                            column_name
                                            + "_"
                                            + excel_titles[i][column_num]
                                        )
                                    else:
                                        column_name = excel_titles[i][column_num]
                            if column_name not in row_content:
                                row_content[column_name] = excel_texts[column_num]
                            else:
                                if row_content[column_name] != excel_texts[column_num]:
                                    row_content[column_name] = (
                                        row_content[column_name]
                                        + "_"
                                        + excel_texts[column_num]
                                    )
                        content_node.text = json.dumps(row_content, ensure_ascii=False)
                    else:  # 没有表头
                        content_node.text = content.get("text", "")
                    # 处理node
                    content_node.relationships = {}
                    content_node.metadata = {}
                    content_node.metadata[
                        LLAMA_INDEX_META_CONTENT_TYPE
                    ] = ContentType.EXCEL_TEXT.name
                    if deployMode == DeployMode.cmcc:
                        content_node.metadata[
                            LLAMA_INDEX_CONFERENCE_CONTENT_TYPE
                        ] = "ordinary_document"
                    content_node.metadata[LLAMA_INDEX_META_PART_NAME] = (
                        root_node.metadata[LLAMA_INDEX_FILE_NAME] + "-" + part_name
                    )
                    content_node.metadata[LLAMA_INDEX_FILE_NAME] = root_node.metadata[
                        LLAMA_INDEX_FILE_NAME
                    ]
                    content_node.metadata[LLAMA_INDEX_FILE_ID] = str(
                        root_node.metadata[LLAMA_INDEX_FILE_ID]
                    )
                    content_node.metadata[LLAMA_INDEX_FOLDER_ID] = str(
                        root_node.metadata[LLAMA_INDEX_FOLDER_ID]
                    )
                    content_node.metadata[LLAMA_INDEX_PAGE_LABLE] = str(
                        excel_row_number
                    )
                    content_node.metadata[LLAMA_INDEX_META_SORT_ID] = (
                        self.chunk_sort_id + 1
                    )
                    self.chunk_sort_id += 1
                    content_nodes.append(content_node)
            if is_all_excel_title:  # 如果所有行都是excel_title
                for row_index, excel_title in enumerate(excel_titles):
                    column_texts = []
                    for column_text in excel_title:
                        if not column_text or column_text not in column_texts:
                            column_texts.append(column_text)
                    content_node = TextNode()
                    # 处理node
                    content_node.text = "\t".join(column_texts)
                    content_node.relationships = {}
                    content_node.metadata = {}
                    content_node.metadata[
                        LLAMA_INDEX_META_CONTENT_TYPE
                    ] = ContentType.EXCEL_TEXT.name
                    if deployMode == DeployMode.cmcc:
                        content_node.metadata[
                            LLAMA_INDEX_CONFERENCE_CONTENT_TYPE
                        ] = "ordinary_document"
                    content_node.metadata[LLAMA_INDEX_META_PART_NAME] = (
                        root_node.metadata[LLAMA_INDEX_FILE_NAME] + "-" + part_name
                    )
                    content_node.metadata[LLAMA_INDEX_FILE_NAME] = root_node.metadata[
                        LLAMA_INDEX_FILE_NAME
                    ]
                    content_node.metadata[LLAMA_INDEX_FILE_ID] = str(
                        root_node.metadata[LLAMA_INDEX_FILE_ID]
                    )
                    content_node.metadata[LLAMA_INDEX_FOLDER_ID] = str(
                        root_node.metadata[LLAMA_INDEX_FOLDER_ID]
                    )
                    content_node.metadata[LLAMA_INDEX_PAGE_LABLE] = str(row_index + 1)
                    content_node.metadata[LLAMA_INDEX_META_SORT_ID] = (
                        self.chunk_sort_id + 1
                    )
                    self.chunk_sort_id += 1
                    content_nodes.append(content_node)
            # add relationship
            for index, content_node in enumerate(content_nodes):
                if index != 0:
                    content_node.relationships[
                        NodeRelationship.PREVIOUS
                    ] = content_nodes[index - 1].as_related_node_info()
                    # print(f"prev node {content_nodes[index-1].as_related_node_info()}")
                if index != len(content_nodes) - 1:
                    content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                        index + 1
                    ].as_related_node_info()
                    # print(f"next node {content_nodes[index+1].as_related_node_info()}")
                node_list.append(content_node)

    """
    把chapters切分处理成nodes,并把结果塞到node_list里面
    Args:
        root_node: 根节点 TextNode
        chapters: 某个标题下的章节内容 List
        node_list: 处理好的nodes结果 List[BaseNode] 这个其实是返回结果
        part_name_list: 前面层级已有的标题part_name List
    """

    def traverse_parts(
        self, root_node: TextNode, chapters: List, node_list: List, part_name_list: List
    ):
        for chapter in chapters:
            part_catalog = chapter.get("catalog", None)
            if part_catalog:
                chapter_title = part_catalog.get("chapter_title", "")
                chapter_subtitle = part_catalog.get("chapter_subtitle", "")
                if chapter_subtitle:
                    if chapter_title:
                        chapter_title += "_" + chapter_subtitle
                    else:
                        chapter_title = chapter_subtitle
            else:
                chapter_title = ""
            tmp_part_name_list = list(part_name_list)
            if chapter_title:
                tmp_part_name_list.append(chapter_title)
            part_name_str = ""
            # 使用"_"将已有的标题拼接起来作为part_name
            for index, part_name in enumerate(tmp_part_name_list):
                if index != 0:
                    part_name_str += "_"
                part_name_str += part_name
            # 处理章节下的内容
            self.traverse_contents(
                root_node, chapter.get("contents", []), node_list, part_name_str
            )
            # 处理章节下的子章节
            self.traverse_parts(
                root_node, chapter.get("chapters", []), node_list, tmp_part_name_list
            )
            # 针对短小的文本内容node做合并处理
            if ask_doc_merge_sub_chapters:
                self.merge_chpters(root_node, node_list, part_name_str)  # 子章节内容少就合到一起

    """
    把短小的文本内容node合并处理成一个node
    Args:
        node_list: 处理好的nodes结果 List[BaseNode] 这个其实是返回结果
        part_name_str: 前面层级已有的标题使用"_"拼起来的part_name String
    """

    def merge_chpters(self, root_node: TextNode, node_list: List, part_name_str: str):
        chpter_nodes_index = []
        is_chapters_all_text = True
        for node_index, node in enumerate(node_list):
            node_part_name = node.metadata[LLAMA_INDEX_META_PART_NAME]
            node_content_type = node.metadata[LLAMA_INDEX_META_CONTENT_TYPE]
            if part_name_str in node_part_name and part_name_str != node_part_name:
                chpter_nodes_index.append(node_index)
                if node_content_type != ContentType.TEXT.name:
                    is_chapters_all_text = False
        if is_chapters_all_text and chpter_nodes_index:
            part_name_list = part_name_str.split("_")
            all_chapters_text = ""
            for chpter_node_index in chpter_nodes_index:
                chpter_node = node_list[chpter_node_index]
                chpter_node_part_name = chpter_node.metadata[LLAMA_INDEX_META_PART_NAME]
                chpter_node_content = chpter_node.text
                # 把标题合并到内容里面 使用'\n'拼接
                for title in chpter_node_part_name.split("_"):
                    if title not in part_name_list and title not in all_chapters_text:
                        all_chapters_text = (
                            all_chapters_text + "\n" + title
                            if all_chapters_text
                            else title
                        )
                if chpter_node_content:
                    all_chapters_text = all_chapters_text + "\n" + chpter_node_content
            # 只有拼接后的内容少于chunk_size才会真的合并
            if (
                len(self._splitter._tokenizer(all_chapters_text))
                < self._splitter.chunk_size
            ):
                node_list[chpter_nodes_index[0]].text = all_chapters_text
                node_list[chpter_nodes_index[0]].metadata[
                    LLAMA_INDEX_META_PART_NAME
                ] = part_name_str
                node_list[chpter_nodes_index[0]].relationships.pop(
                    NodeRelationship.NEXT, None
                )
                for _ in range(len(chpter_nodes_index) - 1):
                    node_list.pop()
                    self.chunk_sort_id -= 1
        # 如果part_name_str不在node_list出现过(表面该标题下既没有正文也没有带有正文的子章节)

        if all(
            part_name_str not in node_part_name
            for node_part_name in [
                node.metadata[LLAMA_INDEX_META_PART_NAME] for node in node_list
            ]
        ):
            part_name_list = part_name_str.split("_")
            content_node = TextNode()
            # 处理node
            content_node.text = part_name_list[-1]
            content_node.relationships = {}
            content_node.metadata = {}
            content_node.metadata[LLAMA_INDEX_META_CONTENT_TYPE] = ContentType.TEXT.name
            if deployMode == DeployMode.cmcc:
                content_node.metadata[
                    LLAMA_INDEX_CONFERENCE_CONTENT_TYPE
                ] = "ordinary_document"
            content_node.metadata[LLAMA_INDEX_META_PART_NAME] = "_".join(
                part_name_list[:-1] if len(part_name_list) > 1 else [""]
            )
            content_node.metadata[LLAMA_INDEX_FILE_NAME] = root_node.metadata[
                LLAMA_INDEX_FILE_NAME
            ]
            content_node.metadata[LLAMA_INDEX_FILE_ID] = str(
                root_node.metadata[LLAMA_INDEX_FILE_ID]
            )
            content_node.metadata[LLAMA_INDEX_FOLDER_ID] = str(
                root_node.metadata[LLAMA_INDEX_FOLDER_ID]
            )
            content_node.metadata[LLAMA_INDEX_PAGE_LABLE] = str(
                node_list[-1].metadata[LLAMA_INDEX_PAGE_LABLE] if node_list else 1
            )
            content_node.metadata[LLAMA_INDEX_META_SORT_ID] = self.chunk_sort_id + 1
            self.chunk_sort_id += 1
            node_list.append(content_node)
        # 合并相同标题下的内容
        for node_index, node in enumerate(node_list):
            node_part_name = node.metadata[LLAMA_INDEX_META_PART_NAME]
            node_content_type = node.metadata[LLAMA_INDEX_META_CONTENT_TYPE]
            if (
                node_part_name == part_name_str
                and node_content_type == ContentType.TEXT.name
                and node_index < len(node_list) - 1
                and node_list[node_index + 1].metadata[LLAMA_INDEX_META_PART_NAME]
                == part_name_str
                and node_list[node_index + 1].metadata[LLAMA_INDEX_META_CONTENT_TYPE]
                == ContentType.TEXT.name
            ):
                # 只有拼接后的内容少于chunk_size才会真的合并
                if (
                    len(
                        self._splitter._tokenizer(
                            node.text + "\n" + node_list[node_index + 1].text
                        )
                    )
                    < self._splitter.chunk_size
                ):
                    node.text = node.text + "\n" + node_list[node_index + 1].text
                    if node_index < len(node_list) - 2:
                        node.relationships[NodeRelationship.NEXT] = node_list[
                            node_index + 2
                        ].as_related_node_info()
                        node_list[node_index + 2].relationships[
                            NodeRelationship.PREVIOUS
                        ] = node.as_related_node_info()
                        # sort_id
                        for following_node in node_list[node_index + 2 :]:
                            following_node.metadata[LLAMA_INDEX_META_SORT_ID] -= 1
                    else:
                        node.relationships.pop(NodeRelationship.NEXT, None)
                    node_list.pop(node_index + 1)
                    self.chunk_sort_id -= 1

    """
    对章节的内容做split切分，处理成node塞到node_list里面
    Args:
        root_node: 根节点 TextNode
        contents: 章节的内容 List
        node_list: 处理好的nodes结果 List[BaseNode] 这个其实是返回结果
        part_name: 标题 String
    """

    def traverse_contents(
        self, root_node: TextNode, contents: List, node_list: List, part_name: str
    ):
        # print(f"traverse content part name {part_name}")
        split_contents = self.get_split_contents(contents)  # 对章节的内容做split切分
        content_nodes = []
        for content in split_contents:
            content_type = content.get("type", "")
            content_node = TextNode()
            content_node.text = content.get("text", "")
            content_node.relationships = {}

            # add meta
            content_node.metadata = {}
            now_part_name = part_name
            if content_type == ContentType.TABLE.name:
                table_title = content.get("table_title", "")
                if table_title:
                    now_part_name += "_" + table_title
                table_description = content.get("table_description", "")
                if table_description:
                    content_node.metadata[
                        LLAMA_INDEX_META_CONTENT_TABLE_DESCRIPTION
                    ] = table_description

            # print(f"node part name {now_part_name}")
            content_node.metadata[LLAMA_INDEX_META_CONTENT_TYPE] = content_type
            content_node.metadata[
                LLAMA_INDEX_CONFERENCE_CONTENT_TYPE
            ] = "ordinary_document"
            content_node.metadata[LLAMA_INDEX_META_PART_NAME] = now_part_name
            content_node.metadata[LLAMA_INDEX_FILE_NAME] = root_node.metadata[
                LLAMA_INDEX_FILE_NAME
            ]
            content_node.metadata[LLAMA_INDEX_FILE_ID] = str(
                root_node.metadata[LLAMA_INDEX_FILE_ID]
            )
            content_node.metadata[LLAMA_INDEX_FOLDER_ID] = str(
                root_node.metadata[LLAMA_INDEX_FOLDER_ID]
            )
            content_node.metadata[LLAMA_INDEX_PAGE_LABLE] = str(content.get("page", ""))
            content_node.metadata[LLAMA_INDEX_META_SORT_ID] = self.chunk_sort_id + 1
            self.chunk_sort_id += 1
            content_nodes.append(content_node)

        # add relationship
        for index, content_node in enumerate(content_nodes):
            if index != 0:
                content_node.relationships[NodeRelationship.PREVIOUS] = content_nodes[
                    index - 1
                ].as_related_node_info()
                # print(f"prev node {content_nodes[index-1].as_related_node_info()}")
            if index != len(content_nodes) - 1:
                content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                    index + 1
                ].as_related_node_info()
                # print(f"next node {content_nodes[index+1].as_related_node_info()}")
            node_list.append(content_node)
            # print(f"content node: {content_node}, part name {content_node.metadata[LLAMA_INDEX_META_PART_NAME]}")

    """
    对跨页章节的内容做合并处理
    Args:
        contents: 章节的内容 List
    Return:
        merged_contents: 合并后章节的内容 List
    """

    def get_merged_contents(self, contents: []) -> []:
        merged_contents = []
        last_page = 0
        text_merged_page = 0
        for content in contents:
            content_type = content.get("content_type", "")
            content_page = self.get_page_number_new(content)
            if content_type == ContentType.TEXT.name:
                content_text = content.get("text", "")
                if (
                    last_page > 0
                    and last_page + 1 == content_page
                    and text_merged_page != content_page
                    and merged_contents[-1].get("content_type") == ContentType.TEXT.name
                ):
                    if merged_contents[-1].get("text", "") and not merged_contents[
                        -1
                    ].get("text", "").endswith("。"):
                        merged_contents[-1]["text"] = (
                            merged_contents[-1]["text"] + "\n" + content_text
                        )
                        last_page = content_page
                        text_merged_page = content_page  # 该页文本已和上页合并
                        continue
                last_page = content_page
            elif content_type == ContentType.TABLE.name:
                content_table_title = content.get("table", "").get("table_title", "")
                if not content.get("table", "").get("table_text", ""):
                    logger.error(
                        f"page {content_page} table title {content_table_title} content is empty."
                    )
                    # content = Content(ContentType.TEXT, content_table_title, content_page)
                    # merged_contents.append(content.to_json())
                    # last_page = content_page
                    continue
                content_table_text = (
                    content.get("table", "").get("table_text", "").get("html", "")
                    if content.get("table", "").get("table_text", "")
                    else ""
                )
                if (
                    last_page > 0
                    and (
                        (
                            last_page + 1 == content_page
                            and text_merged_page != content_page
                        )
                        or (last_page == content_page == 1)
                    )
                    and not content_table_title
                    and merged_contents[-1].get("content_type")
                    == ContentType.TABLE.name
                ):
                    if merged_contents[-1]["table"]["table_text"]:
                        if not is_json_table(content_table_text):
                            merged_contents[-1]["table"]["table_text"]["html"] += (
                                "\n" + content_table_text
                            )
                        else:
                            pre_contents = json.loads(
                                merged_contents[-1]["table"]["table_text"]["html"]
                            )
                            now_contents = json.loads(content_table_text)
                            for now_content in now_contents:
                                pre_contents.append(now_content)
                            merged_contents[-1]["table"]["table_text"][
                                "html"
                            ] = json.dumps(pre_contents, ensure_ascii=False)
                    else:
                        merged_contents[-1]["table"]["table_text"][
                            "html"
                        ] = content_table_text
                    last_page = content_page
                    text_merged_page = content_page  # 该页表格已和上页合并
                    continue
                last_page = content_page
            merged_contents.append(content)
        return merged_contents

    """
    对表格内容按行做split切分
    Args:
        table_text: 表格内容  可能是数组 可能是html
    Return:
        table_texts: 按行拆分后的数组 只有table_text是数组才会按行拆分
    """

    def get_splitted_table_text(self, table_text: str) -> []:
        table_texts = []
        if is_json_table(table_text):  # 表格内容是json数组
            if not ask_doc_index_doc_table_row_split:
                # 不按行拆分处理
                table_texts.extend(self.split_table_json_text(table_text))
            else:
                # 按行拆分处理
                table_text_json = json.loads(table_text)
                row_title = table_text_json[0]
                if len(table_text_json) > 1:
                    for row in table_text_json[1:]:
                        row_and_title = [row_title, row]
                        table_texts.append(
                            json.dumps(row_and_title, ensure_ascii=False)
                        )
                else:
                    table_texts.append(json.dumps(row_title, ensure_ascii=False))
        else:
            table_texts.extend(self.split_tables_only(table_text))
        return table_texts

    """
    对章节的内容做split切分
    Args:
        contents: 章节的内容 List
    Return:
        split_contents: 切分处理后的章节内容 List
    """

    def get_split_contents(self, contents: []) -> []:
        split_contents = []
        # 跨页合并处理
        merged_contents = self.get_merged_contents(copy.deepcopy(contents))

        pre_text = ""
        pre_text_page = 0  # 开头内容的页码
        pre_table_text = ""
        pre_table_description = ""
        pre_table_title = ""
        pre_table_page = 0
        for content in merged_contents:
            content_type = content.get("content_type", "")
            if not content_type:
                continue
            # 文本内容
            elif content_type == ContentType.TEXT.name:
                now_content_text_page = self.get_page_number_new(content)
                # 上一个是table内容
                if pre_table_text:
                    for splitted_table_text in self.get_splitted_table_text(
                        pre_table_text
                    ):
                        split_contents.append(
                            {
                                "text": splitted_table_text,
                                "page": pre_table_page,
                                "type": ContentType.TABLE.name,
                                "table_title": pre_table_title,
                                "table_description": pre_table_description,
                            }
                        )
                pre_table_text = ""
                pre_table_description = ""
                pre_table_title = ""
                now_content_text = content.get("text", "")
                # 前面已有文本内容
                if pre_text:
                    # 内容过长 就不会拼接起来
                    if (
                        len(self._splitter._tokenizer(pre_text + now_content_text))
                        >= self._splitter.chunk_size
                    ):
                        split_contents.append(
                            {
                                "text": pre_text,
                                "page": pre_text_page,
                                "type": ContentType.TEXT.name,
                            }
                        )
                        if (
                            len(self._splitter._tokenizer(now_content_text))
                            >= self._splitter.chunk_size
                        ):
                            now_content_text_chunks = self.split_texts_only(
                                now_content_text
                            )
                            for now_content_text_chunk in now_content_text_chunks:
                                page_verify = self.get_page_number(
                                    now_content_text_chunk, contents
                                )  # 合并导致的误差
                                split_contents.append(
                                    {
                                        "text": now_content_text_chunk,
                                        "page": now_content_text_page
                                        if page_verify <= now_content_text_page
                                        else page_verify,
                                        "type": ContentType.TEXT.name,
                                    }
                                )
                            pre_text = ""
                        else:
                            pre_text = now_content_text
                            pre_text_page = now_content_text_page
                    else:  # 内容会直接拼接起来
                        pre_text += "\n" + now_content_text
                else:  # 前面没有文本内容
                    pre_text_page = now_content_text_page
                    if (
                        len(self._splitter._tokenizer(now_content_text))
                        >= self._splitter.chunk_size
                    ):
                        now_content_text_chunks = self.split_texts_only(
                            now_content_text
                        )
                        for now_content_text_chunk in now_content_text_chunks:
                            page_verify = self.get_page_number(
                                now_content_text_chunk, contents
                            )
                            split_contents.append(
                                {
                                    "text": now_content_text_chunk,
                                    "page": now_content_text_page
                                    if page_verify <= now_content_text_page
                                    else page_verify,
                                    "type": ContentType.TEXT.name,
                                }
                            )
                    else:
                        pre_text = now_content_text
            # table内容
            elif content_type == ContentType.TABLE.name:
                # 前面有文本内容
                if pre_text:
                    split_contents.append(
                        {
                            "text": pre_text,
                            "page": pre_text_page,
                            "type": ContentType.TEXT.name,
                        }
                    )
                pre_text = ""
                now_table_info = content.get("table", "")
                now_table_description = content.get("text", "")
                now_table_page = self.get_page_number_new(content)
                if (
                    now_table_info
                    and now_table_info.get("table_text", "")
                    and now_table_info.get("table_text", "").get("html", "")
                ):
                    now_table_title = now_table_info.get("table_title", "")
                    now_table_text = now_table_info.get("table_text", "").get(
                        "html", ""
                    )
                    # 前面有table内容
                    if pre_table_text:
                        # 如果表格内容不是数组 直接对内容进行切分
                        if not is_json_table(pre_table_text):
                            if pre_table_title and not now_table_title:  # 表合并
                                if (
                                    len(
                                        self._table_splitter._tokenizer(
                                            pre_table_text + now_table_text
                                        )
                                    )
                                    >= self._table_splitter.chunk_size
                                ) and ask_doc_index_doc_table_split:
                                    split_contents.append(
                                        {
                                            "text": pre_table_text,
                                            "page": pre_table_page,
                                            "type": ContentType.TABLE.name,
                                            "table_title": pre_table_title,
                                            "table_description": pre_table_description,
                                        }
                                    )
                                    if (
                                        len(
                                            self._table_splitter._tokenizer(
                                                now_table_text
                                            )
                                        )
                                        >= self._table_splitter.chunk_size
                                    ):
                                        now_table_text_chunks = self.split_tables_only(
                                            now_table_text
                                        )
                                        for (
                                            now_table_text_chunk
                                        ) in now_table_text_chunks:
                                            split_contents.append(
                                                {
                                                    "text": now_table_text_chunk,
                                                    "page": now_table_page,
                                                    "type": ContentType.TABLE.name,
                                                    "table_title": pre_table_title,
                                                    "table_description": pre_table_description,
                                                }
                                            )
                                        pre_table_text = ""
                                        pre_table_description = ""
                                    else:
                                        pre_table_text = now_table_text
                                        pre_table_description = now_table_description
                                        pre_table_page = now_table_page
                                else:
                                    pre_table_text += "\n" + now_table_text
                            else:
                                pre_table_text_chunks = self.split_tables_only(
                                    pre_table_text
                                )
                                for pre_table_text_chunk in pre_table_text_chunks:
                                    split_contents.append(
                                        {
                                            "text": pre_table_text_chunk,
                                            "page": pre_table_page,
                                            "type": ContentType.TABLE.name,
                                            "table_title": pre_table_title,
                                            "table_description": pre_table_description,
                                        }
                                    )
                                if (
                                    len(self._table_splitter._tokenizer(now_table_text))
                                    >= self._table_splitter.chunk_size
                                ):
                                    now_table_text_chunks = self.split_tables_only(
                                        now_table_text
                                    )
                                    for now_table_text_chunk in now_table_text_chunks:
                                        split_contents.append(
                                            {
                                                "text": now_table_text_chunk,
                                                "page": now_table_page,
                                                "type": ContentType.TABLE.name,
                                                "table_title": now_table_title,
                                                "table_description": pre_table_description,
                                            }
                                        )
                                    pre_table_text = ""
                                    pre_table_description = ""
                                    pre_table_title = now_table_title
                                else:
                                    pre_table_text = now_table_text
                                    pre_table_description = now_table_description
                                    pre_table_title = now_table_title
                                    pre_table_page = now_table_page
                        else:  # 如果表格内容是数组 按行对内容进行切分
                            for splitted_table_text in self.get_splitted_table_text(
                                pre_table_text
                            ):
                                split_contents.append(
                                    {
                                        "text": splitted_table_text,
                                        "page": pre_table_page,
                                        "type": ContentType.TABLE.name,
                                        "table_title": pre_table_title,
                                        "table_description": pre_table_description,
                                    }
                                )
                            pre_table_text = now_table_text
                            pre_table_description = now_table_description
                            pre_table_title = now_table_title
                            pre_table_page = now_table_page
                    else:  # 前面没有table内容
                        # 如果表格内容不是数组 直接对内容进行切分
                        if not is_json_table(now_table_text):
                            if (
                                len(self._table_splitter._tokenizer(now_table_text))
                                >= self._table_splitter.chunk_size
                            ):
                                now_table_text_chunks = self.split_tables_only(
                                    now_table_text
                                )
                                for now_table_text_chunk in now_table_text_chunks:
                                    split_contents.append(
                                        {
                                            "text": now_table_text_chunk,
                                            "page": now_table_page,
                                            "type": ContentType.TABLE.name,
                                            "table_title": now_table_title
                                            if now_table_title
                                            else pre_table_title,
                                            "table_description": pre_table_description,
                                        }
                                    )
                                pre_table_title = now_table_title
                            else:
                                pre_table_text = now_table_text
                                pre_table_description = now_table_description
                                pre_table_title = now_table_title
                                pre_table_page = now_table_page
                        else:  # 如果表格内容是数组 按行对内容进行切分
                            pre_table_text = now_table_text
                            pre_table_description = now_table_description
                            pre_table_title = now_table_title
                            pre_table_page = now_table_page
        # 还有文本内容
        if pre_text:
            split_contents.append(
                {"text": pre_text, "page": pre_text_page, "type": ContentType.TEXT.name}
            )
        # 还有table内容
        if pre_table_text:
            for splitted_table_text in self.get_splitted_table_text(pre_table_text):
                split_contents.append(
                    {
                        "text": splitted_table_text,
                        "page": pre_table_page,
                        "type": ContentType.TABLE.name,
                        "table_title": pre_table_title,
                        "table_description": pre_table_description,
                    }
                )

        return split_contents

    """
    对文本内容做split切分，依据句号保证完整性 chunk_size=500
    Args:
        text: 文本内容 String
    Return:
        texts: 切分得到的文本内容 List
    """

    def split_texts_only(self, text: str) -> []:
        contents = text.split("。")
        texts = []
        tmp_content = ""
        for content_index, content in enumerate(contents):
            if content:
                pre_tmp_content = tmp_content
                if not pre_tmp_content:
                    if content_index == len(contents) - 1:
                        pre_tmp_content = content
                    else:
                        pre_tmp_content = content + "。"
                else:
                    if content_index == len(contents) - 1:
                        pre_tmp_content = pre_tmp_content + content
                    else:
                        pre_tmp_content = pre_tmp_content + content + "。"
                length = len(self._splitter._tokenizer(pre_tmp_content))
                # 判断加上当前这句话之后是否会超过chunk_size
                if length >= self._splitter.chunk_size and tmp_content:
                    tmp_texts = self._splitter.split_text(tmp_content)
                    texts.extend(tmp_texts)
                    if content_index == len(contents) - 1:
                        tmp_content = content
                    else:
                        tmp_content = content + "。"
                else:
                    tmp_content = pre_tmp_content
        if tmp_content:
            tmp_texts = self._splitter.split_text(tmp_content)
            texts.extend(tmp_texts)
        return texts

    """
    对表格文本内容(html)做split切分，依据'\n'保证完整性，chunk_size=4096
    Args:
        text: 表格文本内容 String
    Return:
        texts: 切分得到的表格文本内容 List
    """

    def split_tables_only(self, text: str) -> []:
        if not ask_doc_index_doc_table_split:
            return [text]
        contents = text.split("\n")
        texts = []
        tmp_content = ""
        for content in contents:
            if content:
                pre_tmp_content = tmp_content
                if not pre_tmp_content:
                    pre_tmp_content = content
                else:
                    pre_tmp_content = pre_tmp_content + "\n" + content
                length = len(self._table_splitter._tokenizer(pre_tmp_content))
                # 判断加上当前内容后是否会超过chunk_size
                if length >= self._table_splitter.chunk_size and tmp_content:
                    tmp_texts = self._table_splitter.split_text(tmp_content)
                    texts.extend(tmp_texts)
                    tmp_content = content
                else:
                    tmp_content = pre_tmp_content
        if tmp_content:
            tmp_texts = self._table_splitter.split_text(tmp_content)
            texts.extend(tmp_texts)
        return texts

    """
    对表格文本内容(数组)做split切分，依据[]行保证完整性，chunk_size=4096
    Args:
        table_json_text: 表格文本内容 String json
        head_row_num: 列表里面前几行是标题，默认第一行是标题行，值为1
    Return:
        texts: 切分得到的表格文本内容 List
    """

    def split_table_json_text(self, table_json_text: str, head_row_num: int = 1) -> []:
        is_use_list = False  # 是否使用二维数组描述表的内容 如果为否则使用k-v描述(带上表头信息)组织每一行的内容

        texts = []
        contents = json.loads(table_json_text)
        if not ask_doc_index_doc_table_split:
            return [
                table_json_text
                if is_use_list
                else json.dumps(
                    self.tale_list_organized(contents, head_row_num), ensure_ascii=False
                )
            ]
        tmp_content = []
        table_head_row = list(contents[:head_row_num])
        for content in contents[head_row_num:]:
            if not self.is_valid_row(content):
                continue
            if content:
                pre_tmp_content = copy.deepcopy(tmp_content)
                if not pre_tmp_content:
                    pre_tmp_content = list(table_head_row)
                    pre_tmp_content.append(content)
                else:
                    pre_tmp_content.append(content)
                length = len(
                    self._table_splitter._tokenizer(
                        json.dumps(
                            pre_tmp_content
                            if is_use_list
                            else self.tale_list_organized(
                                pre_tmp_content, head_row_num
                            ),
                            ensure_ascii=False,
                        )
                    )
                )
                # 判断加上当前内容后是否会超过chunk_size
                if length >= self._table_splitter.chunk_size and tmp_content:
                    new = len(
                        self._table_splitter._tokenizer(
                            json.dumps(
                                tmp_content
                                if is_use_list
                                else self.tale_list_organized(
                                    tmp_content, head_row_num
                                ),
                                ensure_ascii=False,
                            )
                        )
                    )
                    tmp_texts = self._table_splitter.split_text(
                        json.dumps(
                            tmp_content
                            if is_use_list
                            else self.tale_list_organized(tmp_content, head_row_num),
                            ensure_ascii=False,
                        )
                    )
                    texts.extend(tmp_texts)
                    tmp_content = list(table_head_row)
                    tmp_content.append(content)
                else:
                    tmp_content = copy.deepcopy(pre_tmp_content)
        if tmp_content:
            tmp_texts = self._table_splitter.split_text(
                json.dumps(
                    tmp_content
                    if is_use_list
                    else self.tale_list_organized(tmp_content, head_row_num),
                    ensure_ascii=False,
                )
            )
            texts.extend(tmp_texts)
        return texts

    """
    针对二维数组，使用k-v描述(带上表头信息)组织每一行的内容
    Args:
        head_rows_num: 表头行数，默认只有第一行是表头  int
        tale_list: 二维数组表内容 List
    Return:
        organized_table: 重新组织后的表内容
    """

    def tale_list_organized(self, tale_list: [], head_rows_num: int = 1) -> []:
        if not tale_list:
            return []
        organized_table = []
        table_heads = []
        for table_head_row in tale_list[:head_rows_num]:  # 处理表头
            if not table_heads:
                table_heads = list(table_head_row)
            else:
                for cell_index, table_head_cell in enumerate(table_head_row):
                    if cell_index < len(table_heads):
                        table_heads[cell_index] = (
                            table_head_cell
                            if not table_heads[cell_index]
                            else table_heads[cell_index] + "_" + table_head_cell
                        )
                    else:
                        table_heads.append(table_head_cell)
        for table_row in tale_list[head_rows_num:]:  # 处理内容
            table_row_result = {}
            for table_cell_index, table_row_cell in enumerate(table_row):
                if table_cell_index < len(table_heads):
                    table_row_result[table_heads[table_cell_index]] = table_row_cell
                else:
                    table_row_result[
                        "no_head" + "_" + str(table_cell_index)
                    ] = table_row_cell
            organized_table.append(table_row_result)
        if not organized_table and table_heads:
            table_head_row_result = {}
            for table_head in table_heads:
                table_head_row_result[table_head] = ""
            organized_table.append(table_head_row_result)
        return organized_table

    """
    针对二维数组中的一行数据，判断是否是无效数据行
    Args:
        row: 表行 [x,x,x,x]
    Return:
        is_valid_row: 是否有效数据行
    """

    def is_valid_row(self, row: []) -> bool:
        is_valid_row = True
        if all(not cell for cell in row):
            is_valid_row = False
        elif all(
            not cell or not str(cell).replace("-", "") for cell in row[1:]
        ) and re.match(r"^\d+$", str(row[0])):
            is_valid_row = False
        return is_valid_row

    """
    得到文本内容所在页面数
    Args:
        split_text: 文本内容 String
        contents: 章节的内容 List
    Return:
        page_num: 文本内容所在页面数 int
    """

    def get_page_number(self, split_text: str, contents: []) -> int:
        for content in contents:
            text_paragraphs = split_text.split("\n")
            text_sentences = text_paragraphs[0].split("。")
            if text_sentences and text_sentences[0].strip() in content.get("text", ""):
                extra_info = content.get("extra_info")
                if extra_info:
                    return extra_info.get("real_page_num", 0)
        return 0

    """
    得到内容的页面数
    Args:
        content: 章节的单个内容 dict
    Return:
        page_num: 内容的页面数 int
    """

    def get_page_number_new(self, content: {}) -> int:
        extra_info = content.get("extra_info")
        if extra_info:
            return extra_info.get("real_page_num", 0)
        return 0


def is_json_table(content: str):
    try:
        json_object = json.loads(content)
        if not isinstance(json_object, list):
            return False
    except json.JSONDecodeError:
        return False
    return True


if __name__ == "__main__":
    nodeParser = GeneralNodeParser()
    with open(
        "/Users/<USER>/Downloads/汇总-合作伙伴调研0313.xlsx.json",
        "r",
    ) as f:
        json_datas = json.load(f)
    root_nodes, nodes = nodeParser.get_nodes_from_json(json_datas, 79, 14)
