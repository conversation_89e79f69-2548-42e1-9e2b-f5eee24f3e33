build_doc_builder_yolo_service:
	DOCKER_BUILDKIT=1 docker build python -f deploy/doc-builder/yolo-service/yolo-gpu-service.Dockerfile -t yolo-service:latest

run_yolo_service_cpu:
	docker rm -f yolo-service
	docker run -d --restart=always -v$(shell pwd)/python/nl2metric/nl2document/builder/yolo_service/.env.cpu:/builder/yolo_service/.env --name yolo-service -p 51080:8000 yolo-service:latest
	sleep 60
	bash deploy/doc-builder/yolo-service/yolo-service-test.sh deploy/doc-builder/test_image.png

run_yolo_service_gpu:
	docker rm -f yolo-service
	docker run -d --restart=always -v$(shell pwd)/python/nl2metric/nl2document/builder/yolo_service/.env.gpu:/builder/yolo_service/.env --name yolo-service --gpus '"device=0"' -p 51080:8000 yolo-service:latest
	sleep 60
	bash deploy/doc-builder/yolo-service/yolo-service-test.sh deploy/doc-builder/test_image.png

build_doc_builder_paddle_service:
	DOCKER_BUILDKIT=1 docker build python -f deploy/doc-builder/paddle-service/paddle-gpu-service.Dockerfile -t paddle-service:latest

run_paddle_service_cpu:
	docker rm -f paddle-service
	docker run -d --restart=always -v$(shell pwd)/python/nl2metric/nl2document/builder/paddle_service/.env.cpu:/builder/paddle_service/.env --name paddle-service -p 51081:8001 paddle-service:latest
	sleep 60
	bash deploy/doc-builder/paddle-service/paddle-service-test.sh deploy/doc-builder/test_image.png

run_paddle_service_gpu:
	docker rm -f paddle-service
	docker run -d --restart=always -v$(shell pwd)/python/nl2metric/nl2document/builder/paddle_service/.env.gpu:/builder/paddle_service/.env --name paddle-service --gpus '"device=0"' -p 51081:8001 paddle-service:latest
	sleep 60
	bash deploy/doc-builder/paddle-service/paddle-service-test.sh deploy/doc-builder/test_image.png

run_mixtral_origin:
	docker run -d --restart=always --name ask-bi-vllm-origin --rm --gpus=all -e CUDA_VISIBLE_DEVICES="4,5" --shm-size=10gb --ipc=host   -v /data2/public_file2/LLM_model/Mixtral-8x7B-Instruct-v0.1/awq:/model/mixtral-8-7b -p 30011:20010 ask-bi-vllm:latest  python -m vllm.entrypoints.openai.api_server --max-model-len=16384  --host 0.0.0.0 --model /model/mixtral-8-7b --tensor-parallel-size 2 --load-format safetensors --port 20010 --gpu-memory-utilization 0.7

run_mixtral:
	docker run -d --restart=always --name ask-bi-vllm  --gpus=all -e CUDA_VISIBLE_DEVICES="0,1" --shm-size=10gb --ipc=host   -v /data2/public_file2/LLM_model/merged_jiaohang_mixtral_7b8_750_0223_awq:/model/mixtral-8-7b -p 30010:20010 ask-bi-vllm:latest  python -m vllm.entrypoints.openai.api_server  --max-model-len=16384   --host 0.0.0.0 --model /model/mixtral-8-7b --tensor-parallel-size 2 --load-format safetensors --port 20010 --gpu-memory-utilization 0.7

run_docker_node:
	docker rm -fv ask-bi-node
	docker run --env-file=$(shell pwd)/.env --restart=always --name ask-bi-node -d -p 8000:8000 ask-bi-node:latest

run_docker_python:
	docker rm -fv ask-bi-python
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python -d -p 9099:9099 -v /home/<USER>/docker/python/logs:/ask-bi/python/nl2metric/logs -w /ask-bi/python/nl2metric ask-bi-python:latest  bash start.sh

run_docker_python_arm:
	docker rm -fv ask-bi-python-arm
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python-arm -d -p 9099:9099 -v /home/<USER>/docker/python/logs:/ask-bi/python/nl2metric/logs -w /ask-bi/python/nl2metric --entrypoint bash  ask-bi-python-arm:latest start.sh

run_docker_python_gpu:
	docker rm -fv ask-bi-python
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python --gpus '"device=0"' -e rank_model_device=cuda -d -p 9099:9099 -v /home/<USER>/docker/python/logs:/ask-bi/python/nl2metric/logs -w /ask-bi/python/nl2metric ask-bi-python:latest  bash start.sh

run_docker_python_local:
	docker rm -fv ask-bi-python
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python -d -p 9099:9099 -v $(shell pwd):/ask-bi -w /ask-bi/python/nl2metric ask-bi-python:latest  uvicorn fastapi_server:app --host 0.0.0.0 --port 9099 --workers 1

run_docker_python_debug:
	docker rm -fv ask-bi-python-debug
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python-debug -d -p 10099:9099    -w /ask-bi/python/nl2metric ask-bi-python:latest  bash start.sh

run_docker_document_builder:
	docker rm -fv ask-bi-document-builder
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-document-builder -d  -v $(shell pwd):/ask-bi -w /ask-bi/python/nl2metric ask-bi-python:latest  python builder_server.py

run_docker_document_builder-arm:
	docker rm -fv ask-bi-document-builder-arm
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-document-builder-arm -d --entrypoint python3 -v $(shell pwd)/:/ask-bi -w /ask-bi/python/nl2metric ask-bi-python-arm:latest  builder_server.py

run_docker_document_parser_preview:
	docker rm -fv ask-bi-document-parser-preview
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-document-parser-preview --gpus '"device=0"' -d -p 9599:9599  -v $(shell pwd):/ask-bi  -w /ask-bi/python/nl2metric doc-builder-service:latest  python -m biz.evaluate_biz.main

run_docker_report_generate:
	docker rm -fv ask-bi-report-generate
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-report-generate -d -p 30099:9099  -w /ask-bi/python/nl2metric ask-doc-report-generate:latest  bash start.sh

run_python:
	cd python/nl2metric && CUDA_VISIBLE_DEVICES='' bash start.sh --listen 0.0.0.0:9099

test_nl_tasks:
	@cd python/nl2metric/scripts && bash test-nl-tasks.sh $(filter-out $@,$(MAKECMDGOALS))

lint:
	sh ./scripts/lint-python.sh

insert_few_shot:
	cd python/nl2metric && PYTHONPATH=${PYTHONPATH}:$(shell pwd) python scripts/insert_few_shot.py

flake:
	cd python/nl2metric && PYTHONPATH=${PYTHONPATH}:$(shell pwd) flake8 .

ff: format flake

format:
	cd python/nl2metric && black .
	cd python/nl2metric && isort .
	cd python/nl2metric && black .
